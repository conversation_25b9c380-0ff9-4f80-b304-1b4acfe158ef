build-sr9-job:
  stage: build
  tags:
    - SR9
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_windows_x86.bat -m SR9
  cache:
    key: build-SR9-cache
    paths:
      - build/SR9
    policy: push

release-sr9-job:
  stage: release
  tags:
    - SR9
  rules:
    - if: $model == "SR9"
  before_script:
    - ./ci/shell/get_known_hosts.bat
    - ./ci/shell/pullrelatedrep.bat -r $ResourceBranch -e E:\GitLab-Runner
  script:
    - ./ci/shell/USAPI_Release.bat -X E:\GitLab-Runner\builds\44z3iQM5\0\usplatform\xunit -R E:\GitLab-Runner\code\resource-xunit -m sr9 -a x64 -v $MainVersion -n $VersionAdd


