build-grape-job:
  stage: build
  tags:
    - Grape
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_aarch_64bit.sh -m Grape
  cache:
    key: build-Grape-cache
    paths:
      - build/Grape
    policy: push

release-grape-job:
  stage: release
  tags:
    - Grape
  rules:
    - if: $model == "Grape"
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m Grape -r $ResourceBranch -a $AppcommonBranch -t $CI_COMMIT_BRANCH -v $PreVersion
  script:
    - ./ci/shell/runrelease.sh -m Grape -v $MainVersion -a $VersionAdd -x aarch64