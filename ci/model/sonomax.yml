build-windows-sonomax-job:
  stage: build
  tags:
    - windows_prebuild
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - python ./configure_windows_x86.py -a x64 -m SonoMax
  cache:
    key: build-SonoMax-cache
    paths:
      - build/SonoMax
    policy: push

release-sonomax-job:
  stage: release
  tags:
    - windows_release
  rules:
    - if: $model == "SonoMax"
  before_script:
    - bash.exe ./ci/shell/getknown_hosts.sh -x windows
    - bash.exe ./ci/shell/pullrelatedrep.sh -m SonoMax -r $ResourceBranch -x windows -a $AppcommonBranch -t $CI_COMMIT_BRANCH -v $PreVersion
  script:
    - bash.exe ./ci/shell/runrelease.sh -m SonoMax -v $MainVersion -a $VersionAdd -x windows



