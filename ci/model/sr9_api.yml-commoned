build-USAPI-job:
  stage: build
  tags:
    - SR9
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_windows_x86.bat -a x64 -m SR9 -t ON
  cache:
    key: build-USAPI-cache
    paths:
      - build/USAPI
    policy: push

release-USAPI-job:
  stage: release
  tags:
    - SR9
  rules:
    - if: $model == "USAPI"
  before_script:
    - ./ci/shell/get_known_hosts.bat
    - ./ci/shell/pullrelatedrepapi.bat -r $ResourceBranch -u $UsapiexampleBranch -e E:\GitLab-Runner
  script:
    - ./ci/shell/USAPI_Release.bat -X E:\GitLab-Runner\builds\44z3iQM5\0\usplatform\xunit -U E:\GitLab-Runner\code\usapiexample -R E:\GitLab-Runner\code\resource-xunit -m sr9 -a $HostArch -v $MainVersion -n $VersionAdd -t ON -p $Probes

