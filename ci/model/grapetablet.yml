build-grapetablet-job:
  stage: build
  tags:
    - GrapeTablet
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  script:
    - ./configure_aarch_64bit.sh -m GrapeTablet
  cache:
    key: build-GrapeTablet-cache
    paths:
      - build/GrapeTablet
    policy: push

release-grapetablet-job:
  stage: release
  tags:
    - GrapeTablet
  rules:
    - if: $model == "GrapeTablet"
  before_script:
    - ./ci/shell/getknown_hosts.sh
    - ./ci/shell/pullrelatedrep.sh -m GrapeTablet -r $ResourceBranch -a $AppcommonBranch -t $CI_COMMIT_BRANCH -v $PreVersion
  script:
    - ./ci/shell/runrelease.sh -m GrapeTablet -v $MainVersion -a $VersionAdd -x aarch64
