#!/bin/bash

# Variables
USER="root"  # Replace with the actual user
IP=$1       # Replace with the actual IP address

# 定义远程路径名称
HTML_FILE="/usr/tigerapp/autotest/autotest_report.html"
TEMP_MOUNT_DIR="/usr/tigerapp/autotest_temp"
SHARE_DIR="//*************/temp/autotest"
TARGET_MACHINE="$USER@$IP"
TIME_STAMP=$(date '+%Y%m%d%H%M%S')
NEW_FILE_NAME="${TIME_STAMP}_${IP}_report.html"

# 检查是否设备在线
if ! ping -c 1 "$IP" &> /dev/null; then
    echo "Error: The remote machine at $IP is not online. Check autotest result cannot proceed."
    exit 1
fi

ssh-keyscan -H $IP >> ~/.ssh/known_hosts

# 检查远程 HTML 文件是否存在
echo "Check if the HTML file exists."
ssh "$TARGET_MACHINE" test -f "$HTML_FILE" || { echo "File does not exist: $HTML_FILE"; exit 1; }

# 拷贝到共享目录
echo "Build temp dir: $TEMP_MOUNT_DIR"
ssh "$TARGET_MACHINE" "mkdir -p $TEMP_MOUNT_DIR && echo 'Created temp directory: $TEMP_MOUNT_DIR'"

# 挂载共享目录
echo "Mount shared directory."
ssh "$TARGET_MACHINE" "mount -t cifs $SHARE_DIR $TEMP_MOUNT_DIR -o username='ver',password='ver'"
if ! ssh "$TARGET_MACHINE" "test -d $TEMP_MOUNT_DIR"; then
    echo "Failed to mount the shared directory."
    exit 1
fi

# 将 HTML 文件拷贝到共享目录
echo "Copy the HTML file to the shared directory."
ssh "$TARGET_MACHINE" "cp $HTML_FILE $TEMP_MOUNT_DIR/$NEW_FILE_NAME"

# 取消挂载并删除本地临时文件夹
echo "Unmount and delete temporary dir."
ssh "$TARGET_MACHINE" "umount $TEMP_MOUNT_DIR"
ssh "$TARGET_MACHINE" "rmdir $TEMP_MOUNT_DIR"

# 检查远程 HTML 文件中是否包含 "PassCase:"
echo "Check autotest results"
PASS_CASE=$(ssh "$TARGET_MACHINE" "grep -oP 'PassCase:\s*\K\d+/\d+' $HTML_FILE | cut -d'/' -f1")
TOTAL_CASE=$(ssh "$TARGET_MACHINE" "grep -oP 'PassCase:\s*\K\d+/\d+' $HTML_FILE | cut -d'/' -f2")

echo "Number of available cases: $TOTAL_CASE, Number of pass cases: $PASS_CASE."
echo "Detailed report saved in path:${SHARE_DIR}/${NEW_FILE_NAME}"

# 检查是否成功提取到数字
if [[ -n "$PASS_CASE" && -n "$TOTAL_CASE" ]]; then
    # 检查通过的用例和总用例是否相等
    if [ "$PASS_CASE" = "$TOTAL_CASE" ]; then
        echo "All test cases passed."
        exit 0
    else
        echo "There are test cases that have not passed."
        exit 1
    fi
fi

echo "Other errors"
exit 1
