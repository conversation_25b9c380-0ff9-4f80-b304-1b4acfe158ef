#!/bin/sh
# 增量格式化代码

TARGET_BRANCH_SHA=$(git rev-parse origin/$1)
SOURCE_BRANCH_SHA=$(git rev-parse origin/$2)

echo "TARGET_BRANCH_NAME: " $1
echo "TARGET_BRANCH_SHA: " $TARGET_BRANCH_SHA
echo "SOURCE_BRANCH_NAME: " $2
echo "SOURCE_BRANCH_SHA: " $SOURCE_BRANCH_SHA
echo "COMMIT_REF_NAME: " $3

# 定义需要排除的文件/目录模式列表
EXCLUDE_PATTERNS=(
    "bfpnames.cpp"
    "bfpnames.h"
    # 可以在这里添加更多需要排除的文件/目录模式
)

# 获取合并请求中的增量更改文件列表
CHANGED_FILES=$(git diff --name-only $TARGET_BRANCH_SHA $SOURCE_BRANCH_SHA | grep -E '\.(cpp|h|cc)$')

# 从CHANGED_FILES中过滤掉排除的文件
for pattern in "${EXCLUDE_PATTERNS[@]}"; do
    CHANGED_FILES=$(echo "$CHANGED_FILES" | grep -v "$pattern")
done

echo "Changed files (after exclusion): $CHANGED_FILES"

# 检查是否有改动文件
if [ -z "$CHANGED_FILES" ]; then
    echo "No C++ files to format after exclusion."
    exit 0
fi

# 对改动文件执行 clang-format 并重新提交
for file in $CHANGED_FILES; do
    clang-format -i $file
    git add $file
done

# 自动提交格式化更改
git config --global user.email "<EMAIL>"
git config --global user.name "gitlab"
if ! git diff --cached --quiet; then
    git commit -m "[All][CleanCode] Apply Clang-Format by Gitlab." --no-verify
    git push git@192.168.20.38:usplatform/xunit.git HEAD:$3
fi