#!/bin/bash

# Variables
USER="root"  # Replace with the actual user
IP=$1  # Replace with the actual IP address

# Check if the remote machine is online
if ! ping -c 1 "$IP" &> /dev/null; then
    echo "Error: The remote machine at $IP is not online. Run autotest cannot proceed."
    exit 1
fi

getrepo() 
{
    repName=$1
    if [[ ! -d "~/code/$repName" ]]; then
        git clone git@*************:$2/$repName.git ~/code/$repName
    fi

    pwdtemp=`pwd`
    cd ~/code/$repName
    git fetch
    git reset --hard origin/$3
    echo $pwdtemp
    cd $pwdtemp
}

TARGET_MACHINE="$USER@$IP"
REMOTE_DIR="/usr/tigerapp"  # Replace with the actual remote directory
testcase_branch=$2 # Replace with the actual testcase branch
replay_count=$3

# Add the target machine's key to known_hosts
ssh-keyscan -H $IP >> ~/.ssh/known_hosts

# Pull autotestcase
getrepo "testcase" "usplatform" $testcase_branch

# Remove testcase
ssh "$TARGET_MACHINE" "rm -r $REMOTE_DIR/autotest"

# Get testcase names
testcase_names=$(find ~/code/testcase/ -maxdepth 1 -type d ! -name '.git' ! -path '*/testcase/' -printf "%f ")
echo "This branch testcase: $testcase_names"
if [ -z "$testcase_names" ]; then
    echo "No testcase found."
    exit 1
fi

# Copy testcase
echo "Copying files from ~/code/testcase/* to $TARGET_MACHINE:$REMOTE_DIR/"
ssh "$TARGET_MACHINE" "mkdir -p $REMOTE_DIR/autotest && echo 'Created directory: $REMOTE_DIR/autotest'"
ssh "$TARGET_MACHINE" "rm -r $REMOTE_DIR/autotest/* && echo 'Cleared directory: $REMOTE_DIR/autotest'"
find ~/code/testcase/ -maxdepth 1 -type d \
! -name '.git' \
! -path '*/testcase/' \
-exec scp -r {} $TARGET_MACHINE:$REMOTE_DIR/autotest/ \;

# Start replay
echo "Start replay on SonoAir: $IP..."
ssh -f "$TARGET_MACHINE" \
"export DISPLAY=:0.0 \
&& cd $REMOTE_DIR \
&& exec nohup ./replay --testcase_names $testcase_names --replay_count $replay_count > /dev/null 2>&1 &"

exit 0