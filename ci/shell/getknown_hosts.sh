#!/bin/bash

while getopts ":x:" arg
do
    case $arg in
        x)  sys=$OPTARG;;
    esac
done

# 如果sys未定义，设置默认值为linux
if [ -z "$sys" ]; then
    sys="Linux"
fi

getknown_hosts() 
{
    echo "getknown_hosts and current system is: " $sys

    # 'command -v ssh-agent >/dev/null || ( apk add --update openssh )' 
    if [ $sys != "windows" ]; then 
        eval $(ssh-agent -s)
        echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add  
    fi
    mkdir -p ~/.ssh
    chmod 700 ~/.ssh
    ssh-keyscan ************* > ~/.ssh/known_hosts
    chmod 644 ~/.ssh/known_hosts

    git remote set-url origin git@*************:usplatform/xunit.git
    git config --global user.email "<EMAIL>"
    git config --global user.name "gitlab"
}

getknown_hosts
