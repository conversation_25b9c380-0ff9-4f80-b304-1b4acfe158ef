#!/bin/bash

echo_usage()
{
    cat <<-END
usage: 
    ./runcoverage.sh -s xunit -m Apple

Options:
    -h                      help
    -s                      project dir
    -m <model>              model, like Phoenix, Apple etc.

examples:


END
    exit 0
}

while getopts "hs:m:" arg
do
    case $arg in
        h)  echo_usage;;
        s)  src=$OPTARG;;
        m)  model=$OPTARG;;
    esac
done

cd build

echo "pwd is "  `pwd` ${src} ${model}

gcovr --root ${src} \
--gcov-exclude-directories ${model}/src/test \
--gcov-exclude-directories ${model}/src/resource \
--gcov-exclude-directories ${model}/tests \
--gcov-exclude-directories ${model}/test \
--gcov-exclude="(.+/)?moc_.*" \
--gcov-exclude="(.+/)?ui_.*" \
--gcov-exclude="(.+/)?\.moc$" \
--gcov-exclude="(.+/)?statemachine.cpp" \
--gcov-exclude="(.+/)?statemachine.h" \
--gcov-ignore-parse-errors --xml-pretty --xml --print-summary -o coverage.xml