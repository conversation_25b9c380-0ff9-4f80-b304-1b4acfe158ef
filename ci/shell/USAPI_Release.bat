@echo off
@REM ################################################################################
@REM #                                  process input args                     		#
@REM ################################################################################
set num=0
for %%a in (%*) do set /a num+=1
if /I "%1"=="-h" (
    call:usage
    pause
    goto:end
)

setlocal EnableDelayedExpansion
set index=0
for %%a in (%*) do (
    set a[!index!]=%%~a
    set /a index+=1
)
set /a range=!index!-1
for /l %%n in (0,2,!range!) do (
            if "!a[%%n]!" == "-X" (
                set /a XUNITDIRIndex=%%n+1
            ) else if "!a[%%n]!"=="-R" (
                set /a RESPATHIndex=%%n+1
            ) else if /I "!a[%%n]!"=="-U" (
                set /a USAPIPATHIndex=%%n+1
            ) else if "!a[%%n]!"=="-m" (
                set /a MODELIndex=%%n+1
            )  else if /I "!a[%%n]!"=="-a" (
                set /a VCVARSALL_HOST_ARCHIndex=%%n+1
            ) else if "!a[%%n]!"=="-v" (
                set /a VERSIONIndex=%%n+1
            ) else if /I "!a[%%n]!"=="-p" (
                set /a PROBETYPEIndex=%%n+1
            ) else if /I "!a[%%n]!"=="-t" (
                set /a SR9TYPEIndex=%%n+1
            ) else if /I "!a[%%n]!"=="-n" (
                set /a NAMEINDEX=%%n+1
            )
)

if defined XUNITDIRIndex (
    set XUNITDIR=!a[%XUNITDIRIndex%]!
)
if defined RESPATHIndex (
    set RESPATH=!a[%RESPATHIndex%]!
)
if defined USAPIPATHIndex (
    set USAPIPATH=!a[%USAPIPATHIndex%]!
)
if defined MODELIndex (
    set MODEL=!a[%MODELIndex%]!
)
if defined VCVARSALL_HOST_ARCHIndex (
    set VCVARSALL_HOST_ARCH=!a[%VCVARSALL_HOST_ARCHIndex%]!
    set HOST_ARCH_BIT="32"
)
if defined VERSIONIndex (
    set VERSION=!a[%VERSIONIndex%]!
)
if defined PROBETYPEIndex (
    set PROBETYPE=!a[%PROBETYPEIndex%]!
)
if defined SR9TYPEIndex (
    set SR9TYPE=!a[%SR9TYPEIndex%]!
)
if defined NAMEINDEX (
    set NAME=!a[%NAMEINDEX%]!_%VCVARSALL_HOST_ARCH%
)
setlocal DisableDelayedExpansion
@REM ################################################################################


call:main
goto:end

@echo off
@REM ################################################################################
@REM #                   cmake project-version update                         		#
@REM ################################################################################
:update_cmake_version
setlocal EnableDelayedExpansion

    for /f "delims=" %%i in (%XUNITDIR%\CMakeLists.txt) do (
        set a=%%i
        set "a=!a:project(xultrasound VERSION 0.1.1)=project(xultrasound VERSION %VERSION%)!"
        echo !a!>>tmp.txt
        )
    move tmp.txt %XUNITDIR%\CMakeLists.txt

setlocal DisableDelayedExpansion

goto:end

@echo off
@REM ################################################################################
@REM #                                  build C++                             		#
@REM ################################################################################
:build_Cpp

    if not defined VCVARSALL_HOST_ARCH (
        set VCVARSALL_HOST_ARCH=x86
    ) else if /I "%VCVARSALL_HOST_ARCH%" NEQ "x86" if /I "%VCVARSALL_HOST_ARCH%" NEQ "x64" (
        goto:errortip
    )

    if /I "%VCVARSALL_HOST_ARCH%" equ "x86" (
        set HOST_ARCH_BIT="32"
    ) else (
        set HOST_ARCH_BIT="64"
    )

    if /I %MODEL% equ sr9 (
        set MODEL=SR9
    ) 

    cd /d %XUNITDIR%
    call "%XUNITDIR%\configure_windows_x86.bat" -a %VCVARSALL_HOST_ARCH% -m %MODEL% -t %SR9TYPE%
    if /I %MODEL% equ SR9 (
        set MODEL=sr9
    )

goto:end
:errortip
    echo:
    echo:
    echo ######################  error build paras, please see usage and confir  m######################
    echo MODEL %MODEL%
    echo SR9TYPE %SR9TYPE%
    echo VCVARSALL_HOST_ARCH %VCVARSALL_HOST_ARCH%

goto:end

@echo off
@REM ################################################################################
@REM #                                  git tag                               		#
@REM ################################################################################
:git_tag

    if /I %SR9TYPE% equ ON (
        cd /d %USAPIPATH%
        "C:\Program Files\Git\bin\bash.exe" -i -c "git tag USAPI-v%VERSION%-%NAME%"
        "C:\Program Files\Git\bin\bash.exe" -i -c "git push origin USAPI-v%VERSION%-%NAME%"
        echo =================KEEP RUNNING!!!=================
    )

    cd /d %RESPATH%
    "C:\Program Files\Git\bin\bash.exe" -i -c "git tag USAPI-v%VERSION%-%NAME%"
    "C:\Program Files\Git\bin\bash.exe" -i -c "git push origin USAPI-v%VERSION%-%NAME%"
    echo =================STILL RUNNING!=================

    cd /d %XUNITDIR%
    "C:\Program Files\Git\bin\bash.exe" -i -c "git tag USAPI-v%VERSION%-%NAME%"
    "C:\Program Files\Git\bin\bash.exe" -i -c "git push origin USAPI-v%VERSION%-%NAME%"
    echo =================DO NOT TOUCH!=================

goto:end

@echo off
@REM ################################################################################
@REM #                                  encrypt res                         		#
@REM ################################################################################
:encrypt_res

    mkdir %XUNITDIR%\build\USAPI\bin\res\modelfiles\%MODEL%\mnnnzmrds\cache
    echo modelfiles>EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res %XUNITDIR%\build\USAPI\bin\res /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt
    echo cache_x64>EXCLUDE.txt
    echo cache_x86>>EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res\modelfiles\%MODEL% %XUNITDIR%\build\USAPI\bin\res\modelfiles\%MODEL% /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt
    if /I %VCVARSALL_HOST_ARCH% equ x86 (
        xcopy /Q /E %RESPATH%\res\modelfiles\%MODEL%\mnnnzmrds\cache_x86 %XUNITDIR%\build\USAPI\bin\res\modelfiles\%MODEL%\mnnnzmrds\cache
    ) else if /I %VCVARSALL_HOST_ARCH% equ x64 (
        xcopy /Q /E %RESPATH%\res\modelfiles\%MODEL%\mnnnzmrds\cache_x64 %XUNITDIR%\build\USAPI\bin\res\modelfiles\%MODEL%\mnnnzmrds\cache
    )
    cd /d %XUNITDIR%\build\USAPI\bin
    set PATH=lib;%PATH%;
    set QT_QPA_PLATFORM_PLUGIN_PATH=lib
    presetparamdatabaseread.exe model=%MODEL%

goto:end

@echo off
@REM ################################################################################
@REM #                                  clean res                            		#
@REM ################################################################################
:clean_res

    cd /d %XUNITDIR%\build\USAPI\bin
    xcopy /E /Q %USAPIPATH%\cleanres\bin\Release %XUNITDIR%\build\USAPI\bin
    call cleanres.bat sr9 %PROBETYPE%

goto:end

@echo off
@REM ################################################################################
@REM #                                  make exammode bak                           #
@REM ################################################################################
:make_exammode_bak

    echo HOST_ARCH_BIT %HOST_ARCH_BIT% "%XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\zip.exe"
    cd /d %XUNITDIR%\build\USAPI\bin\
    mkdir res\modelfiles\sr9\exammodebak
    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\zip.exe %XUNITDIR%\build\USAPI\bin
    zip -r -q "res\modelfiles\sr9\exammodebak\exammode_bak.dat" "res\modelfiles\sr9\exammode_human"
    del zip.exe

goto:end

@echo off
@REM ################################################################################
@REM #                              edit probe.ini                           		#
@REM ################################################################################
:deal_ini

    set modeliniDir=%XUNITDIR%\build\USAPI\bin\res\modelfiles\sr9\modelini
    mkdir %modeliniDir%\lib
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\lib %modeliniDir%\lib
    copy %USAPIPATH%\cleanres\shell\cleanprobe_%VCVARSALL_HOST_ARCH%.exe %modeliniDir%

    cd /d %modeliniDir% 
    set PATH=lib;%PATH%
    set QT_QPA_PLATFORM_PLUGIN_PATH=lib
    cleanprobe_%VCVARSALL_HOST_ARCH%.exe %PROBETYPE%

    rd /S /Q %modeliniDir%\lib
    del %modeliniDir%\cleanprobe_%VCVARSALL_HOST_ARCH%.exe

@echo off
@REM ################################################################################
@REM #                         edit modelconfig.ini                            		#
@REM ################################################################################
setlocal EnableDelayedExpansion

    for /f "delims=" %%i in (%modeliniDir%\modelconfig.ini) do (
        set a=%%i 
        set "b=!a:~0,16!"
        if !b! == ModelFilesUseIni (
            set "a=!a:true=false!"
        ) 
        
        if !b! == RenderWidgetSize (
            set "a=RenderWidgetSize=@Size(960 720)"
        ) 

        set "c=!a:~0,15!"
        if !c! == RenderImageSize (
            set "a=RenderImageSize=@Size(960 720)"
        ) 

        set "d=!a:~0,20!"
        if !d! == MainWindowResolution (
            set "a=MainWindowResolution=@Size(1024 768)"
        ) 

        set "f=!a:~0,12!"
        if !f! == DSCImageSize (
            set "a=DSCImageSize=@Size(960 720)"
        )          
        echo !a!>>tmp_ini.txt
    )
    move tmp_ini.txt %modeliniDir%\modelconfig.ini
setlocal DisableDelayedExpansion

goto:end

@echo off
@REM ################################################################################
@REM #                     AssemblyInfo.cs version update                      		#
@REM ################################################################################
:update_cs_version
setlocal EnableDelayedExpansion
    for /f "delims=" %%i in (%USAPIPATH%\Device\Properties\AssemblyInfo.cs) do (
        set a=%%i
        set "a=!a:[assembly: AssemblyVersion("*******")]=[assembly: AssemblyVersion("%VERSION%")]!"
        set "a=!a:[assembly: AssemblyFileVersion("*******")]=[assembly: AssemblyFileVersion("%VERSION%")]!"
        echo !a!>>tmp_DCS.txt
    )
    move tmp_DCS.txt %USAPIPATH%\Device\Properties\AssemblyInfo.cs

    for /f "delims=" %%i in (%USAPIPATH%\UltraSound\Properties\AssemblyInfo.cs) do (
        set a=%%i
        set "a=!a:[assembly: AssemblyVersion("*******")]=[assembly: AssemblyVersion("%VERSION%")]!"
        set "a=!a:[assembly: AssemblyFileVersion("*******")]=[assembly: AssemblyFileVersion("%VERSION%")]!"
        echo !a!>>tmp_UCS.txt
    )
    move tmp_UCS.txt %USAPIPATH%\UltraSound\Properties\AssemblyInfo.cs

    for /f "delims=" %%i in (%USAPIPATH%\USAPIExample\Properties\AssemblyInfo.cs) do (
        set a=%%i
        set "a=!a:[assembly: AssemblyVersion("*******")]=[assembly: AssemblyVersion("%VERSION%")]!"
        set "a=!a:[assembly: AssemblyFileVersion("*******")]=[assembly: AssemblyFileVersion("%VERSION%")]!"
        echo !a!>>tmp_ECS.txt
    )
    move tmp_ECS.txt %USAPIPATH%\USAPIExample\Properties\AssemblyInfo.cs

setlocal DisableDelayedExpansion

goto:end

@echo off
@REM ################################################################################
@REM #                                  build C#                            		#
@REM ################################################################################
:build_CS

    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\amd64\MSBuild.exe" %USAPIPATH%\UltraSound.sln /p:Configuration=Release /p:Platform=%VCVARSALL_HOST_ARCH%
    mkdir %USAPIPATH%\USAPIExample\bin\Release
    copy %USAPIPATH%\Device\bin\Release\Device.dll %USAPIPATH%\USAPIExample\bin\Release
    copy %USAPIPATH%\UltraSound\bin\Release\run.bat %USAPIPATH%\USAPIExample\bin\Release
    copy %USAPIPATH%\UltraSound\bin\Release\UltraSound.dll %USAPIPATH%\USAPIExample\bin\Release
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\amd64\MSBuild.exe" %USAPIPATH%\USAPIExample\USAPIExample.csproj /p:Configuration=Release /p:Platform=%VCVARSALL_HOST_ARCH%

goto:end

@echo off
@REM ################################################################################
@REM #                             doc and pdf                               		#
@REM ################################################################################
:deal_pdf

    mkdir %USAPIPATH%\docfx\src
    del %USAPIPATH%\docfx\_site_pdf\usapi_pdf.pdf
    copy %USAPIPATH%\UltraSound\*.cs %USAPIPATH%\docfx\src
    copy %USAPIPATH%\UltraSound\UltraSound.csproj %USAPIPATH%\docfx\src
    copy %USAPIPATH%\Device\*.cs %USAPIPATH%\docfx\src
    copy %USAPIPATH%\Device\Device.csproj %USAPIPATH%\docfx\src
    cd /d %USAPIPATH%\docfx
    call run.bat

    copy %USAPIPATH%\docfx\_site_pdf\usapi_pdf.pdf %USAPIPATH%\doc
    cd /d %USAPIPATH%\doc
    ren usapi_pdf.pdf "usapi v%VERSION%.pdf"

goto:end

@echo off
@REM ################################################################################
@REM #                             prepare for zip                           		#
@REM ################################################################################
:USAPI_prepare_for_zip

    tasklist | findstr /i ssh-agent.exe && taskkill /f /im ssh-agent.exe
    cd /d %XUNITDIR%\build\USAPI\bin
    set example_Release=%XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\example\USAPIExample\bin\Release

    mkdir %example_Release%\lib
    mkdir %example_Release%\res
    mkdir %example_Release%\styles
    mkdir %example_Release%\plugins
    mkdir %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\doc
    mkdir %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\runtime
    mkdir %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\fpga_fb463d
    echo .pdb>>EXCLUDE.txt
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\lib %example_Release%\lib /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\res %example_Release%\res
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\styles %example_Release%\styles
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\plugins %example_Release%\plugins
    xcopy /E /Q %USAPIPATH%\USAPIExample %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\example\USAPIExample
    rd /S /Q %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\example\USAPIExample\obj

    copy %USAPIPATH%\doc\"usapi v%VERSION%.pdf" %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\doc
    copy %USAPIPATH%\doc\"Description of vessel type detectionV1.0.pdf" %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\doc
    copy %USAPIPATH%\doc\"Description of battery shipping mode.doc" %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\doc
    copy %USAPIPATH%\doc\"How to check link error.pdf" %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\doc
    copy C:\Qt\vcredist\vcredist_msvc2019_x86.exe %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\runtime
    copy C:\Qt\vcredist\vcredist_msvc2019_x64.exe %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\runtime
    @REM copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\shutdownintercept\HookEnable.exe %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\runtime
    @REM copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\shutdownintercept\HookDll.dll %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\runtime
    copy %USAPIPATH%\docfx\api_readme\README.txt %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"

    
    xcopy /E /Q %XUNITDIR%\build\USAPI\test %XUNITDIR%\build\USAPI\bin
    del cleanres.bat cleanres.exe cleanres.exe.config sqlite3.exe cleanprobe_x64.exe cleanprobe_x86.exe 

    cd /d %XUNITDIR%\build\USAPI
    if not exist "usapi_pdb v%VERSION%-%NAME%" md "usapi_pdb v%VERSION%-%NAME%"
    echo .dll>>EXCLUDE.txt
    echo .dll.manifest>>EXCLUDE.txt
    xcopy /E /Q %XUNITDIR%\build\USAPI\bin\lib %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%" /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt 
    copy %USAPIPATH%\Device\bin\Release\Device.pdb %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%"
    copy %USAPIPATH%\UltraSound\bin\Release\UltraSound.pdb %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%"
    copy %USAPIPATH%\USAPIExample\bin\Release\USAPIExample.pdb %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%"
    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\zip.exe %XUNITDIR%\build\USAPI
    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\unzip.exe %XUNITDIR%\build\USAPI\
    zip.exe -r -q "usapi_pdb v%VERSION%-%NAME%.zip" "usapi_pdb v%VERSION%-%NAME%"


    net use "M": \\*************\versionHistory "ver" /user:"ver"
    cd /d M:\sr9\fpga
    for /f %%i in ( ' dir /b' ) do ( set fpga_version=%%i )
    cd /d M:\sr9\fpga\%fpga_version%
    copy fpga_fb463d.zip %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\fpga_fb463d
    copy fpga_fb463d_test.zip %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\fpga_fb463d
    cd /d %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"

setlocal EnableDelayedExpansion
    for /f "delims=" %%i in (README.txt) do (
        set a=%%i
        set "a=!a:1.5.06=%fpga_version%!"
        echo !a!>>tmp_readme.txt 
        )
    move tmp_readme.txt README.txt
setlocal DisableDelayedExpansion

    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\unzip.exe %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\fpga_fb463d
    cd /d %XUNITDIR%\build\USAPI\"usapi v%VERSION%-%NAME%"\fpga_fb463d 
    unzip.exe fpga_fb463d.zip
    del unzip.exe fpga_fb463d.zip
    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\zip.exe %XUNITDIR%\build\USAPI
    cd /d %XUNITDIR%\build\USAPI
    zip.exe -r -q "usapi v%VERSION%-%NAME%.zip" "usapi v%VERSION%-%NAME%"
    del zip.exe


    move "usapi v%VERSION%-%NAME%.zip" M:\sr9

    cd /d M:\softbackup
    if not exist sr9api md sr9api 
    cd /d %XUNITDIR%\build\USAPI\bin\lib %XUNITDIR%\build\USAPI
    move %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%.zip" M:\softbackup\sr9api
    rd /s /Q %XUNITDIR%\build\USAPI\bin\lib %XUNITDIR%\build\USAPI\"usapi_pdb v%VERSION%-%NAME%" 

    net use M: /delete
    echo ==================== Packaging completed, process ended! ====================
    exit /b 0

goto:end

@echo off
@REM ################################################################################
@REM #                             prepare for zip                           		#
@REM ################################################################################
:SR9_prepare_for_zip

    tasklist | findstr /i ssh-agent.exe && taskkill /f /im ssh-agent.exe
    cd /d %XUNITDIR%\build\SR9\bin
    mkdir %XUNITDIR%\build\SR9\bin\res\modelfiles\%MODEL%
    echo modelfiles>EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res %XUNITDIR%\build\SR9\bin\res /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res\modelfiles\%MODEL% %XUNITDIR%\build\SR9\bin\res\modelfiles\%MODEL%

    copy %XUNITDIR%\build\SR9\bin\zip.exe %XUNITDIR%\build\SR9
    cd /d %XUNITDIR%\build\SR9
    mkdir SR9-v%VERSION%
    xcopy /Q /E bin %XUNITDIR%\build\SR9\SR9-v%VERSION%
    zip.exe -r -q "SR9-v%VERSION%.zip" SR9-v%VERSION%
    net use "N": \\*************\versionHistory "ver" /user:"ver"
    del zip.exe
    rd /S /Q SR9-v%VERSION%

    move "SR9-v%VERSION%.zip" N:\sr9
    net use N: /delete
    echo ==================== Packaging completed, process ended! ====================
    exit /b 0

goto:end

@echo off
@REM ################################################################################
@REM #                             prepare for zip                           		#
@REM ################################################################################
:SonoEye_prepare_for_zip

    tasklist | findstr /i ssh-agent.exe && taskkill /f /im ssh-agent.exe

    cd /d %XUNITDIR%
    call "%XUNITDIR%\configure_windows_x86.bat" -a %VCVARSALL_HOST_ARCH% -m %MODEL%

    set year=%date:~10,4%
    set month=%date:~4,2%
    set day=%date:~7,2%
    set fecha=%year%%month%%day%
    set SonoEyePath=%XUNITDIR%\build\%MODEL%\SonoEye(win)-%fecha%

    mkdir %SonoEyePath%\lib
    mkdir %SonoEyePath%\styles
    mkdir %SonoEyePath%\include
    mkdir %SonoEyePath%\plugins\sqldrivers
    mkdir %SonoEyePath%\res\modelfiles\sonoeye
    copy %XUNITDIR%\src\api\uscontextapi\usapi.h %SonoEyePath%\include
    copy %XUNITDIR%\src\api\uscontextapi\usapidef.h %SonoEyePath%\include

    cd /d %XUNITDIR%\build\%MODEL%\bin\lib
    del Hwctrl.dll
    del *.manifest
    rd /S /Q CMakeFiles bfiodevice_autogen
    xcopy /Q /E %XUNITDIR%\build\%MODEL%\bin\lib %SonoEyePath%\lib
    copy C:\Qt\5.15.2\msvc2019\plugins\platforms\qwindows.dll %SonoEyePath%\lib
    copy C:\Qt\5.15.2\msvc2019\plugins\sqldrivers\qsqlite.dll %SonoEyePath%\plugins\sqldrivers
    copy C:\Qt\5.15.2\msvc2019\plugins\styles\qwindowsvistastyle.dll %SonoEyePath%\styles

    cd /d %XUNITDIR%\build\%MODEL%
    echo modelfiles>EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res %SonoEyePath%\res /exclude:EXCLUDE.txt /y
    del EXCLUDE.txt
    xcopy /Q /E %RESPATH%\res\modelfiles\sonoeye %SonoEyePath%\res\modelfiles\sonoeye
    copy %XUNITDIR%\build\%MODEL%\test\uscontextapitest.exe %SonoEyePath%

    cd /d %SonoEyePath%
    echo set PATH=lib;>run.bat
    echo set QT_QPA_PLATFORM_PLUGIN_PATH=lib>>run.bat
    echo uscontextapitest.exe>>run.bat
    echo pause>>run.bat
    
    net use T: \\*************\versionHistory "ver" /user:"ver"
    copy %XUNITDIR%\src\thirdparty\windows_x86_%HOST_ARCH_BIT%bit\otherapp\zip.exe %XUNITDIR%\build\%MODEL%
    cd /d %XUNITDIR%\build\%MODEL%
    zip.exe -r -q "SonoEye(win)-%fecha%.zip" SonoEye(win)-%fecha%
    move %XUNITDIR%\build\%MODEL%\SonoEye(win)-%fecha%.zip T:\SonoEye(win)
    del zip.exe
    net use T: /delete
    echo ==================== Packaging completed, process ended! ====================
    exit /b 0

goto:end


@echo off
@REM ################################################################################
@REM #                                 START!                               		#
@REM ################################################################################
:main

    if not defined NAME (
        set NAME=alpha
    )

    if not defined SR9TYPE (
        set SR9TYPE=OFF
    )
    
    if /I "%MODEL%" equ "sr9" (
        call:update_cmake_version
        call:build_Cpp
        call:git_tag
        if /I "%SR9TYPE%" equ "ON" (
            call:encrypt_res
            call:deal_ini
            call:update_cs_version
            call:build_CS
            call:clean_res
            call:make_exammode_bak
            call:deal_pdf
            call:USAPI_prepare_for_zip
        ) else if /I "%SR9TYPE%" equ "OFF" (
            call:SR9_prepare_for_zip        
        )
    )

    if /I "%MODEL%" equ "SonoEye" (
        call:SonoEye_prepare_for_zip
    )

goto:end

@REM ##############################
@REM usage help
@REM ##############################
:usage
    echo: 
    echo usage:
    echo   USAPI_Release.bat [-h] [-X ^<PROJECT_XUNIT_PATH^>] [-U ^<PROJECT_USAPIEXAMPLE_PATH^>] [-R ^<BRANCH_RESOURCE_PATH^>] [-m ^<model^>] [-a ^<x86 or x64^>] [-v ^<VERSION^>] [-t ^<SR9TYPE^>]
    echo:
    echo:
    echo Options:
    echo -h                                     help
    echo -X ^<PROJECT_XUNIT_PATH^>              ^path of xunit, e.g. D:\xunit\xunit  
    echo -R ^<BRANCH_RESOURCE_PATH^>            ^path of resource-xunit, e.g. D:\xunit\resource-xunit 
    echo -U ^<PROJECT_USAPIEXAMPLE_PATH^>       ^path of USAPIExample, e.g. D:\SR9-api\usapiexample
    echo -m ^<model^>                           model,like SR9,USAPI
    echo -a ^<about bit^>                       choose 32bit or 64bit, default is x86
    echo -v ^<version^>                         update versionInfo 
    echo -p ^<probetype^>                       choose the ^type of probe 
    echo -t ^<sr9type^>                         sr9type, like ON or OFF. when mode=SR9, ON:API software. OFF:Ultrasound Software. default is OFF

    echo:
    echo:
    echo examples:
    echo:
    echo USAPI_Release.bat -X D:\xunit\xunit -U D:\SR9-api\usapiexample -R D:\xunit\resource-xunit -m sr9 -a x86 -v 0.4.0 -t ON -p L25-V
    echo USAPI_Release.bat -X D:\xunit\xunit -R D:\xunit\resource-xunit -m sr9 -a x86 -v 0.4.0
    echo USAPI_Release.bat -X D:\xunit\xunit -R D:\xunit\resource-xunit -m SonoEye -a x86
    goto:end

:end