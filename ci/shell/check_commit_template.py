#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import re
import subprocess
import os

def get_commits_between_branches(source_branch, target_branch):
    """Get the commits between two branches"""
    try:   
        # 先获取所有分支信息
        #fetch_result = subprocess.run(['git', 'fetch', '--all'],
                                    #capture_output=True, text=True, encoding='utf-8')
        #if fetch_result.returncode != 0:
        #    print("Error fetching branches")
        #    print(fetch_result.stderr)
        #    sys.exit(1)

        # 打印所有分支信息
        #branch_result = subprocess.run(['git', 'branch', '-a'],
                                     #capture_output=True, text=True, encoding='utf-8')
        #print("All branches:", branch_result.stdout)

        # 使用完整的远程分支引用
        source_ref = f'origin/{source_branch}'
        target_ref = f'origin/{target_branch}'
        
        # 获取提交信息
        result = subprocess.run(['git', 'log', f'{target_ref}..{source_ref}', '--pretty=%B----END----'],
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            print("Error getting commits between branches")
            print("Source branch:", source_ref)
            print("Target branch:", target_ref)
            print("Error:", result.stderr)
            sys.exit(1)

        # 按分隔符分割，去除空项和首尾空白
        commits = [msg.strip() for msg in result.stdout.split('----END----') if msg.strip()]
        return commits
    except subprocess.CalledProcessError as e:
        print(f"Error getting commits between branches: {e}")
        sys.exit(1)

def get_commit_message():
    """Get the commit message from git log"""
    try:
        result = subprocess.run(['git', 'log', '-1', '--pretty=%B'], 
                              capture_output=True, text=True, encoding='utf-8')
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error getting commit message: {e}")
        sys.exit(1)

def get_section_content(commit_msg, field):
    """Get the content of a section until the next section or end of message"""
    # 转义特殊字符，但保留换行符
    escaped_field = re.escape(field).replace(r'\n', '\n')
    
    # 查找字段开始的位置
    start_match = re.search(escaped_field, commit_msg)
    if not start_match:
        return ""
    
    start_pos = start_match.end()
    
    # 查找下一个段落（以数字开头的行或以'-'开头的行）
    next_section_pattern = r'\n(?:\d+、|\s*-\s+[^\n]+：|\s*[a-e]\.\s)'
    next_match = re.search(next_section_pattern, commit_msg[start_pos:])
    
    if next_match:
        content = commit_msg[start_pos:start_pos + next_match.start()]
    else:
        content = commit_msg[start_pos:]
    
    return content.strip()

def check_field_content(commit_msg, field, field_name, errors):
    """Check if a field has content after it"""
    # 如果 field_name 等于 "同类问题分析"，那么此字段的内容为 field 到 "2、解决方案" 之前
    if field_name == "同类问题分析":
        start_pos = commit_msg.find(field)
        start_pos = commit_msg.find(field) + len(field)  # 找到field的位置后，加上field的长度
        end_pos = commit_msg.find("2、解决方案")
        content = commit_msg[start_pos:end_pos]
        # 使用正则表达式去除所有空白字符（包括空格、制表符、换行符等）
        content = re.sub(r'\s+', '', content)
    else:
        content = get_section_content(commit_msg, field)
        
    #print(f"field_name: {field_name}, content: {content}")

    if not content:
        errors.append(f"Error: '{field_name}' field is empty")
        errors.append(f"  Please add content after '{field}'")
        return False
    return True

def check_yes_no_selection(commit_msg, field, field_name, errors):
    """Check if a field has [是/否] selection"""
    pattern = re.escape(field) + r'\s*\[(是|否)\]'
    match = re.search(pattern, commit_msg)
    if not match:
        errors.append(f"Error: '{field_name}' must select [是] or [否]")
        errors.append(f"  Please modify '{field}' to include [是] or [否]")
        return False
    return True

def check_bug_analysis(commit_msg, errors):
    """Check if bug commit contains all required analysis sections with content"""
    required_fields = [
        ('a. 直接原因：', '直接原因'),
        ('b. 根本原因：', '根本原因'),
        ('c. 是否临时解决方案：', '是否临时解决方案'),
        ('e. 同类问题分析：', '同类问题分析'),
        ('2、解决方案', '解决方案'),
        ('3、影响范围', '影响范围'),
        ('4、Test Case', 'Test Case'),
        ('5、Code Reviewer', 'Code Reviewer')
    ]
    
    # First check if all fields exist and have content
    for field, field_name in required_fields:
        if field not in commit_msg:
            errors.append(f"Error: Missing required field '{field}' in bug analysis")
            errors.append(f"  Please add '{field}' section to your commit message")
        else:
            check_field_content(commit_msg, field, field_name, errors)
    
    # Check similar problem analysis with [是/否] selection
    similar_problem_fields = [
        ('  - 其他模块是否存在类似问题：', '其他模块是否存在类似问题'),
        ('  - 其他版本是否存在类似问题：', '其他版本是否存在类似问题'),
        ('  - 其他机型是否存在类似问题：', '其他机型是否存在类似问题')
    ]
    
    for field, field_name in similar_problem_fields:
        if field in commit_msg:  # Only check if field exists
            check_yes_no_selection(commit_msg, field, field_name, errors)
    
    # Special check for temporary solution
    temp_solution_match = re.search(r'c\. 是否临时解决方案：\[(是|否)\]', commit_msg)
    if not temp_solution_match:
        errors.append("Error: Temporary solution status must be specified as [是] or [否]")
        errors.append("  Please modify 'c. 是否临时解决方案：' to include [是] or [否]")
    
    if temp_solution_match and temp_solution_match.group(1) == '是':
        # If it's a temporary solution, check for risk assessment
        if '  - 临时方案风险：' not in commit_msg:
            errors.append("Error: Missing temporary solution risk assessment")
            errors.append("  Please add '  - 临时方案风险：' section when using temporary solution")
        else:
            check_field_content(commit_msg, '  - 临时方案风险：', '临时方案风险', errors)

def check_commit_template(commit_msg, errors):
    """Check if commit message matches the template requirements"""
    # Check if it's a bug fix commit
    bug_pattern = r'\[(.*?)\]\[BUG:\s*\d*\].*'
    if re.match(bug_pattern, commit_msg):
        if not re.search(r'\[BUG:(\d+)\]', commit_msg):
            errors.append("Error: Bug ID is missing in BUG commit message")
            errors.append("  Please add Bug ID after [BUG:], e.g. [BUG:12345]")
        
        # Check for complete bug analysis
        check_bug_analysis(commit_msg, errors)
        return

    # Check if it's a feature commit
    feature_pattern = r'\[(.*?)\]\[(功能新增|功能变更|性能优化):\d*\]'
    if re.match(feature_pattern, commit_msg):
        # Check if task ID is present
        if not re.search(r'\[(功能新增|功能变更|性能优化):\d+\]', commit_msg):
            errors.append("Error: Task ID is missing in feature commit message")
            errors.append("  Please add Task ID after [功能新增:], [功能变更:], or [性能优化:], e.g. [功能新增:12345]")
        
        # Check for required fields from commitTemplate
        required_fields = [
            ('1、来源', '来源'),
            ('2、实现方案', '实现方案'),
            ('3、影响范围', '影响范围'),
            ('4、Test Case', 'Test Case'),
            ('5、Code Reviewer', 'Code Reviewer')
        ]
        
        for field, field_name in required_fields:
            if field not in commit_msg:
                errors.append(f"Error: Missing required field '{field}' in feature commit")
                errors.append(f"  Please add '{field}' section to your commit message")
            else:
                check_field_content(commit_msg, field, field_name, errors)
        return

    # Check if it's an image enhancement commit
    image_pattern = r'\[(.*?)\]\[图像提升\]'
    if re.match(image_pattern, commit_msg):
        # Check if it contains required sections with content
        required_fields = [
            ('1、来源', '来源'),
            ('2、调图内容', '调图内容'),
            ('3、影响范围', '影响范围'),
            ('4、Test Case', 'Test Case'),
            ('5、Code Reviewer', 'Code Reviewer')
        ]
        for field, field_name in required_fields:
            if field not in commit_msg:
                errors.append(f"Error: Missing required field '{field}' in image enhancement commit")
                errors.append(f"  Please add '{field}' section to your commit message")
            else:
                check_field_content(commit_msg, field, field_name, errors)
        return

    clean_code_pattern = r'\[All\]\[CleanCode\]'
    if re.match(clean_code_pattern, commit_msg):
        # Check if it contains required sections with content
        required_fields = [
            ('1、整洁之道', '整洁之道'),
            ('2、影响范围', '影响范围'),
            ('3、Test Case', 'Test Case'),
            ('4、Code Reviewer', 'Code Reviewer')
        ]
        for field, field_name in required_fields:
            if field not in commit_msg:
                errors.append(f"Error: Missing required field '{field}' in clean code commit")
                errors.append(f"  Please add '{field}' section to your commit message")
            else:
                check_field_content(commit_msg, field, field_name, errors)
        return

    # If none of the above patterns match
    errors.append(commit_msg)
    errors.append("Error: Unrecognized commit type")
    errors.append("  Please use one of the following commit types:")
    errors.append("  - [模块名][BUG:12345] for bug fixes")
    errors.append("  - [模块名][功能新增:12345]/[功能变更:12345]/[性能优化:12345] for new features")
    errors.append("  - [模块名][图像提升] for image enhancements")
    errors.append("  - [All][CleanCode] for clean code changes")

def main():
    # 解析命令行参数
    if len(sys.argv) < 3:
        print("用法: python check_commit_template.py <source_branch> <target_branch>")
        sys.exit(1)
    source_branch = sys.argv[1]
    target_branch = sys.argv[2]

    print(f'From {source_branch} into {target_branch}')

    # 获取2个分支之间的提交信息
    commits_between_branches = get_commits_between_branches(source_branch, target_branch)
    errors = []
    # 循环遍历 commits_between_branches
    for commit_msg in commits_between_branches:
        #print(commit_msg)
        # 忽略这一条提交，不检查 [All][CleanCode] Apply Clang-Format by Gitlab.
        if commit_msg.startswith("[All][CleanCode] Apply Clang-Format by Gitlab."):
            continue

        check_commit_template(commit_msg, errors)

    if errors:
        print("\nCommit message does not match the template requirements:")
        for error in errors:
            print(error)
        print("\nPlease modify your commit message according to the error messages above")
        print("You can use 'git commit --amend' to modify your last commit")
        print("\nFor reference, please check the commit template file: commitTemplate.txt")
        print("You can configure it using: git config commit.template commitTemplate.txt")
        sys.exit(1)

    print("Commit message check passed")
    sys.exit(0)

if __name__ == "__main__":
    main() 