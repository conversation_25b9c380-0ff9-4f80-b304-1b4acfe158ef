#!/bin/bash

echo_usage()
{
    cat <<-END
usage: 
    ./configure_unix_x86_64bit.sh [-h] [-m <model>]

Options:
    -h                              help
    -m <model>                      model, like Phoenix, Apple etc.
    -r <resource-xunit branch>      
    -x <system>                     linux, windows
    -a <appcommonbranch branch>     
    -n <neutral>                    neutral, false or true

examples:


END
    exit 0
}

echo $*
while getopts "hm:r:x:a:n:t:v:" arg
do
    case $arg in
        h)  echo_usage;;
        m)  model=$OPTARG;;
        r)  resbranch=$OPTARG;;
        x)  sys=$OPTARG;;
        a)  appcommonbranch=$OPTARG;;
        n)  neutral=$OPTARG;;
        t)  branch=$OPTARG;;
        v)  prever=$OPTARG;;
    esac
done

getrepo() 
{
    repName="$1"
    if [[ ! -d "$rootdir/code/$repName" ]]; then
        git clone git@192.168.20.38:$2/$repName.git $rootdir/code/$repName
    fi

    pwdtemp=`pwd`
    cd $rootdir/code/$repName
    git fetch
    git reset --hard origin/$3
    echo $pwdtemp
    cd $pwdtemp
}

checkPreVersion()
{
    if [ $prever != "default" ] ; then 
        pwdtemp=`pwd`
        dir="$1"
        cd $dir
        preTag=`git tag --sort=-taggerdate | grep $prever | sort -r | head -n 1`
        cd $pwdtemp
        echo "查找到的tag $preTag"
        if [ -z $preTag ]; then
            echo "$prever 不存在，请校验对应版本是否存在"
            exit 1
        fi
    fi
}

sedstr()
{
    find=$1
    replace=$2
    srcfile=$3
    sed -i "s#$find#$replace#" $srcfile
}

configinipath()
{
    username=$1
    xunitdir=`pwd`
    model=$2
    lowerupmodel=`echo $model | tr '[:upper:]' '[:lower:]'`

    cd $rootdir/code/appcommon/shell/mkpackage_shell/xunit

    # linux runner
    sedstr "/home/<USER>/code/" "$rootdir/code/" init.sh
    sedstr "SourceDir=$rootdir/code/xunit" "SourceDir=${xunitdir}" init.sh
    sedstr "/home/<USER>/code/" "/home/<USER>/code/" pathsetting_$lowerupmodel.ini
    sedstr "AppPath=/home/<USER>/code/xunit/build/${model}/bin/xultrasound" "AppPath=${xunitdir}/build/${model}/bin/xultrasound" pathsetting_$lowerupmodel.ini
    sedstr "SrcPath=/home/<USER>/code/xunit" "SrcPath=${xunitdir}" pathsetting_$lowerupmodel.ini
    sedstr "VersionPath=/home/<USER>/code/xunit/build/${model}/bin/res/general/version.ini" "VersionPath=${xunitdir}/build/${model}/bin/res/general/version.ini" pathsetting_$lowerupmodel.ini
    
    # 受保护分支强制支持版本可追溯
    if [[ $branch == pangu || $branch == pangu_alpha || $branch == pangu_beta || $branch == *_Release ]]; then    
        istest="false"
    else
        # 非受保护分支由IsVersionTraceable决定是否可追溯
        if [ -n "$IsVersionTraceable" ] && [ "$IsVersionTraceable" = "true" ]; then
            istest="false"
        else
            istest="true"
        fi
    fi 
    echo "if it is traceable version: ${istest}"
    sedstr "IsTest=false" "IsTest=${istest}" pathsetting_$lowerupmodel.ini
    temp2Replace=`grep PreVersion pathsetting_$lowerupmodel.ini`
    sedstr "$temp2Replace" "PreVersion=${prever}" pathsetting_$lowerupmodel.ini
    cat pathsetting_$lowerupmodel.ini
    cd $xunitdir
}

configappcommonpath()
{
    dir=$rootdir/code/
    if [[ -d "$dir" ]]; then
        mkdir -p /$dir
    fi

    dir=$rootdir/code/versionHistory/
    if [[ -d "$dir" ]]; then
        mkdir -p $dir
    fi

    dir=$rootdir/code/app_package/
    if [[ -d "$dir" ]]; then
        mkdir -p "$dir"
    fi

    configinipath "gitlab-runner" $1
}

configNeutral()
{
    xunitdir=`pwd`

    modeTemp="$2"
    modeTemp=${modeTemp,,}
    echo ${modeTemp}

    neutralDir="/home/<USER>/code/resource-xunit/res/modelfiles/${modeTemp}/app/neutral/"
    ${neutralDir}configureNeutral.sh $1 ${neutralDir}

    cd $xunitdir
}

rootdir=~

if [ -z $sys ]; then
    sys="linux"
fi

if [ $sys = "windows" ]; then
    rootdir="E:/"
fi

if [ -z $appcommonbranch ]; then
    appcommonbranch="master"
fi


echo $rootdir

# resource-xunit
getrepo "resource-xunit" "usplatform" $resbranch 

# 打包工具appcommon
getrepo "appcommon" "usplatform_thirdparty" $appcommonbranch 

# 配置打包工具路径
configappcommonpath $model

# 配置中性资源
configNeutral $neutral $model

checkPreVersion "${rootdir}/code/resource-xunit"
checkPreVersion "${rootdir}/code/xunit"

# # 拉取testdata
# getrepo "testdata" "usplatform" "master"



