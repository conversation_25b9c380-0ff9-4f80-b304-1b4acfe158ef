#!/bin/bash

echo_usage()
{
    cat <<-END
usage: 
    ./runrelease.sh [-h] [-m]

release and pack software package.

Options:
    -h                      help
    -m <model>              model, like Phoenix, Apple etc.
    -v                      version, like V0.4.2
    -t                      istest, true or false
    -a                      VersionAdd
    -x <system>             linux, windows

examples:
    ./runrelease.sh -m Phoenix
    ./runrelease.sh -m Apple

END
    exit 0
}

echo $*

while getopts ":hm:v:t:a:x:" arg
do
    case $arg in
        h)  echo_usage;;
        m)  model=$OPTARG;;
        v)  version=$OPTARG;;
        t)  istest=$OPTARG;;
        a)  info=$OPTARG;;
        x)  sys=$OPTARG;;
    esac
done

sedstr()
{
    find=$1
    replace=$2
    srcfile=$3
    sed -i "s#$find#$replace#" $srcfile
}

editVersion()
{
    modelLower=`echo $model | tr '[:upper:]' '[:lower:]'`
    temp2Replace=`grep MainVersion ./shell/mkpackage_shell/xunit/pathsetting_$modelLower.ini`
    sedstr "${temp2Replace}" "MainVersion=${version}" ./shell/mkpackage_shell/xunit/pathsetting_$modelLower.ini
}

echo "start release ....."

xunitdir=`pwd`

if [ -z $sys ]; then
    sys="linux"
fi

if [ $sys = "windows" ]; then 
    cd E:/code/appcommon
else
    cd ~/code/appcommon
fi

editVersion

./rebuild_release.sh -m $model -x $sys -a $info 

cd $xunitdir

echo "end release ....."

if [ $sys = "windows" ]; then 
    exit 0
fi