#!/bin/sh
#Check whether the translation in the tsfiles is abnormal.Check if the TS file needs to be updated
#使用了3种规则来检查TS文件的同步情况，根据情况使用不同的规则。默认采用标准规则standard（-s）。
#使用不同的规则须要到对应的.yml配置文件的pre-build-job环节对script关键字中内容进行修改。


url_tiggerapp='git@*************:usplatform_thirdparty/tigerapp.git'

echo_usage()
{
    cat <<-END
usage: 
    ./check_ts.sh [-r] [-s] [-l]
        
check ts files with rigid(-r) or standard(-s) or loose(-l) rule.

Options:
    -r  check *.ts files with rigid rule: Compare All ts Files between local and remote.
    -s  check *.ts files with standard rule: Compare number of Total Lines of All ts Files. (default)
    -l  check *.ts files with loose rule: Compare number of Total Message Elements(unit of translation) of All ts Files.
END
}

if [ -z $1 ];then $1="-s"; fi

if [[ $1 != -s && $1 != -l && $1 != -r ]]
then
    echo_usage
    exit 1
fi

getLine()
{
    files=`find ./$1 -name '*.ts'`
    line=0
    for file in ${files}
    do
        temp=`cat ${file} | wc -l`
        ((line+=${temp}))
    done
    
    echo ${line}
    return $?       
}

# 获取所有ts文件总message元素数量
getMessage()
{
    files=`find ./$1 -name '*.ts'`
    NumMessage=0
    for file in ${files}
    do
        NumMessageinSingleFile=$(grep "<message>" ${file} | wc -w)
        ((NumMessage+=${NumMessageinSingleFile}))
    done
    echo ${NumMessage}
    return $?
}

checkNumberofFiles()
{
    # 定义文件列表数组
    filestoCommit=($(find ./$1 -name '*.ts'))

    git clone ${url_tiggerapp}
    mv -t src tigerapp/otherapp/update/
    rm -rf tigerapp
    ./update_ts_file.sh -u
    rm -rf src/update
    filestoCheckinCI=($(find ./$1 -name '*.ts'))
    
    len1=${#filestoCommit[*]}
    len2=${#filestoCheckinCI[*]}
    if [ $len1 -ne $len2 ];then
        echo "The number of .ts files is not same by executing update_ts_file.sh again" 
        git restore ts/*.ts && exit 1
    fi
}

# 严格规则，比较整体文件
checkUpdateRigid()
{
    checkNumberofFiles ts
    len=${#filestoCommit[*]}
    count=0
    for ((i=0; i<$len; i++));do
        f1=${filestoCommit[$i]}
        f2=${filestoCheckinCI[$i]}
        if ./tests/shell/comparerootcmakelists/diff.sh ${f1} ${f2} ;then
            ((count++))
        fi
    done

    if [ $count -ne $len ];then
        echo "Error: Please update the ts file and check the correctness of the change points!"
        git restore ts/*.ts
        exit 1
    else
        echo "Tip: TS file check completed and passed!"
        git restore ts/*.ts
        exit 0
    fi
}

# 标准规则，比较行数
checkUpdate()
{
    line0=$(getLine ts)
    
    git clone ${url_tiggerapp}
    mv -t src tigerapp/otherapp/update/
    rm -rf tigerapp
    ./update_ts_file.sh -u
    rm -rf src/update
    line1=$(getLine ts)
    
    if [[ ${line0} != ${line1} ]] ;then
        echo "Error: Please update the ts file and check the correctness of the change points!"
        git restore ts/*.ts
        exit 1
    else
        echo "Tip: TS file check completed and passed!"
        git restore ts/*.ts
        exit 0
    fi
}

# 宽松规则，比较TS文件中总翻译单元数量
checkUpdateLoose()
{
    NumMessage0=$(getMessage ts)

    git clone ${url_tiggerapp}
    mv -t src tigerapp/otherapp/update/
    rm -rf tigerapp
    ./update_ts_file.sh -u
    rm -rf src/update
    NumMessage1=$(getMessage ts)

    if [[ ${NumMessage0} != ${NumMessage1} ]]; then
        echo "Error: The Loose version: Please update the TS files and check the correctness of the change points!"
        git restore ts/*.ts
        exit 1
    else
        echo "The Loose version: TS files check completed and passed!"
        git restore ts/*.ts
        exit 0
    fi
}

if [[ $1 == -s ]] ;then
    checkUpdate
fi

if [[ $1 == -l ]] ;then
    checkUpdateLoose
fi

if [[ $1 == -r ]] ;then
    checkUpdateRigid
fi
