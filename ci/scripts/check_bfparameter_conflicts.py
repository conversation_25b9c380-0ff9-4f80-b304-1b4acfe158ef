import os
import xml.etree.ElementTree as ET
from collections import defaultdict
import argparse

def parse_parameter(parameter):
    """Parse all bit information of a single parameter"""
    info = {
        'BitCount': parameter.get('BitCount'),
        'StartBit': parameter.get('StartBit'),
        'StartByte': parameter.get('StartByte'),
        'HighBitCount': parameter.get('HighBitCount'),
        'HighStartBit': parameter.get('HighStartBit'),
        'HighStartByte': parameter.get('HighStartByte'),
        'ThirdBitCount': parameter.get('ThirdBitCount'),
        'ThirdStartBit': parameter.get('ThirdStartBit'),
        'ThirdStartByte': parameter.get('ThirdStartByte'),
        'FourthBitCount': parameter.get('FourthBitCount'),
        'FourthStartBit': parameter.get('FourthStartBit'),
        'FourthStartByte': parameter.get('FourthStartByte')
    }
    return {k: v for k, v in info.items() if v is not None}

def check_bfparameter_conflicts(xml_dir):
    """Check for parameter definition conflicts in bfparameter XML files
    
    Args:
        xml_dir (str): Directory containing bfparameter XML files
    """
    # Get all bfparameter XML files
    bfparameter_files = [f for f in os.listdir(xml_dir) if f.startswith('bfparameter_') and f.endswith('.xml')]
    
    # Store bit information for all parameters
    parameter_info = defaultdict(dict)
    
    # Parse each file
    for file_name in bfparameter_files:
        file_path = os.path.join(xml_dir, file_name)
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Traverse all Parameter elements
            for param in root.findall('.//Parameter'):
                name = param.get('Name')
                if name:
                    param_info = parse_parameter(param)
                    if param_info:  # Only store parameters with bit information
                        parameter_info[name][file_name] = param_info
        except Exception as e:
            print(f"Error parsing file {file_name}: {e}")
    
    # Check for conflicts
    conflicts = []
    for param_name, file_infos in parameter_info.items():
        if len(file_infos) > 1:  # Parameter defined in multiple files
            # Compare bit information across all files
            first_file = list(file_infos.keys())[0]
            first_info = file_infos[first_file]
            
            for other_file, other_info in list(file_infos.items())[1:]:
                if first_info != other_info:
                    conflicts.append({
                        'parameter': param_name,
                        'files': {
                            first_file: first_info,
                            other_file: other_info
                        }
                    })
    
    # Output results
    if conflicts:
        print("\nFound the following parameter definition conflicts:")
        for conflict in conflicts:
            print(f"\nParameter: {conflict['parameter']}")
            for file_name, info in conflict['files'].items():
                print(f"File: {file_name}")
                print("Bit information:")
                for key, value in info.items():
                    print(f"  {key}: {value}")
    else:
        print("\nNo parameter definition conflicts found")

def main():
    parser = argparse.ArgumentParser(
        description='Check for parameter definition conflicts in bfparameter XML files.',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
Examples:
  # Check conflicts in default directory (src/usf/imaging/common/resource/res/xml)
  python check_bfparameter_conflicts.py
  
  # Check conflicts in a specific directory
  python check_bfparameter_conflicts.py --dir /path/to/xml/files
        ''')
    
    parser.add_argument('--dir', 
                       default='src/usf/imaging/common/resource/res/xml',
                       help='Directory containing bfparameter XML files (default: src/usf/imaging/common/resource/res/xml)')
    
    args = parser.parse_args()
    check_bfparameter_conflicts(args.dir)

if __name__ == '__main__':
    main() 