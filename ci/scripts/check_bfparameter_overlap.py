import os
import glob
import xml.etree.ElementTree as ET
from collections import defaultdict
import argparse

def get_bit_ranges(param_attrs, param_name, filename):
    """Calculates the absolute bit ranges for a parameter."""
    ranges = []
    try:
        # Main range
        if 'StartByte' in param_attrs and 'StartBit' in param_attrs and 'BitCount' in param_attrs:
            start_byte = int(param_attrs['StartByte'])
            start_bit = int(param_attrs['StartBit'])
            bit_count = int(param_attrs['BitCount'])
            if bit_count > 0:
                abs_start_bit = (start_byte - 1) * 8 + start_bit
                abs_end_bit = abs_start_bit + bit_count - 1
                ranges.append((abs_start_bit, abs_end_bit, param_name))

        # High range
        if ('HighStartByte' in param_attrs and 'HighStartBit' in param_attrs and
                'HighBitCount' in param_attrs):
            start_byte_h = int(param_attrs['HighStartByte'])
            start_bit_h = int(param_attrs['HighStartBit'])
            bit_count_h = int(param_attrs['HighBitCount'])
            if bit_count_h > 0:
                abs_start_bit_h = (start_byte_h - 1) * 8 + start_bit_h
                abs_end_bit_h = abs_start_bit_h + bit_count_h - 1
                ranges.append((abs_start_bit_h, abs_end_bit_h, param_name + " (High)"))

        # Third range
        if ('ThirdStartByte' in param_attrs and 'ThirdStartBit' in param_attrs and
                'ThirdBitCount' in param_attrs):
            start_byte_t = int(param_attrs['ThirdStartByte'])
            start_bit_t = int(param_attrs['ThirdStartBit'])
            bit_count_t = int(param_attrs['ThirdBitCount'])
            if bit_count_t > 0:
                abs_start_bit_t = (start_byte_t - 1) * 8 + start_bit_t
                abs_end_bit_t = abs_start_bit_t + bit_count_t - 1
                ranges.append((abs_start_bit_t, abs_end_bit_t, param_name + " (Third)"))

        # Fourth range
        if ('FourthStartByte' in param_attrs and 'FourthStartBit' in param_attrs and
                'FourthBitCount' in param_attrs):
            start_byte_f = int(param_attrs['FourthStartByte'])
            start_bit_f = int(param_attrs['FourthStartBit'])
            bit_count_f = int(param_attrs['FourthBitCount'])
            if bit_count_f > 0:
                abs_start_bit_f = (start_byte_f - 1) * 8 + start_bit_f
                abs_end_bit_f = abs_start_bit_f + bit_count_f - 1
                ranges.append((abs_start_bit_f, abs_end_bit_f, param_name + " (Fourth)"))

    except ValueError as e:
        print(f"Warning: Invalid attribute value for parameter '{param_name}' in file {filename}: {e}. Skipping this parameter.")
        return []
    except KeyError as e:
        print(f"Warning: Missing required attribute for parameter '{param_name}' in file {filename}: {e}. Skipping this parameter.")
        return []
    return ranges

def check_overlap(filename, show_duplicates=False):
    """Checks for overlapping bit ranges in a single bfparameter XML file."""
    print(f"--- Checking file: {filename} ---")
    overlaps_found = False
    try:
        tree = ET.parse(filename)
        root = tree.getroot()
    except ET.ParseError as e:
        print(f"Error: Failed to parse XML file {filename}: {e}")
        return True # Indicate error
    except FileNotFoundError:
        print(f"Error: File not found: {filename}")
        return True # Indicate error

    allocated_bits = [] # List to store (start_bit, end_bit, param_name, element)

    # Find the ControlTable group
    control_table_group = None
    for group in root.findall('.//BFParameterGroup'):
        if group.get('Type') == 'ControlTable':
            control_table_group = group
            break

    if control_table_group is None:
        print("No BFParameterGroup with Type='ControlTable' found.")
        return False # No overlaps possible if no table

    # Iterate through parameters in the ControlTable
    for param in control_table_group.findall('.//Parameter'):
        param_name = param.get('Name', 'Unnamed Parameter')
        param_attrs = param.attrib

        current_ranges = get_bit_ranges(param_attrs, param_name, filename)

        for r_new_start, r_new_end, r_new_name in current_ranges:
            # Check against already allocated bits
            for r_old_start, r_old_end, r_old_name, r_old_element in allocated_bits:
                # Check for overlap: (StartA <= EndB) and (StartB <= EndA)
                is_overlapping = r_new_start <= r_old_end and r_old_start <= r_new_end
                # Check if ranges are identical
                is_identical = r_new_start == r_old_start and r_new_end == r_old_end

                # Report overlap only if it exists AND the ranges are not identical
                if is_overlapping and not is_identical:
                    overlap_start = max(r_new_start, r_old_start)
                    overlap_end = min(r_new_end, r_old_end)
                    print(f"  *** Overlap Detected! ***")
                    print(f"    Parameter: '{r_new_name}' ({format_bit_range(r_new_start, r_new_end)})")
                    print(f"    With Parameter: '{r_old_name}' ({format_bit_range(r_old_start, r_old_end)})")
                    print(f"    Overlap Range: {format_bit_range(overlap_start, overlap_end)}")
                    overlaps_found = True
                elif is_identical and show_duplicates:
                    print(f"  *** Duplicate Attribute Detected! ***")
                    print(f"    Parameters: '{r_new_name}' and '{r_old_name}' have identical bit ranges: {format_bit_range(r_new_start, r_new_end)}")
                    
                    # Get line numbers and XML structure
                    new_line = param.sourceline
                    old_line = r_old_element.sourceline
                    
                    # Get parent paths
                    new_path = get_element_path(param)
                    old_path = get_element_path(r_old_element)
                    
                    print(f"    First occurrence at line {old_line}:")
                    print(f"      Path: {old_path}")
                    print(f"      Attributes: {dict(r_old_element.attrib)}")
                    print(f"    Second occurrence at line {new_line}:")
                    print(f"      Path: {new_path}")
                    print(f"      Attributes: {dict(param.attrib)}")

            # Add the new ranges to the allocated list
            allocated_bits.append((r_new_start, r_new_end, r_new_name, param))

    if not overlaps_found:
        print("No memory overlaps found.")

    print("-" * (len(filename) + 20))
    return overlaps_found

def get_element_path(element):
    """Returns the XML path to the given element."""
    path = []
    while element is not None:
        path.append(element.tag)
        element = element.getparent()
    return '/'.join(reversed(path))

def bit_to_byte_bit(bit_position):
    """Converts a bit position to byte and bit within byte."""
    byte = (bit_position // 8) + 1
    bit = bit_position % 8
    return byte, bit

def format_bit_range(start_bit, end_bit):
    """Formats a bit range as byte and bit positions."""
    start_byte, start_bit_in_byte = bit_to_byte_bit(start_bit)
    end_byte, end_bit_in_byte = bit_to_byte_bit(end_bit)
    if start_byte == end_byte:
        return f"Byte {start_byte}, Bit {start_bit_in_byte} to Bit {end_bit_in_byte}"
    else:
        return f"Byte {start_byte}, Bit {start_bit_in_byte} to Byte {end_byte}, Bit {end_bit_in_byte}"

def main():
    """
    Finds bfparameter XML files and checks each for overlaps.
    
    This script checks for memory overlaps in ControlTable parameters within bfparameter_*.xml files.
    It supports checking four types of bit ranges for each parameter:
    1. Main range (StartByte, StartBit, BitCount)
    2. High range (HighStartByte, HighStartBit, HighBitCount)
    3. Third range (ThirdStartByte, ThirdStartBit, ThirdBitCount)
    4. Fourth range (FourthStartByte, FourthStartBit, FourthBitCount)
    
    Usage:
        python check_bfparameter_overlap.py [--show-duplicates]
        
    Options:
        --show-duplicates    Show information about parameters with identical bit ranges
                            (default: False)
    
    Example:
        python check_bfparameter_overlap.py
        python check_bfparameter_overlap.py --show-duplicates
    """
    parser = argparse.ArgumentParser(description='Check for memory overlaps in ControlTable parameters of bfparameter_*.xml files.')
    parser.add_argument('--show-duplicates', action='store_true', 
                      help='Show information about parameters with identical bit ranges')
    args = parser.parse_args()

    search_path = os.path.join('src', 'usf', 'imaging', 'common', 'resource', 'res', 'xml', 'bfparameter_*.xml')
    bf_files = glob.glob(search_path)

    if not bf_files:
        print(f"No bfparameter_*.xml files found in path '{search_path}'.")
        return

    print(f"Found {len(bf_files)} bfparameter files to check...")
    any_overlap_found = False
    for bf_file in sorted(bf_files):
        if check_overlap(bf_file, args.show_duplicates):
            any_overlap_found = True

    print("\nCheck completed.")
    if any_overlap_found:
        print("Memory overlaps detected in some files.")
    else:
        print("No memory overlaps found in any files.")

if __name__ == "__main__":
    main() 