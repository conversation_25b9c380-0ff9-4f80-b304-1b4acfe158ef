#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import glob
import xml.etree.ElementTree as ET
from collections import defaultdict
import filecmp
import shutil



class Parameter:
    def __init__(self, name):
        self.name = name
        self.type = None
        self.min = None
        self.max = None
        self.step = None
        self.value_type = None
        self.is_preset = False
        self.true_value = None
        self.bit_count = None

class BFParameterXmlAnalyzer:
    def __init__(self, xml_file):
        if not os.path.exists(xml_file):
            raise FileNotFoundError(f"XML file not found: {xml_file}")
        self.xml_file = xml_file
        self.parameters = []

    def parse(self):
        tree = ET.parse(self.xml_file)
        root = tree.getroot()
        self._parse_parameters(root)

    def _parse_parameters(self, parent_element):
        valid_value_types = ["Invalid", "Bool", "Int", "Float", "Double", "List", "String", "ByteArray", "Size", "Rect", "PointF"]
        valid_is_preset = ["true", "false"]
        for element in parent_element.findall(".//Parameter"):
            param = Parameter(element.get('Name'))
            param.type = element.get('Type')
            param.min = element.get('Min')
            param.max = element.get('Max')
            param.step = element.get('Step')
            param.value_type = element.get('ValueType', 'Int')
            param.is_preset = element.get('IsPresetParameter', 'false')
            param.true_value = element.get('TrueValue')
            param.bit_count = element.get('BitCount')

            # Calculate min and max if not provided
            if param.min is None or param.max is None or param.step is None:
                self._calculate_min_max(param)

            if param.value_type not in valid_value_types:
                raise ValueError(f"Parameter '{param.name}' has an invalid ValueType: '{param.value_type}'. Must be one of {valid_value_types}")

            if param.is_preset not in valid_is_preset:
                raise ValueError(f"Parameter '{param.name}' has an invalid IsPresetParameter: '{param.is_preset}'. Must be one of {valid_is_preset}")

            self.parameters.append(param)

    def _calculate_min_max(self, param):
        if param.min is None:
            param.min = 0

        if param.max is None:
            if param.bit_count:
                param.max = (1 << int(param.bit_count)) - 1
            else:
                param.max = 0

        if param.step is None:
            if float(param.max) >= 0XFF:
                param.step = 5
            else:
                param.step = 1

    def get_parameters(self):
        return self.parameters


def create_source(xml_file, model):
    analyzer = BFParameterXmlAnalyzer(xml_file)
    analyzer.parse()
    return analyzer.get_parameters()


def generate_header_file(parameters):
    header_content = """#ifndef BFPNAMES_H
#define BFPNAMES_H
#include "usfcommoncoreutilitymodel_global.h"

#include <QString>
#include <QStringList>

/**
 * @brief The BFPNames class BeamFormer all parameters names
 *
 * bfpnames.h bfpnames.cpp are generated by create_bfpnames.py
 * detail description see ci/scripts/create_bfpnames.py
 */
class USF_COMMON_CORE_UTILITYMODEL_EXPORT BFPNames
{
public:
"""
    type_map = {
        "Invalid": "Invalid",
        "Bool": "Bool",
        "Int": "Int",
        "Float": "Double",
        "Double": "Double",
        "List": "List",
        "String": "String",
        "ByteArray": "ByteArray",
        "Size": "Size",
        "Rect": "Rect",
        "PointF": "PointF"
    }

    for param in parameters:
        header_content += f"    static QString {param.name}Str;  //type:{type_map.get(param.value_type, 'Int')} min:{param.min} max:{param.max} step:{param.step} isPreset:{1 if param.is_preset == 'true' else 0}"
        if param.true_value is not None:
            header_content += f" truevalue:{param.true_value}"
        header_content += "\n"

    # Add slots
    header_content += "//    slots:\n"
    for param in parameters:
        header_content += f"//    virtual void onBefore{param.name}Changed(const QVariant& oldValue, QVariant& newValue);\n"
        header_content += f"//    virtual void on{param.name}Changing(const QVariant& value);\n"
        header_content += f"//    virtual void on{param.name}Changed(const QVariant& value);\n"
        header_content += f"//    virtual void onGetting{param.name}ShowValue(QVariant& value);\n"
        header_content += f"//    virtual void onGetting{param.name}Text(QString& value);\n"
        header_content += f"//    virtual void onGetting{param.name}Min(int& value);\n"
        header_content += f"//    virtual void onGetting{param.name}Max(int& value);\n"
        header_content += f"//    virtual void onGetting{param.name}Step(int& value);\n"
        header_content += f"//    virtual void onGetting{param.name}ControlTableValue(const QVariant& value, int& controlTableValue);\n"
        header_content += f"//    virtual void onGetting{param.name}Preset(QVariant& value);\n"
        header_content += "\n"

    header_content += "//    end slots:\n"
    # Add special lists
    header_content += """
    static QString SpacialCompoundStr;
    static QStringList UniformityTgcStrs;
    static QStringList AnalogTgcStrs;
    static QStringList PhasedProbeTgcStrs;

    static QStringList ElastoDigitalTgcStrs;
    static QStringList ElastoAnologTgcStrs;
    static QStringList ElastoUniformityTgcStrs;
    static QStringList LGCStrs;
    static QStringList PostLGCStrs;
    
"""

    header_content += "};\n\n#endif // BFPNAMES_H\n"
    return header_content


def generate_cpp_file(parameters):
    """Generate bfpnames.cpp file content"""
    cpp_content = """#include "bfpnames.h"

"""
    # Add parameter definitions
    for param in parameters:
        cpp_content += f'QString BFPNames::{param.name}Str = "{param.name}";\n'

    # Add special list definitions
    cpp_content += """
QString BFPNames::SpacialCompoundStr = "SpacialCompound";

QStringList BFPNames::UniformityTgcStrs = QStringList()
<< "UniformityTgc00"
<< "UniformityTgc01"
<< "UniformityTgc02"
<< "UniformityTgc03"
<< "UniformityTgc04"
<< "UniformityTgc05"
<< "UniformityTgc06"
<< "UniformityTgc07"
<< "UniformityTgc08"
<< "UniformityTgc09"
<< "UniformityTgc10"
<< "UniformityTgc11"
<< "UniformityTgc12"
<< "UniformityTgc13"
<< "UniformityTgc14"
<< "UniformityTgc15";

QStringList BFPNames::AnalogTgcStrs = QStringList()
<< "AnalogTgc0"
<< "AnalogTgc1"
<< "AnalogTgc2"
<< "AnalogTgc3"
<< "AnalogTgc4"
<< "AnalogTgc5"
<< "AnalogTgc6"
<< "AnalogTgc7"
<< "AnalogTgc8"
<< "AnalogTgc9"
<< "AnalogTgc10"
<< "AnalogTgc11"
<< "AnalogTgc12"
<< "AnalogTgc13"
<< "AnalogTgc14"
<< "AnalogTgc15";

QStringList BFPNames::PhasedProbeTgcStrs = QStringList()
<< "PhasedProbeTgc0"
<< "PhasedProbeTgc1"
<< "PhasedProbeTgc2"
<< "PhasedProbeTgc3"
<< "PhasedProbeTgc4"
<< "PhasedProbeTgc5"
<< "PhasedProbeTgc6"
<< "PhasedProbeTgc7";

QStringList BFPNames::ElastoDigitalTgcStrs = QStringList()
<< "CFMDigitalTgc0"
<< "CFMDigitalTgc1"
<< "CFMDigitalTgc2"
<< "CFMDigitalTgc3"
<< "CFMDigitalTgc4"
<< "CFMDigitalTgc5"
<< "CFMDigitalTgc6"
<< "CFMDigitalTgc7"
<< "CFMDigitalTgc8"
<< "CFMDigitalTgc9"
<< "CFMDigitalTgc10"
<< "CFMDigitalTgc11"
<< "CFMDigitalTgc12"
<< "CFMDigitalTgc13"
<< "CFMDigitalTgc14"
<< "CFMDigitalTgc15";

QStringList BFPNames::ElastoAnologTgcStrs = QStringList()
<< "AnalogTgc0"
<< "AnalogTgc1"
<< "AnalogTgc2"
<< "AnalogTgc3"
<< "AnalogTgc4"
<< "AnalogTgc5"
<< "AnalogTgc6"
<< "AnalogTgc7"

<< "CFMAnologTgc0"
<< "CFMAnologTgc1"
<< "CFMAnologTgc2"
<< "CFMAnologTgc3"
<< "CFMAnologTgc4"
<< "CFMAnologTgc5"
<< "CFMAnologTgc6"
<< "CFMAnologTgc7";

QStringList BFPNames::ElastoUniformityTgcStrs = QStringList()
<< "ElastoEUniformityTgc0"
<< "ElastoEUniformityTgc1"
<< "ElastoEUniformityTgc2"
<< "ElastoEUniformityTgc3"
<< "ElastoEUniformityTgc4"
<< "ElastoEUniformityTgc5"
<< "ElastoEUniformityTgc6"
<< "ElastoEUniformityTgc7"
<< "ElastoEUniformityTgc8"
<< "ElastoEUniformityTgc9"
<< "ElastoEUniformityTgc10"
<< "ElastoEUniformityTgc11"
<< "ElastoEUniformityTgc12"
<< "ElastoEUniformityTgc13"
<< "ElastoEUniformityTgc14"
<< "ElastoEUniformityTgc15"

<< "UniformityTgc00"
<< "UniformityTgc01"
<< "UniformityTgc02"
<< "UniformityTgc03"
<< "UniformityTgc04"
<< "UniformityTgc05"
<< "UniformityTgc06"
<< "UniformityTgc07"
<< "UniformityTgc08"
<< "UniformityTgc09"
<< "UniformityTgc10"
<< "UniformityTgc11"
<< "UniformityTgc12"
<< "UniformityTgc13"
<< "UniformityTgc14"
<< "UniformityTgc15";

QStringList BFPNames::LGCStrs = QStringList()
<< BFPNames::LGC0Str
<< BFPNames::LGC1Str
<< BFPNames::LGC2Str
<< BFPNames::LGC3Str
<< BFPNames::LGC4Str
<< BFPNames::LGC5Str
<< BFPNames::LGC6Str
<< BFPNames::LGC7Str;

QStringList BFPNames::PostLGCStrs = QStringList()
<< BFPNames::PostLGC0Str
<< BFPNames::PostLGC1Str
<< BFPNames::PostLGC2Str
<< BFPNames::PostLGC3Str
<< BFPNames::PostLGC4Str
<< BFPNames::PostLGC5Str
<< BFPNames::PostLGC6Str
<< BFPNames::PostLGC7Str;

"""
    return cpp_content

def main():
    try:
        # Get models from command line arguments
        models = sys.argv[1:]
        
        if not models:
            # If no models specified, extract from XML filenames
            xml_pattern = os.path.join("src", "usf", "imaging", "common", "resource", "res", "xml", "bfparameter_*.xml")
            xml_files = glob.glob(xml_pattern)
            if not xml_files:
                raise FileNotFoundError("No bfparameter_*.xml files found")
            
            models = []
            for xml_file in xml_files:
                # Extract model name from filename
                basename = os.path.basename(xml_file)
                model = basename[len("bfparameter_"):-len(".xml")]
                models.append(model)
                
            models.sort()  # Sort model list
        
        # XML file paths
        xml_files = []
        valid_models = []
        
        for model in models:
            xml_file = os.path.join("src", "usf", "imaging", "common", "resource", "res", "xml", f"bfparameter_{model}.xml")
            if os.path.exists(xml_file):
                xml_files.append(xml_file)
                valid_models.append(model)
        
        if not xml_files:
            raise FileNotFoundError("No valid bfparameter XML files found")
        
        # Collect all parameters
        all_parameters = []  # Initialize as a list
        _existing_params = set()  # Track existing parameter names

        for xml_file, model in zip(xml_files, valid_models):
            print(f"Processing file: {xml_file}")
            params = create_source(xml_file, model)
            if params:
                for param in params:
                    if param.name not in _existing_params:
                        all_parameters.append(param)
                        _existing_params.add(param.name)

        if not all_parameters:
            raise ValueError("No parameters extracted from XML files")
        
        # Generate files
        header_content = generate_header_file(all_parameters)
        cpp_content = generate_cpp_file(all_parameters)

        # Target file paths
        target_dir = os.path.join("src", "usf", "common", "core", "utilitymodel", "common")
        header_file = os.path.join(target_dir, "bfpnames.h")
        cpp_file = os.path.join(target_dir, "bfpnames.cpp")
        
        # If running in apple prebuild environment
        if os.environ.get("CI_JOB_NAME") == "all-pre-build-job":
            # Check if generated files differ from source files
            temp_files = ["temp_bfpnames.h", "temp_bfpnames.cpp"]
            try:
                with open(temp_files[0], "w") as f:
                    f.write(header_content)
                with open(temp_files[1], "w") as f:
                    f.write(cpp_content)
                    
                if not (filecmp.cmp(temp_files[0], header_file) and 
                        filecmp.cmp(temp_files[1], cpp_file)):
                    print("Check if used ./ci/scripts/create_bfpnames.py to generate bfpnames.h and bfpnames.cpp files")
                    raise ValueError("Generated files differ from source files.\n"
                                     "On Unix system, use the command 'python3 ./ci/scripts/create_bfpnames.py' "
                                     "in the 'xunit/' directory to regenerate the file.\n"
                                     "On Windows system, use the command 'python .\\ci\\scripts\\create_bfpnames.py' "
                                     "in the 'xunit\\' directory to regenerate the file (Windows requires install Python environment).\n"
                                     "Usage: Go to http://confluence.chison.com.cn:8090/pages/viewpage.action?pageId=57704519")
                else:
                    print("Generated files are the same as source files")
            finally:
                # Clean up temporary files
                for temp_file in temp_files:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
        else:
            # Write files directly
            os.makedirs(target_dir, exist_ok=True)
            with open(header_file, "w") as f:
                f.write(header_content)
            with open(cpp_file, "w") as f:
                f.write(cpp_content)
            print(f"Files generated successfully:\n{header_file}\n{cpp_file}")
            
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main() 