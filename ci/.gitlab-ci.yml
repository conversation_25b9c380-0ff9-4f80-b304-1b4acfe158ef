# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/README.html#stages

stages:          # List of stages for jobs, and their order of execution
  - pre-pre-build   # 预准备阶段，在正式构建前的初始化工作
  - prebuild        # 预构建阶段，准备构建环境和依赖
  - cppcheck        # C++代码静态分析检查
  - code-format     # 代码格式检查
  - build           # 主要构建阶段
  - test            # 单元测试和集成测试
  - deploy          # 部署阶段，将构建产物部署到目标环境
  - release         # 发布阶段，准备和创建发布版本
  - autotest        # 自动化测试阶段
  - result          # 结果收集和分析阶段
  - report          # 报告生成阶段，汇总所有测试和构建结果



variables:
  # use licensemodeldef.h enum Model, like SonoEye, Phoenix, SR9, Apple etc. 
  # on web http://*************/usplatform/xunit/-/pipelines/new
  model: "bypass"             
  MainVersion: "V0.4.2"
  # 表示版本号结尾中会增加-xxx，xxx可以是用于表示此版本的特殊性的词汇，此例为NTRL，表示中性
  # 版本号，此例为V1.4.9.b61e460dA-NTRL。如果临时测试的版本，可以加上功能名，比如txcode，那版本号为
  # V1.4.9.b61e460dA-txcode；如果是普通版本，则不需要增加此参数
  VersionAdd: "alpha"
  # 资源分支
  ResourceBranch: "pangu"
  # C# DEMO分支
  UsapiexampleBranch: "master"
  # 探头
  Probes: "L25-V L25-D"
  # x64
  HostArch: "x86"
  #appcommon分支
  AppcommonBranch: "master"
  # 中性版本，默认为非中性版
  neutral: "false"
  # 上次版本号
  PreVersion: "default" 
  # 单元测试覆盖率阈值
  CodeCoverageThreshold: $CodeCoverageThreshold
  # 远程部署机器的IP
  Deploy_IP: "**************"
  # 自动化测试触发replay,result阶段
  Autotest: "false"
  # 自动化测试仓库分支
  TestcaseBranch: "main"
  # 自动化测试回放轮次
  ReplayCount: "1"
  # 是否支持版本追溯，如果为true则忽略分支判断，强制设置为可追溯版本（受保护分支不受该字段影响）
  IsVersionTraceable: "false"

include:
  - local: ci/prebuild/prebuild.yml
  - local: ci/model/*.yml

after_script:
  - date
