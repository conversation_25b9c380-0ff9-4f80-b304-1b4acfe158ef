#!/bin/bash

# Function to check if a file is a shell script
is_shell_script() {
    local file="$1"
    # Check if file exists and is a regular file
    if [ ! -f "$file" ]; then
        return 1
    fi
    
    # Check if file has .sh extension or contains shell shebang
    if [[ "$file" == *.sh ]] || head -n 1 "$file" | grep -q "^#!.*sh"; then
        return 0
    fi
    return 1
}

# Find all shell scripts in the project
echo ""

# Initialize counter for non-executable scripts
non_executable_count=0
non_executable_files=()

# Find all files and check if they are shell scripts
while IFS= read -r -d '' file; do
    if is_shell_script "$file"; then
        if [ ! -x "$file" ]; then
            echo "警告: 脚本 '$file' 没有执行权限"
            echo "修复方法: chmod +x '$file'"
            echo ""
            non_executable_files+=("$file")
            ((non_executable_count++))
        fi
    fi
done < <(find . -type f -not -path "*/\.*" -print0)

# Print summary
if [ $non_executable_count -eq 0 ]; then
    echo "所有Shell脚本都具有执行权限 ✓"
    exit 0
else
    echo "发现 $non_executable_count 个Shell脚本没有执行权限"
    echo "请使用上述提供的命令添加执行权限"
    echo ""
    echo "一键修复命令:"
    printf "chmod +x %s\n" "${non_executable_files[@]}"
    exit 1
fi 