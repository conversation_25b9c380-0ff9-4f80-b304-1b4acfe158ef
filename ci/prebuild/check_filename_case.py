#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
from typing import List, Tuple, Set
import fnmatch

def get_code_extensions() -> Set[str]:
    """Get the set of code file extensions to check."""
    return {'.cpp', '.h', '.hpp', '.c', '.py', '.bat', '.sh', '.cc'}

def parse_gitignore(gitignore_path: str) -> List[str]:
    """Parse .gitignore file and return a list of patterns."""
    patterns = []
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
    return patterns

def should_ignore(path: str, gitignore_patterns: List[str]) -> bool:
    """Check if a path should be ignored based on .gitignore patterns."""
    path = path.replace('\\', '/')  # Normalize path separators
    for pattern in gitignore_patterns:
        if pattern.startswith('/'):
            pattern = pattern[1:]
        if pattern.endswith('/'):
            pattern = pattern[:-1]
        if fnmatch.fnmatch(path, pattern) or fnmatch.fnmatch(path, f"**/{pattern}"):
            return True
    return False

def should_check_file(filename: str, code_extensions: Set[str]) -> bool:
    """Check if the file should be checked."""
    # Skip hidden files and directories
    if filename.startswith('.'):
        return False
    
    # Skip common build and dependency directories
    skip_dirs = {
        'build', 'cmake-build-', '.git', '.idea', 'node_modules',
        'venv', '__pycache__', '.pytest_cache'
    }
    
    for skip_dir in skip_dirs:
        if skip_dir in filename:
            return False
    
    # Only check files with specified extensions
    return any(filename.endswith(ext) for ext in code_extensions)

def check_directory(args: Tuple[str, str, Set[str], List[str]]) -> List[str]:
    """Check files in a single directory."""
    root, _, code_extensions, gitignore_patterns = args
    error_files = []
    
    try:
        for filename in os.listdir(root):
            if not should_check_file(filename, code_extensions):
                continue
                
            full_path = os.path.join(root, filename)
            rel_path = os.path.relpath(full_path, os.path.dirname(root))
            
            if should_ignore(rel_path, gitignore_patterns):
                continue
                
            if filename != filename.lower():
                error_files.append(full_path)
    except Exception as e:
        print(f"Error checking directory {root}: {str(e)}", file=sys.stderr)
    
    return error_files

def check_filename_case(directory: str) -> int:
    """Check if all filenames are lowercase using parallel processing."""
    # Get code extensions and gitignore patterns
    code_extensions = get_code_extensions()
    gitignore_path = os.path.join(directory, '.gitignore')
    gitignore_patterns = parse_gitignore(gitignore_path)
    
    # Get all directories to check
    directories = []
    for root, dirs, _ in os.walk(directory):
        # Filter out directories that should be skipped
        dirs[:] = [d for d in dirs if should_check_file(d, code_extensions)]
        directories.append((root, dirs, code_extensions, gitignore_patterns))
    
    # Use ProcessPoolExecutor for parallel processing
    num_workers = max(1, multiprocessing.cpu_count() - 1)  # Leave one CPU free
    error_files = []
    
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        # Submit all directory checks
        future_to_dir = {executor.submit(check_directory, dir_info): dir_info for dir_info in directories}
        
        # Collect results
        for future in future_to_dir:
            try:
                error_files.extend(future.result())
            except Exception as e:
                print(f"Error processing directory: {str(e)}", file=sys.stderr)
    
    if error_files:
        print("Error: Found files with uppercase letters in their names:")
        for file in sorted(error_files):
            print(f"  - {file}")
        print("\nPlease rename these files to use lowercase letters only.")
        return 1
    
    print("All filenames are lowercase.")
    return 0

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_filename_case.py <directory>")
        sys.exit(1)
    
    directory = sys.argv[1]
    if not os.path.isdir(directory):
        print(f"Error: {directory} is not a valid directory")
        sys.exit(1)
    
    sys.exit(check_filename_case(directory)) 