#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import chardet
import argparse
from pathlib import Path
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
from functools import partial
import time
import fnmatch
import platform

# Special character mappings for different encodings
SPECIAL_CHAR_MAPPINGS = {
    'EUC-JP': {
        b'\xA2\xE6': 'Ω',  # Omega symbol in EUC-JP
        b'\xA2\xE7': 'Ω',  # Another possible Omega encoding
    },
    'Windows-1252': {
        b'\xB0': '°',      # Degree symbol
        b'\xE2\x84\x83': '℃',  # Celsius symbol
        b'\xC2\xB0': '°',  # Degree symbol (UTF-8)
        b'\xE2\x84\x83': '℃',  # Celsius symbol (UTF-8)
    }
}

def parse_gitignore(gitignore_path):
    """Parse .gitignore file and return a list of patterns."""
    patterns = []
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
    return patterns

def should_ignore(path, gitignore_patterns):
    """Check if a path should be ignored based on .gitignore patterns."""
    path = path.replace('\\', '/')  # Normalize path separators
    for pattern in gitignore_patterns:
        if pattern.startswith('/'):
            pattern = pattern[1:]
        if pattern.endswith('/'):
            pattern = pattern[:-1]
        if fnmatch.fnmatch(path, pattern) or fnmatch.fnmatch(path, f"**/{pattern}"):
            return True
    return False

def detect_encoding(file_path):
    """Detect the encoding of a file."""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def normalize_line_endings(content):
    """Convert all line endings to LF."""
    # First convert to universal newlines (LF)
    content = content.replace('\r\n', '\n').replace('\r', '\n')
    return content

def fix_special_chars(content, source_encoding):
    """Fix special characters based on source encoding."""
    if source_encoding in SPECIAL_CHAR_MAPPINGS:
        for old_bytes, new_char in SPECIAL_CHAR_MAPPINGS[source_encoding].items():
            try:
                old_char = old_bytes.decode(source_encoding)
                content = content.replace(old_char, new_char)
            except UnicodeDecodeError:
                continue
    
    # Additional fixes for common issues
    content = content.replace('立', 'Ω')  # Fix for EUC-JP Omega symbol
    content = content.replace('â„ƒ', '℃')  # Fix for Windows-1252 Celsius symbol
    
    return content

def should_add_bom():
    """Determine if BOM should be added based on OS."""
    return platform.system().lower() == 'windows'

def convert_to_utf8(file_path, source_encoding):
    """Convert a file to UTF-8 encoding."""
    try:
        # Read the file in binary mode first
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            
        # Try to decode with detected encoding
        try:
            content = raw_data.decode(source_encoding)
        except UnicodeDecodeError:
            # If failed, try to decode with utf-8 first
            try:
                content = raw_data.decode('utf-8')
                source_encoding = 'utf-8'
            except UnicodeDecodeError:
                # If still failed, use 'replace' error handler
                content = raw_data.decode(source_encoding, errors='replace')
        
        # Fix special characters
        if source_encoding.lower() == 'euc-jp':
            content = content.replace('立', 'Ω')
        elif source_encoding.lower() == 'windows-1252':
            content = content.replace('â„ƒ', '℃')
            
        # Normalize line endings to LF
        content = normalize_line_endings(content)
        
        # Write back in UTF-8
        with open(file_path, 'wb') as f:
            # Add BOM only on Windows
            if should_add_bom():
                f.write(b'\xef\xbb\xbf')
            # Write content in UTF-8
            f.write(content.encode('utf-8'))
        
        # Verify the conversion
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            if not is_valid_utf8(result['encoding']):
                return False, file_path, f"Verification failed: file still detected as {result['encoding']}"
            
            # Verify line endings (skip BOM if present)
            start_pos = 3 if raw_data.startswith(b'\xef\xbb\xbf') else 0
            if b'\r\n' in raw_data[start_pos:] or b'\r' in raw_data[start_pos:]:
                return False, file_path, "Verification failed: file still contains CRLF or CR line endings"
        
        return True, file_path, None
    except Exception as e:
        return False, file_path, str(e)

def is_valid_utf8(encoding):
    """Check if the encoding is valid UTF-8 or ASCII."""
    if not encoding:
        return False
    encoding = encoding.lower()
    return encoding in ['utf-8', 'ascii', 'utf-8-sig']

def process_file(file_path):
    """Process a single file."""
    try:
        encoding = detect_encoding(file_path)
        if encoding and not is_valid_utf8(encoding):
            print(f"Converting {file_path} from {encoding} to UTF-8")
            success, path, error = convert_to_utf8(file_path, encoding)
            return success, path, error
        return True, file_path, None
    except Exception as e:
        return False, file_path, str(e)

def get_files_to_process(directory, extensions=None):
    """Get list of files to process."""
    if extensions is None:
        extensions = ['.cpp', '.h', '.hpp', '.c', '.py', '.cmake', '.txt', '.md', '.bat', '.sh', '.cc']
    
    # Get .gitignore patterns
    gitignore_path = os.path.join(directory, '.gitignore')
    gitignore_patterns = parse_gitignore(gitignore_path)
    
    files_to_process = []
    for root, _, files in os.walk(directory):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(full_path, directory)
                if not should_ignore(rel_path, gitignore_patterns):
                    files_to_process.append(full_path)
    return files_to_process

def main():
    parser = argparse.ArgumentParser(description='Convert files to UTF-8 encoding')
    parser.add_argument('directory', help='Directory to process')
    parser.add_argument('--extensions', nargs='+', help='File extensions to process')
    parser.add_argument('--workers', type=int, default=None, 
                      help='Number of worker processes (default: number of CPU cores)')
    args = parser.parse_args()
    
    start_time = time.time()
    
    # Get list of files to process
    files_to_process = get_files_to_process(args.directory, args.extensions)
    total_files = len(files_to_process)
    
    if total_files == 0:
        print("No files found to process.")
        return
    
    # Determine number of workers
    num_workers = args.workers or multiprocessing.cpu_count()
    print(f"Processing {total_files} files using {num_workers} workers...")
    
    # Process files in parallel
    converted_files = []
    failed_files = []
    
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        results = list(executor.map(process_file, files_to_process))
        
        for success, file_path, error in results:
            if success:
                converted_files.append(file_path)
            else:
                failed_files.append((file_path, error))
    
    # Print summary
    print("\nConversion Summary:")
    print(f"Total files processed: {total_files}")
    print(f"Successfully converted: {len(converted_files)}")
    print(f"Failed to convert: {len(failed_files)}")
    print(f"Time taken: {time.time() - start_time:.2f} seconds")
    
    if converted_files:
        print("\nConverted files:")
        for file in converted_files:
            print(f"- {file}")
    
    if failed_files:
        print(f"\nFailed to convert {len(failed_files)} files:")
        for file, error in failed_files:
            print(f"- {file}: {error}")

if __name__ == '__main__':
    main() 