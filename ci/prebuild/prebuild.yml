stages:
  - pre-pre-build
  - prebuild
all-pre-build-job:
  stage: pre-pre-build
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  tags:
    - prebuild
  script:
    - echo "=== 开始检查Shell脚本执行权限 ===" && ./ci/prebuild/check_shell_permissions.sh
    - echo "=== 开始检查CMakeLists.txt文件 ===" && ./ci/prebuild/comparerootcmakelists.sh
    - echo "=== 开始检查文件编码格式 ===" && python ./ci/prebuild/check_utf8_encoding.py .
    - echo "=== 开始检查行尾格式 ===" && python ./ci/prebuild/check_line_endings.py
    - echo "=== 开始检查文件名大小写 ===" && python ./ci/prebuild/check_filename_case.py .
    - echo "=== 开始检查bfpnames文件生成方式 ===" && python ./ci/scripts/create_bfpnames.py
    - echo "=== 开始检查GitCommit提交格式===" && python3 ./ci/shell/check_commit_template.py $CI_COMMIT_REF_NAME $CI_MERGE_REQUEST_TARGET_BRANCH_NAME
