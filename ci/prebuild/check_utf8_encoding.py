#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import chardet
import argparse
from pathlib import Path
import sys
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
import time
import fnmatch

def is_valid_utf8(encoding, confidence, raw_data):
    """Check if the encoding is valid UTF-8 or ASCII."""
    # Empty files are considered valid
    if not raw_data:
        return True
        
    # If no encoding detected but file is empty or contains only ASCII bytes
    if not encoding:
        try:
            raw_data.decode('ascii')
            return True
        except UnicodeDecodeError:
            return False
            
    # Remove BOM if present for encoding detection
    if raw_data.startswith(b'\xef\xbb\xbf'):
        raw_data = raw_data[3:]
            
    encoding = encoding.lower()
    # Accept ASCII as valid UTF-8
    if encoding == 'ascii':
        return True
    # For UTF-8 or UTF-8-SIG, require high confidence
    if encoding in ['utf-8', 'utf-8-sig']:
        # Try to decode as UTF-8
        try:
            raw_data.decode('utf-8')
            return confidence >= 0.7
        except UnicodeDecodeError:
            return False
    return False

def check_encoding(file_path):
    """Check if a file is UTF-8 encoded."""
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            confidence = result['confidence']
            
            is_valid = is_valid_utf8(encoding, confidence, raw_data)
            return is_valid, file_path, encoding
    except Exception as e:
        return False, file_path, str(e)

def parse_gitignore(gitignore_path):
    """Parse .gitignore file and return a list of patterns."""
    patterns = []
    if os.path.exists(gitignore_path):
        with open(gitignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
    return patterns

def should_ignore(path, gitignore_patterns):
    """Check if a path should be ignored based on .gitignore patterns."""
    path = path.replace('\\', '/')  # Normalize path separators
    for pattern in gitignore_patterns:
        if pattern.startswith('/'):
            pattern = pattern[1:]
        if pattern.endswith('/'):
            pattern = pattern[:-1]
        if fnmatch.fnmatch(path, pattern) or fnmatch.fnmatch(path, f"**/{pattern}"):
            return True
    return False

def get_files_to_check(directory, extensions=None):
    """Get list of files to check."""
    if extensions is None:
        extensions = ['.cpp', '.h', '.hpp', '.c', '.py', '.cmake', '.txt', '.md', '.bat', '.sh', '.cc']
    
    # Get .gitignore patterns
    gitignore_path = os.path.join(directory, '.gitignore')
    gitignore_patterns = parse_gitignore(gitignore_path)
    
    files_to_check = []
    for root, _, files in os.walk(directory):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(full_path, directory)
                if not should_ignore(rel_path, gitignore_patterns):
                    files_to_check.append(full_path)
    return files_to_check

def main():
    parser = argparse.ArgumentParser(description='Check files for UTF-8 encoding')
    parser.add_argument('directory', help='Directory to check')
    parser.add_argument('--extensions', nargs='+', help='File extensions to check')
    parser.add_argument('--workers', type=int, default=None,
                      help='Number of worker processes (default: number of CPU cores)')
    args = parser.parse_args()
    
    start_time = time.time()
    
    # Get list of files to check
    files_to_check = get_files_to_check(args.directory, args.extensions)
    total_files = len(files_to_check)
    
    if total_files == 0:
        print("No files found to check.")
        sys.exit(0)
    
    # Determine number of workers
    num_workers = args.workers or multiprocessing.cpu_count()
    print(f"Checking {total_files} files using {num_workers} workers...")
    
    # Check files in parallel
    non_utf8_files = []
    
    with ProcessPoolExecutor(max_workers=num_workers) as executor:
        results = list(executor.map(check_encoding, files_to_check))
        
        for is_valid, file_path, encoding in results:
            if not is_valid:
                non_utf8_files.append((file_path, encoding))
    
    # Print results
    if non_utf8_files:
        print("\nError: Found files that are not UTF-8 or ASCII encoded:")
        for file_path, encoding in non_utf8_files:
            print(f"- {file_path} (detected encoding: {encoding})")
        print("\nTo fix this, run the following command:")
        print(f"python ./ci/prebuild/convert_to_utf8.py {args.directory}")
        print(f"\nTime taken: {time.time() - start_time:.2f} seconds")
        sys.exit(1)
    else:
        print(f"All {total_files} files are UTF-8 or ASCII encoded.")
        print(f"Time taken: {time.time() - start_time:.2f} seconds")
        sys.exit(0)

if __name__ == '__main__':
    main() 