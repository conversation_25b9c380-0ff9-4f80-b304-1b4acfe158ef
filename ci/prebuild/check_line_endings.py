#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
from pathlib import Path

def get_git_attributes_files():
    """获取.gitattributes中指定的文件列表"""
    files = []
    with open('.gitattributes', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and 'text eol=lf' in line:
                pattern = line.split()[0]
                if pattern.startswith('*'):
                    # 使用git ls-files来获取匹配的文件
                    cmd = ['git', 'ls-files', pattern]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    if result.returncode == 0:
                        files.extend(result.stdout.splitlines())
    return files

def check_line_endings(file_path):
    """检查文件的行尾格式"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
            if b'\r\n' in content:
                return False
        return True
    except Exception as e:
        print(f"Error checking {file_path}: {str(e)}")
        return True

def main():
    # 获取需要检查的文件列表
    files_to_check = get_git_attributes_files()
    
    # 检查每个文件
    files_with_crlf = []
    for file_path in files_to_check:
        if not check_line_endings(file_path):
            files_with_crlf.append(file_path)
    
    # 如果有文件使用CRLF行尾，输出错误信息
    if files_with_crlf:
        print("错误：以下文件使用了CRLF行尾，但.gitattributes要求使用LF行尾：")
        for file_path in files_with_crlf:
            print(f"  - {file_path}")
        print("\n解决方案：")
        print("1. 使用git config --global core.autocrlf false 禁用自动转换")
        print("2. 使用dos2unix工具转换文件：")
        print("   - Windows: 安装dos2unix工具后运行: dos2unix <文件名>")
        print("   - Linux/Mac: 运行: dos2unix <文件名>")
        print("3. 或者使用git命令转换：")
        print("   git add --renormalize .")
        sys.exit(1)
    
    print("检查完成：所有文件都使用LF行尾。")

if __name__ == '__main__':
    main() 