# 在文件开头添加MODEL_CHANGED变量定义
if(NOT DEFINED MODEL_CHANGED)
    set(MODEL_CHANGED OFF CACHE BOOL "Indicates if MODEL has been changed")
endif()

# 添加findlib目录到CMake模块搜索路径
list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake/findlib")

# 设置缺省的Qt路径，如果未指定CMAKE_PREFIX_PATH则使用默认路径
function(set_default_qt_path)
    if("${CMAKE_PREFIX_PATH}" STREQUAL "")
        if(WIN32)
            if(EXISTS "D:/Qt/5.15.2/msvc2019_64")
                set(CMAKE_PREFIX_PATH "D:/Qt/5.15.2/msvc2019_64" PARENT_SCOPE)
            else()
                set(CMAKE_PREFIX_PATH "C:/Qt/5.15.2/msvc2019_64" PARENT_SCOPE)
            endif()
        elseif(UNIX AND NOT APPLE)
            set(CMAKE_PREFIX_PATH "/opt/Qt/5.15.2/gcc_64" PARENT_SCOPE)
        else()
            message(FATAL_ERROR "please set CMAKE_PREFIX_PATH value for find Qt5...")
        endif()
    endif()
    add_definitions(-DQT_PREFIX_PATH="${CMAKE_PREFIX_PATH}")
    message("CMAKE_PREFIX_PATH = ${CMAKE_PREFIX_PATH}")
endfunction()

macro(find_package_qt)
    set_default_qt_path()
    set(CMAKE_AUTOUIC ON)
    set(CMAKE_AUTOMOC ON)
    set(CMAKE_AUTORCC ON)
    set(CMAKE_INCLUDE_CURRENT_DIR ON)
    find_package(${BUILD_QT} COMPONENTS Core Widgets Gui Xml Sql Network OpenGL Multimedia WebSockets Quick Qml QmlModels Scxml REQUIRED)
    include_directories(${libname} ${Qt5Core_INCLUDE_DIRS} ${Qt5Widgets_INCLUDE_DIRS} ${Qt5Gui_INCLUDE_DIRS} ${Qt5Xml_INCLUDE_DIRS}
                                                    ${Qt5Sql_INCLUDE_DIRS} ${Qt5Network_INCLUDE_DIRS} ${Qt5OpenGL_INCLUDE_DIRS} ${Qt5Multimedia_INCLUDE_DIRS}
                                                    ${Qt5WebSockets_INCLUDE_DIRS} ${Qt5Quick_INCLUDE_DIRS} ${Qt5Qml_INCLUDE_DIRS} ${Qt5QmlModels_INCLUDE_DIRS} ${Qt5Scxml_INCLUDE_DIRS})
    if(APPLE)
    elseif(WIN32)
        find_package(${BUILD_QT} COMPONENTS PrintSupport SerialPort Test Bluetooth REQUIRED)
        include_directories(${libname} ${Qt5PrintSupport_INCLUDE_DIRS} ${Qt5SerialPort_INCLUDE_DIRS} ${Qt5Test_INCLUDE_DIRS}
                                                    ${Qt5Bluetooth_INCLUDE_DIRS})
    elseif(ANDROID)
        find_package(${BUILD_QT} COMPONENTS PrintSupport SerialPort Test Bluetooth AndroidExtras REQUIRED)
        include_directories(${libname} ${Qt5PrintSupport_INCLUDE_DIRS} ${Qt5SerialPort_INCLUDE_DIRS} ${Qt5Test_INCLUDE_DIRS}
                                                    ${Qt5Bluetooth_INCLUDE_DIRS} ${Qt5AndroidExtras_INCLUDE_DIRS})
    elseif(UNIX)
        find_package(${BUILD_QT} COMPONENTS PrintSupport SerialPort Test Bluetooth X11Extras REQUIRED)
        include_directories(${libname} ${Qt5PrintSupport_INCLUDE_DIRS} ${Qt5SerialPort_INCLUDE_DIRS} ${Qt5Test_INCLUDE_DIRS}
                                                    ${Qt5Bluetooth_INCLUDE_DIRS} ${Qt5X11Extras_INCLUDE_DIRS})
    endif()

    set(QT_SCXMLC "${CMAKE_PREFIX_PATH}/bin/qscxmlc")
    add_definitions(-DCMAKE_PREFIX_PATH="${CMAKE_PREFIX_PATH}")
    
    # 将所有需要的 Qt 库赋值给 QT_LIBRARIES 变量
    set(QT_LIBRARIES Qt5::Core 
                       Qt5::Widgets 
                       Qt5::Gui 
                       Qt5::Xml 
                       Qt5::Sql 
                       Qt5::Network 
                       Qt5::OpenGL 
                       Qt5::Multimedia 
                       Qt5::WebSockets 
                       Qt5::Quick 
                       Qt5::Qml 
                       Qt5::QmlModels 
                       Qt5::Scxml)
    if(APPLE)
    elseif(WIN32)
        list(APPEND QT_LIBRARIES Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth)
    elseif(ANDROID)
        list(APPEND QT_LIBRARIES Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth Qt5::AndroidExtras)
    elseif(UNIX)
        list(APPEND QT_LIBRARIES Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth Qt5::X11Extras)
    endif()
endmacro(find_package_qt)

# out para qrcs
macro(qt_add_resources_custom)
    list(APPEND qrcs_temp ${ARGN})
    list(LENGTH qrcs_temp qrcs_temp_length)

    if(${qrcs_temp_length} STREQUAL "0")
        return()
    endif(${qrcs_temp_length} STREQUAL "0")

    qt5_add_resources(qrcs ${qrcs_temp})
endmacro(qt_add_resources_custom)

# out para ui_cxxs
# Qt5 has content qt_wrap_ui
macro(qt_wrap_ui2)
    list(APPEND uis_temp ${ARGN})
    list(LENGTH uis_temp uis_temp_length)

    if(${uis_temp_length} STREQUAL "0")
        return()
    endif(${uis_temp_length} STREQUAL "0")

    qt5_wrap_ui(ui_cxxs ${uis_temp})
endmacro(qt_wrap_ui2)

function (qt_process_files)
    set(headers "")
    set(cpps "")
    set(uis "")

    include_directories("${CMAKE_CURRENT_SOURCE_DIR}")

    foreach(fileName ${ARGN})
        get_filename_component(extension ${fileName} EXT)
        if(extension STREQUAL ".cpp" OR extension STREQUAL ".c" OR extension STREQUAL ".cc"  OR extension STREQUAL ".mm" )
            get_filename_component(bareName ${fileName} NAME_WE)
            get_filename_component(absPath ${fileName} ABSOLUTE)
            get_filename_component(filePath ${absPath} PATH)

            if(filePath STREQUAL "")
                set(thisHeader ${bareName}.h)
                set(thisUi ${bareName}.ui)
            else()
                set(thisHeader  ${filePath}/${bareName}.h)
                set(thisUi ${filePath}/${bareName}.ui)
            endif()

            if(EXISTS ${thisHeader})
                list(APPEND headers ${thisHeader})
            endif()

            if(EXISTS ${thisUi})
                list(APPEND uis ${thisUi})
            endif()
            list(APPEND cpps ${fileName})

        elseif("${extension}" STREQUAL ".h")
            list(APPEND headers ${fileName})
        elseif("${extension}" STREQUAL ".ui")
            list(APPEND uis ${fileName})
        elseif("${extension}" STREQUAL ".rc")
            message("version file : ${fileName}")
        else()
            message(FATAL_ERROR "unknown file type : ${fileName}")
        endif()
    endforeach()

    qt_wrap_ui2(ui_cxxs ${uis})

    list(LENGTH cpps cpp_number)
    if(${cpp_number} GREATER 1)
        list(REMOVE_DUPLICATES cpps)
    endif()
    set(qt_processed_headers ${headers} PARENT_SCOPE)
    set(qt_processed_cpps ${cpps} PARENT_SCOPE)
    set(qt_processed_ui_cxxs ${ui_cxxs} PARENT_SCOPE)
endfunction()

function (add_executable_qt exeName)
    qt_process_files(${ARGN})
    set(M_PRODUCTNAME ${exeName})
    set(M_PRODUCTFILENAME "${exeName}${CMAKE_EXECUTABLE_SUFFIX}")

    if(EXISTS "${PROJECT_SOURCE_DIR}/version.rc.in")
        set(${exeName}_VERSION_RC "${PROJECT_BINARY_DIR}/versionfiles/${exeName}_version.rc")
        configure_file("${PROJECT_SOURCE_DIR}/version.rc.in" "${${exeName}_VERSION_RC}")
    endif()

    include_directories(${CMAKE_CURRENT_BINARY_DIR})
    add_executable(${exeName}
        ${qt_processed_cpps}
        ${qt_processed_ui_cxxs}
        ${qt_processed_headers}
        ${${exeName}_VERSION_RC})
    target_link_libraries(${exeName} ${QT_LIBRARIES})
    automoc_target(${exeName})
endfunction()

function (set_exe_in_test exeName)
    set_target_properties(${exeName} PROPERTIES
        RUNTIME_OUTPUT_DIRECTORY ${TEST_EXE_PATH})
endfunction()

function (add_library_qt_internal libName type)
    qt_process_files(${ARGN})
    include_directories(${CMAKE_CURRENT_BINARY_DIR})

    set(M_PRODUCTNAME ${libName})
    if(${type} STREQUAL "SHARED")
        set(M_PRODUCTFILENAME "${libName}${CMAKE_SHARED_LIBRARY_SUFFIX}")
    elseif(${type} STREQUAL "STATIC")
        set(M_PRODUCTFILENAME "${libName}${CMAKE_STATIC_LIBRARY_SUFFIX}")
    else()
        set(M_PRODUCTFILENAME ${libName})
    endif()

    if(EXISTS "${PROJECT_SOURCE_DIR}/version.rc.in")
        set(${libName}_VERSION_RC "${PROJECT_BINARY_DIR}/versionfiles/${libName}_version.rc")
        configure_file("${PROJECT_SOURCE_DIR}/version.rc.in" "${${libName}_VERSION_RC}")
    endif()

    add_library(${libName} ${type}
        ${qt_processed_cpps}
        ${qt_processed_ui_cxxs}
        ${qt_processed_headers}
        ${${libName}_VERSION_RC})
        
    target_link_libraries(${libName} Qt5::Core Qt5::Widgets Qt5::Gui Qt5::Xml Qt5::Sql Qt5::Network Qt5::OpenGL Qt5::Multimedia Qt5::WebSockets Qt5::Quick Qt5::Qml Qt5::QmlModels Qt5::Scxml)
    if(APPLE)
    elseif(WIN32)
        target_link_libraries(${libName} Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth)
    elseif(ANDROID)
        target_link_libraries(${libName} Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth Qt5::AndroidExtras)
    elseif(UNIX)
        target_link_libraries(${libName} Qt5::PrintSupport Qt5::SerialPort Qt5::Test Qt5::Bluetooth Qt5::X11Extras)
    endif()

    automoc_target(${libName})
    if(NOT BUILD_SYS STREQUAL "android")
    set_target_properties(${libName} PROPERTIES
        LIBRARY_OUTPUT_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib"
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib")
    endif()
    if(MINGW)
        set_target_properties(${libName} PROPERTIES PREFIX "")
    endif()
endfunction()

function (add_library_qt libName)
    if(APPLE)
        add_library_qt_internal(${libName} STATIC ${ARGN})
    else()
        add_library_qt_internal(${libName} SHARED ${ARGN})
    endif()
    if(NOT BUILD_SYS STREQUAL "android")
    set_target_properties(${libName} PROPERTIES
        LIBRARY_OUTPUT_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib"
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/lib")
    endif()
    install(TARGETS ${libName}
        RUNTIME DESTINATION lib
        LIBRARY DESTINATION lib)
endfunction(add_library_qt)

function (add_plugin_qt libName)
    add_library_qt_internal(${libName} MODULE ${ARGN})
    set_target_properties(${libName} PROPERTIES
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/plugins
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/plugins")
    install(TARGETS ${libName}
        RUNTIME DESTINATION plugins
        LIBRARY DESTINATION plugins)
endfunction(add_plugin_qt)

function (include_depends)
    foreach(thisDepends ${ARGN})
        if(TARGET ${thisDepends})
            get_target_property(inc_dir ${thisDepends} INCLUDE_DIRECTORIES)
        endif()
        list(REMOVE_DUPLICATES inc_dir)
        if(inc_dir STREQUAL "inc_dir-NOTFOUND")
            message(FATAL_ERROR "target ${thisDepends} not defined yet")
        endif()
        include_directories(${inc_dir})
    endforeach()
endfunction()

function (target_include_depends)
    set(targetlib ${ARGV0})
    set(mylist ${ARGV})
    list(SUBLIST mylist 1 ${ARGC}-1 depends)
    foreach(thisDepends ${depends})
        if(TARGET ${thisDepends})
            get_target_property(inc_dir ${thisDepends} INCLUDE_DIRECTORIES)
        endif()
        list(REMOVE_DUPLICATES inc_dir)
        if(inc_dir STREQUAL "inc_dir-NOTFOUND")
            message(FATAL_ERROR "target ${thisDepends} not defined yet")
        endif()
        target_include_directories(${targetlib} PRIVATE ${inc_dir})
    endforeach()
endfunction()

function (add_custom_test targetName)
    get_target_property(filePath ${targetName} RUNTIME_OUTPUT_DIRECTORY)
    add_test(NAME ${targetName}
             COMMAND ${filePath}/${targetName}
             WORKING_DIRECTORY ${TEST_EXE_PATH})
endfunction()

function (simple_exe cppFile)
    get_filename_component(bareName ${cppFile} NAME_WE)
    add_executable(${bareName} ${cppFile})
    automoc_target(${bareName})
    target_link_libraries(${bareName} ${ARGN})
endfunction()

function(simple_test cppFile)
    get_filename_component(bareName ${cppFile} NAME_WE)
    simple_exe(${cppFile} ${ARGN})
    set_exe_in_test(${bareName})
    add_custom_test(${bareName})
endfunction()

function(simple_gtest cppFile)
    get_filename_component(bareName ${cppFile} NAME_WE)
    add_executable(${bareName} ${cppFile})
    target_include_directories(${bareName} PRIVATE ${GOOGLETEST_INCLUDE_DIRS})
    automoc_target(${bareName})
    target_link_libraries(${bareName} ${ARGN}
                                        ${GTEST_LIB}
                                        ${GTEST_MAIN_LIB}
                                        ${GMOCK_LIB}
                                        ${GMOCK_MAIN_LIB}                                
                                        )
    if(UNIX)
        target_link_libraries(${bareName} ${ARGN} pthread)
    endif()                              
    set_exe_in_test(${bareName})
    add_custom_test(${bareName})
endfunction()

function(automoc_target targetName)
     set_target_properties(${targetName} PROPERTIES AUTOMOC ON)
endfunction()

function(target_files isOpened)
        set(filelist "")
        if(${isOpened})
                foreach(fileName ${ARGN})
            list(APPEND filelist ${fileName})
        endforeach()
        endif()
        set(TARGET_FILE ${filelist} PARENT_SCOPE)
endfunction()

macro(collect_usapi_srcs)
    foreach(relSrc ${ARGN})
        get_filename_component(absSrc ${relSrc} ABSOLUTE)
        list(APPEND integrated_usapi_srcs ${absSrc})
    endforeach()
    set(integrated_usapi_srcs ${integrated_usapi_srcs} PARENT_SCOPE)
endmacro(collect_usapi_srcs)

macro(option4us var value help_text)
    # 参数有效性检查
    if("${var}" STREQUAL "")
        message(FATAL_ERROR "option4us: Variable name cannot be empty")
        return()
    endif()

    # 检查value是否为有效的布尔值
    if(NOT "${value}" MATCHES "^(ON|OFF|TRUE|FALSE|1|0)$")
        message(FATAL_ERROR "option4us: Default value must be a valid boolean (ON/OFF/TRUE/FALSE/1/0)")
        return()
    endif()

    # 先移除之前的宏定义（如果存在）
    remove_definitions(-D${var})

    # 检查是否是MODEL相关的配置
    if(MODEL_CHANGED)
        # 如果MODEL改变了，使用新的默认值
        set(${var} ${value} CACHE BOOL ${help_text} FORCE)
    else()
        # 如果MODEL没有改变，保持现有值（允许命令行参数覆盖）
        if(NOT DEFINED ${var})
            set(${var} ${value} CACHE BOOL ${help_text})
        endif()
    endif()

    # 根据新值添加宏定义
    if(${var})
        add_definitions(-D${var})
    endif()

    # 输出实际值而不是默认值，使用格式化使括号内信息右对齐
    string(LENGTH "${var}" var_length)
    string(LENGTH "${${var}}" value_length)
    math(EXPR total_spaces "50 - ${var_length} - ${value_length}")
    if(${total_spaces} GREATER 0)
        string(REPEAT " " ${total_spaces} spaces)
    else()
        set(spaces " ")
    endif()
    message(STATUS "       ${var} = ${${var}}${spaces}(${help_text})")
endmacro(option4us)

macro(showconfigureinfo)
    message("
        -- -------------------------------------------------------------------------------------------
        -- Basic Configuration
        -- PROJECT_NAME                       = ${PROJECT_NAME}
        -- CMAKE_BUILD_TYPE                   = ${CMAKE_BUILD_TYPE}
        -- MODEL                              = ${MODEL}
        -- CMAKE_C_COMPILER                   = ${CMAKE_C_COMPILER}
        -- CMAKE_CXX_COMPILER                 = ${CMAKE_CXX_COMPILER}
        -- 
        -- Output Directory Configuration
        -- CMAKE_RUNTIME_OUTPUT_DIRECTORY     = ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
        -- CMAKE_LIBRARY_OUTPUT_DIRECTORY     = ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
        -- 
        -- Third Party Library Paths
        -- BOOST_ROOT                         = ${BOOST_ROOT}
        -- CMAKE_PREFIX_PATH                  = ${CMAKE_PREFIX_PATH}
        -- 
        -- Test and Library Paths
        -- TEST_EXE_PATH                      = ${TEST_EXE_PATH}
        -- THIRDPARTYLIB_PATH                 = ${THIRDPARTYLIB_PATH}
        -- -------------------------------------------------------------------------------------------
        ")
endmacro(showconfigureinfo)

macro(thirdparty_prefix_path lib_name)
    string(TOUPPER ${lib_name} upper_lib_name)
    string(TOLOWER ${lib_name} lower_lib_name)
    set(${upper_lib_name}_ROOT ${THIRDPARTYLIB_PATH}/${lower_lib_name}/)
    if(MSVC)
        set(libtype "Release")
        if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
            set(libtype "Debug")
        endif()
        set(${upper_lib_name}_RUNTIMELIB_PATH ${THIRDPARTYLIB_PATH}/${lower_lib_name}/${THIRDPARTYLIB_NAME}/${libtype})
        set(${upper_lib_name}_LIBPATH ${${upper_lib_name}_ROOT}lib/${libtype})
    else()
        set(${upper_lib_name}_RUNTIMELIB_PATH ${THIRDPARTYLIB_PATH}/${lower_lib_name}/${THIRDPARTYLIB_NAME})
        set(${upper_lib_name}_LIBPATH ${${upper_lib_name}_ROOT}lib)
    endif(MSVC)
endmacro(thirdparty_prefix_path)
