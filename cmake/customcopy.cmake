# 为了解决CMake无法拷贝软连接问题

#[[
    funcname: mycopyfunction
    params  : source<源地址>, target<目标地址>
    desc    : 通过传入的源地址和目标地址，首先检测源地址是否存在，如果存在则进行复制操作；如果源地址不存在忽略此次复制操作
    author  : wangshang
]]
function(mycopyfunction source target)
    if (EXISTS ${source})
        file(COPY ${source} DESTINATION ${target} FOLLOW_SYMLINK_CHAIN)
    else()
    	message(WARNING "${source} is not exists")
    endif()
endfunction(mycopyfunction)


#[[
    自定义命令调用方式: 
    	set(sourceDir ../../lib/build-testso-Desktop_Qt_5_15_2_GCC_64bit-Debug/) # 预设源地址
	set(targetDir ../../lib/build-calculator-Desktop_Qt_5_15_2_GCC_64bit-Debug/test/) # 预设目标地址
	add_custom_command(TARGET executable
	    POST_BUILD # 构建完成后开始执行自定义命令
	    COMMAND cmake -DsourceDirector=${sourceDir} -DtargetDirector=${targetDir} -P customcopy.cmake # 说明：-D传参必须写在调用-P之前，命令区分大小写
	    )
]]
mycopyfunction(${sourceDirector} ${targetDirector}) # 调用自定义拷贝函数，并传入源地址和目标地址
