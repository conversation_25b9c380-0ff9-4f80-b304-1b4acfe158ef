#
# The module defines the following variables:
# NEED<PERSON>AN<PERSON>LE_FOUND - True if Need<PERSON>Angle found.
# NEEDLEANGLE_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# NEEDLEANGLE_LIBRARIES - List of libraries when using <PERSON>leAngle.
#
thirdparty_prefix_path(<PERSON><PERSON><PERSON>ngle)

find_path ( NEEDLEANGLE_INCLUDE_DIR
            NAMES
                autoneedleangle.h
            HINTS
                ${NEEDLEANGLE_ROOT}/include
            )
set(NEEDLEANGLE_INCLUDE_DIRS ${NEEDLEANGLE_INCLUDE_DIR})
message("Found needleangle headers: ${NEEDLEANGLE_INCLUDE_DIRS}")


find_library ( NEEDLEANGLE_LIBRARY
            NAMES
                NeedleAngle
            HINTS
                ${NEEDLEANGLE_LIBPATH}
             )
set(NEEDLEANGLE_LIBRARIES ${NEEDLEANGLE_LIBRARY})
message("Found NEEDLEANGLE libs: ${NEEDLEANGLE_LIBRARIES}")
