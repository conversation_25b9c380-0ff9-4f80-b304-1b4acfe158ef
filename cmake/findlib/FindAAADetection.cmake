thirdparty_prefix_path(aaadetection)

find_path ( AAADETECTION_INCLUDE_DIR
            NAMES
                aaadetection_api.h
                aaadetectioninfo.h
            HINTS
                ${AAADETECTION_ROOT}/include
            )
set(AAADETECTION_INCLUDE_DIRS ${AAADETECTION_INCLUDE_DIR})
message("Found aaadetection headers: ${AAADETECTION_INCLUDE_DIRS}")


find_library ( AAADETECTION_LIBRARY
            NAMES
                sonoaaa
            HINTS
                ${AAADETECTION_ROOT}/lib
             )
set(AAADETECTION_LIBRARIES ${AAADETECTION_LIBRARY})
message("Found aaadetection libs: ${AAADETECTION_LIBRARIES}")
