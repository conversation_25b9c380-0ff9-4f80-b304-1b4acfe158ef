#
# The module defines the following variables:
# CKEY<PERSON>ARD_FOUND - True if <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> found.
# CKEYBOARD_INCLUDE_DIRS - where to find ckeyboard.h, etc.
# CKEYBOARD_LIBRARIES - List of libraries when using CKEYBOARD.
#

thirdparty_prefix_path(CKEYBOARD)

find_path ( <PERSON><PERSON><PERSON><PERSON>AR<PERSON>_INCLUDE_DIR
            NAMES
                ckeyboard.h
            HINTS
                ${CKEYBOARD_ROOT}/include
            )
set(CKEYBOARD_INCLUDE_DIRS ${CKEYBOARD_INCLUDE_DIR})
message("Found CKEYBOARD headers: ${CKEYBOARD_INCLUDE_DIRS}")

find_library ( CKEYBOARD_LIBRARY
            NAMES 
                CKeyboard
            HINTS
                ${CKEYBOARD_LIBPATH}
             )
set(CKEYBOARD_LIBRARIES ${CKEYBOARD_LIBRARY})
message("Found CKEYBOARD libs: ${CKEYBOARD_LIBRARIES}")
