thirdparty_prefix_path(autobline)

find_path ( AUTOBLINE_INCLUDE_DIR
            NAMES
                blinedetectioninfo.h
                blinedetection_api.h
            HINTS
	        ${AUTOBLINE_ROOT}/include
            )
set(AUTOBLINE_INCLUDE_DIRS ${AUTOBLINE_INCLUDE_DIR})
message("Found autobline headers: ${AUTOBLINE_INCLUDE_DIRS}")

find_library ( AUTOBLINE_LIBRARY
            NAMES
                autobline
            HINTS
	        ${AUTOBLINE_LIBPATH}
             )
set(AUTOBLINE_LIBRARIES ${AUTOBLINE_LIBRARY})
message("Found autobline libs: ${AUTOBLINE_LIBRARIES}")

