thirdparty_prefix_path(pleuralline)

find_path ( PLEURALLINE_INCLUDE_DIR
            NAMES
                pleuralline.h
            HINTS
	        ${PLEURALLINE_ROOT}/include
            )
set(PLEURALLINE_INCLUDE_DIRS ${PLEURALLINE_INCLUDE_DIR})
message("Found pleuralline headers: ${PLEURALLINE_INCLUDE_DIRS}")

find_library ( PLEURALLINE_LIBRARY
            NAMES
                lungalg
            HINTS
	        ${PLEURALLINE_LIBPATH}
             )
set(PLEURALLINE_LIBRARIES ${PLEURALLINE_LIBRARY})
message("Found pleuralline libs: ${PLEURALLINE_LIBRARIES}")

