thirdparty_prefix_path(sonovessel)

find_path ( SONOVESSEL_INCLUDE_DIR
            NAMES
                VesselMeasure.h
            HINTS
                ${SONOVESSEL_ROOT}/include
          )
set(SONOVESSEL_INCLUDE_DIRS ${SONOVESSEL_INCLUDE_DIR})
message("Found VesselMeasure headers: ${SONOVE<PERSON>EL_INCLUDE_DIRS}")

find_library ( SONOVESSEL_LIBRARY
               NAMES
                   VesselMeasure
               HINTS
                   ${SONOVESSEL_ROOT}/lib
             )
set(SONOVESSEL_LIBRARIES ${SONOVESSEL_LIBRARY})
message("Found VesselMeasure libs: ${SONOVESSEL_LIBRARIES}")
