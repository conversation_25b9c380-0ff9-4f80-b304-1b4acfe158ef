thirdparty_prefix_path(qtwebengine)
             
find_library ( Qt5WEBENGINECORE_LIBPATH
            NAMES
                Qt5WebEngineCore
            HINTS
                ${QTWEBENGINE_LIBPATH}
             )
             
find_library ( QT5WEBENGINEWIDGETS_LIBPATH
            NAMES
                Qt5WebEngineWidgets
            HINTS
                ${QTWEBENGINE_LIBPATH}
             )

set(QTWEBENGINE_LIBRARIES ${Qt5WEBENGINECORE_LIBPATH} ${QT5WEBENGINEWIDGETS_LIBPATH})
message("Found qtWebEngine libs: ${QTWEBENGINE_LIBRARIES}")
