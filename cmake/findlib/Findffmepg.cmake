#
# The module defines the following variables:
# FFMPEG_FOUND - True if ffmpeg related lib found.
# FFMPEG_INCLUDE_DIRS - where to find *.h, etc.
# FFMPEG_LIBRARIES - List of libraries when using ffmpeg.
#

thirdparty_prefix_path(ffmpeg)
set(FFMPEG_INCLUDE_DIR ${FFMPEG_ROOT}include/)
set(FFMPEG_BIN_DIR ${FFMPEG_ROOT}bin/)
set(FFMPEG_INCLUDE_DIRS ${FFMPEG_INCLUDE_DIR})

find_library ( FFMPEG_AVCODEC_LIBRARY
                 NAMES
                     avcodec
                 HINTS
                     ${FFMPEG_ROOT}/lib
             )
find_library ( FFMPEG_AVFORMAT_LIBRARY
                   NAMES
                       avformat
                   HINTS
                       ${FFMPEG_ROOT}/lib
                    )
find_library ( FFMPEG_AVUTIL_LIBRARY
                   NAMES
                       avutil
                   HINTS
                       ${FFMPEG_ROOT}/lib
                    )
find_library ( FFMPEG_SWSCALE_LIBRARY
                   NAMES
                       swscale
                   HINTS
                       ${FFMPEG_ROOT}/lib
                    )
find_library ( FFMPEG_SWRESAMPLE_LIBRARY
                   NAMES
                       swresample
                   HINTS
                       ${FFMPEG_ROOT}/lib
                    )
set(FFMPEG_LIBRARIES ${FFMPEG_AVCODEC_LIBRARY} ${FFMPEG_AVFORMAT_LIBRARY} ${FFMPEG_AVUTIL_LIBRARY}
    ${FFMPEG_SWSCALE_LIBRARY} ${FFMPEG_SWRESAMPLE_LIBRARY})
message("Found ffmpeg libs: ${FFMPEG_LIBRARIES}")
