#
# The module defines the following variables:
# POWERKEY_FOUND - True if <PERSON><PERSON><PERSON><PERSON><PERSON> found.
# POWERKEY_INCLUDE_DIRS - where to find *.h, etc.
# POWERKEY_LIBRARIES - List of libraries when using POWERKEY.
#

thirdparty_prefix_path(powerkey)

find_path ( POWERKEY_INCLUDE_DIR
            NAMES
                HwCtrlPwrkeyFliter.h
            HINTS
                ${POWERKEY_ROOT}/include
            )
set(POWERKEY_INCLUDE_DIRS ${POWERKEY_INCLUDE_DIR})
message("Found powerkey headers: ${POWERKEY_INCLUDE_DIRS}")


find_library ( POWERKEY_LIBRARY
            NAMES
                pwrkeyfilter
            HINTS
                ${POWERKEY_LIBPATH}
             )
set(POWERKEY_LIBRARIES ${POWERKEY_LIBRARY})
message("Found powerkey libs: ${POWERKEY_LIBRARIES}")
