#
# The module defines the following variables:
# RTIMT_FOUND - True if RTIMT found.
# RTIMT_INCLUDE_DIRS - where to find rtimt.h, etc.
# RTIMT_LIBRARIES - List of libraries when using RTIMT.
#
thirdparty_prefix_path(rtimt)

find_path ( RTIMT_INCLUDE_DIR
            NAMES
                rtimt.h
            HINTS
                ${RTIMT_ROOT}/include
            )
set(RTIMT_INCLUDE_DIRS ${RTIMT_INCLUDE_DIR})
message("Found rtimt headers: ${RTIMT_INCLUDE_DIRS}")

find_library ( RTIMT_LIBRARY
            NAMES
                rtimt
            HINTS
                ${RTIMT_ROOT}/lib
             )
set(RTIMT_LIBRARIES ${RTIMT_LIBRARY})
message("Found rtimt libs: ${RTIMT_LIBRARIES}")

