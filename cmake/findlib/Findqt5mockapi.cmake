#
# The module defines the following variables:
# QT5MOCKAPI_FOUND - True if imt found.
# QT5MOCKAPI_INCLUDE_DIRS - where to find imt.h, etc.
# QT5MOCKAPI_LIBRARIES - List of libraries when using imt.
#

find_path ( QT5MOCKAPI_INCLUDE_DIR
            NAMES
	    QWebElement QWebFrame
            HINTS
	    ${QT5MOCKAPI_ROOT}/include
            )
set(QT5MOCKAPI_INCLUDE_DIRS ${QT5MOCKAPI_INCLUDE_DIR})
message("Found qt5mockapi headers: ${QT5MOCKAPI_INCLUDE_DIRS}")


