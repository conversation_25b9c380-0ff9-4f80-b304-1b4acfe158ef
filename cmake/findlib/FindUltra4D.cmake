#
# The module defines the following variables:
# ULTRA4D_FOUND - True if Ultra4D found.
# ULTRA4D_INCLUDE_DIRS - where to find Ultra4D.h, etc.
# ULTRA4D_LIBRARIES - List of libraries when using Ultra4D.
#

thirdparty_prefix_path(ultra4d)

find_path ( ULTRA4D_INCLUDE_DIR
            NAMES
                UltraViewer.h
            HINTS
                ${ULTRA4D_ROOT}/include
            )
set(ULTRA4D_INCLUDE_DIRS ${ULTRA4D_INCLUDE_DIR})
message("Found Ultra4D headers: ${ULTRA4D_INCLUDE_DIRS}")


find_library ( ULTRA4D_LIBRARY
            NAMES 
                Ultra4D
            HINTS
                ${ULTRA4D_LIBPATH}
             )
set(ULTRA4D_LIBRARIES ${ULTRA4D_LIBRARY})
message("Found Ultra4D libs: ${ULTRA4D_LIBRARIES}")
