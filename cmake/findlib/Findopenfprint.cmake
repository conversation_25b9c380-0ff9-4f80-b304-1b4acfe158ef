#
# The module defines the following variables:
# OPENFPRINT_FOUND - True if openfprint found.
# OPENFPRINT_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# OPENFPRINT_LIBRARIES - List of libraries when using openfprint.
#

thirdparty_prefix_path(openfprint)

find_path ( OPENFPRINT_INCLUDE_DIR
    NAMES
    openfingerprintdev_global.h
    openfingerprintdev.h
    HINTS
    ${OPENFPRINT_ROOT}/include
    )
set(OPENFPRINT_INCLUDE_DIRS ${OPENFPRINT_INCLUDE_DIR})
message("Found openfprint headers: ${OPENFPRINT_INCLUDE_DIRS}")

find_library (OPENFPRINTWRAP_LIBRARY
    NAMES
    openfprint
    HINTS
    ${OPENFPRINT_ROOT_DIR}/lib
    ${OPENFPRINT_ROOT}/lib
    )

find_library (OPENFINGERPRINTER_LIBRARY
    NAMES
    fprint-2
    HINTS
    ${OPENFPRINT_ROOT_DIR}/lib
    ${OPENFPRINT_ROOT}/lib
    )

set(OP<PERSON>FPRINT_LIBRARIES ${OPENFPRINTWRAP_LIBRARY} ${OPENFINGERPRINTER_LIBRARY})
message("Found openfprint libs: ${OPENFPRINT_LIBRARIES}")
