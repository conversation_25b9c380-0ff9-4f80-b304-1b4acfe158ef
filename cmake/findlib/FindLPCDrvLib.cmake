#
# The module defines the following variables:
# LPCDRV_FOUND - True if LPCDRV found.
# LPCDRV_INCLUDE_DIRS - where to find *.h, etc.
# LPCDRV_LIBRARIES - List of libraries when using LPCDRV.
#

thirdparty_prefix_path(lpcdrvLib)

find_path ( LPCDRV_INCLUDE_DIR
            NAMES
                clpc.h
                HwctrlCmn.h
            HINTS
                ${LPCDRV_ROOT}/include
            )
set(LPCDRV_INCLUDE_DIRS ${LPCDRV_INCLUDE_DIR})
message("Found LPCDrv headers: ${LPCDRV_INCLUDE_DIRS}")


find_library ( LPCDRV_LIBRARY
            NAMES
                HwctrlLpc
				HwctrlLog
            HINTS
                ${LPCDRV_ROOT_DIR}/lib
                ${LPCDRV_ROOT}/lib
             )
set(LPCDRV_LIBRARIES ${LPCDRV_LIBRARY})
message("Found imgver libs: ${LPCDRV_LIBRARIES}")
