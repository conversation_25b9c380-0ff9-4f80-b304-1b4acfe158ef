#
# The module defines the following variables:
# ELEMENTDETECT_INVIWO_FOUND - True if elementtest found.
# ELEMENTDETECT_INCLUDE_DIRS - where to find ElementTest.h, etc.
# ELEMENTDETECT_LIBRARIES - List of libraries when using ElementTest.
#


thirdparty_prefix_path(ELEMENTDETECT)

find_path ( ELEMENTDETECT_INCLUDE_DIR
            NAMES
            wafertest.h
            HINTS
                ${ELEMENTDETECT_ROOT}/include
            )

set(ELEMENTDETECT_INCLUDE_DIRS ${ELEMENTDETECT_INCLUDE_DIR})
message("Found elementdetect headers: ${ELEMENTDETECT_INCLUDE_DIRS}")

find_library( ELEMENTDETECT_LIBRARY
            NAMES
                wafertest
            HINTS
                ${ELEMENTDETECT_LIBPATH}
            )

#set(ELEMENTDETECT_LIBRARY ${ELEMENTDETECT_LIBPATH}/libwafertest.so)
set(ELEMENTDETECT_LIBRARIES ${ELEMENTDETECT_LIBRARY})
message("Found elementdetect libs: ${ELEMENTDETECT_LIBRARIES}")
