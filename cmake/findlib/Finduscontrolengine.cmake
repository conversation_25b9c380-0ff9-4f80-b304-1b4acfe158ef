#
# The module defines the following variables:
# <PERSON>ON<PERSON><PERSON><PERSON>GINE_FOUND - True if USCONTROLENGINE found.
# USCON<PERSON><PERSON><PERSON>GINE_INCLUDE_DIRS - where to find uscontrolengineuiapi.h, etc.
# USCONTROLENGINE_LIBRARIES - List of libraries when using USCONTR<PERSON>ENGINE.
#


thirdparty_prefix_path(USCON<PERSON><PERSON>ENGINE)

find_path ( USCONTR<PERSON><PERSON>GINE_INCLUDE_DIR
            NAMES
                uscontrolengineuiapi.h
            HINTS
                ${USCONTROLENGINE_ROOT}/include
            )
set(USCONTR<PERSON><PERSON>GINE_INCLUDE_DIRS ${USCONTROLENGINE_INCLUDE_DIR})
message("Found USCONTROLENGINE headers: ${USCONTROLENGINE_INCLUDE_DIRS}")

message("<PERSON>ONTROLENGINE_ROOT: ${USCONTR<PERSON>ENGINE_ROOT}")
message("USCONTR<PERSON>ENGINE_LIBPATH: ${USCONTROLENGINE_LIBPATH}")
find_library ( USCONTROLENGINE_LIBRARY
            NAMES 
                uscontrolengine
            HINTS
                ${USCONTROLENGINE_LIBPATH}
             )

find_library ( USCON<PERSON><PERSON>EN<PERSON>NEUI_LIBRARY
            NAMES 
	    #uscontrolengine uscontrolengineui
                uscontrolengineui
            HINTS
                ${USCONTROLENGINE_LIBPATH}
             )

if(USE_WALLFILTER)
find_library ( WALLFILTER_LIBRARY
         NAMES
             wallfilter
         HINTS
             ${USCONTROLENGINE_LIBPATH}
          )
endif()


set(USCONTROLENGINE_LIBRARIES ${USCONTROLENGINE_LIBRARY} ${USCONTROLENGINEUI_LIBRARY})
if(USE_WALLFILTER)
list(APPEND USCONTROLENGINE_LIBRARIES ${WALLFILTER_LIBRARY})
endif()
message("Found USCONTROLENGINE libs: ${USCONTROLENGINE_LIBRARIES}")
