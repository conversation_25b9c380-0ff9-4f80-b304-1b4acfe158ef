thirdparty_prefix_path(sonovf)

find_path ( SONOVF_INCLUDE_DIR
            NAMES
                AutoVesselDetection.h
            HINTS
	        ${SONOVF_ROOT}/include
            )
set(SONOVF_INCLUDE_DIRS ${SONOVF_INCLUDE_DIR})
message("Found sonovf headers: ${SONOVF_INCLUDE_DIRS}")

find_library ( SONOVF_LIBRARY
            NAMES 
                AutoBFlow
            HINTS
                ${SONOVF_ROOT}/lib
             )
set(SONOVF_LIBRARIES ${SONOVF_LIBRARY})
message("Found sonovf libs: ${SONOVF_LIBRARIES}")



