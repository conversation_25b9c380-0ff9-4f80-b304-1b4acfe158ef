thirdparty_prefix_path(autoef)

find_path ( AUTOEF_INCLUDE_DIR
            NAMES
                autoef.hpp
            HINTS
                ${AUTOEF_ROOT}/include
            )
set(AUTOEF_INCLUDE_DIRS ${AUTOEF_INCLUDE_DIR})
message("Found autoef headers: ${AUTOEF_INCLUDE_DIRS}")

find_library ( AUTOEF_LIBRARY
            NAMES
                lvsec
            HINTS
                ${AUTOEF_LIBPATH}
             )
set(AUTOEF_LIBRARIES ${AUTOEF_LIBRARY})
message("Found lvsec libs: ${AUTOEF_LIBRARIES}")




