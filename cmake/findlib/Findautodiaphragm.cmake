thirdparty_prefix_path(autodiaphragm)

find_path ( AUTODIAPHRAGM_INCLUDE_DIR
            NAMES
		dainterface.h
            HINTS
	        ${AUTODIAPHRAGM_ROOT}/include
            )
set(AUTODIAPHRAGM_INCLUDE_DIRS ${AUTODIAPH<PERSON>GM_INCLUDE_DIR})
message("Found autodiaphragm headers: ${AUTODIAPHRAGM_INCLUDE_DIRS}")


find_library ( AUTODIAPHRAGM_LIBRARY
            NAMES 
                diaphragmproject
            HINTS
                ${AUTODIAPHRAGM_ROOT}/lib
             )
set(AUTODIAPHRAGM_LIBRARIES ${AUTODIAPHRAGM_LIBRARY})
message("Found autodiaphragm libs: ${AUTODIAPHRAGM_LIBRARIES}")


