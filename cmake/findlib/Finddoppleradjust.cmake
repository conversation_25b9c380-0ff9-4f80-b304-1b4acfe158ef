#
# The module defines the following variables:
# DOPPLERADJUST_FOUND - True if autofreeze found.
# DOPPLERADJUST_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# DOPPLERADJUST_LIBRARIES - List of libraries when using autofreeze.
#
thirdparty_prefix_path(doppleradjust)

find_path ( DOP<PERSON><PERSON>ADJUST_INCLUDE_DIR
            NAMES
               VesselDetection.h
            HINTS
                ${DOPPLERADJUST_ROOT}/include
            )
set(DOPPLERADJUST_INCLUDE_DIRS ${DOPPLERADJUST_INCLUDE_DIR})
message("Found autofreeze headers: ${DOPPLERADJUST_INCLUDE_DIRS}")


find_library ( DOPPLERADJUST_LIBRARY
            NAMES 
                IntelligentDoppler
            HINTS
                ${DOPPLERADJUST_LIBPATH}
             )
set(DOPPLERADJUST_LIBRARIES ${DOPPLERADJUST_LIBRARY})
message("Found DOPPLERADJUST libs: ${DOPPLERADJUST_LIBRARIES}")
