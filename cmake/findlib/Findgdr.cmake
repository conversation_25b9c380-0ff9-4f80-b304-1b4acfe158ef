#
# The module defines the following variables:
# GDR_FOUND - True if <PERSON><PERSON> found.
# GDR_INCLUDE_DIRS - where to gdr.h, etc.
# GDR_LIBRARIES - List of libraries when using GDR.
#

thirdparty_prefix_path(gdr)

find_path ( GDR_INCLUDE_DIR
            NAMES
                gdrmap.h
            HINTS
                ${GDR_ROOT}/include
            )
set(GDR_INCLUDE_DIRS ${GDR_INCLUDE_DIR})
message("Found GDR headers: ${GDR_INCLUDE_DIRS}")

find_library ( GDR_LIBRARY
            NAMES 
                gdrmap64
            HINTS
                ${GDR_LIBPATH}
             )
set(GDR_LIBRARIES ${GDR_LIBRARY})
message("Found GDR libs: ${GDR_LIBRARIES}")
