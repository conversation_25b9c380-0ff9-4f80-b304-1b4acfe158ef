#
# The module defines the following variables:
# FINGERPRINT_FOUND - True if fingerprint found.
# FINGERPRINT_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# FINGERPRINT_LIBRARIES - List of libraries when using fingerprint.
#

thirdparty_prefix_path(fingerprint)

find_path ( FINGERPRINT_INCLUDE_DIR
            NAMES
                fingerprintdev.h
            HINTS
                ${FINGERPRINT_ROOT}/include
            )
set(FINGERPRINT_INCLUDE_DIRS ${FINGERPRINT_INCLUDE_DIR})
message("Found fingerprint headers: ${FINGERPRINT_INCLUDE_DIRS}")


find_library ( FINGERPRINT_LIBRARY
            NAMES 
                fingerprint
            HINTS
                ${FINGERPRINT_ROOT}/lib
             )
set(FINGERPRINT_LIBRARIES ${FINGERPRINT_LIBRARY})
message("Found FINGERPRINT libs: ${FINGERPRINT_LIBRARIES}")
