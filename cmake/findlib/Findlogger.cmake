#
# The module defines the following variables:
# LOGGER_FOUND - True if logger found.
# LOGGER_INCLUDE_DIRS - where to find applogger.h, etc.
# LOGGER_LIBRARIES - List of libraries when using logger.
#

thirdparty_prefix_path(logger)

find_path ( LOG<PERSON>R_INCLUDE_DIR
            NAMES
                logger.h
            HINTS
                ${LOGGER_ROOT}/include
            )
set(LOGGER_INCLUDE_DIRS ${LOGGER_INCLUDE_DIR})
message("Found logger headers: ${LOGGER_INCLUDE_DIRS}")

find_library ( LOGGER_LIBRARY
            NAMES 
                logger logger_${ANDROID_ABI}
            HINTS
                ${LOGGER_LIBPATH}
             )
set(LOGGER_LIBRARIES ${LOGGER_LIBRARY})
message("Found logger libs: ${LOGGER_LIBRARIES}")
