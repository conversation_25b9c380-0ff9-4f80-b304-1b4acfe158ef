thirdparty_prefix_path(sonomsk)
find_path ( SONOMSK_INCLUDE_DIR
            NAMES
                msk_api.hpp
            HINTS
                ${SONOMSK_ROOT}/include
            )
set(SONOMSK_INCLUDE_DIRS ${SONOMSK_INCLUDE_DIR})
message("Found msk_api headers: ${SONOMSK_INCLUDE_DIRS}")

find_library ( SONOMSK_LIBRARY
            NAMES
                mskAlg
            HINTS
                ${SONOMSK_ROOT}/lib
             )
set(SONOMSK_LIBRARIES ${SONOMSK_LIBRARY})
message("Found mskAlg libs: ${SONOMSK_LIBRARIES}")




