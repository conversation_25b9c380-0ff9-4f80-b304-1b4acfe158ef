# - Find MuParser
# muParser is an extensible high performance math expression parser library written in C++
# http://muparser.sourceforge.net
#
# The module defines the following variables:
# MUPARSER_FOUND - True if Mu<PERSON>ars<PERSON> found.
# MUPARSER_INCLUDE_DIRS - where to find muParser.h, etc.
# MUPARSER_LIBRARIES - List of libraries when using MuParser.
#
#=============================================================================
# Copyright (C) 2005-2013 EDF-EADS-Phimeca
#
# Distributed under the OSI-approved BSD License (the "License");
# see accompanying file Copyright.txt for details.
#
# This software is distributed WITHOUT ANY WARRANTY; without even the
# implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
# See the License for more information.
#=============================================================================
# (To distributed this file outside of CMake, substitute the full
# License text for the above reference.)


thirdparty_prefix_path(muparser)

find_path ( MUPARSER_INCLUDE_DIR
            NAMES
                muParser.h
            HINTS
                ${MUPARSER_ROOT}/include
            )
set(MUPARSER_INCLUDE_DIRS ${MUPARSER_INCLUDE_DIR})
message("Found muparser headers: ${MUPARSER_INCLUDE_DIRS}")


find_library ( MUPARSER_LIBRARY
            NAMES
                muparser
            HINTS
                ${MUPARSER_LIBPATH}
             )
set(MUPARSER_LIBRARIES ${MUPARSER_LIBRARY})
message("Found muparser libs: ${MUPARSER_LIBRARIES}")

