#
# The module defines the following variables:
# JSONREADER_FOUND - True if jsonreader found.
# JSONREADER_INCLUDE_DIRS - where to find json.h, etc.
# JSONREADER_LIBRARIES - List of libraries when using jsonreader.
#

thirdparty_prefix_path(jsonreader)

find_path ( J<PERSON><PERSON><PERSON><PERSON>R_INCLUDE_DIR
            NAMES 
                json
            HINTS
                ${JSONREADER_ROOT}/include
             )
set(JSONREADER_INCLUDE_DIRS ${JSONREADER_INCLUDE_DIR})
message("Found jsonreader headers: ${JSONREADER_INCLUDE_DIRS}")

find_library ( JSONR<PERSON>DER_LIBRARY
            NAMES 
                jsonreader jsonreader_${ANDROID_ABI}
            HINTS
                ${JSONREADER_LIBPATH}
             )
set(JSONREADER_LIBRARIES ${JSONREADER_LIBRARY})
message("Found jsonreader libs: ${J<PERSON>NREADER_LIBRARIES}")
