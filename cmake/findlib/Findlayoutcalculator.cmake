#
# The module defines the following variables:
# LAYOUTCALCULATOR_FOUND - True if layoutcalculator found.
# LAYOUTCALCULATOR_INCLUDE_DIRS - where to find gridlayoutcalculator.h, etc.
# LAYOUTCALCULATOR_LIBRARIES - List of libraries when using layoutcalculator.
#

thirdparty_prefix_path(layoutcalculator)

find_path ( LAYOUTCA<PERSON>ULATOR_INCLUDE_DIR
            NAMES 
                gridlayoutcalculator.h
            HINTS
                ${LAYOUTCALCULATOR_ROOT}/include
             )
set(LAYOUTCALCULATOR_INCLUDE_DIRS ${LAYOUTCALCULATOR_INCLUDE_DIR})
message("Found layoutcalculator headers: ${LAYOUTCALCULATOR_INCLUDE_DIRS}")


find_library ( LAYOUTCALCULATOR_LIBRARY
            NAMES 
                layoutcalculator layoutcalculator_${ANDROID_ABI}
            HINTS
                ${LAYOUTCALCULATOR_LIBPATH}
             )
set(LAYOUTCALCULATOR_LIBRARIES ${LAYOUTCALCULATOR_LIBRARY})
message("Found layoutcalculator libs: ${LAYOUTCALCULATOR_LIBRARIES}")
