thirdparty_prefix_path(sonocarotidguide)

find_path ( SONOC<PERSON>OT<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIR
            NAMES
                sonocarotidguide64.h
            HINTS
                ${SONOCAROTIDGUIDE_ROOT}/include
            )
set(SONOCAROTIDGUIDE_INCLUDE_DIRS ${SONOCAROTIDGUIDE_INCLUDE_DIR})
message("Found carotidguide headers: ${SONOCAROTIDGUIDE_INCLUDE_DIRS}")


find_library ( SONOCAROTIDGUIDE_LIBRARY
            NAMES
                SonoCarotidGuide64
            HINTS
                ${SONOCAROTIDGUIDE_LIBPATH}
             )
set(SONOCAR<PERSON><PERSON><PERSON>UIDE_LIBRARIES ${SONOCAROTIDGUIDE_LIBRARY})
message("Found carotidguide libs: ${SONOCAROTIDGUIDE_LIBRARIES}")
