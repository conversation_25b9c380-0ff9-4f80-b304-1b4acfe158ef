#
# The module defines the following variables:
# AUTOFREEZE_FOUND - True if autofreeze found.
# AUTOFREEZE_INCLUDE_DIRS - where to find nzmrd.hpp, etc.
# AUTOFREEZE_LIBRARIES - List of libraries when using autofreeze.
#
thirdparty_prefix_path(AUTOFREEZE)

find_path ( AUTOF<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIR
            NAMES
                autofreeze.h
            HINTS
                ${AUTOFREEZE_ROOT}/include
            )
set(AUTOFREEZE_INCLUDE_DIRS ${AUTOFREEZE_INCLUDE_DIR})
message("Found autofreeze headers: ${AUTOFREEZE_INCLUDE_DIRS}")


find_library ( AUTOFREEZE_LIBRARY
            NAMES 
                autofreeze
            HINTS
                ${AUTOFREEZE_LIBPATH}
             )
set(AUTOFREEZE_LIBRARIES ${AUTOFREEZE_LIBRARY})
message("Found AUTOFREEZE libs: ${AUTOFREEZE_LIBRARIES}")
