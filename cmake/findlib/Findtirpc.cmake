#
# The module defines the following variables:
# TIRPC_FOUND - True if tirpc found.
# TIRPC_INCLUDE_DIRS - where to find rpc/des_crypt.h, etc.
# TIRPC_LIBRARIES - List of libraries when using tirpc.
#

set(TIRPC_ROOT ${THIRDPARTYLIB_PATH}/tirpc/)

find_path ( TIRPC_INCLUDE_DIR
            NAMES
                des_crypt.h
            HINTS
                ${TIRPC_ROOT}/include
            )
set(TIRPC_INCLUDE_DIRS ${TIRPC_INCLUDE_DIR})
message("Found tirpc headers: ${TIRPC_INCLUDE_DIRS}")


find_library ( TIRPC_LIBRARY
            NAMES 
                tirpc
            HINTS
                ${TIRPC_ROOT}/lib
             )

find_library ( CRYPT_LIBRARY
            NAMES
                crypt
            HINTS
                ${TIRPC_ROOT}/lib
             )
set(TIRPC_LIBRARIES ${TIRPC_LIBRARY} ${CRYPT_LIBRARY})
message("Found tirpc libs: ${TIRPC_LIBRARIES}")
