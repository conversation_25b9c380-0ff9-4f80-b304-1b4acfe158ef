#
# The module defines the following variables:
# DBG<PERSON><PERSON>P_FOUND - True if DBGH<PERSON><PERSON> found.
# DBGHELP_INCLUDE_DIRS - where to find DBGHELP.h, etc.
# DBGHELP_LIBRARIES - List of libraries.
#

thirdparty_prefix_path(DBGHELP)

find_path (DBGHELP_INCLUDE_DIR
            NAMES
                DBGHELP.h
            HINTS
                ${DBGHELP_ROOT}/include
    )
set(DBGHELP_INCLUDE_DIRS ${DBGHELP_INCLUDE_DIR})
message("Found DBGHELP headers: ${DBGHELP_INCLUDE_DIRS}")


find_library (DBGHELP_LIBRARY
            NAMES 
                DBGHELP
            HINTS
                ${DBGHELP_ROOT_DIR}/lib
                ${DBGHELP_ROOT}/lib
             )
set(DBGHELP_LIBRARIES ${DBGHELP_LIBRARY})
message("Found DBGHELP libs: ${DBGHELP_LIBRARIES}")
