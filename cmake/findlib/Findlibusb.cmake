#
# The module defines the following variables:
# LIBUSB_FOUND - True if libusb found.
# LIBUSB_INCLUDE_DIRS - where to find *.h, etc.
# LIBUSB_LIBRARIES - List of libraries when using libusb.
#

thirdparty_prefix_path(libusb)

find_path ( LIBUSB_INCLUDE_DIR
            NAMES
                libusb.h
            HINTS
                ${LIBUSB_ROOT}/include
            )
set(LIBUSB_INCLUDE_DIRS ${LIBUSB_INCLUDE_DIR})
message("Found libusb headers: ${LIBUSB_INCLUDE_DIRS}")


find_library ( LIBUSB_LIBRARY
            NAMES
                usb-1.0 usb1.0 libusb-1.0.lib
            HINTS
                ${LIBUSB_LIBPATH}
            )
set(LIBUSB_LIBRARIES ${LIBUSB_LIBRARY})
message("Found libusb libs: ${LIBUSB_LIBRARIES}")
