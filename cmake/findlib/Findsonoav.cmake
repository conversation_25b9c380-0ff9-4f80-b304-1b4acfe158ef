#
# The module defines the following variables:
# VA_INVIWO_FOUND - True if va found.
# VA_INCLUDE_DIRS - where to find va.h, etc.
# VA_LIBRARIES - List of libraries when using va.
#

thirdparty_prefix_path(sonoav)

find_path ( SONOAV_INCLUDE_DIR
            NAMES
            VasDet.h
            HINTS
	    ${SONOAV_ROOT}/include
            )
    set(SONOAV_INCLUDE_DIRS ${SONOAV_INCLUDE_DIR})
    message("Found sonoav headers: ${SONOAV_INCLUDE_DIRS}")

    find_library ( SONOAV_LIBRARY
            NAMES
                vasdet
            HINTS
	    ${SONOAV_LIBPATH}
             )
     set(SONOAV_LIBRARIES ${SONOAV_LIBRARY})
     message("Found sonoav libs: ${SONOAV_LIBRARIES}")

