#
# The module defines the following variables:
# ODS4D_FOUND - True if ODS4D found.
# ODS4D_INCLUDE_DIRS - where to find ods4dapi.h, etc.
# ODS4D_LIBRARIES - List of libraries when using ODS4D.
#

thirdparty_prefix_path(ods4d)

find_path ( ODS4D_INCLUDE_DIR
            NAMES
                ods4dapi.h
            HINTS
                ${ODS4D_ROOT}/include
            )
set(ODS4D_INCLUDE_DIRS ${ODS4D_INCLUDE_DIR})
message("Found ODS4D headers: ${ODS4D_INCLUDE_DIRS}")

find_library ( ODS4D_LIBRARY
            NAMES
                Ods4DApi
            HINTS
                ${ODS4D_LIBPATH}
             )
set(ODS4D_LIBRARIES ${ODS4D_LIBRARY})
message("Found ODS4D libs: ${ODS4D_LIBRARIES}")
