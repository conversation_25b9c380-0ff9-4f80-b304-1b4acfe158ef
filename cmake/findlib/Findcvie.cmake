#
# The module defines the following variables:
# CVIE_FOUND - True if CVI<PERSON> found.
# CVIE_INCLUDE_DIRS - where to cvie.h, etc.
# CVIE_LIBRARIES - List of libraries when using CVIE.
#

thirdparty_prefix_path(cvie)

find_path ( CVIE_INCLUDE_DIR
            NAMES
                cvie.h
            HINTS
                ${CVIE_ROOT}/include
            )
set(CVIE_INCLUDE_DIRS ${CVIE_INCLUDE_DIR})
message("Found CVIE headers: ${CVIE_INCLUDE_DIRS}")

find_library ( CVIE_LIBRARY
            NAMES 
                cvie64
            HINTS
                ${CVIE_LIBPATH}
             )
set(CVIE_LIBRARIES ${CVIE_LIBRARY})
message("Found CVIE libs: ${CVIE_LIBRARIES}")
