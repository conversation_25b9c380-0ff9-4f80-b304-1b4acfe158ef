#
# The module defines the following variables:
# INVIWO_FOUND - True if inviwo found.
# INVIWO_INCLUDE_DIRS - where to find chisonultrasoundimageprocess.h, etc.
# INVIWO_LIBRARIES - List of libraries when using chisonultrasoundimageprocess.
#

thirdparty_prefix_path(inviwo)

find_path ( INVIWO_INCLUDE_DIR
            NAMES
            chisonultrasoundimageprocess.h
            HINTS
                ${INVIWO_ROOT}/include
            )

set(INVIWO_INCLUDE_DIRS ${INVIWO_INCLUDE_DIR})
message("Found inviwo headers: ${INVIWO_INCLUDE_DIRS}")

find_library ( INVIWO_LIBRARY
            NAMES 
                chisonultrasoundimageprocess
            HINTS
                ${INVIWO_ROOT_DIR}/lib
                ${INVIWO_ROOT}/lib
             )
set(INVIWO_LIBRARIES ${INVIWO_LIBRARY})
message("Found inviwo libs: ${INVIWO_LIBRARIES}")
