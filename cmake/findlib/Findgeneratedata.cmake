
thirdparty_prefix_path(generatedata)

find_path ( GENERATEDATA_INCLUDE_DIR
            NAMES
                GenerateData.h
            HINTS
                ${GENERATEDATA_ROOT}/include
            )
set(GENERATEDATA_INCLUDE_DIRS ${GENERATEDATA_INCLUDE_DIR})

message("Found generatedata headers: ${GENERATEDATA_INCLUDE_DIRS}")

find_library ( GENERATEDATA_LIBRARY
            NAMES 
                generatedata
            HINTS
                ${GENERATEDATA_LIBPATH}
             )
set(GENERATEDATA_LIBRARIES ${GENERATEDATA_LIBRARY})
message("Found generatedata libs: ${GENERATEDATA_LIBRARIES}")

