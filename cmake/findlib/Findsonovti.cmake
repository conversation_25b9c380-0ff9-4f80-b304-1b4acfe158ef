thirdparty_prefix_path(sonovti)

find_path ( SONOVTI_INCLUDE_DIR
            NAMES
                sonovti.h
            HINTS
	        ${SONOVTI_ROOT}/include
            )
set(SONOVTI_INCLUDE_DIRS ${SONOVTI_INCLUDE_DIR})
message("Found sonovti headers: ${SONOVTI_INCLUDE_DIRS}")

find_library ( SONOVTI_LIBRARY
            NAMES
                Measure_rtvti
            HINTS
	        ${SONOVTI_LIBPATH}
             )
set(SONOVTI_LIBRARIES ${SONOVTI_LIBRARY})
message("Found sonovti libs: ${SONOVTI_LIBRARIES}")

