thirdparty_prefix_path(sonothyroid)
find_path ( SONOTH<PERSON><PERSON>ID_INCLUDE_DIR
            NAMES
                sonothyroid.h
            HINTS
	        ${SONOTHYROID_ROOT}/include
            )
set(SONOTHYROID_INCLUDE_DIRS ${SONOTHYROID_INCLUDE_DIR})
message("Found sonothyroid headers: ${SONOTHYROID_INCLUDE_DIRS}")

find_path ( SONOTHYROIDTRACE_INCLUDE_DIR
            NAMES
                jrsg.h
            HINTS
                ${SONOTHYROID_ROOT}/include
            )
set(SONOTHYROIDTRACE_INCLUDE_DIRS ${SONOTHYROIDTRACE_INCLUDE_DIR})
message("Found sonothyroid trace headers: ${SONOTHYROIDTRACE_INCLUDE_DIRS}")

find_library ( SONOTHYROID_LIBRARY
            NAMES 
                thyroidClass
            HINTS
       	        ${SONOTHYROID_ROOT}/lib
             )
set(SONOTHYROID_LIBRARIES ${SONOTHYROID_LIBRARY})
message("Found sonothyroid libs: ${SON<PERSON>HYROID_LIBRARIES}")

find_library ( SONOTH<PERSON>RO<PERSON>TRACE_LIBRARY
            NAMES
                jrsg
            HINTS
                ${SONOTHYROID_ROOT}/lib
             )
set(SONOTHYROIDTRACE_LIBRARIES ${SONOTHYROIDTRACE_LIBRARY})
message("Found sonothyroid trace libs: ${SONOTHYROIDTRACE_LIBRARIES}")


