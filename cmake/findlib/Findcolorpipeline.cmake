#
# The module defines the following variables:
# COLORPIPELINE_FOUND - True if colorpipeline found.
# COLORPIPELINE_INCLUDE_DIRS - where to find iuspipeline.h, etc.
# COLORPIPELINE_LIBRARIES - List of libraries when using chisonultrasoundimageprocess.
#

thirdparty_prefix_path(colorpipeline)

find_path ( COLORPIPELINE_INCLUDE_DIR
            NAMES
                iuspipeline.h
            HINTS
                ${COLORPIPELINE_ROOT}/include
            )
set(COLORPIPELINE_INCLUDE_DIRS ${COLORPIPELINE_INCLUDE_DIR})
message("Found colorpipeline headers: ${COLORPIPELINE_INCLUDE_DIRS}")

find_library ( COL<PERSON><PERSON><PERSON><PERSON>INE_LIBRARY
            NAMES 
                uspipeline
            HINTS
                ${COLORPIPELINE_ROOT_DIR}/lib
                ${COLORPIPELINE_ROOT}/lib
             )
set(COLORPIPELINE_LIBRARIES ${COLORPIPELINE_LIBRARY})
message("Found colorpipeline libs: ${COLORPIPELINE_LIBRARIES}")
