#
# The module defines the following variables:
# IMGNETCLIENT_FOUND - True if imgnetclient found.
# IMGNETCLIENT_INCLUDE_DIRS - where to find imgnetclient.h, etc.
# IMGNETCLIENT_LIBRARIES - List of libraries when using imgnetclient.
#

thirdparty_prefix_path(imgnetclient)

find_path ( IMGNETCLIENT_INCLUDE_DIR
            NAMES
                imgnetclient.h
            HINTS
                ${IMGNETCLIENT_ROOT}/include
            )

set(IMGNETCLIENT_INCLUDE_DIRS ${IMGNETCLIENT_INCLUDE_DIR})
message("Found imgnetclient headers: ${IMGNETCLIENT_INCLUDE_DIRS}")

find_library ( IMGNETCLIENT_LIBRARY
            NAMES
                imgnetclient
            HINTS
                ${IMGNETCLIENT_ROOT}/lib
             )
set(IMGNETCLIENT_LIBRARIES ${IMGNETCLIENT_LIBRARY})
message("Found imgnetclient libs: ${IMGNETCLIENT_LIBRARIES}")
