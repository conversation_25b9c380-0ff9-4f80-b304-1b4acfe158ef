# option4us(var,value,help_text)   <var> can also be used as macro in code.
# 图像功能
option4us(USE_PREPROCESS            ON  "use preprocess")
option4us(USE_CVIE                  OFF "use cvie filter to process image before colormap, note: this didn't check runtime gstreamer cvie requirement, you must ensure runtime has cvie and iimage must set to ON")
option4us(USE_IIMAGE                OFF "use iimage filter to process image before colormap, note: this didn't check runtime gstreamer iimage requirement, you must ensure runtime has iimage")
option4us(USE_FREEM                 OFF "use FreeM" )
option4us(USE_PANORAMIC             OFF "enable panoramic" )
option4us(USE_4D                    OFF "use 4D" )
option4us(USE_FREEHAND3D            OFF "use Free Hand 3D" )
option4us(USE_LINE_XCONSTRAST       ON  "use line data x contrast")
option4us(USE_PW_SOUND              ON  "play pw sound by soft")
option4us(USE_AUTOFREEZE            OFF "use auto freeze")
option4us(USE_AUTONEEDLEANGLE       OFF "use auto needle angle")
option4us(USE_DOPPLERADJUST         OFF "use doppler auto adjust")
option4us(USE_XBF_GenerateData      OFF "XBF get data194 data195 data66")
option4us(USE_ELEMENTDETECT         OFF "use element test")
option4us(USE_USCONTROLENGINE       OFF "Use this tools in preset mode and use this to generate blockdata.")
option4us(USE_WALLFILTER            OFF "Use this tools in preset mode and use this to generate wallfilter blockdata.")
option4us(USE_PW_PROCESS_1024       ON "Use PW Process Call Back Buffer Lenth 1024.")
option4us(USE_TGC_12                OFF "use 12 seg tgc")
option4us(USE_TGC_10                OFF "use 10 seg tgc")
option4us(USE_HIGHDENSITY_INT       ON  "HighDensity use type int")

# 应用功能
option4us(USE_ADMINVIEW             ON  "admin view. use ON as default" )
option4us(USE_SIGNATURE             OFF "Use Handwritten signature")
option4us(USE_PWTRACE               OFF "PWTRACE for pwtrace lib. use OFF as default" )
option4us(USE_AUTOCYSTIC            OFF "use auto-cystic lib for breast and thyroid. use OFF as default" )
option4us(USE_AUTOBLADDER           OFF "use auto-bladder lib. use OFF as default" )
option4us(USE_IMT                   OFF "IMT for imt lib. use OFF as default" )
option4us(USE_AUTODIAPH             OFF "sono diaph")
option4us(USE_VA                    OFF "use VA.")
option4us(USE_SONOAV                OFF "use SONOAV.")
option4us(USE_SONOTHYROID           OFF "use sonothyroid")
option4us(USE_SONOOB                OFF "use SonoOB")
option4us(USE_SONOCARDIAC           OFF "use SonoCardiac")
option4us(USE_RTIMT                 OFF "use realtime IMT measurement")
option4us(USE_SONONERVE             OFF "use SonoNerve")
option4us(USE_SONOMSK               OFF "use SonoMSK")
option4us(USE_SONOCAROTIDGUIDE      OFF "Use  Carotid Guide.")
option4us(USE_AUTOEF                OFF "Use  AutoEF.")
option4us(USE_AAADETECTION          OFF "Use AAADETECTION.")
option4us(USE_SONOVESSEL            OFF "Use  SonoVessel.")
option4us(USE_SONOVF                OFF "Use  SONOVF.")

# 设备功能
option4us(USE_KEYBOARD              OFF "Support device keyboard" )
option4us(USE_MOTOR                 ON  "use motor" )
option4us(USE_LPCDRV                OFF "LPC driver for battery control. use ON as default" )
option4us(USE_BATTERY_COUNT_2       OFF "Battery count control. use OFF as default,means 1 battery. ON means 2 battery" )
option4us(USE_USBVIDEOPRINT         OFF "USBVIDEOPRINT for usb video printer. use OFF as default" )
option4us(USE_IMGVERSION            OFF "get kernel image version")
option4us(USE_VIRTUAL_KEYBOARD      OFF "use virtual keyboard")
option4us(USE_BT                    OFF "use Bluetooth.")
option4us(USE_IMGNETCLIENT          OFF "use Imagenetclient.")
option4us(USE_FAN_ALARM             OFF "Check the fan speed function.")
option4us(USE_SIMULATEORIENTATION   ON  "screen orientation changed by shortcut")
option4us(USE_ULTRAREMOTE           OFF "use ultraremote")
option4us(USE_AJECPLDUPDATE         OFF "use ajecpld update")
option4us(USE_RDMA                  OFF "Use  RDMA.")
option4us(USE_ALSA                  OFF "play pw sound on unix")
option4us(USE_QT_AUDIO              ON  "qt pw sound player")
option4us(USE_TARGET_PALM           OFF "Use handheld ultrasound probe, FPGA and data format.")
option4us(USE_USB_IO                OFF "Use libusb.")
option4us(USE_PINYINIME             ON "chinese pinyin input method. use ON as default" )
option4us(USE_FINGERPRINT           OFF "Fingerprint identification. use ON as default" )
option4us(USE_OPENFINGERPRINT       OFF "openfinugerprint identification. use ON as default" )
option4us(USE_POWERKEY              OFF "Shut down key block.")

# 调试功能
option4us(USE_DEBUG                 OFF "use Debug mode")
option4us(USE_GOOGLETEST            OFF "Use google test.")
option4us(USE_UNITTEST              OFF "use unit test")
option4us(USE_ASAN                  OFF "ASan is a memory error detector.")
option4us(USE_TSAN                  OFF "TSan is a thread error detector.")
option4us(USE_GRAPHICS_DEBUG        OFF "graphics view will draw some assit items")
option4us(USE_KB_SHORTCUT           ON  "Support keyboard system shortcut" )
option4us(USE_RUN_MODE_VIRTUAL      ON  "run virtual mode" )
option4us(USE_PARA_DEBUG            OFF "USE_PARA_DEBUG is on, application will show keyboard dialog and parameter dialog." )
option4us(USE_STRICTMODE            OFF "Use strict mode to keep the program logic is strict correct!!" )
option4us(USE_PROBE_PORTS_ORDERED   OFF "probe ports ordered and correspond with probe selection ui")
option4us(USE_POWERBUTTONMonitor    OFF "use power button monitor to listener powerkey clicked, such as ebit and grapetablet")

# 其他功能
option4us(USE_OPENGLES              OFF "Use opengl es.")
option4us(USE_OPENGL                ON  "Use opengl.")
option4us(USE_FFMPEG                ON  "Use ffmpeg for avi transfer")
option4us(USE_QTWEBENGINE           OFF "Use  QT WebEngine lib.")
option4us(USE_P9API                 OFF "enable P9 API")
option4us(USE_TIRPC                 OFF "include des crypt for ubuntu 20.04")
