# =============================================================================
# Platform Detection and Basic Definitions
# =============================================================================

# Operating System Detection
if(WIN32)
    set(BUILD_SYS "windows")
    add_definitions(-DSYS_WINDOWS)
elseif(APPLE)
    set(BUILD_SYS "apple")
    add_definitions(-DSYS_APPLE)
elseif(ANDROID)
    set(BUILD_SYS "android")
    add_definitions(-DSYS_ANDROID)
elseif(UNIX)
    set(BUILD_SYS "unix")
    add_definitions(-DSYS_UNIX)
endif()

# CPU Architecture Detection
if(BUILD_TAR STREQUAL "x86")
    add_definitions(-DTAR_X86)
elseif(BUILD_TAR STREQUAL "ios")
    add_definitions(-DTAR_IOS)
elseif(BUILD_TAR STREQUAL "aarch")
    add_definitions(-DTAR_AARCH)
elseif(BUILD_TAR STREQUAL "arm")
    add_definitions(-DTAR_ARM)
endif()

# Bitness Detection
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(BUILD_ARC "64bit")
    add_definitions(-DARC_64BIT)
else()
    set(BUILD_ARC "32bit")
    add_definitions(-DARC_64BIT)
endif()

# =============================================================================
# Third-party Library Configuration
# =============================================================================

set(BUILD_SYS_TAR_ARC_NAME ${BUILD_SYS}_${BUILD_TAR}_${BUILD_ARC})
set(THIRDPARTYLIB_PATH ${PROJECT_SOURCE_DIR}/src/thirdparty/${BUILD_SYS_TAR_ARC_NAME})
set(CMAKE_FIND_ROOT_PATH ${CMAKE_FIND_ROOT_PATH} ${THIRDPARTYLIB_PATH})

# Platform-specific third-party configurations
if(APPLE)
    # iOS platform uses Qt5, requires mock for qwebframe/qwebengine
    set(QT5MOCKAPI_ROOT ${THIRDPARTYLIB_PATH}/qt5mockapi/)
    find_package(qt5mockapi REQUIRED)
endif()

# Library naming conventions
if(MSVC)
    add_definitions(-DPRETTY_FUNCTION=__FUNCTION__)
    set(THIRDPARTYLIB_NAME "bin")
    set(LIB_REFIX "")
    set(LIB_POSTFIX "$<$<CONFIG:Debug>:d>")
    set(LIB_STATIC_EXT ".lib")
else()
    add_definitions(-DPRETTY_FUNCTION=__PRETTY_FUNCTION__)
    set(THIRDPARTYLIB_NAME "lib")
    set(LIB_REFIX "lib")
    set(LIB_POSTFIX "")
    set(LIB_STATIC_EXT ".a")
endif()

# =============================================================================
# Compiler and Linker Settings
# =============================================================================

if(MSVC)
    # Character set and diagnostics
    add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
    
    # VS2017 and later specific options
    string(REGEX MATCH "^19[1-9][0-9]" ISVS2017LATER "${MSVC_VERSION}")
    if(ISVS2017LATER)
        add_compile_options("$<$<C_COMPILER_ID:MSVC>:/diagnostics:caret>")
        add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/diagnostics:caret>")
    endif()

    # Warning levels and flags
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3 /WX /wd4996 /wd4661 /wd4172")

    # Debug and Release configurations
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
    set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")
    set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /DEBUG /OPT:REF /OPT:ICF")

    # Response file settings for handling long command lines
    set(CMAKE_C_USE_RESPONSE_FILE_FOR_INCLUDES 1)
    set(CMAKE_CXX_USE_RESPONSE_FILE_FOR_INCLUDES 1)
    set(CMAKE_C_USE_RESPONSE_FILE_FOR_OBJECTS 1)
    set(CMAKE_CXX_USE_RESPONSE_FILE_FOR_OBJECTS 1)
    set(CMAKE_C_USE_RESPONSE_FILE_FOR_LIBRARIES 1)
    set(CMAKE_CXX_USE_RESPONSE_FILE_FOR_LIBRARIES 1)
    set(CMAKE_C_RESPONSE_FILE_LINK_FLAG "@")
    set(CMAKE_CXX_RESPONSE_FILE_LINK_FLAG "@")
    set(CMAKE_OBJECT_PATH_MAX 260)
    
    # Debug helpers
    add_compile_options(/showIncludes)
else()
    # Code coverage options
    option(USE_GCOV "Use gcov for code coverage" OFF)
    if(USE_GCOV)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fprofile-arcs -ftest-coverage")
    endif()

    # Compiler-specific warning configurations
    set(nowarning_flags_cpp "")
    if(${CMAKE_CXX_COMPILER_ID} STREQUAL "Clang") 
        set(warning_flags "${warning_flags} -Qunused-arguments")
        set(nowarning_flags_cpp "${nowarning_flags_cpp} -Wno-inconsistent-missing-override -Wno-unused-value -Wno-switch -Wno-constant-conversion")
    endif()

    set(nowarning_flags "-Wno-unused-result -Wno-unused")

    # Common compiler flags
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${warning_flags} -fPIC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${warning_flags} ${nowarning_flags_cpp} -fPIC")

    # Sanitizer options
    if(USE_ASAN)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address,undefined -fno-omit-frame-pointer -g")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address,undefined")
    endif()
    if(USE_TSAN)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=thread,undefined -fno-omit-frame-pointer -g")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=thread,undefined")
    endif()

    # Enable ccache if available
    find_program(CCACHE_FOUND ccache)
    if(CCACHE_FOUND)
        set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
        set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)
    endif()
endif()
