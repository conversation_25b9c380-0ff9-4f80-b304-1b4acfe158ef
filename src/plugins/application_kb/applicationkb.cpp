#include "applicationkb.h"
#include <QDebug>
#include "mainmodule.h"
#include <QApplication>
#include "setting.h"
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QDir>
#include "applogger.h"
#include "resource.h"
#include "logmanager.h"
#include "propertyconfigurator.h"

ApplicationKB::ApplicationKB()
    : m_MainModule(NULL)
{
}

ApplicationKB::~ApplicationKB()
{

}

int ApplicationKB::run(int argc, char **argv)
{
    Q_UNUSED(argc);
    Q_UNUSED(argv);

    if(AppLogger::fileLog())
    {
        Log4Qt::PropertyConfigurator::configure(Resource::loggerConf);
        Log4Qt::LogManager::setHandleQtMessages(true);
        Log4Qt::Logger * log = Log4Qt::Logger::rootLogger();
        log->info("                                                                               ");
        log->info("   @@@@@@@@@@        @@@@       @@@      @@@       @@@@@@@@     @@@      @@@   ");
        log->info("   @@@@@@@@@@@@     @@@@@@      @@@@@    @@@     @@@@@@@@@@@   @@@@      @@@   ");
        log->info("   @@@@     @@@    @@@ @@@      @@@@@@   @@@    @@@@           @@@@      @@@   ");
        log->info("   @@@@    @@@@    @@@  @@@     @@@ @@@  @@@   @@@@            @@@@      @@@   ");
        log->info("   @@@@@@@@@@@    @@@   @@@@    @@@  @@@ @@@   @@@@   @@@@@@   @@@@      @@@   ");
        log->info("   @@@@@@        @@@@@@@@@@@    @@@   @@@@@@   @@@@      @@@    @@@      @@@   ");
        log->info("   @@@@          @@@     @@@@   @@@    @@@@@    @@@@@    @@@    @@@@   @@@@@   ");
        log->info("   @@@@         @@@@      @@@@  @@@     @@@@      @@@@@@@@@@     @@@@@@@@@@    ");
        log->info("                                                                               ");
    }
    qApp->setApplicationName(Setting::instance().defaults().appTitle());

    connect(qApp, SIGNAL(aboutToQuit()), this, SLOT(onApplicationQuit()));

    m_MainModule = new MainModule();
    m_MainModule->run();

    return 0;
}

/**
 * @brief onApplicationQuit qt 释放主窗口资源必须在app.exec()退出之前执行
 * 否则会出错，这里保证退出前释放资源
 */
void ApplicationKB::onApplicationQuit()
{
    delete m_MainModule;
}

#if (QT_VERSION < QT_VERSION_CHECK(5,0,0))
QT_BEGIN_NAMESPACE

Q_EXPORT_PLUGIN2(application_kb, ApplicationKB)

QT_END_NAMESPACE
#endif
