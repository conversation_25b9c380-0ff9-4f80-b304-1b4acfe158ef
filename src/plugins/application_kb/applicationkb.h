#ifndef APPLICATIONKB_H
#define APPLICATIONKB_H
#include "application_kb_global.h"

#include <QObject>
#include "iapplication.h"
#include "applicationbase.h"

class MainModule;
class APPLICATION_KBSHARED_EXPORT  ApplicationKB : public ApplicationBase
{
    Q_OBJECT
    Q_INTERFACES(IApplication)
#if (QT_VERSION >= QT_VERSION_CHECK(5,0,0))
    Q_PLUGIN_METADATA(IID IAPPLICATION_IID)
#endif
public:
    ApplicationKB();
    ~ApplicationKB();
    int run(int argc, char **argv);
private slots:
    void onApplicationQuit();
private:
    MainModule* m_MainModule;
};

#endif // APPLICATIONKB_H
