
#include "qcustomplotadaptor.h"

#include <algorithm>

#include <QString>
#include <QHBoxLayout>
#include <QDebug>

QCustomPlotAdaptor::QCustomPlotAdaptor()
    :m_impl(this){

    //m_impl.addGraph();
    m_impl.setAttribute(Qt::WA_TransparentForMouseEvents);

    QHBoxLayout *layout=new QHBoxLayout();

    layout->addWidget(&m_impl);

    this->setLayout(layout);

    m_impl.adjustSize();
    //支持多胞胎
    m_impl.legend->setPaddingLeft(-5);
    m_impl.legend->setMarginRight(-10);
    m_impl.legend->setMinimumSize(0,0);
    //生长曲线取消图标中的legend，改为在checkbox中显示图标,完善rebase后代码
    m_impl.legend->setVisible(false);

//    m_impl.legend->setPositionStyle(QCPLegend::psBottomRight);
}

void QCustomPlotAdaptor::setXYAxisLable(
                const QString& xLable,const QString& yLable){

    m_impl.xAxis->setLabel(xLable);
    m_impl.yAxis->setLabel(yLable);

    m_impl.replot();
}

void QCustomPlotAdaptor::setXYY2AxisLable(const QString& xLable, const QString& yLable, const QString& y2Lable)
{
    m_impl.xAxis->setLabel(xLable);
    m_impl.yAxis->setLabel(yLable);
    m_impl.yAxis2->setLabel(y2Lable);

    m_impl.yAxis->setLabelColor(QColor(0, 100, 0));
    m_impl.yAxis2->setLabelColor(QColor(255, 127, 0));


     m_impl.xAxis->setGrid(true);
     m_impl.yAxis->setGrid(true);

//     m_impl.xAxis->setSubGrid(true);
//     m_impl.yAxis->setSubGrid(true);

     m_impl.xAxis->setTickStep(1);//设置x轴刻度步长
     m_impl.yAxis->setTickStep(1);
     m_impl.yAxis2->setTickStep(1);
     m_impl.xAxis->setAutoTickStep(false);//设置x轴自动刻度步长
     m_impl.yAxis->setAutoTickStep(false);//设置y轴自动刻度步长
     m_impl.yAxis2->setAutoTickStep(false);//设置y轴自动刻度步长

    m_impl.replot();
}


void QCustomPlotAdaptor::setXYData(
        QVector<double> xData,QVector<double> yData, QColor color, bool KeyAxis){

//    const double minX=*std::min_element(xData.begin(),xData.end());
//          double maxX=*std::max_element(xData.begin(),xData.end());
//          double minY=*std::min_element(yData.begin(),yData.end());
//          double maxY=*std::max_element(yData.begin(),yData.end());

//    const double xRange=maxX-minX;
//          double yRange=maxY-minY;

//    m_impl.graph(0)->setData(xData,yData);
          int num = m_impl.graphCount();

          {
              if(KeyAxis)
              {
                  m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);
                  m_impl.graph(num)->setData(xData,yData);
              }
              else
              {
                  m_impl.addGraph(m_impl.yAxis, m_impl.xAxis);
                  m_impl.graph(num)->setData(yData,xData);
              }

          }
//          m_impl.graph(num)->setData(xData,yData);
          //[BUG:23037]OB报告页内添加两个生长曲线，生长曲线图不清晰
          m_impl.graph(num)->setPen(QPen(color, 2));

//    m_impl.xAxis->setRange(minX-xRange*0.2,maxX+xRange*0.2);
//    m_impl.yAxis->setRange(minY,maxY+yRange*0.1);

          m_impl.replot();
}

void QCustomPlotAdaptor::setXYY2Data(QVector<double> xData,QVector<double> yData, QColor color, bool isyAxis2)
{
    int num = m_impl.graphCount();
    {
        if(isyAxis2)
        {
            m_impl.addGraph(m_impl.xAxis, m_impl.yAxis2);
            m_impl.graph(num)->setData(xData,yData);
        }
        else
        {
            m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);
            m_impl.graph(num)->setData(xData,yData);
        }
    }
    //曲线上显示测量点为实心圆圈
    m_impl.graph(num)->setScatterStyle(QCP::ssDisc);
    //[BUG:23037]OB报告页内添加两个生长曲线，生长曲线图不清晰
    m_impl.graph(num)->setPen(QPen(color, 2));
    m_impl.replot();
}

//支持多胞胎; 增加多次检查后查看历史记录
void QCustomPlotAdaptor::setXYRange(double minX, double maxX, double minY, double maxY, double xerror, double yerror)
{
    const double xRange=maxX-minX;
          double yRange=maxY-minY;
    m_impl.xAxis->setRange(minX - xRange*xerror,maxX+xRange*xerror);
    //m_impl.xAxis->setSubTickCount(2);
    m_impl.yAxis->setRange(minY - yRange*yerror,maxY+yRange*yerror);
    //m_impl.yAxis->setSubTickCount(2);
}

//void void QCustomPlotAdaptor::setXYTickCount()
//{
//    m_impl.xAxis->setSubTickCount()
//    xAxis->ticker()->setTickCount(6);
//}

void QCustomPlotAdaptor::setXYY2Range(double minX, double maxX, double minY, double maxY, double minY2, double maxY2, double xerror, double yerror)
{
    m_impl.xAxis->setRange(minX, maxX);
    m_impl.yAxis->setRange(minY, maxY);
    m_impl.yAxis2->setRange(minY2, maxY2);
}

void QCustomPlotAdaptor::addXYData(double x, double y, int index, bool current)
{
    int i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);
    QPen pen;
    if(current)
    {//[BUG:15337]调整生长曲线中标志,使当前测量的标志相较之前更明显一些
        m_impl.graph(i)->setScatterSize(8);
        pen.setWidthF(1.5);
    }
    else
    {//[BUG:15336]现在刷新不再刷新公式
        m_impl.graph(i)->setScatterSize(5);
        pen.setWidthF(1.0);
    }
    switch (index)
    {
    case 0:
        m_impl.graph(i)->setName("A");
        pen.setColor(Qt::blue);
        m_impl.graph(i)->setScatterStyle(QCP::ssPlus);
        break;
    case 1:
        m_impl.graph(i)->setName("B");
        pen.setColor("#9400D3");
        m_impl.graph(i)->setScatterStyle(QCP::ssCross);
        break;
    case 2:
        m_impl.graph(i)->setName("C");
        pen.setColor("#CD661D");
        m_impl.graph(i)->setScatterStyle(QCP::ssStar);
        break;
    case 3:
        m_impl.graph(i)->setName("D");
        pen.setColor("#698B22");
        m_impl.graph(i)->setScatterStyle(QCP::ssPlusCircle);
        break;
    default:
        m_impl.graph(i)->setName("UNKNOW");
        m_impl.graph(i)->setScatterStyle(QCP::ssNone);
        break;
    }
    m_impl.graph(i)->setPen(pen);
    m_impl.graph(i)->addData(x, y);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    int legendIndex = m_impl.legend->itemCount() - 1;
    //防止legend中的item重复
    for(int j = 0; j < i; j++)
    {
        if(m_impl.graph(j)->name() == m_impl.graph(i)->name())
        {
            m_impl.legend->removeItem(legendIndex);
        }
    }
    m_impl.replot();
}

void QCustomPlotAdaptor::addSonoImtXYData(double x, double y, int index)
{
    int i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);
    QPen pen;

    switch (index)
    {
    case 0:
        m_impl.graph(i)->setScatterStyle(QCP::ssPlus);
        m_impl.graph(i)->setScatterSize(15);

        break;
    case 1:
        m_impl.graph(i)->setScatterStyle(QCP::ssCross);
        m_impl.graph(i)->setScatterSize(15);
        break;
    case 2:
        m_impl.graph(i)->setScatterStyle(QCP::ssStar);
        m_impl.graph(i)->setScatterSize(15);
        break;
    default:
        m_impl.graph(i)->setScatterStyle(QCP::ssNone);
        break;
    }
    m_impl.graph(i)->addData(x, y);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    int legendIndex = m_impl.legend->itemCount() - 1;
//    //防止legend中的item重复
//    for(int j = 0; j < i; j++)
//    {
//        if(m_impl.graph(j)->name() == m_impl.graph(i)->name())
//        {
//            m_impl.legend->removeItem(legendIndex);
//        }
//    }

    m_impl.legend->removeItem(legendIndex);
    m_impl.replot();
}

void QCustomPlotAdaptor::setXYData(double xData, QList<QList<float> > yData)
{
    int i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);
    float miny = yData[0][0], maxy = yData[0][0];
          for(int i = 0; i < yData.size(); i++)
          {
              for(int j = 0; j < yData.at(i).size(); j++)
              {
                  miny = miny > yData[i][j] ? yData[i][j] : miny;
                  maxy = maxy > yData[i][j] ? maxy : yData[i][j];
              }
          }
    const float minY = miny;
          float maxY = maxy;

    float step = (maxY - minY) / 20.0;


    QVector<double> x, y;
    for(float i = minY; i < maxY; i += step)
    {
        y << i;
        x << xData;
    }
    m_impl.graph(i)->setData(x, y);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    m_impl.graph(i)->setScatterStyle(QCP::ssDot);
    m_impl.legend->clearItems();
    m_impl.replot();
}

void QCustomPlotAdaptor::setSonoImtXYData(QVector<double> xData, QVector<double> yMaxData, QVector<double> yMinData, QVector<double> yMeanData)
{
    m_impl.legend->setVisible(true);
    //m_impl.legend->setFont(QFont("Helvetica", 9));
    //m_impl.legend->setTextColor(Qt::red);

    //m_impl.legend->setIconSize(50, 20);
    m_impl.legend->setBorderPen(Qt::NoPen);

    int i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);

    m_impl.graph(i)->setData(xData, yMaxData);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    m_impl.graph(i)->setName(tr("Max"));
    m_impl.graph(i)->setScatterStyle(QCP::ssPlus);
    m_impl.graph(i)->setScatterSize(15);

    i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);

    m_impl.graph(i)->setData(xData, yMinData);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    m_impl.graph(i)->setScatterStyle(QCP::ssCross);
    m_impl.graph(i)->setName(tr("Min"));
    m_impl.graph(i)->setScatterSize(15);

    i = m_impl.graphCount();
    m_impl.addGraph(m_impl.xAxis, m_impl.yAxis);

    m_impl.graph(i)->setData(xData, yMeanData);
    m_impl.graph(i)->setLineStyle(QCPGraph::lsNone);
    m_impl.graph(i)->setScatterStyle(QCP::ssStar);
    m_impl.graph(i)->setName(tr("Mean"));
    m_impl.graph(i)->setScatterSize(15);

    //m_impl.plotLayout()->setRowStretchFactor(1, 0.001);
    //m_impl.legend->clearItems();
    //customPlot->plotLayout()->addElement(1 , 0, customPlot->legend);
    //customPlot->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignBottom|Qt::AlignRight);
    //m_impl.axisRect().in    asetInsetAlignment(0,Qt::AlignTop|Qt::AlignRight);

    m_impl.replot();
}

void QCustomPlotAdaptor::setTitle(const QString &title)
{
    m_impl.setTitle(title);
    m_impl.replot();
}

bool QCustomPlotAdaptor::savePng(const QString &fileName, int width, int height, double scale, int quality)
{
    return m_impl.savePng(fileName, width, height, scale, quality);
}

void QCustomPlotAdaptor::clearGraphs()
{
    m_impl.clearGraphs();
}

void QCustomPlotAdaptor::setXYTick(int xLength, int y)
{
    m_impl.xAxis->setAutoTickStep(true);
    m_impl.xAxis->setAutoTickCount(xLength);
    m_impl.yAxis->setAutoTickStep(true);
    m_impl.yAxis->setAutoTickCount(y);
    m_impl.replot();
}

void QCustomPlotAdaptor::setXYTicks(bool xshow, bool yshow)
{
    m_impl.xAxis->setTicks(xshow);
    m_impl.yAxis->setTicks(yshow);
}

void QCustomPlotAdaptor::setXTicks(bool show)
{
    m_impl.xAxis->setTicks(show);
}

void QCustomPlotAdaptor::setYTicks(bool show)
{
    m_impl.yAxis->setTicks(show);
}

void QCustomPlotAdaptor::setXYTickLabels(bool xshow, bool yshow)
{
    m_impl.xAxis->setTickLabels(xshow);
    m_impl.yAxis->setTickLabels(yshow);
}

void QCustomPlotAdaptor::setXTickVector(QVector<double> v)
{
    m_impl.xAxis->setAutoTicks(false);
    m_impl.xAxis->setAutoTickLabels(false);
    m_impl.xAxis->setTicks(true);
    m_impl.xAxis->setTickVector(v);
    m_impl.xAxis->setTickLabels(true);
    QVector<QString> vs;
    for(int i = 0; i < v.size(); i++)
    {
        vs.append(QString::number(v.at(i)));
    }
    m_impl.xAxis->setTickVectorLabels(vs);
}

void QCustomPlotAdaptor::setYTickVector(QVector<double> v)
{
    m_impl.yAxis->setAutoTicks(false);
    m_impl.yAxis->setAutoTickLabels(false);
    m_impl.yAxis->setTicks(true);
    m_impl.yAxis->setTickVector(v);
    m_impl.yAxis->setTickLabels(true);
    QVector<QString> vs;
    for(int i = 0; i < v.size(); i++)
    {
        vs.append(QString::number(v.at(i)));
    }
    m_impl.yAxis->setTickVectorLabels(vs);
}


void QCustomPlotAdaptor::setPlotBackground(QColor color, QColor x, QColor y)
{
    m_impl.setColor(color);
    m_impl.xAxis->setBasePen(QPen(x));
    m_impl.yAxis->setBasePen(QPen(y));
    m_impl.xAxis->setTickPen(QPen(x));
    m_impl.yAxis->setTickPen(QPen(y));
    m_impl.xAxis->setSubTickPen(QPen(x));
    m_impl.yAxis->setSubTickPen(QPen(y));
    m_impl.xAxis->setTickLabelColor(x);
    m_impl.yAxis->setTickLabelColor(y);
}

void QCustomPlotAdaptor::setXYGrid(bool flag)
{
    m_impl.xAxis->setGrid(flag);
    m_impl.yAxis->setGrid(flag);
}

void QCustomPlotAdaptor::setMargin(int left, int right, int top, int bottom)
{
    m_impl.setAutoMargin(false);
    m_impl.setMargin(left, right, top, bottom);
}

void QCustomPlotAdaptor::setupFullAxesBox()
{
    m_impl.setupFullAxesBox();//四边安装轴并显示
}

void QCustomPlotAdaptor::setXYTickStep(double xStep, double yStep)
{
    m_impl.xAxis->setAutoTickStep(false);
    m_impl.xAxis->setTickStep(xStep);
    //m_impl.xAxis->setTickLabelPadding()
    m_impl.yAxis->setAutoTickStep(false);
    m_impl.yAxis->setTickStep(yStep);
}

void QCustomPlotAdaptor::setAxisCount(int count)
{
    //如果显示三个坐标轴的话，x,y,y2
    if(count == 3)
    {
        m_impl.yAxis2->setVisible(true);
    }
    else
    {
        m_impl.yAxis2->setVisible(false);
    }
}

void QCustomPlotAdaptor::setXYSubTickCount(int x, int y)
{
    m_impl.xAxis->setSubTickCount(x);
    m_impl.yAxis->setSubTickCount(y);
    m_impl.xAxis->setSubGrid(true);
    m_impl.yAxis->setSubGrid(true);
}

void QCustomPlotAdaptor::setXYAutoSubTicks(bool x, bool y)
{
    m_impl.xAxis->setAutoSubTicks(x);
    m_impl.yAxis->setAutoSubTicks(y);
}

void QCustomPlotAdaptor::setXY2TickLabels(bool xshow, bool yshow)
{
    m_impl.xAxis2->setTickLabels(xshow);
    m_impl.yAxis2->setTickLabels(yshow);
}

void QCustomPlotAdaptor::setXYTickLength(int inside, int outside)
{
    m_impl.xAxis->setTickLength(inside, outside);
    m_impl.yAxis->setTickLength(inside, outside);
    m_impl.xAxis2->setTickLength(inside, outside);
    m_impl.yAxis2->setTickLength(inside, outside);
    int subInside = inside/2;
    int subOutside = outside/2;
    m_impl.xAxis->setSubTickLength(subInside, subOutside);
    m_impl.yAxis->setSubTickLength(subInside, subOutside);
    m_impl.xAxis2->setSubTickLength(subInside, subOutside);
    m_impl.yAxis2->setSubTickLength(subInside, subOutside);
}

void QCustomPlotAdaptor::replot()
{
    m_impl.replot();
}
