/*
 * =====================================================================================
 *
 *       Filename:  qcustomplotplugin.h
 *
 *    Description:  
 *
 *        Version:  1.0
 *        Created:  2013年03月21日 08时28分30秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  WangXinyu (), <EMAIL>
 *   Organization:  
 *
 * =====================================================================================
 */

#ifndef Q_CUSTOM_PLOT_PLUGIN_H
#define Q_CUSTOM_PLOT_PLUGIN_H
#include "plot_plugin_global.h"

#include <QObject>

#include "iplotfactory.h"

class PLOT_PLUGINSHARED_EXPORT  QCustomPlotPlugin:public QObject ,public IPlotFactory
{
    Q_OBJECT
    Q_INTERFACES(IPlotFactory)
#if (QT_VERSION >= QT_VERSION_CHECK(5,0,0))
    Q_PLUGIN_METADATA(IID IPLOTFACTORY_IID)
#endif
public:
    virtual ~QCustomPlotPlugin (){}

    virtual IPlot* createIPlot();

private:
};

#endif /* end of include guard */
