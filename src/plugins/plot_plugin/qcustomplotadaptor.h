/***************************************************************************
 **                                                                        **
 **  QCustomPlotAdaptor
 **                                                                        **
 **  This program is free software: you can redistribute it and/or modify  **
 **  it under the terms of the GNU General Public License as published by  **
 **  the Free Software Foundation, either version 3 of the License, or     **
 **  (at your option) any later version.                                   **
 **                                                                        **
 **  This program is distributed in the hope that it will be useful,       **
 **  but WITHOUT ANY WARRANTY; without even the implied warranty of        **
 **  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         **
 **  GNU General Public License for more details.                          **
 **                                                                        **
 **  You should have received a copy of the GNU General Public License     **
 **  along with this program.  If not, see http://www.gnu.org/licenses/.   **
 **                                                                        **
 ****************************************************************************
 ** license exception: software which load this plugin
 ** can be under any license ,with no warranty
 **/

#ifndef Q_CUSTOM_PLOT_ADAPTOR_H
#define Q_CUSTOM_PLOT_ADAPTOR_H
#include "plot_plugin_global.h"





#include "iplot.h"
#include "qcustomplot.h"

class PLOT_PLUGINSHARED_EXPORT  QCustomPlotAdaptor :public IPlot
{
        Q_OBJECT
            
    public:
        QCustomPlotAdaptor();

        virtual void setXYAxisLable(
                const QString& xLable,const QString& yLable);

        virtual void setXYY2AxisLable(const QString& xLable, const QString& yLable, const QString& y2Lable);
        virtual void setXYData(QVector<double> xData,QVector<double> yData, QColor color, bool KeyAxis);
        virtual void setSonoImtXYData(QVector<double> xData, QVector<double> yMaxData, QVector<double> yMinData, QVector<double> yMeanData);
        virtual void setXYY2Data(QVector<double> xData,QVector<double> yData, QColor color, bool keyAxis);
        virtual void setXYRange(double minX, double maxX, double minY, double maxY, double xerror, double yerror);
        virtual void setXYY2Range(double minX, double maxX, double minY, double maxY, double minY2, double maxY2, double xerror, double yerror);
        // 支持多胞胎；增加多次检查后查看历史记录
        virtual void addXYData(double x, double y, int index, bool current);
        virtual void addSonoImtXYData(double x, double y, int index);
        virtual void setXYData(double xData, QList< QList<float> > yData);
        virtual void setTitle(const QString &title);
        virtual bool savePng(const QString &fileName, int width, int height, double scale, int quality);

        virtual void clearGraphs();
        virtual void setXYTick(int x, int y);
        virtual void setXYTicks(bool xshow, bool yshow);
        virtual void setXTicks(bool show);
        virtual void setYTicks(bool show);
        virtual void setXYTickLabels(bool xshow, bool yshow);
        virtual void setXTickVector(QVector<double> v);
        virtual void setYTickVector(QVector<double> v);
        virtual void setPlotBackground(QColor color, QColor x, QColor y);
        virtual void setXYGrid(bool flag);
        virtual void setMargin(int left, int right, int top, int bottom);
        virtual void setupFullAxesBox();
        virtual void setXYTickStep(double xStep, double yStep);
        virtual void setAxisCount(int count);

        virtual void setXYSubTickCount(int x, int y);
        virtual void setXYAutoSubTicks(bool x, bool y);
        virtual void setXY2TickLabels(bool xshow, bool yshow);
        virtual void setXYTickLength(int inside, int outside=0);

        virtual ~QCustomPlotAdaptor(){};

public slots:
        virtual void replot();
private:
//        void setXYRange(double minX, double maxX, double minY, double maxY);


    private:

        QCustomPlot m_impl;

};



#endif /* end of include guard: Q_CUSTOM_PLOT_ADAPTOR_H */
