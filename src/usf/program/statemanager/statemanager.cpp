#include "statemanager.h"
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
#include "qscxml.h"
#elif (QT_VERSION >= QT_VERSION_CHECK(5, 14, 2)) && (!defined SYS_APPLE)
#include "scxmlstatemachine.h"
#endif
#include "abstractstate.h"
#include "appsetting.h"
#include "infostruct.h"
#include "istatemanager.h"
#include "logger.h"
#include "qmessagebox.h"
#include "setting.h"
#include "statecontainer.h"
#include "stateeventnames.h"
#include <QDebug>
#include <QFile>
#include <QTextStream>

LOG4QT_DECLARE_STATIC_LOGGER(log, StateManager)

StateManager& StateManager::getInstance()
{
    static StateManager _instance;
    return _instance;
}

StateManager::StateManager(QObject* parent)
    : QObject(parent)
    , m_ScxmlLoader(NULL)
    , m_Paused(false)
    , m_IsZoomOn(false)
    , m_IsRealTimeSaving(false)
    , m_isCurrentProbePhaseArray(false)
    , m_IsNonPhasedProbeCW(false)
    , m_IsZoomEnabled(false)
    , m_IsProbeFourD(false)
    , m_InStatic3DState(false)
    , m_IsTrapezoidalMode(false)
    , m_IsBCImages(false)
    , m_isCanUndoFreeze(true)
    , m_IsSupportM(true)
    , m_IsInPanZoom(false)
    , m_PreStateName("")
    , m_IsDeviceOpen(false)
    , m_IsSupportSonoNeedle(false)
    , m_IsProbeSupportMVI(false)
    , m_ForceRTMeasurement(false)
    , m_StateManagerFacade(NULL)
{
    m_StateContainer = new StateContainer(this);
    initMVISwitchableMode();
}

StateManager::~StateManager()
{
    if (m_ScxmlLoader != NULL)
    {
        delete m_ScxmlLoader;
    }
}

void StateManager::append(AbstractState* value)
{
    m_StateContainer->append(value);
}

AbstractState* StateManager::state(const QString& name) const
{
    return m_StateContainer->state(name);
}

AbstractState* StateManager::currentState() const
{
    return m_StateContainer->currentState();
}

const QStringList& StateManager::filter(bool filter2) const
{
    return filter2 ? m_Filter2 : m_Filter;
    ;
}

void StateManager::setFilter(const QStringList filter, bool setFilter2)
{
    setFilter2 ? m_Filter2 = filter : m_Filter = filter;
    qDebug() << "setFilter" << m_Filter << "setFilter2" << m_Filter2;
}

bool StateManager::isPaused() const
{
    return m_Paused;
}

void StateManager::setPaused(bool value)
{
    m_Paused = value;
}

void StateManager::loadStateMachine(IStateManager* stateManagerFacade, const QString& fileName)
{
    if (m_ScxmlLoader) // Only enable loading the states at app startup
    {
        return;
    }
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    m_ScxmlLoader = QScxml::load(fileName);
    m_ScxmlLoader->registerObject(stateManagerFacade, "stateManager" /*, true*/);

    // Debugging
    QStringList events = m_ScxmlLoader->knownEventNames();

    log()->debug("[%1]", events.join(","));
#elif (QT_VERSION >= QT_VERSION_CHECK(5, 14, 2))
    m_ScxmlLoader = ScxmlStateMachine::fromFile(fileName);
    m_ScxmlLoader->registerObject(stateManagerFacade);
#endif

    disConnectSignalsWithStateFacade();
    m_StateManagerFacade = stateManagerFacade;
    connectSignalsWithStateFacade();
}

void StateManager::generateEventNamesFile(const QHash<QString, QString>& shortcuts)
{
    QStringList events = m_ScxmlLoader->knownEventNames();
    std::sort(events.begin(), events.end());

    {
        QFile data("stateeventnames.h");
        if (data.open(QFile::WriteOnly | QFile::Truncate))
        {
            QTextStream out(&data);

            out << "#ifndef STATEEVENTNAMES_H\n";
            out << "#define STATEEVENTNAMES_H\n";
            out << "#include \"commonunit_global.h\"\n";
            out << "\n";
            out << "#include <QString>\n";
            out << "#include <QStringList>\n";
            out << "\n";
            out << "//Shortcut:\"Qt::CTRL + Qt::ALT + Qt::Key_D\" toggle system shortcuts.\n";
            out << "class COMMONUNITSHARED_EXPORT StateEventNames\n";
            out << "{\n";
            out << "public:\n";

            out << "//State event names\n";
            foreach (QString str, events)
            {
                QString shortcut;
                if (shortcuts.contains(str))
                {
                    shortcut = shortcuts.value(str);
                }
                QStringList strs = str.split('.');
                //                str.remove('.');
                out << QString("    static QString %1; //%2 shortcut:%3\n").arg(strs.last()).arg(str).arg(shortcut);
            }

            out << "//State names\n";
            foreach (const QString& name, m_StateContainer->stateNames())
            {
                out << QString("    static QString %1State;\n").arg(name);
            }

            out << "//State filters\n";
            out << QString("    static QStringList DialogStateFilter();\n");
            out << QString("    static QString NothingStateFilter();\n");
            out << "};\n";
            out << "\n";
            out << "#endif // STATEEVENTNAMES_H\n";
        }
    }

    {
        QFile data("stateeventnames.cpp");
        if (data.open(QFile::WriteOnly | QFile::Truncate))
        {
            QTextStream out(&data);

            out << "#include \"stateeventnames.h\"\n";
            out << "\n";
            out << "//State event names\n";
            foreach (QString str, events)
            {
                QStringList strs = str.split('.');
                //                QString event = str;
                //                str.remove('.');

                out << QString("QString StateEventNames::%1 = \"%2\";\n").arg(strs.last()).arg(str);
            }

            out << "//State names\n";
            foreach (const QString& name, m_StateContainer->stateNames())
            {
                out << QString("QString StateEventNames::%1State = \"%1\";\n").arg(name);
            }

            out << "//State filters\n";
            out << QString("QStringList StateEventNames::DialogStateFilter()\n");
            out << QString("{\n");
            out << QString("    return QStringList() << StateEventNames::Exit << StateEventNames::ShutDown;\n");
            out << QString("}\n");

            out << QString("QString StateEventNames::NothingStateFilter()\n");
            out << QString("{\n");
            out << QString("    return \"NothingFilter\";\n");
            out << QString("}\n");

            out << "\n";
        }
    }
}

QStringList StateManager::knownEventNames() const
{
    if (m_ScxmlLoader != NULL)
    {
        return m_ScxmlLoader->knownEventNames();
    }
    return QStringList();
}

void StateManager::setCurrentState(const QString& curState)
{
    if (curState == QString())
    {
        return;
    }

    if (m_StateContainer->currentState() != NULL)
    {
        m_PreStateName = m_StateContainer->currentState()->name();
    }
    log()->debug("StateManager %1 \"%2\"", __FUNCTION__, curState);

    m_StateContainer->setCurrentState(curState);

    setCurrentActiveMode(curState);
    /*
     * 添加mode参数是为了确定多布局下的target
     * 如果mode参数不为空，则target应给为mode参数所指向的target
     * 如果mode参数为空， 则target为上次imageing的target
     * 在layout布局下需要切换到pre mode时，需要经过LayoutActiveToPre来间接切换过去。但是在切换到
     *          LayoutActiveToPre状态时，mode传回的是event
     */
    m_StateContainer->setCurrentModePara(m_ScxmlLoader->currentMode());
}

QString StateManager::currentMode() const
{
    return m_ScxmlLoader->currentMode();
}

void StateManager::setCurrentMode(const QString& value)
{
    m_ScxmlLoader->setCurrentMode(value);
}

bool StateManager::isZoomSelect()
{
    // 2023-06-09 Write by AlexWang 优化状态机中多B下进入局部放大预备状态时切换激活区域的判定逻辑
    return m_IsZoomSelect;
}

bool StateManager::isRegionZoomOn()
{
    // 2023-06-13 Write by AlexWang [bug:65066] 返回RegionZoomOn参数值
    return m_IsRegionZoomOn;
}

QString StateManager::currentEvent() const
{
    return m_currentEvent;
}

QString StateManager::postEventName() const
{
    return m_PostEventName;
}

void StateManager::setCurrentEvent(const QString& value)
{
    m_currentEvent = value;
}

QString StateManager::topHistory() const
{
    if (!m_stateHistory.isEmpty())
    {
        return m_stateHistory.top();
    }
    else
    {
        return QString();
    }
}

void StateManager::onEntry()
{
    m_StateContainer->onEntry();
}

void StateManager::onExit()
{
    m_StateContainer->onExit();
}

void StateManager::runTool(const QString& value)
{
    log()->debug("StateManager runTool %1", value);
    emit runningTool(value);
}

void StateManager::runTool(const QString& value, bool add)
{
    log()->debug("StateManager runTool %1 %2", value, add);
    emit runningTool(value, add);
}

void StateManager::runTool(const QString& value, const QString& arg)
{
    log()->debug("StateManager runTool %1 %2", value, arg);
    emit runningTool(value, arg);
}

void StateManager::stopTool(const QString& value)
{
    log()->debug("StateManager stopTool %1", value);
    emit stopedTool(value);
}

void StateManager::start()
{
    if (m_ScxmlLoader != NULL)
    {
        m_ScxmlLoader->start();
    }
}

void StateManager::stop()
{
    if (m_ScxmlLoader != NULL)
    {
        m_ScxmlLoader->stop();
    }
    disConnectSignalsWithStateFacade();
}

void StateManager::postEvent(const QString& eventName)
{
    log()->info(QString("Post Event:%1 m_Filter:(%2) m_Preventor:%3 m_Paused:%4")
                    .arg(eventName)
                    .arg(m_Filter.join(" "))
                    .arg(m_Preventor.toString())
                    .arg(m_Paused ? "true" : "false"));

    if (m_Paused)
    {
        return;
    }

    if ((m_Filter.isEmpty() || m_Filter.contains(eventName)) &&
        (m_Filter2.isEmpty() || m_Filter2.contains(eventName)) && !m_Preventor.contains(eventName))
    {
        if (m_ScxmlLoader != NULL)
        {
            m_PostEventName = eventName;
            emit beforePostEvent(eventName);
            m_ScxmlLoader->postNamedEvent(eventName);
        }
    }
}

void StateManager::testDebug(const QString& value)
{
    qDebug() << value;
}

void StateManager::setCurrentActiveB(const QString& value)
{
    m_currentActiveB = value;
}

QString StateManager::currentActiveB() const
{
    return m_currentActiveB;
}
/*
 *有两个地方调用次方法
 * 1、在xml状态机状态切换的setCurrenState的时候，这时候状态发生变化，需要设置当前激活widget的mode
 * 2、在layout布局模式下，由于当前激活widget的mode已经无法由状态机的状态切换来确定，故需要设置
 * 3、m_CurrentActiveMode始终返回当前激活widget的mode
 */
void StateManager::setCurrentActiveMode(const QString& value)
{
    m_CurrentActiveMode = value;
}

QString StateManager::currentActiveMode() const
{
    return m_CurrentActiveMode;
}

bool StateManager::isZoomEnabled() const
{
    return m_IsZoomEnabled;
}

void StateManager::setIsZoomEnabled(int isZoomEnabled)
{
    m_IsZoomEnabled = isZoomEnabled;
}

void StateManager::changeBCImages(bool value)
{
    m_IsBCImages = value;
}

PreventorEvent StateManager::preventor() const
{
    return m_Preventor;
}

void StateManager::setPreventor(const PreventorEvent& value)
{
    m_Preventor = value;
}

void StateManager::addPreventor(const QString& value, PreventorEvent::PreventorType type)
{
    m_Preventor.addPreventorRef(value, type);
}

void StateManager::removePreventor(const QString& value, PreventorEvent::PreventorType type)
{
    m_Preventor.removePreventorRef(value, type);
}

bool StateManager::isZoomOn() const
{
    return m_IsZoomOn;
}

void StateManager::setZoomOn(bool value)
{
    m_IsZoomOn = value;
}

void StateManager::setZoomSelect(bool value)
{
    // 2023-06-09 Write by AlexWang 优化状态机中多B下进入局部放大预备状态时切换激活区域的判定逻辑
    m_IsZoomSelect = value;
}

void StateManager::setRegionZoomOn(bool value)
{
    // 2023-06-13 Write by AlexWang [bug:65066] 更新RegionZoomOn参数值
    m_IsRegionZoomOn = value;
}

bool StateManager::isRealTimeSaving() const
{
    return m_IsRealTimeSaving;
}

void StateManager::setRealTimeSaving(bool value)
{
    m_IsRealTimeSaving = value;
}

void StateManager::setProbePhaseArray(bool value)
{
    m_isCurrentProbePhaseArray = value;
}

void StateManager::setProbeLinear(bool value)
{
    m_IsCurrentProbeLinear = value;
}

void StateManager::setProbeIsSupportSonoNeedle(bool value)
{
    m_IsSupportSonoNeedle = value;
}

void StateManager::setProbeIsSupportMVI(bool value)
{
    m_IsProbeSupportMVI = value;
}

void StateManager::setIsNonPhasedProbeCW(bool value)
{
    m_IsNonPhasedProbeCW = value;
}

void StateManager::toggleCurrentStateToFreqFalseState()
{
    if (currentStateIsFreqTrue())
    {
        postEvent(StateEventNames::Update);
    }
}

void StateManager::setProbeFourD(bool value)
{
    m_IsProbeFourD = value;
}

bool StateManager::isBCImages() const
{
    return m_IsBCImages;
}

bool StateManager::isCanUndoFreezeWithoutMsg() const
{
    return isCanUndoFreeze(false);
}

bool StateManager::isCanUndoFreeze(bool showMsg) const
{
    if (!m_isCanUndoFreeze && showMsg)
    {
        emit showNoProbeWarning();
    }
    return m_isCanUndoFreeze;
}

bool StateManager::isSupportM() const
{
    return m_IsSupportM;
}

void StateManager::setSupportM(const bool supportM)
{
    m_IsSupportM = supportM;
}

void StateManager::setSupportLR(const bool supportLR)
{
    m_IsSupportLR = supportLR;
}

int StateManager::layout() const
{
    return m_Layout;
}

void StateManager::setLayout(int layout)
{
    m_Layout = layout;
}

int StateManager::activeIndex() const
{
    return m_ActiveIndex;
}

void StateManager::setActiveIndex(int index)
{
    m_ActiveIndex = index;
}

void StateManager::setIsInPanZoom(bool isInPanZoom)
{
    m_IsInPanZoom = isInPanZoom;
}

bool StateManager::isInPanZoom() const
{
    return m_IsInPanZoom;
}

QString StateManager::getPreStateName()
{
    return m_PreStateName;
}

bool StateManager::currentStateIsFreqTrue() const
{
    if (currentState() != NULL)
    {
        return !currentState()->name().contains("PW");
    }
    else
    {
        return false;
    }
}

void StateManager::setIsCanUndoFreeze(bool isCanUndoFreeze)
{
    m_isCanUndoFreeze = isCanUndoFreeze;
}

void StateManager::pushStateHistory(const QString& value)
{
    m_stateHistory.push(value);
}

QString StateManager::popStateHistory()
{
    if (!m_stateHistory.isEmpty())
    {
        return m_stateHistory.pop();
    }
    else
    {
        return QString();
    }
}

bool StateManager::isSupportRealTimeMeasure() const
{
    if (m_CurvedPanoramicIsRuning == 1)
    {
        return false;
    }
    else
    {
        return Setting::instance().defaults().isSupportRealTimeMeasure() || m_ForceRTMeasurement;
    }
}

bool StateManager::isSupportRealTimeCommentAndArrow() const
{
    return m_CurvedPanoramicIsRuning == 1 ? false : Setting::instance().defaults().isSupportRealTimeCommentAndArrow();
}

bool StateManager::isSupportRealTimeBodyMark() const
{
    return m_CurvedPanoramicIsRuning == 1 ? false : Setting::instance().defaults().isSupportRealTimeBodyMark();
}

QString StateManager::currentFreezeStatus() const
{
    QString freezeSet = Setting::instance().defaults().imageFreezeStatus();
    QString filter = QString().append("FuncKey.").append(freezeSet);
    if (m_Preventor.contains(filter) || (m_CurvedPanoramicIsRuning == 1 && freezeSet == StateEventNames::Measurement))
    {
        freezeSet = StateEventNames::CinePlayState;
    }
    return freezeSet;
}

bool StateManager::isSupportColorM() const
{
    return m_isCurrentProbePhaseArray && AppSetting::isColorMEnable();
}

bool StateManager::isSupportTDI() const
{
    return m_isCurrentProbePhaseArray && AppSetting::isFunctionEnabled(LicenseItemKey::KeyTDI);
}

bool StateManager::isSupportCW() const
{
    //临时修改:[BUG]60882:6MC和6E支持CW，但是打图没有频谱（放水里） 目前仅支持相控阵开启CW
    //    return /*(m_IsNonPhasedProbeCW || m_isCurrentProbePhaseArray)*/m_isCurrentProbePhaseArray &&
    //    AppSetting::isFunctionEnabled(LicenseItemKey::KeyCW);

    //调试代码,最终会把lisence加上
    return (m_IsNonPhasedProbeCW || m_isCurrentProbePhaseArray) && AppSetting::isFunctionEnabled(LicenseItemKey::KeyCW);
}

bool StateManager::isSupportCPADPD() const
{
    return AppSetting::isFunctionEnabled(LicenseItemKey::KeyPD);
}

bool StateManager::isSupportSonoNeedle() const
{
    return m_IsSupportSonoNeedle && AppSetting::isFunctionEnabled(LicenseItemKey::KeySonoNeedle);
}

bool StateManager::isSupportColor() const
{
    return AppSetting::isColor();
}

bool StateManager::isSupportPW() const
{
    return AppSetting::isPW();
}

bool StateManager::isSupportLR() const
{
    return m_IsSupportLR;
}

bool StateManager::isSupportElastography() const
{
    return AppSetting::isElastography();
}

bool StateManager::isSupportFreeM() const
{
    return m_isCurrentProbePhaseArray;
}

bool StateManager::isSupportFreehand3D() const
{
    return AppSetting::isFreehand3D();
}

bool StateManager::isSupportFourD() const
{
    // TODO 添加探头过滤和license控制
    return m_IsProbeFourD && AppSetting::isFourD();
}

bool StateManager::isInFourDState() const
{
    return currentState()->name().contains("FourD");
}

bool StateManager::isInStatic3DState() const
{
    return m_InStatic3DState;
}

bool StateManager::setInStatic3DState(bool value)
{
    m_InStatic3DState = value;
    return false;
}

bool StateManager::isSupportCurvedPanoramic() const
{
    return AppSetting::isCurvedPanoramicEnable();
}

bool StateManager::isSupportIntelligentDoppler() const
{
    return m_IsCurrentProbeLinear && AppSetting::isIntelligentDoppler();
}

bool StateManager::isSupportSonoThyroid() const
{
    if (!m_IsSupportSonothyroid && AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoThyroid))
        emit PromptInformation(tr("Please switch to B mode."));

    return m_IsSupportSonothyroid && AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoThyroid);
}

bool StateManager::isSupportSonoThyroidWithoutMessage() const
{
    return m_IsSupportSonothyroid && AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoThyroid);
}

void StateManager::setSupportSonoThyroid(bool b)
{
    m_IsSupportSonothyroid = b;
}

void StateManager::stressechoEn(bool b)
{
    m_StressEchoEn = b;
}

void StateManager::stressechoSupport(bool b)
{
    m_SupportStressEcho = b;
}

bool StateManager::isStressechoEn()
{
    return m_StressEchoEn;
}

bool StateManager::isSonoCardiacEn()
{
    return m_SonoCardiacEn;
}

bool StateManager::isSupportStressecho()
{
    return m_SupportStressEcho;
}

void StateManager::initMVISwitchableMode()
{
    m_mviSwitchableMode << "Imaging"
                        << "BMode"
                        << "OneLayout"
                        << "BCMode"
                        << "SonoNeedleMode"
                        << "BPDMode"
                        << "BDPDMode"
                        << "BTDIMode"
                        << "BMVIMode"
                        << "BCDPWMode"
                        << "BMVIDMode"
                        << "BMVIDPWMode"
                        << "BDPreMode"
                        << "BCDPreMode"
                        << "BPDDPreMode"
                        << "BDPDDPreMode"
                        << "BTDIDPreMode"
                        << "BPDDPWMode"
                        << "BDPDDPWMode"
                        << "BMPreMode"
                        << "BCMPreMode"
                        << "BMVIDPreMode"
                        << "BDPWMode"
                        << "EMode"
                        << "PanZoom"
                        << "Zoom"
                        << "ZoomOn"
                        << "SonoNerve"
                        << "SonoMSK";
}

void StateManager::connectSignalsWithStateFacade()
{
    if (m_StateManagerFacade != NULL)
    {
        connect(this, SIGNAL(stopedTool(QString)), m_StateManagerFacade, SIGNAL(stopedTool(QString)));
        connect(this, SIGNAL(runningTool(QString)), m_StateManagerFacade, SIGNAL(runningTool(QString)));
        connect(this, SIGNAL(runningTool(QString, bool)), m_StateManagerFacade, SIGNAL(runningTool(QString, bool)));
        connect(this, SIGNAL(runningTool(QString, QString)), m_StateManagerFacade,
                SIGNAL(runningTool(QString, QString)));
        connect(this, SIGNAL(beforePostEvent(QString)), m_StateManagerFacade, SIGNAL(beforePostEvent(QString)));
        connect(this, SIGNAL(showNoProbeWarning()), m_StateManagerFacade, SIGNAL(showNoProbeWarning()));
        connect(this, SIGNAL(PromptInformation(QString)), m_StateManagerFacade, SIGNAL(PromptInformation(QString)));
    }
}

void StateManager::disConnectSignalsWithStateFacade()
{
    if (m_StateManagerFacade != NULL)
    {
        disconnect(this, SIGNAL(stopedTool(QString)), m_StateManagerFacade, SIGNAL(stopedTool(QString)));
        disconnect(this, SIGNAL(runningTool(QString)), m_StateManagerFacade, SIGNAL(runningTool(QString)));
        disconnect(this, SIGNAL(runningTool(QString, bool)), m_StateManagerFacade, SIGNAL(runningTool(QString, bool)));
        disconnect(this, SIGNAL(runningTool(QString, QString)), m_StateManagerFacade,
                   SIGNAL(runningTool(QString, QString)));
        disconnect(this, SIGNAL(beforePostEvent(QString)), m_StateManagerFacade, SIGNAL(beforePostEvent(QString)));
        disconnect(this, SIGNAL(showNoProbeWarning()), m_StateManagerFacade, SIGNAL(showNoProbeWarning()));
        disconnect(this, SIGNAL(PromptInformation(QString)), m_StateManagerFacade, SIGNAL(PromptInformation(QString)));
    }
}

void StateManager::setSonoCardiacEn(bool SonoCardiacEn)
{
    m_SonoCardiacEn = SonoCardiacEn;
}

bool StateManager::needEnd() const
{
    return m_NeedEnd;
}

void StateManager::setNeedEnd(bool NeedEnd)
{
    m_NeedEnd = NeedEnd;
}

bool StateManager::curvedPanoramicIsCallBack() const
{
    return m_CurvedPanoramicIsCallBack;
}

void StateManager::setCurvedPanoramicIsCallBack(bool curvedPanoramicIsCallBack)
{
    m_CurvedPanoramicIsCallBack = curvedPanoramicIsCallBack;
}

bool StateManager::isSupportAutoEF() const
{
    return m_IsSupportAutoEF;
}

void StateManager::setIsSupportAutoEF(bool value)
{
    m_IsSupportAutoEF = value;
}

bool StateManager::isSupportAutoEFSingle() const
{
    return m_IsSupportAutoEFSingle;
}

void StateManager::setIsSupportAutoEFSingle(bool value)
{
    m_IsSupportAutoEFSingle = value;
}

void StateManager::setIsCloseSonoAV(bool value)
{
    m_IsCloseSonoAV = value;
}

void StateManager::setIsNeedShowElementCheckDlg(bool value)
{
    m_IsNeedShowElementCheckDlg = value;
}

bool StateManager::IsNeedShowElementCheckDlg()
{
    return m_IsNeedShowElementCheckDlg;
}

int StateManager::curvedPanoramicLayout() const
{
    return m_CurvedPanoramicLayout;
}

void StateManager::setCurvedPanoramicLayout(int curvedPanoramicLayout)
{
    m_CurvedPanoramicLayout = curvedPanoramicLayout;
}

bool StateManager::curvedPanoramicIsRuning() const
{
    return m_CurvedPanoramicIsRuning;
}

void StateManager::setCurvedPanoramicIsRuning(bool curvedPanoramicIsRuning)
{
    m_CurvedPanoramicIsRuning = curvedPanoramicIsRuning;
}

bool StateManager::isSupportMVI() const
{
    return m_IsProbeSupportMVI && AppSetting::isFunctionEnabled(LicenseItemKey::KeyMVI);
}

bool StateManager::isMVISwitchable() const
{
    bool isSwithable = false;

    if (isSupportMVI())
    {
        if (currentState() != nullptr)
        {
            qDebug() << PRETTY_FUNCTION << "[LUKAS]" << currentState()->name();
            if ((m_Layout == Layout_1x2) || (m_Layout == Layout_2x2))
            {
                isSwithable = false;
            }
            else
            {
                isSwithable = m_mviSwitchableMode.contains(currentState()->name());
            }
        }
        else
        {
            isSwithable = true;
        }
    }

    qDebug() << PRETTY_FUNCTION << "[LUKAS] switchable" << isSwithable;

    return isSwithable;
}

bool StateManager::isSupportSonoNerve() const
{
    return m_IsSupportSonoNerve && AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoNerve);
}

bool StateManager::isCloseSonoNerve() const
{
    return m_IsCloseSonoNerve;
}

bool StateManager::isSupportSonoMSK() const
{
    return m_IsSupportSonoMSK /*&& AppSetting::isFunctionEnabled(LicenseItemKey::LicenseKeyType::KeySonoNerve)*/;
}

bool StateManager::isCloseSonoMSK() const
{
    return m_IsCloseSonoMSK;
}

int StateManager::modeID() const
{
    return m_ModeID;
}

void StateManager::setModeID(int value)
{
    m_ModeID = value;
}

void StateManager::setForceRTMeasurement(bool value)
{
    m_ForceRTMeasurement = value;
}

void StateManager::setIsSupportSonoNerve(bool value)
{
    m_IsSupportSonoNerve = value;
}

void StateManager::setIsCloseSonoNerve(bool value)
{
    m_IsCloseSonoNerve = value;
}

void StateManager::setIsSupportSonoMSK(bool value)
{
    m_IsSupportSonoMSK = value;
}

void StateManager::setIsCloseSonoMSK(bool value)
{
    m_IsCloseSonoMSK = value;
}

bool StateManager::isDeviceOpen() const
{
    return m_IsDeviceOpen;
}

void StateManager::setIsDeviceOpen(bool isOpen)
{
    m_IsDeviceOpen = isOpen;
}

bool StateManager::isSupportUnFreezeInProbe()
{
    return AppSetting::isP9Series();
}

bool StateManager::isCloseSonoAV() const
{
    return m_IsCloseSonoAV;
}
