#include "bcdgeometrycontroller.h"
#include "bfadfreqparameter.h"
#include "bfcoordtransform.h"
#include "bfdepthparameters.h"
#include "bffocusparameters.h"
#include "bfpnames.h"
#include "bfscanwidthparameter.h"
#include "controltablesender.h"
#include "infostruct.h"
#include "isonoparameters.h"
#include "modelconfig.h"
#include "muxpnames.h"
#include "parameter.h"
#include "physicalgeometrytransform.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "probephysicalgeometry.h"
#include "realcompare.h"
#include "util.h"
#include <QtMath>
// #include "logger.h"

static const int COLOR_ALIGN_LINE = 4;

// LOG4QT_DECLARE_STATIC_LOGGER(log, BCDGeometryController)

BCDGeometryController::BCDGeometryController(ISonoParameters* parameters, QObject* parent, bool considerZoomOn)
    : PhysicalGeometryController(parameters, parent, considerZoomOn)
    , m_GeometryTransform(NULL)
    // TODO 以下边界参数需要根据不同机器控制边界
    , m_RoiTopBorder(1)
    , m_RoiMinWidthFactor(0.1f)
    , m_RoiMaxWidthFactor(0.9f)
    , m_RoiMinHeightFactor(0.1f)
    , m_RoiMaxHeightFactor(0.95f)
    , m_RoiEdgeLines(8)
    , m_GateMinHeightMM(1.0f)
    , m_GateMaxHeightMM(8.0f)
    , m_ROIPNames(new ROIMuxPNames())
    , m_DopGatePNames(new DopGateMuxPNames())
    , m_RoiStableTime(-1)
{
    m_ROIPNames->setSonoParameters(parameters);
    m_DopGatePNames->setSonoParameters(parameters);

    connect(m_ROIPNames, SIGNAL(stateChanged()), this, SLOT(resetParameters()));
    connect(m_DopGatePNames, SIGNAL(stateChanged()), this, SLOT(resetParameters()));
}

void BCDGeometryController::setRoiTopBorder(int value)
{
    m_RoiTopBorder = value;
}

void BCDGeometryController::setRoiMinWidthFactor(float value)
{
    m_RoiMinWidthFactor = value;
}

void BCDGeometryController::setRoiMaxWidthFactor(float value)
{
    m_RoiMaxWidthFactor = value;
}

void BCDGeometryController::setRoiMinHeightFactor(float value)
{
    m_RoiMinHeightFactor = value;
}

void BCDGeometryController::setRoiMaxHeightFactor(float value)
{
    m_RoiMaxHeightFactor = value;
}

void BCDGeometryController::setRoiEdgeLines(int value)
{
    m_RoiEdgeLines = value;
}

/**
 * @brief BCDGeometryController::setGateMinHeightMM
 * @todo 如果beamformerbase中的这个值与此变量初始值不一致，需要在beamformer中设置
 * @param value
 */
void BCDGeometryController::setGateMinHeightMM(float value)
{
    m_GateMinHeightMM = value;
}

/**
 * @brief BCDGeometryController::setGateMaxHeightMM
 * @todo 如果beamformerbase中的这个值与此变量初始值不一致，需要在beamformer中设置
 * @param value
 */
void BCDGeometryController::setGateMaxHeightMM(float value)
{
    m_GateMaxHeightMM = value;
}

void BCDGeometryController::calOriginalRegionAndCoordTransform(ProbeImageRegion& region, BFCoordTransform& ct) const
{
    const ProbeDataInfo& probe = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    ProbeParameters pParameters(
        probe, m_SonoParameters->pDV(BFPNames::StartDepthMMStr), 0.0f, m_SonoParameters->pBV(BFPNames::ZoomOnStr),
        m_SonoParameters->pIV(BFPNames::StartScanLineColorStr), m_SonoParameters->pIV(BFPNames::StopScanLineColorStr));

    ct = BFCoordTransform(probe, m_SonoParameters->pDV(BFPNames::StartDepthMMStr),
                          m_SonoParameters->pDV(BFPNames::PixelSizeMMStr), m_SonoParameters->pBV(BFPNames::ZoomOnStr),
                          m_SonoParameters->pIV(BFPNames::StartScanLineColorStr),
                          m_SonoParameters->pIV(BFPNames::StopScanLineColorStr), m_RevLineNum);

    region = ProbeImageRegion(
        m_SonoParameters->pIV(BFPNames::StartScanLineColorStr), m_SonoParameters->pIV(BFPNames::StopScanLineColorStr),
        m_SonoParameters->pDV(BFPNames::StartDepthMMStr), pParameters, m_SonoParameters->pDV(BFPNames::DepthMMStr),
        m_SonoParameters->pDV(BFPNames::PixelSizeMMStr));

    // 2025-05-09 Write by AlexWang [BUG:78659] 进入局部放大状态后，使用StartScanLineColor和StopScanLineColor
    if (m_SonoParameters->pBV(BFPNames::ZoomOnStr))
    {
        limitRegionToBImageSize(region, ct, m_SonoParameters->pIV(BFPNames::StartScanLineColorStr),
                                m_SonoParameters->pIV(BFPNames::StopScanLineColorStr));
    }
    else
    {
        limitRegionToBImageSize(region, ct, m_StartLine, m_StopLine);
    }
}

void BCDGeometryController::geometry2SonoParametersWithLineDensity()
{
    int midLine = m_SonoParameters->pIV(BFPNames::RoiMidLineStr);
    int halfLine = m_SonoParameters->pIV(BFPNames::RoiHalfLinesStr);
    double linespacing = m_SonoParameters->pDV(BFPNames::LineSpacingColorStr);
    m_SonoParameters->setPV(BFPNames::StartScanDisColorStr, linespacing * (midLine - halfLine));
    m_SonoParameters->setPV(BFPNames::StopScanDisColorStr, linespacing * (midLine + halfLine));
    m_SonoParameters->setPV(BFPNames::ScanDisDopplerStr, linespacing * m_SonoParameters->pIV(BFPNames::DScanLineStr));
}

void BCDGeometryController::getRoiGeometry(ProbePhysicalGeometry& g) const
{
    if (m_GeometryTransform != NULL)
    {
        g = m_GeometryTransform->geometry();
    }
}

void BCDGeometryController::setRoiGeometry(const ProbePhysicalGeometry& g) const
{
    if (m_GeometryTransform != NULL)
    {
        m_GeometryTransform->setGeometry(g);
    }
}

void BCDGeometryController::setRoiMinSize(const ProbePhysicalSize& value)
{
    if (m_GeometryTransform != NULL)
    {
        m_GeometryTransform->setMinSize(value);
    }
}

void BCDGeometryController::setRoiMaxSize(const ProbePhysicalSize& value)
{
    if (m_GeometryTransform != NULL)
    {
        m_GeometryTransform->setMaxSize(value);
    }
}

void BCDGeometryController::getGateGeometry(ProbePhysicalGeometry& g) const
{
    getRoiGeometry(g);
}

void BCDGeometryController::setGateGeometry(const ProbePhysicalGeometry& g) const
{
    setRoiGeometry(g);
}

void BCDGeometryController::setGateSize(const ProbePhysicalSize& size) const
{
    if (m_GeometryTransform != NULL)
    {
        m_GeometryTransform->setSize(size);
    }
}

void BCDGeometryController::setGateMinSize(const ProbePhysicalSize& value)
{
    setRoiMinSize(value);
}

void BCDGeometryController::setGateMaxSize(const ProbePhysicalSize& value)
{
    setRoiMaxSize(value);
}

qreal BCDGeometryController::gateRegionHeight() const
{
    return m_BaseTransform->region().height();
}

void BCDGeometryController::onResetRegionAndCoordTransform(const ProbeImageRegion& region, const BFCoordTransform& ct)
{
    m_BaseTransform->setCoordTransform(ct);

    const ProbeDataInfo& probe = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    BFScanWidthParameter sp(probe, m_SonoParameters->pMax(BFPNames::ScanWidthStr), isSupportAnyDensity(),
                            m_SonoParameters->pIV(BFPNames::C_RX_LNUMStr));

    ProbeImageRegion limitedRegion = region;
    limitedRegion.setLeft(qMax(sp.startLine() + m_RoiEdgeLines, region.left()));
    limitedRegion.setRight(qMin(sp.stopLine() - m_RoiEdgeLines, region.right()));

    if (m_SonoParameters->pBV(BFPNames::ColorMStr) || m_SonoParameters->pBV(BFPNames::IsRoiVisibleStr))
    {
        // color m
        // 需要控制ROIMidLine在整个Region中可以移动，所以要控制ROI的limitedregion在region之外的RoiHalfLines，并且考虑
        // clor m 时，下发的ColorStartLine ColorStopLine为ROIMidLine的 +/- 4范围
        limitedRegion.setLeft(limitedRegion.left() -
                              m_SonoParameters->pIV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfLines)) + COLOR_ALIGN_LINE);
        limitedRegion.setRight(limitedRegion.right() +
                               m_SonoParameters->pIV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfLines)) -
                               COLOR_ALIGN_LINE);
    }
    else if (m_SonoParameters->pIV(BFPNames::SyncModeStr) == Sync_CD ||
             (m_SonoParameters->pIV(BFPNames::SyncModeStr) == Sync_C &&
              (m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) ||
               m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr))))
    {
        // CD mode,控制limitregion的left and right 整除4，这样可以统一DScanLine和ROI的范围统一
        int left = limitedRegion.left();
        int right = limitedRegion.right();
        adjustedLeftAndRight(left, right);

        limitedRegion.setLeft(left);
        limitedRegion.setRight(right);
    }

    ProbePhysicalPointF topMidPt = ProbePhysicalPointF(limitedRegion.center().x(), limitedRegion.top());
    QPointF topBorderMidPixelPt = ct.convertPhysicsToLogic(topMidPt);
    // ROI顶部限制，都应在 proberegion顶部中心边界的基础上 + 限制值
    topBorderMidPixelPt.ry() += m_RoiTopBorder;
    //设置ROI的上边界，FPGA 是以像素来控制的,PW状态应该是一半大小
    limitedRegion.setTop(ct.convertPtToPhysics(topBorderMidPixelPt).y());
    ProbeDataInfo info = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    if (info.IsPhasedArray || info.IsLinear)
    {
        limitedRegion.setBottom(limitedRegion.top() +
                                limitedRegion.height() *
                                    ModelConfig::instance().value(ModelConfig::ROIRegionHeightFactor, 1.0).toFloat());
    }

    // 2025-05-09 Write by AlexWang [BUG:78659] 进入局部放大状态后，处于PW预备模式或是PW模式时
    // 设置成region，如果设置成limitedRegion会由于计算移动区域的范围导致无法移动多普勒扫查线
    if (m_SonoParameters->pBV(BFPNames::ZoomOnStr) && m_SonoParameters->pIV(BFPNames::SyncModeStr) != Sync_C &&
        m_SonoParameters->pIV(BFPNames::SyncModeStr) != Sync_CD &&
        (m_SonoParameters->pIV(BFPNames::SyncModeStr) == Sync_D ||
         m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr)))
    {
        m_BaseTransform->setRegion(region);
    }
    else
    {
        m_BaseTransform->setRegion(limitedRegion);
    }
}

void BCDGeometryController::onResetGateParameters(const ProbeImageRegion& region)
{
    Parameter* volumeMMP = m_SonoParameters->parameter(m_DopGatePNames->pName(DopGateMuxPNames::SampleVolumeMM));
    setGateMinSize(ProbePhysicalSize(0, m_GateMinHeightMM));
    setGateMaxSize(ProbePhysicalSize(region.width(), m_GateMaxHeightMM));

    Parameter* volumeP = m_SonoParameters->parameter(m_DopGatePNames->pName(DopGateMuxPNames::SampleVolume));

    // 此时门的高度如果大于范围的高度，需要自动调节门的高度
    while (volumeMMP->doubleValue() > gateRegionHeight())
    {
        volumeP->multi(false);
        if (volumeP->intValue() == volumeP->min())
        {
            break;
        }
    }

    double dStartDepthMM = m_SonoParameters->pDV(m_DopGatePNames->pName(DopGateMuxPNames::DopplerStartDepthMM));
    float volumeMM = m_SonoParameters->pFV(m_DopGatePNames->pName(DopGateMuxPNames::SampleVolumeMM));

    ProbePhysicalGeometry gate(m_SonoParameters->pIV(m_DopGatePNames->pName(DopGateMuxPNames::DScanLine)), 0,
                               (qreal)(dStartDepthMM + volumeMM / 2), (qreal)(volumeMM / 2));
    setGateGeometry(gate);
}

void BCDGeometryController::onResetRoiParameters(const ProbeImageRegion& region)
{
    // const ProbeDataInfo &probe = ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr));
    // 2023-04-28 Modify by AlexWang 替换使用超声参数集合的指针作为传入参数的构造函数，此处计算受放大因素的影响
    ProbeParameters pParameters(m_SonoParameters, m_considerZoomOn);
    ProbeImageRegion limitedRegion = m_BaseTransform->region();

    // roi最大最小宽度设置，同时必须小于regionWidth
    // 2023-05-06 Modify by AlexWang 兼容放大和非放大状态时实际的物理线数
    int minWidth = pParameters.actualLines() * m_RoiMinWidthFactor;
    int maxWidth = pParameters.actualLines() * m_RoiMaxWidthFactor;
    int limitedWidth = limitedRegion.width();
    int limitedHeight = limitedRegion.height();
    //因为多波束原因,线号需要为4倍数，另外设计上由于中心线和半宽的原因因此需要为8倍数
    minWidth -= minWidth % 8;
    maxWidth -= maxWidth % 8;

    if (minWidth > limitedWidth)
    {
        minWidth = limitedWidth / 2;
    }
    if (maxWidth > limitedWidth)
    {
        maxWidth = limitedWidth;
    }
    //设置ROI的最大和最小Size

    setRoiMinSize(ProbePhysicalSize(minWidth, limitedHeight * m_RoiMinHeightFactor));
#ifdef SYS_APPLE
    double maxHeight = limitedHeight * m_RoiMaxHeightFactor;
    double maxHeight2 = limitedHeight * 0.5; //掌超限制了高度，不能超过256个point size
    setRoiMaxSize(ProbePhysicalSize(maxWidth, qMin(maxHeight, maxHeight2)));
#else
    setRoiMaxSize(ProbePhysicalSize(maxWidth, limitedHeight * m_RoiMaxHeightFactor));
#endif

    ProbePhysicalGeometry roi(m_SonoParameters->pIV(m_ROIPNames->pName(ROIMuxPNames::RoiMidLine)),
                              m_SonoParameters->pIV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfLines)),
                              m_SonoParameters->pDV(m_ROIPNames->pName(ROIMuxPNames::RoiMidDepthMM)),
                              m_SonoParameters->pDV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfDepthMM)));
    setRoiGeometry(roi);
}

void BCDGeometryController::changeGateSize(bool add)
{
    Parameter* volumeMMP = m_SonoParameters->parameter(m_DopGatePNames->pName(DopGateMuxPNames::SampleVolumeMM));
    Parameter* volumeP = m_SonoParameters->parameter(m_DopGatePNames->pName(DopGateMuxPNames::SampleVolume));

    int svOldIndex = volumeP->intValue();
    int svIndex = svOldIndex;
    int svMin = volumeP->min();
    int svMax = volumeP->max();

    if (Util::multiValue(svIndex, svMin, svMax, add, false))
    {
        ControlTablePauseControl pauseCt(m_ControlTable);
        // 此时形状不稳定，通知glyph绘制，不下发控制表
        volumeP->setValue(svIndex);

        if (svIndex != svOldIndex)
        {
            qreal volumeMM = (qreal)(volumeMMP->doubleValue());

            if (volumeMM > gateRegionHeight())
            {
                volumeP->setValue(svOldIndex);
            }
            else
            {
                ProbePhysicalGeometry gate;
                getGateGeometry(gate);
                setGateSize(ProbePhysicalSize(gate.width(), volumeMM));
            }
        }
    }
}

void BCDGeometryController::roiGeometry2SonoParameters(const ProbePhysicalGeometry& roi)
{
    int midLine = roi.midX();
    int halfLines = roi.halfWidth();
    adjustedMidAndHalfLines(midLine, halfLines);

    m_SonoParameters->setPV(m_ROIPNames->pName(ROIMuxPNames::RoiMidLine), midLine);
    m_SonoParameters->setPV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfLines), halfLines);
    m_SonoParameters->setPV(m_ROIPNames->pName(ROIMuxPNames::RoiMidDepthMM), roi.midY());
    m_SonoParameters->setPV(m_ROIPNames->pName(ROIMuxPNames::RoiHalfDepthMM), roi.halfHeight());
}

void BCDGeometryController::gateGeometry2SonoParameters(const ProbePhysicalGeometry& gate)
{
    ControlTablePauseControl pauseCt(m_ControlTable);
    m_SonoParameters->setPV(m_DopGatePNames->pName(DopGateMuxPNames::DopplerStartDepthMM), gate.top());
    //通知glyph绘制，不下发控制表
    m_SonoParameters->setPV(m_DopGatePNames->pName(DopGateMuxPNames::DScanLine), gate.midX());
}

void BCDGeometryController::sendRoiParameters(const ProbePhysicalGeometry& roi)
{
    int oldstartlinecolor = m_SonoParameters->pIV(BFPNames::StartLineColorStr);
    int oldstoplinecolor = m_SonoParameters->pIV(BFPNames::StopLineColorStr);
    int oldtoplinecolor = m_SonoParameters->pIV(BFPNames::TopBorderColorStr);
    int oldbottomlinecolor = m_SonoParameters->pIV(BFPNames::BottomBorderColorStr);
    int scanmd = m_SonoParameters->pIV(BFPNames::SystemScanModeStr);

    ControlTableSender ct(m_ControlTable);

    // CM模式下只有上下布局，无全屏模式
    //目前roi框需要根据不同的模式来调整pixelsize的数值
    bool isUpdatePixelSizeWithUpDownLayout =
        m_SonoParameters->pBV(BFPNames::ColorMStr) ||
        (((SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D);
    //采用基于线数据方案执行, 后续再重构兼容整帧方案
    double pixelsize = isUpdatePixelSizeWithUpDownLayout ? bfDepthParameters().getPixelizeMMWithFactor(0.5) /
                                                               m_SonoParameters->pFV(BFPNames::RenderImageZoomCofStr)
                                                         : m_SonoParameters->pDV(BFPNames::PixelSizeMMStr);

    double pixelSizeMMWithoutZoomCoef =
        BFDepthParameters::pixelSizeMMWithoutZoomCoef(pixelsize, m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr),
                                                      m_SonoParameters->pDV(BFPNames::RenderImageZoomCofStr),
                                                      m_SonoParameters->pFV(BFPNames::FixedSWImageZoomCofStr));

    // 2023-06-21 Write by AlexWang [bug:65304] 局部放大状态下计算血流上下边界时需要减去StartDepthMM
    qreal topPixel = roi.top() / pixelSizeMMWithoutZoomCoef;
    qreal bottomPixel = roi.bottom() / pixelSizeMMWithoutZoomCoef;
    qreal startDepthPixel = 0.0;
    if (m_SonoParameters->pBV(BFPNames::ZoomOnStr))
    {
        startDepthPixel = m_SonoParameters->pDV(BFPNames::StartDepthMMStr) / pixelSizeMMWithoutZoomCoef;

        if (RealCompare::IsGreater(startDepthPixel, topPixel) || RealCompare::IsGreater(startDepthPixel, bottomPixel))
        {
            startDepthPixel = 0;
        }
    }
    m_SonoParameters->setPV(BFPNames::TopBorderColorStr, (int)(topPixel - startDepthPixel));
    m_SonoParameters->setPV(
        BFPNames::BottomBorderColorStr,
        qCeil(bottomPixel - startDepthPixel)); //精度问题会导致彩色跟底部宽有细微差距，这里向上取整补足精度
    // log()->debug() << PRETTY_FUNCTION
    //                << ", roi top: " << roi.top()
    //                << ", roi bottom: " << roi.bottom()
    //                << ", pixelSizeMMWithoutZoomCoef: " << pixelSizeMMWithoutZoomCoef
    //                << ", BFPNames::StartDepthMM: " << m_SonoParameters->pDV(BFPNames::StartDepthMMStr)
    //                << ", startDepthPixel: " << startDepthPixel
    //                << ", topPixel: " << topPixel
    //                << ", bottomPixel: " << bottomPixel
    //                << ", BFPNames::TopBorderColor: " << m_SonoParameters->pIV(BFPNames::TopBorderColorStr)
    //                << ", BFPNames::BottomBorderColor: " << m_SonoParameters->pIV(BFPNames::BottomBorderColorStr);

    int midLine = roi.midX();
    int halfLines = roi.halfWidth();
    adjustedMidAndHalfLines(midLine, halfLines);

    // colorM下需要下发固定值的扫查线数控制字
    if (m_SonoParameters->pBV(BFPNames::ColorMStr))
    {
        QString colorLineDensityStr = BFPNames::ColorLineDensityStr;
        QString beamIndexStr = BFPNames::MBColorStr;
        if (m_SonoParameters->pBV(BFPNames::ElastoEnStr))
        {
            beamIndexStr = BFPNames::ElastoMBColorStr;
        }
        else if (m_SonoParameters->pBV(BFPNames::TDIEnStr))
        {
            colorLineDensityStr = BFPNames::TDILineDensityStr;
            beamIndexStr = BFPNames::MBTDIStr;
        }
        else if (m_SonoParameters->pBV(BFPNames::MVIModeStr))
        {
            colorLineDensityStr = BFPNames::MVILineDensityStr;
            beamIndexStr = BFPNames::MBMVIStr;
        }
        else
        {
            if (m_SonoParameters->pIV(BFPNames::ColorImageModeStr) == Color_PD)
            {
                if (m_SonoParameters->pBV(BFPNames::SonoNeedleStr))
                {
                    colorLineDensityStr = BFPNames::ColorLineDensitySNStr;
                    beamIndexStr = BFPNames::MBSNStr;
                }
                else
                {
                    colorLineDensityStr = BFPNames::ColorLineDensityStr;
                    beamIndexStr = BFPNames::MBPDStr;
                }
            }
        }

        int offset = 0;
        switch (m_SonoParameters->pIV(beamIndexStr))
        {
        case 0:
            offset = 1;
            break;
        case 1:
            offset = 2;
            break;
        case 2:
            offset = 4;
            break;
        case 3:
            offset = 8;
            break;
        default:
            break;
        }
        offset *= (m_SonoParameters->pBV(colorLineDensityStr) ? 1 : 2);

        m_SonoParameters->setPV(BFPNames::StartLineColorStr, midLine - offset);
        m_SonoParameters->setPV(BFPNames::StopLineColorStr, midLine + offset);
    }
    else
    {
        m_SonoParameters->setPV(BFPNames::StartLineColorStr, midLine - halfLines);
        m_SonoParameters->setPV(BFPNames::StopLineColorStr, midLine + halfLines);
    }

    if (m_SonoParameters->pBV(BFPNames::ColorMStr))
    {
        // MScanLine 需要使用force方式下发，否则会出现CW模式按M，切换到CM模式时，mscanline没有下发
        // BUG:7269
        if (m_SonoParameters->pBV(BFPNames::TDIEnStr))
        {
            m_SonoParameters->setPV(BFPNames::MScanLineStr, m_SonoParameters->pIV(BFPNames::RoiMidLineTDIStr), true);
        }
        else
        {
            m_SonoParameters->setPV(BFPNames::MScanLineStr, m_SonoParameters->pIV(BFPNames::RoiMidLineStr), true);
        }
    }

    if (((SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) != Sync_D)
    {
        // BC、BCD模式，focusPosC跟着ROI的中心深度移动
        int focusPos = BFFocusParameters().getFocusPosByDepthMM(
            ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr)), roi.center().y(),
            m_SonoParameters->pIV(BFPNames::FocusNumCStr));
        m_SonoParameters->setPV(BFPNames::FocusPosCStr, focusPos);
    }

    int changed = abs(m_SonoParameters->pIV(BFPNames::StartLineColorStr) - oldstartlinecolor) +
                  abs(m_SonoParameters->pIV(BFPNames::StopLineColorStr) - oldstoplinecolor) +
                  abs(m_SonoParameters->pIV(BFPNames::TopBorderColorStr) - oldtoplinecolor) +
                  abs(m_SonoParameters->pIV(BFPNames::BottomBorderColorStr) - oldbottomlinecolor);

    if ((scanmd == SystemScanModeBPW || scanmd == SystemScanModeColorPW) || changed > 0)
        emit imageShapeUnstable(1);
    emit elastoImageShapeUnstable();
}

void BCDGeometryController::sendGateParameters(const ProbePhysicalGeometry& gate)
{
    if (m_SonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr) ||
        m_SonoParameters->pBV(BFPNames::IsCWDScanLineVisibleStr))
    {
        return;
    }

    ControlTableSender ct(m_ControlTable);

    //下发控制表
    m_SonoParameters
        ->parameter(m_SonoParameters->pBV(BFPNames::TDIEnStr) ? BFPNames::SampleVolumeTDIStr
                                                              : BFPNames::SampleVolumeStr)
        ->update();
    // HprfEnStr和SampleDepthDopStr在这里update的原因与下面prtofd一样
    m_SonoParameters->parameter(BFPNames::HprfEnStr)->update();
    m_SonoParameters->parameter(BFPNames::PWPointNumPerLineStr)->update();

    const QVariant& variant = m_SonoParameters->parameter(BFPNames::DopSteeringAngleStr)->showValue();
    // qCos的形参传入的是弧度值，而我们的偏转角是角度值，需要转换
    const qreal steeringRadian = qDegreesToRadians(variant.toReal());
    const double angleCosValue = qCos(steeringRadian) == 0 ? 1 : qCos(steeringRadian);
    if (m_SonoParameters->pBV(BFPNames::HprfEnStr))
    {
        m_SonoParameters->parameter(BFPNames::SampleDepthDopStr)->update();
        m_SonoParameters->parameter(BFPNames::DopSampleDepthWithClockSizeStr)->update();
    }
    else
    {
        //采用基于线数据方案执行, 后续再重构兼容整帧方案
        // SampleDepthDopStr这个参数用在老的整帧方案上，即如：Ebit机型
        m_SonoParameters->setPV(BFPNames::SampleDepthDopStr,
                                (int)(gate.top() / BFDepthParameters::pixelSizeMMWithoutZoomCoef(
                                                       bfDepthParameters().getPixelizeMMWithFactor(0.5) /
                                                           m_SonoParameters->pFV(BFPNames::RenderImageZoomCofStr),
                                                       m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr),
                                                       m_SonoParameters->pDV(BFPNames::RenderImageZoomCofStr),
                                                       m_SonoParameters->pFV(BFPNames::FixedSWImageZoomCofStr))));
        // DopSampleDepthWithClockSizeStr这个参数用在新方案（线数据），即如：Apple,ATOM
        m_SonoParameters->setPV(
            BFPNames::DopSampleDepthWithClockSizeStr,
            (int)(gate.top() /
                  BFADFreqParameter::pixelLenMM(m_SonoParameters->pIV(BFPNames::ADFreqMHzStr), false)));
    }
    //采样门物理距离x8192取整
    m_SonoParameters->setPV(BFPNames::CWTxFocusDisStr, (int)(gate.center().y() / angleCosValue * 8192));
    //采样门物理距离/变焦距离四舍五入
    //变焦距离=8*pixelLenMM
    m_SonoParameters->setPV(BFPNames::CWRvFocusPosStr,
                            qRound(gate.center().y() / angleCosValue /
                                   BFADFreqParameter::pixelLenMM(m_SonoParameters->pIV(BFPNames::ADFreqMHzStr)) / 8));
    //下发控制表
    m_SonoParameters->parameter(m_DopGatePNames->pName(DopGateMuxPNames::DScanLine))->update();

    if (((SyncModeType)m_SonoParameters->pIV(BFPNames::SyncModeStr) & Sync_D) == Sync_D)
    {
        // BD模式，focusPosC跟着采样门的中心深度移动
        int focusPos = BFFocusParameters().getFocusPosByDepthMM(
            ProbeDataSet::instance().getProbe(m_SonoParameters->pIV(BFPNames::ProbeIdStr)), gate.center().y(),
            m_SonoParameters->pIV(BFPNames::FocusNumCStr));
        m_SonoParameters->setPV(BFPNames::FocusPosCStr, focusPos);
    }

    if (m_SonoParameters->pBV(BFPNames::FreqSpectrumStr) || m_SonoParameters->pBV(BFPNames::TriplexModeStr))
    {
        emit bcdGeometryChanged();
    }

    emit imageUnstable(ModelConfig::instance().value(ModelConfig::DLineImageUnstableTimeMs, 200).toInt());
}

void BCDGeometryController::adjustedLeftAndRight(int& left, int& right)
{
    // 注释前在P9上C模式右侧会出现彩色竖条
    // 2023.5.18测试凸阵、线阵、相控阵的C模式，增益调节为0，未出现彩色竖条
    //同时放开倍数处理，切换C模式的线密度后图像不刷新，ZeusComplexParameter::onRoiTriggerCompleted中采用的是
    // ProbePhysicalGeometry的信息，未做倍数处理，会导致成像参数与线数据不匹配
    if (isSupportAnyDensity())
    {
        return;
    }
    // 控制limitregion的left and right 整除4，这样可以统一DScanLine和ROI的范围统一
    int mod = left % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        left += COLOR_ALIGN_LINE - mod;
    }

    mod = right % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        right -= mod;
    }
}

void BCDGeometryController::adjustedMidAndHalfLines(int& midLine, int& halfLines)
{
    // 注释前在P9上C模式右侧会出现彩色竖条
    // 2023.5.18测试凸阵、线阵、相控阵的C模式，增益调节为0，未出现彩色竖条
    //同时放开倍数处理，切换C模式的线密度后图像不刷新，ZeusComplexParameter::onRoiTriggerCompleted中采用的是
    // ProbePhysicalGeometry的信息，未做倍数处理，会导致成像参数与线数据不匹配
    if (isSupportAnyDensity())
    {
        return;
    }
    //控制绘制和下发时都保持整除4,FPGA真机测试，ECO、EBit必须满足此条件，QBit在高密度
    //时不需要控制，低密度时，一定要整除2,现在为了软件方便处理，都统一成必须整除4
    int mod = halfLines % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        halfLines -= mod;
    }
    mod = midLine % COLOR_ALIGN_LINE;
    if (mod != 0)
    {
        midLine -= mod;
    }

    // 判断左右侧边界与region的位置，调整到region内
    if ((midLine - halfLines) <= m_BaseTransform->region().left())
    { //左侧超出
        midLine = m_BaseTransform->region().left() + halfLines;
    }
    else if ((midLine + halfLines) >= m_BaseTransform->region().right())
    { //右侧超出
        midLine = m_BaseTransform->region().right() - halfLines;
    }
    else
    {
    }
}

BFDepthParameters& BCDGeometryController::bfDepthParameters() const
{
    static BFDepthParameters parameters;
    parameters.setPixelLen(BFADFreqParameter::pixelLenMM(m_SonoParameters->pIV(BFPNames::ADFreqMHzStr)) /
                           m_SonoParameters->pFV(BFPNames::FixedSWImageZoomCofStr));
    parameters.setImageZoomCoef(m_SonoParameters->pIV(BFPNames::ImageZoomCoefStr) / 100.0);
    parameters.setZoomOn(m_SonoParameters->pBV(BFPNames::ZoomOnStr));
    parameters.setZoomedCQYZ(m_SonoParameters->pIV(BFPNames::ZoomedCQYZStr));
    parameters.setCQYZ(m_SonoParameters->pIV(BFPNames::CQYZStr));

    return parameters;
}

void BCDGeometryController::updateLineDensityInfo()
{
    int midline = m_SonoParameters->pIV(BFPNames::RoiMidLineStr);
    int halflines = m_SonoParameters->pIV(BFPNames::RoiHalfLinesStr);
    m_StartLine = midline - halflines;
    m_StopLine = midline + halflines;
    m_RevLineNum = m_SonoParameters->pIV(BFPNames::C_RX_LNUMStr);
}

bool BCDGeometryController::isSupportAnyDensity()
{
    return ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity, false).toBool();
}
