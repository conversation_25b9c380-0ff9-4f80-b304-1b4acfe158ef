/*
 * =====================================================================================
 *
 *       Filename:  sharedglyphswidget.cpp
 *
 *    Description:
 *
 *        Version:  1.0
 *        Created:  2012年07月30日 16时32分34秒
 *       Revision:  none
 *       Compiler:  gcc
 *
 *         Author:  Wang<PERSON>inyu (), <EMAIL>
 *   Organization:
 *
 * =====================================================================================
 */

#include "sharedglyphswidget.h"

#include <QGraphicsItem>
#include <QPainter>

#include "bfpnames.h"
#include "formula.h"
#include "probedataset.h"
#include "probeparameters.h"
#include "bfcoordtransform.h"
#include "parameter.h"
#include "util.h"
#include "bfscanwidthparameter.h"
#include "modelconfig.h"
#include "imageinfodef.h"
#include "syncmodetype.h"

const QPointF SharedGlyphsWidget::ORIGIN_OFFSET(2, 2);

SharedGlyphsWidget::SharedGlyphsWidget(QObject* parent, const ISonoParameters& sonoParameters, const QRect& windowRect,
                                       const QRect& rect, const QRect& region, int index)
    : BaseGlyphsWidget(parent, sonoParameters, index)
{
    m_rect = rect; // 图像区
    m_localRect = m_rect.translated(-m_rect.left(), -m_rect.top());

    m_WindowRect = windowRect; //渲染区
    m_WindowLocalRect = m_WindowRect.translated(-m_WindowRect.left(), -m_WindowRect.top());

    m_region = region;

    Q_ASSERT(m_WindowRect.size() == m_WindowLocalRect.size());
    Q_ASSERT(m_WindowLocalRect.topLeft() == QPoint(0, 0));

    BaseGlyphsWidget::partitionNode()->setPos(m_WindowRect.topLeft());
    BaseGlyphsWidget::partitionClipNode()->setPos(m_WindowRect.topLeft());
    partitionNode()->setRect(m_WindowLocalRect);
    partitionClipNode()->setRect(m_WindowLocalRect);
    //不能改 不能改 不能改 会导致相控阵下panzoom后进C崩溃
    setClipsChildrenToShape(true);
    setClipsItemChildrenToShape(true);
}

struct PainterStateRAII
{

    PainterStateRAII(QPainter* p)
        : m_p(p)
    {
        m_p->save();
    }
    ~PainterStateRAII()
    {
        m_p->restore();
    }

    QPainter* m_p;
};

QTransform SharedGlyphsWidget::flipTransform()
{

    // TODO 若每次计算为性能瓶颈，可考虑连接Up/Left信号并缓存结果
    return Formula::flipTransform(pBV(getActiveBParaName(BFPNames::UpStr)), pBV(getActiveBParaName(BFPNames::LeftStr)));
}

QTransform SharedGlyphsWidget::flipAndRectZoomCoefTransform()
{

    // TODO 若每次计算为性能瓶颈，可考虑连接Up/Left信号并缓存结果

    return Formula::flipAndRectZoomCoefTransform(pBV(getActiveBParaName(BFPNames::UpStr)),
                                                 pBV(getActiveBParaName(BFPNames::LeftStr)), m_localRect.size(),
                                                 scaleFactor());
}

qreal SharedGlyphsWidget::scaledYInRectCoord(qreal originY)
{
    QPointF transformedPoint = QPointF(0, originY) * Formula::imageZoomCoefTransform(rect().size(), scaleFactor());

    return transformedPoint.y();
}

qreal SharedGlyphsWidget::scaledYInWindowRectCoord(qreal originY)
{
    QPointF transformedPoint =
        QPointF(0, originY) * Formula::imageZoomCoefTransform(windowRect().size(), scaleFactor());

    return transformedPoint.y();
}

qreal SharedGlyphsWidget::imageRectStartYInWindowRect()
{
    return (windowRect().height() - rect().height()) / 2;
}

qreal SharedGlyphsWidget::imageRectStartXInWindowRect()
{
    return (windowRect().width() - rect().width()) / 2;
}

void SharedGlyphsWidget::resetGraphicItemPos(QGraphicsItem* item)
{
    QPointF pos = item->pos();
    item->setPos(pos.x() + imageRectStartXInWindowRect(), pos.y() + imageRectStartYInWindowRect());
}

void SharedGlyphsWidget::resetGraphicItemPosX(QGraphicsItem* item)
{
    item->setX(item->pos().x() + imageRectStartXInWindowRect());
}

void SharedGlyphsWidget::resetGraphicItemPosY(QGraphicsItem* item)
{
    item->setY(item->pos().y() + imageRectStartYInWindowRect());
}

qreal SharedGlyphsWidget::scaleFactor()
{
    return pIV(getActiveBParaName(BFPNames::ImageZoomCoefStr)) / qreal(100);
}

qreal SharedGlyphsWidget::scaledLength(qreal length)
{
    return scaleFactor() * length;
}

QPointF SharedGlyphsWidget::scanLineAndDepthScreenPos(int scanLine, qreal depth, int totalLine)
{

    qreal startDepthMM = pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
          pixelSizeMM = pRV(getActiveBParaName(BFPNames::PixelSizeMMStr));

    if (RealCompare::AreEqual(pixelSizeMM, 0.0))
    {
        // TODO:正确的时间设置BIndex
        //退出multiB,pixel0123被清零,在m_BIndex还未重新设置时会有信号槽触到到这里,所以此时读到的pixel为0
        //由于是中间一个过度状态,最后还会被正确设置,所以先做一个预防处理
        startDepthMM = pRV(BFPNames::StartDepthMMStr);
        pixelSizeMM = pRV(BFPNames::PixelSizeMMStr);
    }

    // 2023-05-04 Modify by AlexWang 替换使用超声参数集合的指针作为传入参数的构造函数，需要考虑局部放大对参数计算的影响
    // 2025-01-14 Fix by AI Assistant: 在colorm模式下使用正确的扫描线范围参数，解决扫查线与取样门分离问题
    BFCoordTransform bfCoordTrans;
    SyncModeType syncMode = (SyncModeType)pIV(BFPNames::SyncModeStr);
    if ((syncMode & Sync_C) == Sync_C || (syncMode & Sync_D) == Sync_D)
    {
        // colorm模式下使用StartLineColorStr/StopLineColorStr参数，确保与取样门坐标系统一致
        const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr));
        bfCoordTrans = BFCoordTransform(probeDataInfo, startDepthMM, pixelSizeMM, pBV(BFPNames::ZoomOnStr),
                                        pIV(BFPNames::StartLineColorStr), pIV(BFPNames::StopLineColorStr), totalLine);
    }
    else
    {
        // B模式下使用默认参数
        bfCoordTrans = BFCoordTransform(&sonoParameters(), true, 0, 0, totalLine);
    }

    QPointF result;

    bfCoordTrans.convertPhysicsToLogic(scanLine, depth, result);

    return result;
}

qreal SharedGlyphsWidget::scanAreaWidth()
{
#ifdef WIN32
    return -1;
#else
    BFScanWidthParameter bfScanWidthParameter(
        ProbeDataSet::instance().getProbe(pIV(BFPNames::ProbeIdStr)), parameter(BFPNames::ScanWidthStr)->max(),
        ModelConfig::instance().value(ModelConfig::IsSupportAnyDensity).toBool(), pIV(BFPNames::B_RX_LNUMStr));

    QTransform transform = flipAndRectZoomCoefTransform();

    QLineF line(scanLineAndDepthScreenPos(bfScanWidthParameter.startLine(), pRV(BFPNames::DepthMMStr),
                                          pIV(BFPNames::B_RX_LNUMStr)) *
                    transform,
                scanLineAndDepthScreenPos(bfScanWidthParameter.stopLine(), pRV(BFPNames::DepthMMStr),
                                          pIV(BFPNames::B_RX_LNUMStr)) *
                    transform);

    return line.length();
#endif
}

BFCoordTransform SharedGlyphsWidget::coordTransform() const
{
    int probeId = pIV(BFPNames::ProbeIdStr);

    qreal startDepthMM = pRV(getActiveBParaName(BFPNames::StartDepthMMStr)),
          pixelSizeMM = pRV(getActiveBParaName(BFPNames::PixelSizeMMStr));

    if (RealCompare::AreEqual(pixelSizeMM, 0.0))
    {
        // TODO:正确的时间设置BIndex
        //退出multiB,pixel0123被清零,在m_BIndex还未重新设置时会有信号槽触到到这里,所以此时读到的pixel为0
        //由于是中间一个过度状态,最后还会被正确设置,所以先做一个预防处理
        startDepthMM = pRV(BFPNames::StartDepthMMStr);
        pixelSizeMM = pRV(BFPNames::PixelSizeMMStr);
    }

    const ProbeDataInfo& probeDataInfo = ProbeDataSet::instance().getProbe(probeId);

    BFCoordTransform bfCoordTrans(probeDataInfo, startDepthMM, pixelSizeMM, pBV(BFPNames::ZoomOnStr),
                                  pIV(BFPNames::StartLineStr), pIV(BFPNames::StopLineStr), pIV(BFPNames::B_RX_LNUMStr));
    return bfCoordTrans;
}

RotatableGlyphsWidget::RotatableGlyphsWidget(QObject* parent, const ISonoParameters& sonoParameters,
                                             const QRect& windowRect, const QRect& rect, const QRect& region, int index)
    : SharedGlyphsWidget(parent, sonoParameters, windowRect, rect, region, index)
{
    connect(parameter(getActiveBParaName(BFPNames::RotationStr)), SIGNAL(valueChanging(QVariant)), this,
            SLOT(onRotationChanged(QVariant)));

    connect(parameter(getActiveBParaName(BFPNames::ZoomOnStr)), SIGNAL(valueChanged(QVariant)), this,
            SLOT(changeSizeAndClipStatus()));

    connect(parameter(getActiveBParaName(BFPNames::ImageZoomCoefStr)), SIGNAL(valueChanged(QVariant)), this,
            SLOT(changeSizeAndClipStatus()));

    connect(parameter(getActiveBParaName(BFPNames::PanZoomMidPixelStr)), SIGNAL(valueChanged(QVariant)), this,
            SLOT(changeSizeAndClipStatus()));

    connect(parameter(getActiveBParaName(BFPNames::PanZoomMidDepthPixelStr)), SIGNAL(valueChanged(QVariant)), this,
            SLOT(changeSizeAndClipStatus()));

    onRotationChanged(pV(getActiveBParaName(BFPNames::RotationStr)));
}

void RotatableGlyphsWidget::changeSizeAndClipStatus()
{
}

void RotatableGlyphsWidget::rotate(const QVariant& value)
{
    // panzoom outer 旋转控制
    QGraphicsRectItem* node = partitionNode();
    node->setTransformOriginPoint(node->rect().center());
    node->setRotation(value.toInt());

    QGraphicsRectItem* clipNode = partitionClipNode();
    clipNode->setTransformOriginPoint(clipNode->rect().center());
    clipNode->setRotation(value.toInt());
}

bool RotatableGlyphsWidget::getRectByZoomCoef(QRectF& retRect)
{
    if (pIV(BFPNames::ImageZoomCoefStr) <= 100)
    {
        return false;
    }

    int layout = pIV(BFPNames::LayoutStr);
    QSize renderWidgetSize = pV(BFPNames::RenderWidgetSizeStr).toSize();
    int rotation = pIV(BFPNames::RotationStr);
    bool bHorizontal = (rotation == 90 || rotation == 270);
    ImageScale scale = pV(BFPNames::TwoDImageScaleFactorStr).value<ImageScale>();
    int halfWidth = 0.0;
    int halfHeight = 0.0;
    if (bHorizontal)
    {
        halfHeight = renderWidgetSize.width() * scale.xScale;
        halfWidth = renderWidgetSize.height() * scale.yScale;
    }
    else
    {
        halfWidth = renderWidgetSize.width() * scale.xScale;
        halfHeight = renderWidgetSize.height() * scale.yScale;
    }
    if (layout == Layout_1x1)
    {
        if (bHorizontal)
        {
            retRect.setRect(0, -(renderWidgetSize.width() - windowRect().height()) / 2.0, halfWidth,
                            halfHeight + (renderWidgetSize.width() - windowRect().height()));
            return true;
        }
    }

    if (layout == Layout_2x2)
    {
        int direction = (rotation == 90) ? 0 : -1;

        retRect.setRect(0, (rect().x() - windowRect().x()), halfWidth,
                        halfHeight + ((rect().x() - windowRect().x())) * direction);
        return true;
    }
    return false;
}

void RotatableGlyphsWidget::onRotationChanged(const QVariant& value)
{
    rotate(value);
}
