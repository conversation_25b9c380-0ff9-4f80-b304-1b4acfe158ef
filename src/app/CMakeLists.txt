
include_depends(usf.com.core.utilitymodel)
set(binname ${PROJECT_NAME}${APP_SUFFIX})

add_executable_qt(${binname} main.cpp)


add_custom_command(TARGET ${binname}
   POST_BUILD
   COMMAND cmake -DsourceDirector=${THIRDPARTYLIB_PATH}/otherapp/ -DtargetDirector=${CMAKE_RUNTIME_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
   )


target_link_libraries(${binname}
    usf.com.core.utilitymodel resource_exam resource_imaging ${QT_LIBRARIES})

add_dependencies(${binname} application_kb plot_plugin)
