#ifndef LINEPIXELDATAFILE_H
#define LINEPIXELDATAFILE_H

#include "custompixeldata_global.h"
#include "dicompalettefileheaddef.h"

class IPixelDataFileHead;

class CUSTOMPIXELDATASHARED_EXPORT LinePixelDataFile
{
public:
    LinePixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    virtual ~LinePixelDataFile();
    const IPixelDataFileHead& fileHead() const;
    virtual bool isValid() const;
    virtual const quint8* framePixelData(int curIndex);
    virtual const quint8* pixelData() const = 0;
    quint16 bitsStored() const;
    quint16 bitsAllocated() const;
    quint16 bytesAllocated() const;
    quint16 samplesPerPixel() const;
    quint16 planarConfiguration() const;
    const char* photometricInterpretation() const;
    quint16 columns() const;
    quint16 rows() const;
    quint32 numberOfFrames() const;
    quint32 numberOfFramesReaded() const;
    quint32 pixelSizeReaded() const;
    quint8 colorspace() const;

protected:
    quint32 readFrame(int index, void* ptr, quint32 size) const;

protected:
    bool m_opt_verbose;
    bool m_opt_debug;
    const IPixelDataFileHead& m_FileHead;
    quint32 m_NumberOfFrames;
    quint32 m_NumberOfSrcFrames;
    quint32 m_NumberOfFramesReaded;
    PaletteFile_head m_PaletteHead;
};

#endif // LINEPIXELDATAFILE_H
