#include "lineuncompressedpixeldatafile.h"
#include "ipixeldatafilehead.h"
#include <new>

LineUncompressedPixelDataFile::LineUncompressedPixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose,
                                                             bool opt_debug)
    : LinePixelDataFile(fileHead, opt_verbose, opt_debug)
{
    if ((m_PixelData = (quint8*)tryAllocateMemory(m_NumberOfFrames, fileHead.imageSize())) == NULL)
    {
        qDebug("Allocate memory failed!\n");
    }
    else
    {
        quint32 imageSize = fileHead.imageSize();
        quint32 i = 0;
        for (i = 0; i < m_NumberOfFrames; i++)
        {
            if (readFrame(i, (void*)(m_PixelData + i * imageSize), imageSize) != imageSize)
            {
                qDebug("read %d Frame error\n", i);
                break;
            }
        }
        m_NumberOfFramesReaded = i;

        if (opt_verbose)
        {
            qDebug("org frames: %u, org read frames: %u, readed frames: %u\n", m_FileHead.numberOfFrames(),
                   m_FileHead.numberOfReadFrames(), m_NumberOfFramesReaded);
        }
    }
}

LineUncompressedPixelDataFile::~LineUncompressedPixelDataFile()
{
    if (m_PixelData != NULL)
    {
        delete[] m_PixelData;
        m_PixelData = NULL;
    }
}

bool LineUncompressedPixelDataFile::isValid() const
{
    return LinePixelDataFile::isValid() && m_PixelData != NULL;
}

const quint8* LineUncompressedPixelDataFile::pixelData() const
{
    return m_PixelData;
}

quint8* LineUncompressedPixelDataFile::tryAllocateMemory(quint32& frame, quint32 frameSize)
{
    return new (std::nothrow) quint8[frame * frameSize];
}
