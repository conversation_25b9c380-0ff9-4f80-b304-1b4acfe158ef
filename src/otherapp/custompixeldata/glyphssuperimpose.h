#ifndef GLYPHSSUPERIMPOSE_H
#define GLYPHSSUPERIMPOSE_H

#include "custompixeldata_global.h"
#include <stdint.h>
#include "iglyphssuperimpose.h"
#include "dicompalettefileheaddef.h"
#include <QImage>
#include <QHash>

class CineFrameReader;
class BackgroundOverlayRender;
class SonoParameters;
class IBeamFormer;

class CUSTOMPIXELDATASHARED_EXPORT GlyphsSuperimpose : public IGlyphsSuperimpose
{
    Q_OBJECT
public:
    explicit GlyphsSuperimpose(const QString& cineAbsoluteFilePath, const QString& paletteFilePath,
                               OutImageTypeEnum::OutImageType imageType = OutImageTypeEnum::RGB,
                               const bool forAvi = false, QObject* parent = 0);
    ~GlyphsSuperimpose();
    int layout() const;

    int activeLayoutIndex() const;

    int dataType() const;

    int imageWidth() const;

    int imageHeight() const;

    int fps() const;

    int frameCount() const;

    void setColorMap(uchar* map1, uchar* map2);

    bool readMixedFrameByIndex(const int index, uchar* imageBuffer);
    bool isOutputGray() const;
    int samplePerPixel() const;

private:
    uchar* readPalette(const QString& paletteFileName);
    void freeMem();
    void superimposeGlyphs(uchar* imageBuffer, QImage::Format rgbformat);
    void swapRB(QImage& image);
    void initBeamFormer();
private slots:
    void onActiveLayoutSonoParametersChanged(SonoParameters* sonoParameters);

private:
    CineFrameReader* m_CineFrameReader;
    BackgroundOverlayRender* m_GlyphsRender;
    QImage m_GlyphsImage;
    QString m_PaletteFileName;
    PaletteFile_head m_PaletteHead;
    uchar* m_Palette;
    uchar* m_ImageBuffer;
    OutImageTypeEnum::OutImageType m_OutImageType;
    static const QHash<OutImageTypeEnum::OutImageType, int> m_FormatSize;
    IBeamFormer* m_BeamFormer;
    const bool m_ForAvi;
};

#endif // GLYPHSSUPERIMPOSE_H
