#ifndef MULTIFRAMEPIXELDATAFILEHEAD_H
#define MULTIFRAMEPIXELDATAFILEHEAD_H

#include "custompixeldata_global.h"
#include "abstractpixeldatafilehead.h"

class IGlyphsSuperimpose;

class CUSTOMPIXELDATASHARED_EXPORT MultiFramePixelDataFileHead : public AbstractPixelDataFileHead
{
public:
    MultiFramePixelDataFileHead(IGlyphsSuperimpose* glyphsSuperimpose, const DcmStorage_Option& gStorageOption,
                                int defFps, bool opt_verbose);
    ~MultiFramePixelDataFileHead();
    virtual bool readFrame(int index, void* imageBuffer, quint32 size = 0) const;
    virtual bool isValid() const;
    virtual quint16 columns() const;
    virtual quint16 rows() const;
    virtual quint32 bytesPerLine() const;
    virtual quint32 numberOfFrames() const;
    virtual bool isValidFps() const;

private:
    IGlyphsSuperimpose* m_GlyphsSuperimpose;
};

#endif // MULTIFRAMEPIXELDATAFILEHEAD_H
