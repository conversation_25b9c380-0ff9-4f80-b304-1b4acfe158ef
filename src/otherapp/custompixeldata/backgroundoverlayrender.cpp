#include "backgroundoverlayrender.h"
#include "sonoparameters.h"
#include "pimplglyphswidget.h"
#include "bfpnames.h"
#include "ilinebuffer.h"
#include <QPainter>
#include <QDebug>

BackgroundOverlayRender::BackgroundOverlayRender(ILineBuffer* lineBuffer)
    : m_LineBuffer(lineBuffer)
{
    modeGlyphsWidget = new PimplGlyphsWidget(&scene);
}

BackgroundOverlayRender::~BackgroundOverlayRender()
{
    if (NULL == modeGlyphsWidget)
    {
        return;
    }
    delete modeGlyphsWidget;
    modeGlyphsWidget = NULL;
}

void BackgroundOverlayRender::setSize(SonoParameters* sonoParameters)
{
    QSize size = sonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    modeGlyphsWidget->setBufferManager(m_LineBuffer);
    modeGlyphsWidget->overlay().setSize(size);
}

PimplGlyphsWidget* BackgroundOverlayRender::pimplGlyphsWidget()
{
    return modeGlyphsWidget;
}

QImage BackgroundOverlayRender::render(SonoParameters* sonoParameters)
{
    QSize size = sonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    modeGlyphsWidget->setBufferManager(m_LineBuffer);
    modeGlyphsWidget->overlay().setSize(size);
    modeGlyphsWidget->setSonoParameters(sonoParameters);

    QImage canvas(size, QImage::Format_RGB888);

    // set background not blend anything,but a red component for debug purpose
    // none-filling image area should has alpha-red
    // save canvas to png can verify result
    canvas.fill(QColor(0, 0, 0, 255));

    QPainter painter(&canvas);
    modeGlyphsWidget->overlay().scene()->render(&painter);
    return canvas;
}
