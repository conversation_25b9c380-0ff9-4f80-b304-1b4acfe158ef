#ifndef ABSTRACTPIXELDATAFILEHEAD_H
#define ABSTRACTPIXELDATAFILEHEAD_H

#include "custompixeldata_global.h"
#include "ipixeldatafilehead.h"

class CUSTOMPIXELDATASHARED_EXPORT AbstractPixelDataFileHead : public IPixelDataFileHead
{
public:
    AbstractPixelDataFileHead(const DcmStorage_Option& gStorageOption, int defFps);
    virtual ~AbstractPixelDataFileHead();
    virtual quint16 fps() const;
    virtual PhotometricInterpretationType photometricInterpretationType() const;
    virtual quint16 samplesPerPixel() const;
    virtual quint32 imageSize() const;
    virtual quint32 numberOfReadFrames() const;
    /**
     * @brief quality 1...100, 0:lossless
     * @return
     */
    virtual quint8 quality() const;
    virtual quint8 colorspace() const;
    virtual bool isNeedCompress() const;
    virtual bool isNeedSample() const;
    virtual float sampleInterval() const;
    virtual const DcmStorage_Option& storageOption() const;

protected:
    int m_Fps;
    PhotometricInterpretationType m_InterpretationType;
    quint32 m_SamplesPerPixel;
    quint32 m_ImageSize;
    quint32 m_NumberOfReadFrames;
    float m_SampleInterval;
    DcmStorage_Option m_StorageOption;
};

#endif // ABSTRACTPIXELDATAFILEHEAD_H
