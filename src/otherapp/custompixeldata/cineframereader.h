#ifndef CINEFRAMEREADER_H
#define CINEFRAMEREADER_H

#include "baselinebuffer.h"
#include "custompixeldata_global.h"
#include "dicompalettefileheaddef.h"
#include "glrender.h"
#include "sonobufferstorer.h"
#include <QList>
#include <QMutex>
#include <QPair>
#include <QString>
#include <QThread>
#include <QWaitCondition>

/**
 * @brief The ImageCineReader class
 *
 */
class BackgroundOverlayRender;
class ChisonUltrasoundContext;
class ImageEventArgs;
class SonoParameters;
class SonoBuffersStorer;
class ILineBuffer;
class ImagePainterRender;
class IImageSaveHelper;
class CUSTOMPIXELDATASHARED_EXPORT CineFrameReader : public BaseLineBuffer
{
    Q_OBJECT
public:
    explicit CineFrameReader(const QString& cineAbsoluteFilePath, QImage::Format outputImageFormat,
                             int bytesCountPerPixel, QObject* parent = 0);
    ~CineFrameReader();
    void initImageCineReader(BackgroundOverlayRender* render);
    BufferTypeEnum::BufferType bufferType() const;
    int startIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    void setStartIndex(const int startIndex, const int layoutIndex = -1);
    int endIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    void setEndIndex(const int endIndex, const int layoutIndex = -1);
    int currentIndex(bool toShowValue = true, const int layoutIndex = -1) const;
    void setCurrentIndex(const int currentIndex, const int layoutIndex = -1, bool needUpdateFreezeBar = true);
    virtual LoadErrorCode load(const QString& filePath, const int layoutIndex = -1);
    bool checkLoadFilesValidity(const QString& filePath, const int layoutIndex = -1);
    void removeAll(const int layoutIndex = -1, bool refresh = true);
    SonoParameters* getSonoParametersByLayoutIndex(const int layoutIndex) const;
    void superimposeGlyphs(uchar* imageBuffer, QImage::Format rgbformat);

    int layout() const;

    int activeLayoutIndex() const;

    int dataType() const;

    int imageSize() const;

    int imageWidth() const;

    int imageHeight() const;

    QImage::Format outImageFormat() const;
    /**
     * @brief fps 这里的FPS影响dicomscu和mov2mpg采样频率，将会根据 20/fps()进行采样
     * @return
     */
    int fps() const;

    int frameCount(const int layoutIndex = -1) const;

    void setColorMap(uchar* map1, uchar* map2);

    bool readFrameByIndex(const int index, uchar* imageData);

    SonoParameters* getSonoParameters() const;

    ILineBuffer* lineBuffer() const;
signals:
    void activeLayoutSonoParametersChanged(SonoParameters*);

private slots:
    void onNewImage(ImageEventArgs* imageEventArgs);

private:
    void resetRelatedSize();
    Q_INVOKABLE void execInitImageCineReader();

    Q_INVOKABLE void execDestoryImageCineReader();
    void copyToImageDataByOutFormat(uchar* destImageData, int imageSize);
    void createSonoparameters();
    GLRender* glRender(ImageEventArgs::ImageType type);

private:
    ChisonUltrasoundContext* m_ChisonContext;
    SonoParameters* m_SonoParameters;
    SonoBuffersStorer* m_SonoBuffersStorer;
    QHash<int, SonoBufferStorer*> m_SonoBufferStorers;
    QThread* m_Thread;
    ImagePainterRender* m_ImagePainterRender;
    QHash<ImageEventArgs::ImageType, GLRender*> m_Renders;
    QPainter* m_ImagePainter;
    QImage* m_Image;
    QMutex m_PainterLocker;

    QString m_SonoParameterFilePath;
    QString m_CineFilePath;

    QMutex m_FrameReaderLocker;
    QWaitCondition m_FrameReaderWaiter;
    uchar* m_ImageBuffer;
    int m_ImageCount;
    int m_ImageReceiveCount;
    int m_DataType;
    bool m_IsOutputGray;
    int m_BytesCountPerPixel;
    QImage::Format m_OutImageFormat;
    int m_LastReadIndex;
    BackgroundOverlayRender* m_Render;
    IImageSaveHelper* m_ImageSaveHelper;
};

#endif // CINEFRAMEREADER_H
