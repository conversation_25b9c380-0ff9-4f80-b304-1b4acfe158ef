#ifndef LINEUNCOMPRESSEDPIXELDATAFILE_H
#define LINEUNCOMPRESSEDPIXELDATAFILE_H

#include "custompixeldata_global.h"
#include "linepixeldatafile.h"

class CUSTOMPIXELDATASHARED_EXPORT LineUncompressedPixelDataFile : public LinePixelDataFile
{
public:
    LineUncompressedPixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    ~LineUncompressedPixelDataFile();
    virtual bool isValid() const;
    virtual const quint8* pixelData() const;

private:
    quint8* tryAllocateMemory(quint32& frame, quint32 frameSize);

private:
    quint8* m_PixelData;
};

#endif // LINEUNCOMPRESSEDPIXELDATAFILE_H
