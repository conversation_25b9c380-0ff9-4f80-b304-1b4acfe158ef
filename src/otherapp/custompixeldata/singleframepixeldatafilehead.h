#ifndef SINGLEFRAMEPIXELDATAFILEHEAD_H
#define SINGLEFRAMEPIXELDATAFILEHEAD_H

#include "custompixeldata_global.h"
#include "abstractpixeldatafilehead.h"
#include <stdio.h>

class CUSTOMPIXELDATASHARED_EXPORT SingleFramePixelDataFileHead : public AbstractPixelDataFileHead
{
public:
    SingleFramePixelDataFileHead(const QString& pixelDatafileName, OutImageTypeEnum::OutImageType imageType,
                                 const DcmStorage_Option& gStorageOption, int defFps, bool opt_verbose);
    ~SingleFramePixelDataFileHead();
    virtual bool readFrame(int index, void* imageBuffer, quint32 size = 0) const;
    virtual bool isValid() const;
    virtual quint16 columns() const;
    virtual quint16 rows() const;
    virtual quint32 bytesPerLine() const;
    virtual quint32 numberOfFrames() const;
    virtual bool isValidFps() const;

private:
    FILE* m_pFile;
    FileStruct_head m_FileHead;
};

#endif // SINGLEFRAMEPIXELDATAFILEHEAD_H
