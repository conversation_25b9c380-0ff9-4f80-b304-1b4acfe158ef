#ifndef BACKGROUNDOVERL<PERSON>YRENDER_H
#define BACKGROUNDOVERLAYRENDER_H

#include "custompixeldata_global.h"
#include <QPixmap>
#include <QSize>
#include <QGraphicsScene>

class SonoParameters;
class <PERSON>ineBuffer;
class PimplGlyphsWidget;

class C<PERSON><PERSON>MP<PERSON>ELDATASHARED_EXPORT BackgroundOverlayRender
{
public:
    BackgroundOverlayRender(ILineBuffer* lineBuffer);
    QImage render(SonoParameters* sonoParameters);
    ~BackgroundOverlayRender();
    void setSize(SonoParameters* sonoParameters);

    PimplGlyphsWidget* pimplGlyphsWidget();

private:
    ILineBuffer* m_LineBuffer;
    PimplGlyphsWidget* modeGlyphsWidget;
    QGraphicsScene scene;
};

#endif // BACKGROUNDOVERLAYRENDER_H
