#include "abstractpixeldatafilehead.h"

AbstractPixelDataFileHead::AbstractPixelDataFileHead(const DcmStorage_Option& gStorageOption, int defFps)
    : m_Fps(defFps)
    , m_InterpretationType(rgb)
    , m_SamplesPerPixel(3)
    , m_ImageSize(0)
    , m_NumberOfReadFrames(0)
    , m_SampleInterval(0.0f)
    , m_StorageOption(gStorageOption)
{
}

AbstractPixelDataFileHead::~AbstractPixelDataFileHead()
{
}

quint16 AbstractPixelDataFileHead::fps() const
{
    return m_Fps;
}

PhotometricInterpretationType AbstractPixelDataFileHead::photometricInterpretationType() const
{
    return m_InterpretationType;
}

quint16 AbstractPixelDataFileHead::samplesPerPixel() const
{
    return m_SamplesPerPixel;
}

quint32 AbstractPixelDataFileHead::imageSize() const
{
    return m_ImageSize;
}

quint32 AbstractPixelDataFileHead::numberOfReadFrames() const
{
    return m_NumberOfReadFrames;
}

quint8 AbstractPixelDataFileHead::quality() const
{
    return m_StorageOption.CompressionRatio;
}

quint8 AbstractPixelDataFileHead::colorspace() const
{
    return m_StorageOption.CompressionMode;
}

bool AbstractPixelDataFileHead::isNeedCompress() const
{
    return IsNeedCompress((PDcmStorage_Option)&m_StorageOption);
}

bool AbstractPixelDataFileHead::isNeedSample() const
{
    return m_StorageOption.DcmFramerate > 0 && m_StorageOption.DcmFramerate != m_StorageOption.CineFramerate;
}

float AbstractPixelDataFileHead::sampleInterval() const
{
    return m_SampleInterval;
}

const DcmStorage_Option& AbstractPixelDataFileHead::storageOption() const
{
    return m_StorageOption;
}
