#ifndef IGLYPHSSUPERIMPOSE_H
#define IGLYPHSSUPERIMPOSE_H

#include "custompixeldata_global.h"
#include <QObject>

class SonoParameters;
class CUSTOMPIXELDATASHARED_EXPORT IGlyphsSuperimpose : public QObject
{
    Q_OBJECT
public:
    virtual ~IGlyphsSuperimpose();
    /**
     * @brief layout 获取绘制界面Layout信息，单B，2B，4B
     * @return : Layout_1x1, Layout_1x2, Layout_2x2
     */
    virtual int layout() const = 0;
    /**
     * @brief activeLayoutIndex 获取多B模式下激活区域的LayoutIndex
     * @return : 0, 1, 2, 3
     */
    virtual int activeLayoutIndex() const = 0;
    /**
     * @brief dataType 数据类型
     * @return 这里默认返回0
     */
    virtual int dataType() const = 0;
    /**
     * @brief imageWidth 获取图像宽度
     * @return
     */
    virtual int imageWidth() const = 0;
    /**
     * @brief imageHeight 获取图像高度
     * @return
     */
    virtual int imageHeight() const = 0;
    /**
     * @brief fps 获取帧频
     * @return 这里默认返回10
     */
    virtual int fps() const = 0;
    /**
     * @brief frameCount 获取缓存帧数
     * @return
     */
    virtual int frameCount() const = 0;
    /**
     * @brief setColorMap 设置colormap映射表
     * @param map1 映射表1
     * @param map2 映射表2
     */
    virtual void setColorMap(uchar* map1, uchar* map2) = 0;
    /**
     * @brief readMixedFrameByIndex 根据索引获取帧数据并叠加图元的像素信息
     * @param index 索引
     * @param imageData 像素数据存储地址
     * @return
     */
    virtual bool readMixedFrameByIndex(const int index, uchar* imageData) = 0;

    virtual bool isOutputGray() const = 0;

    virtual int samplePerPixel() const = 0;

protected:
    explicit IGlyphsSuperimpose(QObject* parent = 0);
};

#endif // IGLYPHSSUPERIMPOSE_H
