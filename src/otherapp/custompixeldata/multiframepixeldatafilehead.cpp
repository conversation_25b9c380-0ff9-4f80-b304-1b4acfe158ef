#include "multiframepixeldatafilehead.h"
#include "glyphssuperimpose.h"
#include "setting.h"

MultiFramePixelDataFileHead::MultiFramePixelDataFileHead(IGlyphsSuperimpose* glyphsSuperimpose,
                                                         const DcmStorage_Option& gStorageOption, int defFps,
                                                         bool opt_verbose)
    : AbstractPixelDataFileHead(gStorageOption, defFps)
    , m_GlyphsSuperimpose(glyphsSuperimpose)
{
    {
        //针对dicom导出，将cineframerate设置为实时导出病例的帧频
        m_StorageOption.CineFramerate = isValidFps() ? m_GlyphsSuperimpose->fps() : m_StorageOption.CineFramerate;
        // DcmFramerate值在用户设置为最大值的情况下，如果实际导出病例CineFramerate的值不超过DcmFramerate最大值的情况下
        //需要DcmFramerate同步更新为CineFramerate的值，确保不会进行抽帧，按照导出病例的fps进行播放
        if (m_StorageOption.DcmFramerate == Setting::instance().defaults().dicomMaxFramerate())
        {
            m_StorageOption.DcmFramerate = qMin(m_StorageOption.CineFramerate, m_StorageOption.DcmFramerate);
        }
    }
    m_InterpretationType = m_GlyphsSuperimpose->isOutputGray() ? monochrome : rgb;
    m_SamplesPerPixel = m_GlyphsSuperimpose->samplePerPixel();
    m_ImageSize = m_GlyphsSuperimpose->imageWidth() * m_GlyphsSuperimpose->imageHeight() * m_SamplesPerPixel;
    m_NumberOfReadFrames = m_GlyphsSuperimpose->frameCount();
    m_SampleInterval = 1.0f;
    if (m_NumberOfReadFrames > 1 && isNeedSample())
    {
        m_SampleInterval = (float)m_StorageOption.CineFramerate / m_StorageOption.DcmFramerate;
        m_NumberOfReadFrames = m_NumberOfReadFrames * m_StorageOption.DcmFramerate / m_StorageOption.CineFramerate;
        if (m_NumberOfReadFrames == 0)
        {
            m_NumberOfReadFrames = 1;
        }
    }

    if (isNeedSample() || (m_StorageOption.DcmFramerate != m_Fps && isValidFps()))
    {
        m_Fps = m_StorageOption.DcmFramerate;
    }

    if (opt_verbose)
    {
        qDebug("DCM fps %d CineFps:%d DcmFps:%d\n\
               PixelDataFileHead: isValid %d, fps %d,\n\
               photoType %d, samplesPerPixel %d,\n\
               columns %d, rows %d, imageSize %u,\n\
               numberOfFrames %u, numberOfReadFrames %u,\n\
               isNeedCompress %d, isNeedSample %d,\n\
               sampleInterval %f.\n",
               m_Fps, m_StorageOption.CineFramerate, m_StorageOption.DcmFramerate, isValid(), fps(),
               photometricInterpretationType(), samplesPerPixel(), columns(), rows(), imageSize(), numberOfFrames(),
               numberOfReadFrames(), isNeedCompress(), isNeedSample(), sampleInterval());
    }
}

MultiFramePixelDataFileHead::~MultiFramePixelDataFileHead()
{
}

bool MultiFramePixelDataFileHead::readFrame(int index, void* imageBuffer, quint32 size) const
{
    Q_UNUSED(size)
    return m_GlyphsSuperimpose->readMixedFrameByIndex(index, (uchar*)imageBuffer);
}

bool MultiFramePixelDataFileHead::isValid() const
{
    return m_GlyphsSuperimpose != NULL && m_ImageSize > 0 && m_NumberOfReadFrames > 0;
}

quint16 MultiFramePixelDataFileHead::columns() const
{
    return m_GlyphsSuperimpose->imageWidth();
}

quint16 MultiFramePixelDataFileHead::rows() const
{
    return m_GlyphsSuperimpose->imageHeight();
}

quint32 MultiFramePixelDataFileHead::bytesPerLine() const
{
    return m_GlyphsSuperimpose->imageWidth() * m_SamplesPerPixel;
}

quint32 MultiFramePixelDataFileHead::numberOfFrames() const
{
    return m_GlyphsSuperimpose->frameCount();
}

bool MultiFramePixelDataFileHead::isValidFps() const
{
    return m_GlyphsSuperimpose->fps() > 0 && m_GlyphsSuperimpose->fps() < 0XFFFF;
}
