#include "singleframepixeldatafilehead.h"
#include <QDebug>

SingleFramePixelDataFileHead::SingleFramePixelDataFileHead(const QString& pixelDatafileName,
                                                           OutImageTypeEnum::OutImageType imageType,
                                                           const DcmStorage_Option& gStorageOption, int defFps,
                                                           bool opt_verbose)
    : AbstractPixelDataFileHead(gStorageOption, defFps)
{
    memset(&m_FileHead, 0, sizeof(FileStruct_head));
    if (pixelDatafileName.isEmpty())
    {
        qDebug("SingleFramePixelDataFileHead:fileName is NULL.\n");
        return;
    }

    if ((m_pFile = fopen(pixelDatafileName.toLatin1().data(), "rb")) == NULL)
    {
        qDebug("SingleFramePixelDataFileHead: open file %s failed.\n", pixelDatafileName.toLatin1().data());
    }
    else
    {
        if (fread(&m_FileHead, sizeof(FileStruct_head), 1, m_pFile) == 1)
        {
            // m_FileHead.DataType
            // 0: .img or .cin raw data
            // -1 : monochrome or rgb bmp data
            m_InterpretationType = (imageType == OutImageTypeEnum::Gray) ? monochrome : rgb;

            m_SamplesPerPixel = (m_InterpretationType != rgb) ? 1 : 3;
            m_ImageSize = m_FileHead.Width * m_FileHead.Height * m_SamplesPerPixel;

            //如果filehead.fps是有效值,重新调整s_DcmStorage_Option的值
            if (isValidFps())
            {
                if (!isNeedSample())
                {
                    //不采样,则保持两个framerate都是filehead.fps
                    m_StorageOption.DcmFramerate = m_FileHead.fps;
                }

                m_StorageOption.CineFramerate = m_FileHead.fps;
            }

            m_NumberOfReadFrames = m_FileHead.Frame;
            m_SampleInterval = 1.0f;
            if (m_NumberOfReadFrames > 1 && isNeedSample())
            {
                m_SampleInterval = (float)m_StorageOption.CineFramerate / m_StorageOption.DcmFramerate;
                m_NumberOfReadFrames =
                    m_NumberOfReadFrames * m_StorageOption.DcmFramerate / m_StorageOption.CineFramerate;
                if (m_NumberOfReadFrames == 0)
                {
                    m_NumberOfReadFrames = 1;
                }
            }

            if (isNeedSample() || (m_StorageOption.DcmFramerate != m_Fps && isValidFps()))
            {
                m_Fps = m_StorageOption.DcmFramerate;
            }

            if (opt_verbose)
            {
                printf("DCM fps %d CineFps:%d DcmFps:%d\n", m_Fps, m_StorageOption.CineFramerate,
                       m_StorageOption.DcmFramerate);
            }
        }
        else
        {
            printf("File Pixel Data Getter: read head failed.\n");
            fclose(m_pFile);
            m_pFile = NULL;
        }
    }
}

SingleFramePixelDataFileHead::~SingleFramePixelDataFileHead()
{
    if (m_pFile != NULL)
    {
        fclose(m_pFile);
        m_pFile = NULL;
    }
}

bool SingleFramePixelDataFileHead::readFrame(int index, void* imageBuffer, quint32 size) const
{
    Q_UNUSED(index)
    return fread(imageBuffer, sizeof(unsigned char), size, m_pFile) == size;
}

bool SingleFramePixelDataFileHead::isValid() const
{
    return m_pFile != NULL && m_ImageSize > 0 && m_NumberOfReadFrames > 0;
}

quint16 SingleFramePixelDataFileHead::columns() const
{
    return m_FileHead.Width;
}

quint16 SingleFramePixelDataFileHead::rows() const
{
    return m_FileHead.Height;
}

quint32 SingleFramePixelDataFileHead::bytesPerLine() const
{
    return m_FileHead.Width * m_SamplesPerPixel;
}

quint32 SingleFramePixelDataFileHead::numberOfFrames() const
{
    return m_FileHead.Frame;
}

bool SingleFramePixelDataFileHead::isValidFps() const
{
    return m_FileHead.fps > 0 && m_FileHead.fps < 0XFFFF;
}
