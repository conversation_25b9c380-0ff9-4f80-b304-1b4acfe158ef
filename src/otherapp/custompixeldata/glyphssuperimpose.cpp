#include "glyphssuperimpose.h"
#include "appsetting.h"
#include "backgroundoverlayrender.h"
#include "beamformerfactorycreator.h"
#include "bfpnames.h"
#include "cineframereader.h"
#include "ibeamformer.h"
#include "ibeamformerfactory.h"
#include "sonoparameters.h"
#include <QDebug>
#include <QPainter>
#include <sys/stat.h>

static int fileSize(const char* filename)
{
    struct stat statbuf;
    if (stat(filename, &statbuf) == 0)
    {
        return statbuf.st_size;
    }
    else
    {
        perror("get file size failed");
        return 0;
    }
}
const QHash<OutImageTypeEnum::OutImageType, int> GlyphsSuperimpose::m_FormatSize =
    QHash<OutImageTypeEnum::OutImageType, int>{
        {OutImageTypeEnum::Gray, 1},
        {OutImageTypeEnum::RGB, 3},
        {OutImageTypeEnum::RGBA, 4},
        {OutImageTypeEnum::BGR, 3},
    };

GlyphsSuperimpose::GlyphsSuperimpose(const QString& cineAbsoluteFilePath, const QString& paletteFilePath,
                                     OutImageTypeEnum::OutImageType imageType, const bool forAvi, QObject* parent)
    : IGlyphsSuperimpose(parent)
    , m_CineFrameReader(new CineFrameReader(cineAbsoluteFilePath, (QImage::Format)imageType,
                                            (m_FormatSize.contains(imageType) ? m_FormatSize[imageType] : 0)))
    , m_GlyphsRender(new BackgroundOverlayRender(m_CineFrameReader))
    , m_PaletteFileName(paletteFilePath)
    , m_Palette(NULL)
    , m_ImageBuffer(NULL)
    , m_OutImageType(imageType)
    , m_BeamFormer(NULL)
    , m_ForAvi(forAvi)
{
    initBeamFormer();
    connect(m_CineFrameReader, SIGNAL(activeLayoutSonoParametersChanged(SonoParameters*)), this,
            SLOT(onActiveLayoutSonoParametersChanged(SonoParameters*)), Qt::DirectConnection);
    // TODO luodebing initImageCineReader 中有大量耗时代码，虽然用BlockingQueuedConnection来处理，但从设计角度来看不符合
    m_CineFrameReader->initImageCineReader(m_GlyphsRender);

    if ((m_Palette = readPalette(m_PaletteFileName)) == NULL)
    {
        freeMem();
        return;
    }

    SonoParameters* sonoParameters = m_CineFrameReader->getSonoParameters();

    if (m_ForAvi)
    {
        sonoParameters->setPV(BFPNames::HideTCGForCineStr, true);
        sonoParameters->setPV(BFPNames::HideLGCForCineStr, true);
        int layoutNums = sonoParameters->pIV(BFPNames::LayoutStr);
        for (int i = 0; i < layoutNums; i++)
        {
            SonoParameters* modifySonoParameters = m_CineFrameReader->getSonoParametersByLayoutIndex(i);
            if (modifySonoParameters != NULL)
            {
                modifySonoParameters->setPV(BFPNames::HideTCGForCineStr, true);
                modifySonoParameters->setPV(BFPNames::HideLGCForCineStr, true);
            }
        }
    }

    m_GlyphsImage = m_GlyphsRender->render(sonoParameters);
}

GlyphsSuperimpose::~GlyphsSuperimpose()
{
    if (m_CineFrameReader != NULL)
    {
        delete m_CineFrameReader;
        m_CineFrameReader = NULL;
    }

    //    if(m_GlyphsRender != NULL)
    //    {
    //        delete m_GlyphsRender;
    //        m_GlyphsRender = NULL;
    //    }

    if (m_BeamFormer != NULL)
    {
        delete m_BeamFormer;
        m_BeamFormer = NULL;
    }
}

int GlyphsSuperimpose::layout() const
{
    return m_CineFrameReader->layout();
}

int GlyphsSuperimpose::activeLayoutIndex() const
{
    return m_CineFrameReader->activeLayoutIndex();
}

int GlyphsSuperimpose::dataType() const
{
    return m_CineFrameReader->dataType();
}

int GlyphsSuperimpose::imageWidth() const
{
    return m_CineFrameReader->imageWidth();
}

int GlyphsSuperimpose::imageHeight() const
{
    return m_CineFrameReader->imageHeight();
}

int GlyphsSuperimpose::fps() const
{
    return m_CineFrameReader->fps();
}

int GlyphsSuperimpose::frameCount() const
{
    return m_CineFrameReader->frameCount();
}

void GlyphsSuperimpose::setColorMap(uchar* map1, uchar* map2)
{
    m_CineFrameReader->setColorMap(map1, map2);
}

bool GlyphsSuperimpose::readMixedFrameByIndex(const int index, uchar* imageBuffer)
{
    if (!m_CineFrameReader->readFrameByIndex(index, imageBuffer))
    {
        return false;
    }

    superimposeGlyphs(imageBuffer, m_CineFrameReader->outImageFormat());

    return true;
}

bool GlyphsSuperimpose::isOutputGray() const
{
    return m_OutImageType == OutImageTypeEnum::Gray;
}
int GlyphsSuperimpose::samplePerPixel() const
{
    return GlyphsSuperimpose::m_FormatSize.value(m_OutImageType);
}
uchar* GlyphsSuperimpose::readPalette(const QString& paletteFileName)
{
    if (paletteFileName.isEmpty())
    {
        return NULL;
    }

    uchar* palette = NULL;
    FILE* paletteFile = NULL;

    QByteArray arrFileName = paletteFileName.toLatin1();
    const char* fileName = arrFileName.constData();
    int fSize = fileSize(fileName);

    if ((paletteFile = fopen(fileName, "rb")) != NULL)
    {
        if (fSize == 1024)
        {
            //兼容老的版本，只考虑一块区域，一个palette，老的palette文件没有PaletteFile_head，只有1024字节
            m_PaletteHead.ImageRegionType = ImageRegionTypeEnum::Region_Single;
            m_PaletteHead.PaletteColorType = PaletteColorTypeEnum::Type_RGB;
            m_PaletteHead.LeftOrUpsideWidth = imageWidth();
            m_PaletteHead.LeftOrUpsideHeight = imageHeight();
            m_PaletteHead.RoiLeft = 0;
            m_PaletteHead.RoiTop = 0;
            m_PaletteHead.RoiWidth = 0;
            m_PaletteHead.RoiHeight = 0;
        }
        else
        {
            if (fread(&m_PaletteHead, sizeof(PaletteFile_head), 1, paletteFile) != 1)
            {
                return NULL;
            }
        }

        int paletteCount = 1;
        if (m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_LR ||
            m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_UD ||
            m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_LRElasto)
        {
            paletteCount = 2;
        }
        m_PaletteHead.RoiWidth += 2; //增加2个像素，防止边缘没有被处理.（这里为了速度是2个像素一起被处理的）
        palette = (uchar*)new (std::nothrow) uint32_t[256 * paletteCount];
        if (palette != NULL)
        {
            if ((int)fread(palette, sizeof(uchar), 1024 * paletteCount, paletteFile) == (1024 * paletteCount))
            {
                //这里由于超声软件使用的图像排布为BGR（小端系统下的Qimage::RGB32），因此需要翻转下
                if (m_PaletteHead.PaletteColorType == PaletteColorTypeEnum::Type_RGB)
                {
                    uint8_t value = 0;
                    for (int i = 0; i < 256 * paletteCount; i++)
                    {
                        value = palette[i * 4 + 0];
                        palette[i * 4 + 0] = palette[i * 4 + 2];
                        palette[i * 4 + 2] = value;
                    }
                }

                if (paletteCount == 1)
                {
                    setColorMap(palette, palette);
                }
                else
                {
                    setColorMap(palette, palette + 256 * 4);
                }
                fclose(paletteFile);
                return palette;
            }
            else
            {
                delete[] palette;
                fclose(paletteFile);
            }
        }
        else
        {
            fclose(paletteFile);
        }
    }

    return NULL;
}

void GlyphsSuperimpose::freeMem()
{
    if (m_Palette != NULL)
    {
        delete[] m_Palette;
        m_Palette = NULL;
    }
}

void GlyphsSuperimpose::superimposeGlyphs(uchar* imageBuffer, QImage::Format rgbformat)
{
    QImage srcImage(imageBuffer, imageWidth(), imageHeight(), rgbformat);
    QColor glyphsColor;
    for (int x = 0; x < srcImage.width(); ++x)
    {
        for (int y = 0; y < srcImage.height(); ++y)
        {
            glyphsColor = QColor(m_GlyphsImage.pixel(x, y));
            if (glyphsColor.red() != 0 || glyphsColor.blue() != 0 || glyphsColor.green() != 0)
            {
                srcImage.setPixel(x, y, glyphsColor.rgb());
            }
        }
    }
}

void GlyphsSuperimpose::swapRB(QImage& image)
{
    int btsPP = image.depth() / 8;
    if (btsPP == 3 || btsPP == 4)
    {
        for (int y = 0; y < image.height(); ++y)
        {
            uchar* line = image.scanLine(y);
            for (int x = 0; x < image.width(); ++x)
            {
                uchar tmp = 0;
                tmp = line[x * btsPP + 0];
                line[x * btsPP + 0] = line[x * btsPP + 2];
                line[x * btsPP + 2] = tmp;
            }
        }
    }
}

void GlyphsSuperimpose::initBeamFormer()
{
    IBeamFormerFactory* bfFactory = BeamFormerFactoryCreator::createFactory(AppSetting::model());
    m_BeamFormer = bfFactory->create(this);
    m_BeamFormer->setIsAutoConnect(false);
    delete bfFactory;
}

void GlyphsSuperimpose::onActiveLayoutSonoParametersChanged(SonoParameters* sonoParameters)
{
    m_BeamFormer->setSonoParameters(sonoParameters);
    m_BeamFormer->connect(sonoParameters->parameter(BFPNames::FrameAvgStr),
                          SIGNAL(gettingControlTableValue(QVariant, int&)), m_BeamFormer,
                          SLOT(onGettingFrameAvgControlTableValue(QVariant, int&)),
                          Qt::ConnectionType(Qt::DirectConnection | Qt::UniqueConnection));
}
