#include "linecompressedpixeldatafile.h"
#include "ipixeldatafilehead.h"
#include <new>
#include <string.h>
#include <stdio.h>
LineCompressedPixelDataFile::LineCompressedPixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose,
                                                         bool opt_debug)
    : LinePixelDataFile(fileHead, opt_verbose, opt_debug)
    , m_ImageSize(fileHead.imageSize())
    , m_SamplesPerPixel(fileHead.sampleInterval())
    , m_ImageBuffer(NULL)
{
    m_ImageBuffer = (quint8*)new (std::nothrow) int[fileHead.imageSize() / sizeof(int)];
    if (m_ImageBuffer == NULL)
    {
        qDebug("CompressedPixelDataFile: new image buffer error\n");
        return;
    }
    memset(m_ImageBuffer, 0, fileHead.bytesPerLine());
}

LineCompressedPixelDataFile::~LineCompressedPixelDataFile()
{
    if (m_ImageBuffer != NULL)
    {
        delete[] m_ImageBuffer;
        m_ImageBuffer = NULL;
    }
}

bool LineCompressedPixelDataFile::isValid() const
{
    return LinePixelDataFile::isValid() && m_ImageBuffer != NULL;
}

const quint8* LineCompressedPixelDataFile::framePixelData(int curIndex)
{
    if (readFrame(curIndex, (void*)m_ImageBuffer, m_ImageSize) != m_ImageSize)
    {
        qDebug("read %d Frame error\n", curIndex);
        return NULL;
    }

    m_NumberOfFramesReaded++;
    return m_ImageBuffer;
}

const quint8* LineCompressedPixelDataFile::pixelData() const
{
    return m_ImageBuffer;
}
