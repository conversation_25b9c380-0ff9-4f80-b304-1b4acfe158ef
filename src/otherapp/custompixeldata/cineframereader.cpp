#include "cineframereader.h"
#include "bfpnames.h"
#include "chisonultrasoundcontext.h"
#include "chisonultrasoundtypes.h"
#include "ilinebuffer.h"
#include "imageeventargs.h"
#include "imagepainterrender.h"
#include "infostruct.h"
#include "resource.h"
#include "sonobuffersstorer.h"
#include "sonoparameters.h"
#include "timerthread.h"
#include <QDataStream>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QImage>
#include <QSize>
//#include <unistd.h>
#include "appsetting.h"
#include "backgroundoverlayrender.h"
#include "ecgglyphrender.h"
#include "ecgsettings.h"
#include "imageinfodef.h"
#include "imagerenderlayoutrects.h"
#include "sonoparafileprovider.h"
#include "util.h"
#include "variantutil.h"
#include "xmlbfparameterloader.h"
#include <QPainter>
#include "imagesavehelper.h"

CineFrameReader::CineFrameReader(const QString& cineAbsoluteFilePath, QImage::Format outputImageFormat,
                                 int bytesCountPerPixel, QObject* parent)
    : BaseLineBuffer(parent)
    , m_ChisonContext(NULL)
    , m_SonoParameters(NULL)
    , m_SonoBuffersStorer(NULL)
    , m_Thread(NULL)
    , m_ImagePainterRender(NULL)
    , m_ImagePainter(NULL)
    , m_Image(NULL)
    , m_SonoParameterFilePath(cineAbsoluteFilePath)
    , m_CineFilePath(cineAbsoluteFilePath)
    , m_ImageBuffer(NULL)
    , m_ImageCount(0)
    , m_ImageReceiveCount(0)
    , m_DataType(cineAbsoluteFilePath.endsWith(Resource::cineExt) ? 0 : -1)
    , m_IsOutputGray(outputImageFormat == QImage::Format_Indexed8)
    , m_BytesCountPerPixel(bytesCountPerPixel)
    , m_OutImageFormat(outputImageFormat)
    , m_LastReadIndex(-1)
    , m_Render(nullptr)
    , m_ImageSaveHelper(new ImageSaveHelper)
{
    m_CineFilePath.remove(m_CineFilePath.length() - 4, 4);
    m_Thread = new TimerThread("CineFrameReader");
    m_Thread->start();
    moveToThread(m_Thread);
}

CineFrameReader::~CineFrameReader()
{
    staticMetaObject.invokeMethod(this, "execDestoryImageCineReader", Qt::BlockingQueuedConnection);
    m_Thread->quit();
    m_Thread->wait();
    delete m_Thread;
    m_Thread = NULL;

    if (m_ChisonContext != NULL)
    {
        Util::SafeDeletePtr(m_ChisonContext);
    }

    if (m_ImageSaveHelper != NULL)
    {
        Util::SafeDeletePtr(m_ImageSaveHelper);
    }
}

BufferTypeEnum::BufferType CineFrameReader::bufferType() const
{
    return BufferTypeEnum::LineImage;
}

int CineFrameReader::startIndex(bool toShowValue, const int layoutIndex) const
{
    Q_UNUSED(toShowValue);
    Q_UNUSED(layoutIndex);
    return -1;
}

void CineFrameReader::setStartIndex(const int startIndex, const int layoutIndex)
{
    Q_UNUSED(startIndex);
    Q_UNUSED(layoutIndex);
}

int CineFrameReader::endIndex(bool toShowValue, const int layoutIndex) const
{
    Q_UNUSED(toShowValue);
    Q_UNUSED(layoutIndex);
    return -1;
}

void CineFrameReader::setEndIndex(const int endIndex, const int layoutIndex)
{
    Q_UNUSED(endIndex);
    Q_UNUSED(layoutIndex);
}

int CineFrameReader::currentIndex(bool toShowValue, const int layoutIndex) const
{
    Q_UNUSED(toShowValue);
    Q_UNUSED(layoutIndex);
    return -1;
}

void CineFrameReader::setCurrentIndex(const int currentIndex, const int layoutIndex, bool needUpdateFreezeBar)
{
    Q_UNUSED(currentIndex);
    Q_UNUSED(layoutIndex);
}

LoadErrorCode CineFrameReader::load(const QString& filePath, const int layoutIndex)
{
    Q_UNUSED(filePath);
    Q_UNUSED(layoutIndex);
    return LoadErrorCode::Success;
}

bool CineFrameReader::checkLoadFilesValidity(const QString& filePath, const int layoutIndex)
{
    Q_UNUSED(filePath);
    Q_UNUSED(layoutIndex);
    return false;
}

void CineFrameReader::removeAll(const int layoutIndex, bool refresh)
{
    Q_UNUSED(layoutIndex);
    Q_UNUSED(refresh);
}

SonoParameters* CineFrameReader::getSonoParametersByLayoutIndex(const int layoutIndex) const
{
    if (isLayoutIndexValid(layoutIndex))
    {
        if (m_SonoBuffersStorer->activeIndex() == layoutIndex)
        {
            return m_ChisonContext->lineBuffer()->getSonoParametersByLayoutIndex(0);
        }
        else if (m_SonoBufferStorers.contains(layoutIndex))
        {
            return m_SonoBufferStorers.value(layoutIndex)->sonoParameters();
        }
        else
        {
            return NULL;
        }
    }
    else
    {
        return m_SonoParameters;
    }
}

void CineFrameReader::execDestoryImageCineReader()
{
    disconnect(this);
    delete m_ChisonContext;
    m_ChisonContext = NULL;
    delete m_SonoParameters;
    m_SonoParameters = NULL;
    delete m_SonoBuffersStorer;
    m_SonoBuffersStorer = NULL;
    qDeleteAll(m_SonoBufferStorers.values());
    m_SonoBufferStorers.clear();
    delete m_ImagePainterRender;
    m_ImagePainterRender = NULL;
    delete m_ImagePainter;
    m_ImagePainter = NULL;
    delete m_Image;
    m_Image = NULL;
}

void CineFrameReader::initImageCineReader(BackgroundOverlayRender* render)
{
    m_Render = render;
    staticMetaObject.invokeMethod(this, "execInitImageCineReader", Qt::BlockingQueuedConnection);
}
void CineFrameReader::resetRelatedSize()
{
    m_SonoParameters->setPV(BFPNames::RenderWidgetSizeStr, m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize());
    ImageRenderLayoutRects layout;
    layout.setSonoParameters(m_SonoParameters);
    layout.initialize();
    int displayFormat = layout.getDisplayFormat((SystemScanMode)m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
    layout.updateRenderLayoutInfos(displayFormat);
    m_SonoParameters->setPV(BFPNames::LayoutBImageSizeStr, layout.getLayoutBImageSize());
    m_SonoParameters->setPV(BFPNames::BImageSizeStr, layout.getBImageSize());
    m_SonoParameters->setPV(BFPNames::DSCImageRectsStr, layout.getDSCImageRects());

    int layoutNums = m_SonoParameters->pIV(BFPNames::LayoutStr);
    for (int i = 0; i < layoutNums; i++)
    {
        SonoParameters* modifySonoParameters = getSonoParametersByLayoutIndex(i);
        if (modifySonoParameters != NULL)
        {
            modifySonoParameters->setPV(BFPNames::RenderWidgetSizeStr,
                                        m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize());
        }
    }
}

void CineFrameReader::superimposeGlyphs(uchar* imageBuffer, QImage::Format rgbformat)
{
    QImage canvas(m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize(), QImage::Format_RGB888);

    // set background not blend anything,but a red component for debug purpose
    // none-filling image area should has alpha-red
    // save canvas to png can verify result
    canvas.fill(QColor(0, 0, 0, 255));

    QPainter painter(&canvas);
    m_Render->pimplGlyphsWidget()->overlay().scene()->render(&painter);

    QImage srcImage(imageBuffer, imageWidth(), imageHeight(), rgbformat);
    QColor glyphsColor;
    for (int x = 0; x < srcImage.width(); ++x)
    {
        for (int y = 0; y < srcImage.height(); ++y)
        {
            glyphsColor = QColor(canvas.pixel(x, y));
            if (glyphsColor.red() != 0 || glyphsColor.blue() != 0 || glyphsColor.green() != 0)
            {
                srcImage.setPixel(x, y, glyphsColor.rgb());
            }
        }
    }
}

void CineFrameReader::execInitImageCineReader()
{
    ImageScale is;
    ImageRenderLayoutInfo irl;
    if (m_IsOutputGray)
    {
        m_ChisonContext = new ChisonUltrasoundContext(m_ImageSaveHelper, ChisonUltrasound::ChisonUltrasound_OutputGray);
    }
    else
    {
        m_ChisonContext =
            new ChisonUltrasoundContext(m_ImageSaveHelper, ChisonUltrasound::ChisonUltrasound_AVITransform);
    }
    connect(m_ChisonContext, SIGNAL(activeLayoutSonoParametersChanged(SonoParameters*)), this,
            SIGNAL(activeLayoutSonoParametersChanged(SonoParameters*)), Qt::DirectConnection);

    createSonoparameters();

    QFile file(m_SonoParameterFilePath);
    if (file.exists() && file.open(QFile::ReadOnly))
    {
        QDataStream dataStream(&file);
        if (!m_SonoParameters->load(dataStream))
        {
#ifdef WIN32
#else
            qDebug() << PRETTY_FUNCTION << "Can not load sonoparameters";
#endif
            return;
        }
    }

    m_SonoBuffersStorer = new SonoBuffersStorer();
    if (!m_SonoBuffersStorer->load(m_CineFilePath))
    {
#ifdef WIN32
#else
        qDebug() << PRETTY_FUNCTION << "Can not load Golbal FrameInfo";
#endif
        return;
    }

    m_ChisonContext->setSonoParameters(m_SonoParameters);
    m_ChisonContext->start();

    //[Apple][BUG:57372]:【3D
    // UI】发送到U盘，Task里很容易出Fail，但是U盘实际会收到这些病例图片。且U盘的病例里面，会出现很多空文件夹。
    // 1、解决方案
    // mov2mpg进程初始化的时候，pipeline创建完了，再load linebuffer，
    //此处将原来 CineFrameReader::execInitImageCineReader 函数尾的sleep放到ChisonUltrasoundContext::start函数调用之后。
    // 2、影响范围
    // mov2mpg使用
    // 3、Test Case
    // mov2mpg使用
    Util::sleep(1);

    m_ChisonContext->lineBuffer()->load(m_CineFilePath, m_SonoBuffersStorer->activeIndex());

    connect(m_ChisonContext, SIGNAL(newImage(ImageEventArgs*)), this, SLOT(onNewImage(ImageEventArgs*)),
            Qt::DirectConnection);

    for (int i = 0; i < m_SonoBuffersStorer->layout(); i++)
    {
        // m_SonoBuffersStorer主要用作非激活区域的静态图像刷新，
        //在单个sonobuffer存在多个render的情况下，需要回调，用作刷新
        if (i != m_SonoBuffersStorer->activeIndex() || m_ChisonContext->existUnActiveRender(i))
        {
            SonoBufferStorer* bufferStorer = new SonoBufferStorer();
            QString loadFilePath = QString("%1/%2").arg(m_CineFilePath).arg(i);
            QDir dir(loadFilePath);
            if (dir.exists())
            {
                ByteBuffer byteBuffer;
                if (bufferStorer->load(loadFilePath, byteBuffer, m_SonoParameters) != LoadErrorCode::Success)
                {
                    return;
                }
                m_SonoBufferStorers.insert(i, bufferStorer);
            }
        }
    }
    this->resetRelatedSize();
    if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeE)
    {
        m_ImageCount = 1;
    }
    else
    {
        m_ImageCount = m_SonoParameters->pV(BFPNames::ImageRectsStr).toList().size();
        if (m_SonoParameters->pBV(BFPNames::HasAutoEFResultStr))
        {
            m_ImageCount = 1;
        }
    }

    QGLContext* context = NULL;
#if (QT_VERSION < QT_VERSION_CHECK(5, 0, 0))
    context = new QGLContext(QGLFormat());
#endif
    EcgGlyphRender* ecgRender = new EcgGlyphRender(this, context);
    ecgRender->setOverLay(&m_Render->pimplGlyphsWidget()->overlay());
    ecgRender->setEndLength(10.0f);
    m_Renders[ImageEventArgs::ImageECG] = ecgRender;

    EcgSettings::instance().setRLineVisible(false);
    foreach (GLRender* render, m_Renders)
    {
        if (render != NULL)
        {
            render->init();
        }
    }

    ecgRender->setSonoParameters(m_SonoParameters);
    bool isECGOn = m_SonoParameters->pBV(BFPNames::ECGEnStr);
    ecgRender->setActive(isECGOn);
    ecgRender->setWholeImageRect(QRect(QPoint(0, 0), m_SonoParameters->pV(BFPNames::RenderImageSizeStr).toSize()));
    if (isECGOn)
    {
        ecgRender->reorganizeShaders();
        if (isECGOn)
        {
            EcgGlyphRender* ecgRender = static_cast<EcgGlyphRender*>(glRender(ImageEventArgs::ImageECG));
            if (ecgRender != NULL)
            {
                ecgRender->updateECGPosLevel(m_SonoParameters->pIV(BFPNames::ActiveBStr),
                                             m_SonoParameters->pMax(BFPNames::ECGPosStr) -
                                                 m_SonoParameters->pIV(BFPNames::ECGPosStr));
                ecgRender->ecgInvertChanged(m_SonoParameters->pIV(BFPNames::ActiveBStr),
                                            m_SonoParameters->pIV(BFPNames::ECGInvertStr));
            }
        }
        // onECGPosChanged(m_SonoParameters->pIV(BFPNames::ECGPosStr));
        ecgRender->resetEcgData();
        ecgRender->resetProcessData();
    }

    m_ImagePainterRender = new ImagePainterRender(NULL);
    m_ImagePainterRender->setBufferManager(this);
    m_ImagePainterRender->setSonoParameters(m_SonoParameters);

    m_Image = new QImage(m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize(), m_ImagePainterRender->ImageFormat());
    m_ImagePainter = new QPainter;
    memset(m_Image->bits(), 0, m_Image->sizeInBytes());
    // TODO!!!! pictureLoad
    foreach (SonoBufferStorer* bufferStorer, m_SonoBufferStorers.values())
    {
        if ((bufferStorer != NULL) && (!bufferStorer->images().isEmpty()))
        {
            ImageEventArgs* data = bufferStorer->images().values().first();
            data->setLayout(m_SonoParameters->pIV(BFPNames::LayoutStr));
            data->setSystemScanMode(m_SonoParameters->pIV(BFPNames::SystemScanModeStr));
            data->setIndex(m_SonoBufferStorers.key(bufferStorer));
            m_ImagePainterRender->setData(data);
            m_ImagePainter->begin(m_Image);
            m_ImagePainterRender->draw(m_ImagePainter);
            m_ImagePainter->end();
        }
    }

    // 等待gstreamer创建pipeline
    //    Util::sleep(1);
}

int CineFrameReader::layout() const
{
    return m_SonoBuffersStorer->layout();
}

int CineFrameReader::activeLayoutIndex() const
{
    return m_SonoBuffersStorer->activeIndex();
}

int CineFrameReader::dataType() const
{
    return m_DataType;
}
int CineFrameReader::imageSize() const
{
    return imageWidth() * imageHeight() * m_BytesCountPerPixel;
}
int CineFrameReader::imageWidth() const
{
    QSize imageSize = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    return imageSize.width();
}

int CineFrameReader::imageHeight() const
{
    QSize imageSize = m_SonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    return imageSize.height();
}

int CineFrameReader::fps() const
{
    return m_ChisonContext->lineBuffer()->defaultFps();
}

int CineFrameReader::frameCount(const int layoutIndex) const
{
    int frameCount = 0;

    if ((layoutIndex < 0) || (layoutIndex == m_SonoBuffersStorer->activeIndex()))
    {
        frameCount = m_ChisonContext->lineBuffer()->frameCount();
    }
    else if (m_SonoBufferStorers.contains(layoutIndex))
    {
        frameCount = (m_SonoBufferStorers[layoutIndex]->images().count() > 0) ? 1 : 0;
    }
    else
    {
        // Do noting
    }

    return frameCount;
}

void CineFrameReader::setColorMap(uchar* map1, uchar* map2)
{
    m_ChisonContext->setColorMap(map1, map2);
}
QImage::Format CineFrameReader::outImageFormat() const
{
    return m_OutImageFormat;
}
bool CineFrameReader::readFrameByIndex(const int index, uchar* imageData)
{
#ifdef WIN32
#else
    qDebug() << PRETTY_FUNCTION << index;
#endif
    int imageSize = this->imageSize();
    m_ImageBuffer = imageData;
    if (m_LastReadIndex == index)
    {
        copyToImageDataByOutFormat(imageData, imageSize);
        m_ImageBuffer = NULL;
        return true;
    }
    m_LastReadIndex = index;
    m_ImageReceiveCount = 0;
    m_ChisonContext->lineBuffer()->requestFlush(0, index);

    m_FrameReaderLocker.lock();
    if (!m_FrameReaderWaiter.wait(&m_FrameReaderLocker, 30000))
    {
        if (m_ImageReceiveCount == 0)
        {
#ifdef WIN32
#else
            qDebug() << PRETTY_FUNCTION << " QWaitCondition time out";
#endif
            m_FrameReaderLocker.unlock();
            return false;
        }
    }
#ifdef WIN32
#else
    qDebug() << PRETTY_FUNCTION << " QWaitCondition Get Image";
#endif
    copyToImageDataByOutFormat(imageData, imageSize);
    superimposeGlyphs(imageData, m_OutImageFormat);
    m_ImageBuffer = NULL;
    m_FrameReaderLocker.unlock();
    Util::usleep(100);
    return true;
}

void CineFrameReader::copyToImageDataByOutFormat(uchar* destImageData, int imageSize)
{
    memset(m_ImageBuffer, 0, imageSize);
    if (m_BytesCountPerPixel != m_Image->sizeInBytes())
    {
        const QImage& image = m_Image->convertToFormat(m_OutImageFormat);
        memcpy(destImageData, image.bits(), imageSize);
    }
    else
    {
        memcpy(destImageData, m_Image->bits(), imageSize);
    }
}

void CineFrameReader::createSonoparameters()
{
    SonoParaFileProvider provider;
    QString path = provider.getResourceFile(AppSetting::model().toStdString().c_str());
    XmlBFParameterLoader* bfparameterLoader = new XmlBFParameterLoader(path);

    BFParameters bfPara = bfparameterLoader->build();
    m_SonoParameters = bfPara.m_SonoParameters;
    m_Render->setSize(m_SonoParameters);

    delete bfparameterLoader;
}

SonoParameters* CineFrameReader::getSonoParameters() const
{
    return m_SonoParameters;
}

ILineBuffer* CineFrameReader::lineBuffer() const
{
    return m_ChisonContext->lineBuffer();
}

GLRender* CineFrameReader::glRender(ImageEventArgs::ImageType type)
{
    if (m_Renders.contains(type))
    {
        return m_Renders[type];
    }

    return NULL;
}

void CineFrameReader::onNewImage(ImageEventArgs* imageEventArgs)
{
    if (m_ImageBuffer != NULL)
    {
        m_PainterLocker.lock();
        imageEventArgs->setIndex(m_SonoBuffersStorer->activeIndex());
        GLRender* render = NULL;
        switch (imageEventArgs->imageType())
        {
        case ImageEventArgs::ImageECG:
        {
            render = glRender(ImageEventArgs::ImageECG);
            if (render != NULL)
            {
                render->setData(imageEventArgs);
            }
        }
        break;
        default:
        {
            if (m_ImagePainterRender != NULL)
            {
                m_ImagePainterRender->setData(imageEventArgs);
            }
        }
        break;
        }
        // m_ImagePainterRender->setData(imageEventArgs);
        m_ImagePainter->begin(m_Image);
        m_ImagePainterRender->draw(m_ImagePainter);
        foreach (GLRender* render, m_Renders)
        {
            if (render != NULL)
            {
                render->render();
            }
        }
        m_ImagePainter->end();
        m_ImageReceiveCount++;
        m_PainterLocker.unlock();
#ifdef WIN32
#else
        qDebug() << PRETTY_FUNCTION << " ActiveIndex:" << m_SonoBuffersStorer->activeIndex()
                 << " ImageType:" << imageEventArgs->imageType() << " FrameIndex:" << imageEventArgs->frameIndex()
                 << " Width:" << imageEventArgs->width() << " Height:" << imageEventArgs->height()
                 << " Layout:" << m_SonoBuffersStorer->layout() << " m_ImageReceiveCount:" << m_ImageReceiveCount
                 << " m_ImageCount:" << m_ImageCount;
#endif
        if (m_SonoBuffersStorer->layout() == Layout_1x1)
        {
            if (m_ImageReceiveCount >= m_ImageCount)
            {
                m_FrameReaderWaiter.wakeAll();
            }
        }
        else if (m_SonoBuffersStorer->layout() == Layout_1x2)
        {
            m_FrameReaderWaiter.wakeAll();
        }
        else if (m_SonoBuffersStorer->layout() == Layout_2x2)
        {
            m_FrameReaderWaiter.wakeAll();
        }
        else
        {
#ifdef WIN32
#else
            qDebug() << PRETTY_FUNCTION << " unknown layout :" << m_SonoBuffersStorer->layout();
#endif
        }
    }
}
