#ifndef LINECOMPRESSEDPIXELDATAFILE_H
#define LINECOMPRESSEDPIXELDATAFILE_H

#include "custompixeldata_global.h"
#include "linepixeldatafile.h"

class CUSTOMPIXELDATASHARED_EXPORT LineCompressedPixelDataFile : public LinePixelDataFile
{
public:
    LineCompressedPixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    ~LineCompressedPixelDataFile();

    virtual bool isValid() const;
    virtual const quint8* framePixelData(int curIndex);
    virtual const quint8* pixelData() const;

private:
    quint32 m_ImageSize;
    quint16 m_SamplesPerPixel;
    quint8* m_ImageBuffer;
};

#endif // LINECOMPRESSEDPIXELDATAFILE_H
