#include "linepixeldatafile.h"
#include "ipixeldatafilehead.h"
#include "iglyphssuperimpose.h"
#include <string.h>
#include <stdio.h>
#include <sys/stat.h>
#include <new>
#include <QDebug>

LinePixelDataFile::LinePixelDataFile(const IPixelDataFileHead& fileHead, bool opt_verbose, bool opt_debug)
    : m_opt_verbose(opt_verbose)
    , m_opt_debug(opt_debug)
    , m_FileHead(fileHead)
    , m_NumberOfFrames(fileHead.numberOfReadFrames())
    , m_NumberOfFramesReaded(0)
{
}

LinePixelDataFile::~LinePixelDataFile()
{
}

const IPixelDataFileHead& LinePixelDataFile::fileHead() const
{
    return m_FileHead;
}

bool LinePixelDataFile::isValid() const
{
    return m_FileHead.isValid();
}

const quint8* LinePixelDataFile::framePixelData(int curIndex)
{
    return NULL;
}

quint16 LinePixelDataFile::bitsStored() const
{
    return 8;
}

quint16 LinePixelDataFile::bitsAllocated() const
{
    return 8;
}

quint16 LinePixelDataFile::bytesAllocated() const
{
    return 1;
}

quint16 LinePixelDataFile::samplesPerPixel() const
{
    return m_FileHead.samplesPerPixel();
}

quint16 LinePixelDataFile::planarConfiguration() const
{
    return 0;
}

const char* LinePixelDataFile::photometricInterpretation() const
{
    return PhotometricInterpretationStr[m_FileHead.photometricInterpretationType()];
}

quint16 LinePixelDataFile::columns() const
{
    return m_FileHead.columns();
}

quint16 LinePixelDataFile::rows() const
{
    return m_FileHead.rows();
}

quint32 LinePixelDataFile::numberOfFrames() const
{
    return m_NumberOfFrames;
}

quint32 LinePixelDataFile::numberOfFramesReaded() const
{
    return m_NumberOfFramesReaded;
}

quint32 LinePixelDataFile::pixelSizeReaded() const
{
    return m_FileHead.imageSize() * numberOfFramesReaded();
}

quint8 LinePixelDataFile::colorspace() const
{
    return m_FileHead.colorspace();
}

quint32 LinePixelDataFile::readFrame(int index, void* ptr, quint32 size) const
{
    //以下使用最近邻抽帧的方法
    float fIndex = index * m_FileHead.sampleInterval();
    int pre = fIndex;
    int next = pre + 1;
    //获取最近邻index
    int readIndex = 0;
    if (fIndex >= (pre + 0.5f))
    {
        readIndex = (int)next;
    }
    else
    {
        readIndex = (int)pre;
    }
#ifdef WIN32
#else
    qDebug() << PRETTY_FUNCTION << " index:" << index << " readIndex:" << readIndex;
#endif
    if (m_FileHead.readFrame(readIndex, (uchar*)ptr, size))
    {
        return size;
    }
    else
    {
        return 0;
    }
}

// static int fileSize(const char* filename)
//{
//    struct stat statbuf;
//    if(stat(filename, &statbuf) == 0)
//    {
//        return statbuf.st_size;
//    }
//    else
//    {
//        perror("get file size failed");
//        return 0;
//    }
//}
