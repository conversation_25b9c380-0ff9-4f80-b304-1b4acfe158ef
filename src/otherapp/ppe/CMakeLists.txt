include_depends(usf.com.ui.controls)


set(src
        main.cpp
)

add_executable_qt(PPE ${src})

target_link_libraries(PPE usf.com.ui.controls)

add_custom_command(TARGET PPE
   POST_BUILD
   COMMAND cmake -DsourceDirector=${PROJECT_SOURCE_DIR}/src/app/shell/${BUILD_SYS_TAR_ARC_NAME}/ -DtargetDirector=${CMAKE_RUNTIME_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
   COMMAND cmake -DsourceDirector=${THIRDPARTYLIB_PATH}/otherapp/ -DtargetDirector=${CMAKE_RUNTIME_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
   )
