#include <QApplication>
#include "sninput.h"
#include "keyboardcontrol.h"
#include "qssloader.h"
#include "traceballconfig.h"
#include "deviceutil.h"
#include "applicationinfo.h"
#include "systemexitobj.h"

int main(int argc, char* argv[])
{
    QApplication a(argc, argv);
    // PPE软件调用update_app.exe程序，交给了守护进程去执行
    //所以PPE软件需要退出
    QObject::connect(&SystemExitObj::instance(), &SystemExitObj::beginExit, &a, &QApplication::exit,
                     Qt::QueuedConnection);
    QssLoader::loadCurrentModelQss();

    QString traceballVendor = TraceballConfig::instance().getTraceballVendor();
    TraceballParam param = TraceballConfig::instance().curTraceballParam();
    ApplicationInfo::instance().setTraceballSensitivity(traceballVendor, param.accel, param.accel_limit);
    DeviceUtil::enableTouchPadTapping(traceballVendor);
    KeyboardControl::instance().init();
    SNInput snInput(false);
    return a.exec();
}
