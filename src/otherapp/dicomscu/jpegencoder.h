#ifndef JPEGENCODER_H
#define JPEGENCODER_H

#include "basepixeldataencoder.h"

class IPixelDataFileHead;
class JpegCodecEncoder;

class JpegEncoder : public BasePixelDataEncoder
{
public:
    JpegEncoder(const IPixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    ~JpegEncoder();
    virtual E_TransferSyntax supportedTransferSyntax() const;
    virtual OFCondition encode(DcmItem* dataset) const;

protected:
    virtual OFCondition insertImageType(DcmItem* dataset) const;

private:
    bool m_Verbose;
    bool m_Lossless;
    int m_Quality;
    Uint8 m_colorspace;
    JpegCodecEncoder* m_Encoder;
};

#endif // JPEGENCODER_H
