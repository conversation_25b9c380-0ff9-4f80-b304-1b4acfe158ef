#include "jpegencoder.h"
#include "dcmtk/dcmdata/dcitem.h"
#include "linecompressedpixeldatafile.h"
#include "dcmtk/dcmjpeg/djrplol.h"  /* for DJ_RPLossless */
#include "dcmtk/dcmjpeg/djrploss.h" /* for DJ_RPLossy */
#include "dcmtk/dcmjpeg/djcparam.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/dcmdata/dcdeftag.h"
#include "jpegencoderp14sv1.h"
#include "jpegencoderbaseline.h"
#include "ipixeldatafilehead.h"

static DJCodecParameter* registerCodecs(
    E_CompressionColorSpaceConversion pCompressionCSConversion, E_UIDCreation pCreateSOPInstanceUID, OFBool pVerbose,
    OFBool pOptimizeHuffman, int pSmoothingFactor, int pForcedBitDepth, Uint32 pFragmentSize, OFBool pCreateOffsetTable,
    E_SubSampling pSampleFactors, OFBool pWriteYBR422, OFBool pConvertToSC, unsigned long pWindowType,
    unsigned long pWindowParameter, double pVoiCenter, double pVoiWidth, unsigned long pRoiLeft, unsigned long pRoiTop,
    unsigned long pRoiWidth, unsigned long pRoiHeight, OFBool pUsePixelValues, OFBool pUseModalityRescale,
    OFBool pAcceptWrongPaletteTags, OFBool pAcrNemaCompatibility, OFBool pRealLossless)
{
    return new DJCodecParameter(pCompressionCSConversion,
                                EDC_photometricInterpretation, // not relevant, used for decompression only
                                pCreateSOPInstanceUID,
                                EPC_default, // not relevant, used for decompression only
                                pVerbose, pOptimizeHuffman, pSmoothingFactor, pForcedBitDepth, pFragmentSize,
                                pCreateOffsetTable, pSampleFactors, pWriteYBR422, pConvertToSC, pWindowType,
                                pWindowParameter, pVoiCenter, pVoiWidth, pRoiLeft, pRoiTop, pRoiWidth, pRoiHeight,
                                pUsePixelValues, pUseModalityRescale, pAcceptWrongPaletteTags, pAcrNemaCompatibility,
                                pRealLossless);
}

JpegEncoder::JpegEncoder(const IPixelDataFileHead& fileHead, bool opt_verbose, bool opt_debug)
    : BasePixelDataEncoder(new LineCompressedPixelDataFile(fileHead, opt_verbose, opt_debug))
    , m_Verbose(opt_verbose)
    , m_Lossless(fileHead.quality() == 0)
    , m_Quality(fileHead.quality())
    , m_colorspace(fileHead.colorspace())
    , m_Encoder(NULL)
{
    if (m_Lossless)
    {
        m_Encoder = new JpegEncoderP14SV1();
    }
    else
    {
        m_Encoder = new JpegEncoderBaseline();
    }
}

JpegEncoder::~JpegEncoder()
{
    if (m_Encoder != NULL)
    {
        delete m_Encoder;
        m_Encoder = NULL;
    }

    freeMem();
}

E_TransferSyntax JpegEncoder::supportedTransferSyntax() const
{
    return m_Encoder->supportedTransferSyntax();
}

OFCondition JpegEncoder::encode(DcmItem* dataset) const
{
    if (m_Encoder == NULL || m_PixelDataFile == NULL)
    {
        return EC_MemoryExhausted;
    }

    if (!m_PixelDataFile->isValid())
    {
        return EC_IllegalParameter;
    }

    OFBool opt_acceptWrongPaletteTags = OFFalse;
    OFBool opt_acrNemaCompatibility = OFFalse;
    OFBool opt_verbose = (OFBool)m_Verbose;
    // JPEG options
    E_TransferSyntax opt_oxfer = supportedTransferSyntax();
    OFCmdUnsignedInt opt_selection_value = 6;
    OFCmdUnsignedInt opt_point_transform = 0;
    OFBool opt_huffmanOptimize = OFFalse;
    OFCmdUnsignedInt opt_smoothing = 0;
    int opt_compressedBits = 0; // 0=auto, 8/12/16=force
    E_CompressionColorSpaceConversion opt_compCSconversion = (m_colorspace == 2) ? ECC_lossyYCbCr : ECC_lossyRGB;
    E_SubSampling opt_sampleFactors = ESS_444;
    OFBool opt_useYBR422 = OFFalse;
    OFCmdUnsignedInt opt_fragmentSize = 0; // 0=unlimited
    OFBool opt_createOffsetTable = OFTrue;
    int opt_windowType = 0; /* default: no windowing; 1=Wi, 2=Wl, 3=Wm, 4=Wh, 5=Ww, 6=Wn, 7=Wr */
    OFCmdUnsignedInt opt_windowParameter = 0;
    OFCmdFloat opt_windowCenter = 0.0, opt_windowWidth = 0.0;
    E_UIDCreation opt_uidcreation = EUC_never;
    OFBool opt_secondarycapture = OFFalse;
    OFCmdUnsignedInt opt_roiLeft = 0, opt_roiTop = 0, opt_roiWidth = 0, opt_roiHeight = 0;
    OFBool opt_usePixelValues = OFTrue;
    OFBool opt_useModalityRescale = OFFalse;
    OFBool opt_trueLossless = m_Lossless ? OFTrue : OFFalse;

    DJCodecParameter* cp =
        registerCodecs(opt_compCSconversion, opt_uidcreation, opt_verbose, opt_huffmanOptimize, (int)opt_smoothing,
                       opt_compressedBits, (Uint32)opt_fragmentSize, opt_createOffsetTable, opt_sampleFactors,
                       opt_useYBR422, opt_secondarycapture, opt_windowType, opt_windowParameter, opt_windowCenter,
                       opt_windowWidth, opt_roiLeft, opt_roiTop, opt_roiWidth, opt_roiHeight, opt_usePixelValues,
                       opt_useModalityRescale, opt_acceptWrongPaletteTags, opt_acrNemaCompatibility, opt_trueLossless);

    class AutoPointer
    {
    public:
        AutoPointer(DJCodecParameter* cp)
            : m_Cp(cp)
        {
        }
        ~AutoPointer()
        {
            delete m_Cp;
        }

    private:
        DJCodecParameter* m_Cp;
    } cpPointer(cp);

    // create representation parameters for lossy and lossless
    DJ_RPLossless rp_lossless((int)opt_selection_value, (int)opt_point_transform);
    DJ_RPLossy rp_lossy((int)m_Quality);

    const DcmRepresentationParameter* rp = &rp_lossy;
    if ((opt_oxfer == EXS_JPEGProcess14SV1TransferSyntax) || (opt_oxfer == EXS_JPEGProcess14TransferSyntax))
        rp = &rp_lossless;

    OFCondition result = m_Encoder->encode(dataset, m_PixelDataFile, rp, cp);
    if (result.good())
    {
        result = insertFrameInfo(dataset, m_PixelDataFile);
    }

    return result;
}

OFCondition JpegEncoder::insertImageType(DcmItem* dataset) const
{
    return dataset->putAndInsertString(DCM_ImageType, "ORIGINAL\\PRIMARY");
}
