#include "timelogger.h"
#include <stdio.h>

TimeLogger::TimeLogger()
{
    gettimeofday(&m_Before, NULL);
}

void TimeLogger::restart()
{
    gettimeofday(&m_Before, NULL);
}

double TimeLogger::logger(char* info, bool output)
{
    gettimeofday(&m_After, NULL);
    long sec = m_After.tv_sec - m_Before.tv_sec;
    double ms = (m_After.tv_usec - m_Before.tv_usec) / 1000.0;
    ms = sec * 1000 + ms;
    if (output && info != NULL)
    {
        printf("%s Time:\t%.3f ms\n\n", info, ms);
    }

    return ms;
}
