#ifndef DCMCMD_H
#define DCMCMD_H

#include "cmdcfg.h"

typedef int (*Apps_Function_TYPE)(int, char**);
typedef int(Main_Function)(int, char**);

typedef struct _App_Dep
{
    const char* name;
    Apps_Function_TYPE app;
} App_Dep;
// extern "C"
//{
#if BUILD_APP_STORE
Main_Function main_storescu;
#endif
#if BUILD_APP_ECHO
Main_Function main_echoscu;
#endif
#if BUILD_APP_DCMCJPEG
Main_Function main_dcmcjpeg;
#endif
#if BUILD_APP_DCMJ2PNM
Main_Function main_dcmj2pnm;
#endif
#if BUILD_APP_FINDSCU
Main_Function main_findscu;
#endif
#if BUILD_APP_DCMPSPRT
Main_Function main_dcmpsprt;
#endif
#if BUILD_APP_DCMPRSCU
Main_Function main_dcmprscu;
#endif
#if BUILD_APP_MKREPORT
Main_Function main_mkreport;
#endif
#if BUILD_APP_MKDCMFILE
Main_Function main_mkdcmfile;
#endif
#ifdef BUILD_APP_DCMSRSCU
Main_Function main_dcmsrscu;
#endif
#ifdef BUILD_APP_MKDCMSR
Main_Function main_mkdcmsr;
#endif
#ifdef BUILD_APP_MOVESCU
Main_Function main_movescu;
#endif
#ifdef BUILD_APP_MPPSSCU
Main_Function main_mppsscu;
#endif
//}
#endif
