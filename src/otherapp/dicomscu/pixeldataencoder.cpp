#include "pixeldataencoder.h"
#include "databasemanager.h"
#include "displaystyleinfoloader.h"
#include "exammodepresethandlerfactory.h"
#include "glyphssuperimpose.h"
#include "jpegencoder.h"
#include "licenseinitializer.h"
#include "multiframepixeldatafilehead.h"
#include "probedatasetfacade.h"
#include "rawdataencoder.h"
#include "renderlayoutconfigureloader.h"
#include "resource.h"
#include "scanmodeinfoconverter.h"
#include "singleframepixeldatafilehead.h"
#include "exammodepresetdatahandlerprovider.h"
#include <QFile>

PixelDataEncoder::PixelDataEncoder(const char* fileName, const char* paletteFileName, const char* sonoparameterFile,
                                   bool isPrinttask, const DcmStorage_Option& gStorageOption, int defFps,
                                   bool opt_verbose, bool opt_debug)
    : m_FileHead(NULL)
    , m_Encoder(NULL)
{
    OutImageTypeEnum::OutImageType imageType = isPrinttask ? OutImageTypeEnum::Gray : OutImageTypeEnum::RGB;

    QString pixeldatafileName = QFile::symLinkTarget(fileName);

    if (pixeldatafileName.isEmpty())
    {
        m_FileHead = new SingleFramePixelDataFileHead(fileName, imageType, gStorageOption, defFps, opt_verbose);
    }
    else
    {
        DataBaseManager dbManager;
        ProbeDataSetFacade probeDataSet;
        ExamModePresetDataHandlerProvider presetDataHandlerProvider;
        LicenseInitializer licenseinitializer;
        licenseinitializer.initialize();
        ScanModeInfoConverter::instance().read(Resource::scanModeInfoName);
        RenderLayoutConfigureLoader::instance().read(Resource::renderLayoutConfigName);
        DisplayStyleInfoLoader::instance().load(Resource::displayFormatName);

        ExamModePresetHandlerFactory::instance()->setDataHandlerProvider(&presetDataHandlerProvider);
        ExamModePresetHandlerFactory::instance()->create(PresetModule::DataHandlerType::DataHandler, &dbManager,
                                                         &probeDataSet);
        IGlyphsSuperimpose* glyphsSuperimpose = new GlyphsSuperimpose(pixeldatafileName, paletteFileName, imageType);
        m_FileHead = new MultiFramePixelDataFileHead(glyphsSuperimpose, gStorageOption, defFps, opt_verbose);
    }

    if (m_FileHead->isNeedCompress())
    {
        m_Encoder = new JpegEncoder(*m_FileHead, opt_verbose, opt_debug);
    }
    else
    {
        m_Encoder = new RawDataEncoder(*m_FileHead, opt_verbose, opt_debug);
    }
}

PixelDataEncoder::~PixelDataEncoder()
{
    if (m_FileHead != NULL)
    {
        delete m_FileHead;
        m_FileHead = NULL;
    }

    if (m_Encoder != NULL)
    {
        delete m_Encoder;
        m_Encoder = NULL;
    }
}

Uint32 PixelDataEncoder::numberOfFrames() const
{
    return m_Encoder->numberOfFrames();
}

Uint32 PixelDataEncoder::pixelSize() const
{
    return m_Encoder->pixelSize();
}

E_TransferSyntax PixelDataEncoder::supportedTransferSyntax() const
{
    return m_Encoder->supportedTransferSyntax();
}

OFCondition PixelDataEncoder::encode(DcmItem* dataset) const
{
    return m_Encoder->encode(dataset);
}
