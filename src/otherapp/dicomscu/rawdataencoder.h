#ifndef RAWDATAENCODER_H
#define RAWDATAENCODER_H

#include "basepixeldataencoder.h"

class IPixelDataFileHead;
class UncompressedPixelDataFile;

class RawDataEncoder : public BasePixelDataEncoder
{
public:
    RawDataEncoder(const IPixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    ~RawDataEncoder();
    virtual E_TransferSyntax supportedTransferSyntax() const;
    virtual OFCondition encode(DcmItem* dataset) const;

protected:
    virtual OFCondition insertImageType(DcmItem* dataset) const;

private:
};

#endif // RAWDATAENCODER_H
