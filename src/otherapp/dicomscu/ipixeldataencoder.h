#ifndef IPIXELDATAENCODER_H
#define IPIXELDATAENCODER_H

#include "dcmtk/dcmdata/dcxfer.h"
#include "dcmtk/config/osconfig.h"
#include "dcmtk/ofstd/ofcond.h"

class DcmItem;

class IPixelDataEncoder
{
public:
    virtual ~IPixelDataEncoder();
    /**
     * @brief numberOfFrames 做完 encode 后才有合法值
     * @return
     */
    virtual Uint32 numberOfFrames() const = 0;
    /**
     * @brief pixelSize 做完 encode 后才有合法值
     * @return
     */
    virtual Uint32 pixelSize() const = 0;
    virtual E_TransferSyntax supportedTransferSyntax() const = 0;
    virtual OFCondition encode(DcmItem* dataset) const = 0;
};

#endif // IPIXELDATAENCODER_H
