#include "pixeldatafilehead.h"
#include "dcmutilitydef.h"
#include <string.h>

PixelDataFileHead::PixelDataFileHead(const char* fileName, PhotometricInterpretationType rawInterpretationType,
                                     PhotometricInterpretationType screenInterpretationType,
                                     const DcmStorage_Option& gStorageOption, int defFps, bool opt_verbose)
    : m_pFile(NULL)
    , m_Fps(0)
    , m_InterpretationType(rgb)
    , m_SamplesPerPixel(3)
    , m_FrameSize(0)
    , m_ImageSize(0)
    , m_NumberOfReadFrames(0)
    , m_SampleInterval(0.0f)
    , m_StorageOption(gStorageOption)
{
    memset(&m_FileHead, 0, sizeof(FileStruct_head));

    if (fileName == NULL)
    {
        printf("PixelDataFileHead:fileName is NULL.\n");
        return;
    }

    if ((m_pFile = fopen(fileName, "rb")) == NULL)
    {
        printf("PixelDataFileHead: open file %s failed.\n", fileName);
    }
    else
    {
        if (fread(&m_FileHead, sizeof(FileStruct_head), 1, m_pFile) == 1)
        {
            // m_FileHead.DataType
            // 0: .img or .cin raw data
            // -1 : monochrome or rgb bmp data
            m_InterpretationType = (m_FileHead.DataType >= 0) ? rawInterpretationType : screenInterpretationType;
            m_SamplesPerPixel = (m_InterpretationType != rgb) ? 1 : 3;
            m_ImageSize = m_FileHead.Width * m_FileHead.Height * m_SamplesPerPixel;
            m_FrameSize = (m_FileHead.DataType >= 0) ? (m_FileHead.Width * m_FileHead.Height) : m_ImageSize;

            //如果filehead.fps是有效值,重新调整s_DcmStorage_Option的值
            if (isValidFps())
            {
                if (!isNeedSample())
                {
                    //不采样,则保持两个framerate都是filehead.fps
                    m_StorageOption.DcmFramerate = m_FileHead.fps;
                }

                m_StorageOption.CineFramerate = m_FileHead.fps;

                //                if(m_StorageOption.CineFramerate < m_StorageOption.DcmFramerate)
                //                {
                //                    m_StorageOption.DcmFramerate = m_StorageOption.CineFramerate;
                //                }
            }

            m_NumberOfReadFrames = m_FileHead.Frame;
            m_SampleInterval = 1.0f;
            if (m_NumberOfReadFrames > 1 && isNeedSample())
            {
                m_SampleInterval = (float)m_StorageOption.CineFramerate / m_StorageOption.DcmFramerate;
                m_NumberOfReadFrames =
                    m_NumberOfReadFrames * m_StorageOption.DcmFramerate / m_StorageOption.CineFramerate;
                if (m_NumberOfReadFrames == 0)
                {
                    m_NumberOfReadFrames = 1;
                }
            }

            m_Fps = defFps;
            if (isNeedSample() || (m_StorageOption.DcmFramerate != m_Fps && isValidFps()))
            {
                m_Fps = m_StorageOption.DcmFramerate;
            }

            if (opt_verbose)
            {
                printf("DCM fps %d CineFps:%d DcmFps:%d\n", m_Fps, m_StorageOption.CineFramerate,
                       m_StorageOption.DcmFramerate);
            }
        }
        else
        {
            printf("File Pixel Data Getter: read head failed.\n");
            fclose(m_pFile);
            m_pFile = NULL;
        }
    }

    if (opt_verbose)
    {
        printf("PixelDataFileHead: isValid %d, fps %d, isNeedPalette %d,\n\
                 photoType %d, samplesPerPixel %d,\n\
                 columns %d, rows %d, frameSize %u,\n\
                 imageSize %u, numberOfFrames %u,\n\
                 numberOfReadFrames %u, isNeedCompress %d,\n\
                 isNeedSample %d, sampleInterval %f.\n",
               isValid(), fps(), isNeedPalette(), photometricInterpretationType(), samplesPerPixel(), columns(), rows(),
               frameSize(), imageSize(), numberOfFrames(), numberOfReadFrames(), isNeedCompress(), isNeedSample(),
               sampleInterval());
    }
}

PixelDataFileHead::~PixelDataFileHead()
{
    if (m_pFile != NULL)
    {
        fclose(m_pFile);
        m_pFile = NULL;
    }
}

bool PixelDataFileHead::isValid() const
{
    return m_pFile != NULL && m_FrameSize > 0 && m_ImageSize > 0 && numberOfReadFrames() > 0 &&
           m_NumberOfReadFrames > 0;
}

FILE* PixelDataFileHead::pFile() const
{
    return m_pFile;
}

Uint16 PixelDataFileHead::fps() const
{
    return m_Fps;
}

bool PixelDataFileHead::isNeedPalette() const
{
    return (m_FileHead.DataType >= 0 && (m_InterpretationType == rgb || m_InterpretationType == palette_color));
}

bool PixelDataFileHead::isMovie() const
{
    return (m_FileHead.DataType >= 0);
}

PhotometricInterpretationType PixelDataFileHead::photometricInterpretationType() const
{
    return m_InterpretationType;
}

Uint16 PixelDataFileHead::samplesPerPixel() const
{
    return m_SamplesPerPixel;
}

Uint16 PixelDataFileHead::columns() const
{
    return m_FileHead.Width;
}

Uint16 PixelDataFileHead::rows() const
{
    return m_FileHead.Height;
}

Uint32 PixelDataFileHead::bytesPerLine() const
{
    return m_FileHead.Width * m_SamplesPerPixel;
}

Uint32 PixelDataFileHead::frameSize() const
{
    return m_FrameSize;
}

Uint32 PixelDataFileHead::imageSize() const
{
    return m_ImageSize;
}

Uint32 PixelDataFileHead::numberOfFrames() const
{
    return m_FileHead.Frame;
}

Uint32 PixelDataFileHead::numberOfReadFrames() const
{
    return m_NumberOfReadFrames;
}

Uint8 PixelDataFileHead::quality() const
{
    return m_StorageOption.CompressionRatio;
}

Uint8 PixelDataFileHead::colorspace() const
{
    return m_StorageOption.CompressionMode;
}

bool PixelDataFileHead::isNeedCompress() const
{
    return IsNeedCompress((PDcmStorage_Option)&m_StorageOption);
}

bool PixelDataFileHead::isNeedSample() const
{
    return m_StorageOption.DcmFramerate > 0 && m_StorageOption.DcmFramerate != m_StorageOption.CineFramerate;
}

float PixelDataFileHead::sampleInterval() const
{
    return m_SampleInterval;
}

bool PixelDataFileHead::isValidFps() const
{
    return m_FileHead.fps > 0 && m_FileHead.fps < 0XFFFF;
}

const DcmStorage_Option& PixelDataFileHead::storageOption() const
{
    return m_StorageOption;
}
