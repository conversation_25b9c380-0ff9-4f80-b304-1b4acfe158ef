#ifndef MKREPORT_H
#define MKREPORT_H

#include "cmdcfg.h"

typedef struct _DcmSrInfo_tag
{
    char PatientsName[256]; // VR:PN MaxLen:64 dcmtk:// not correct: max length of PN is 3*64+2 = 194 characters (not
                            // bytes!)
    char <PERSON>ientID[64];     // VR:LO MaxLen:64
    char PatientsSex[16];   // VR:CS MaxLen:16
    char PatientsAge[4];    // VR:AS MaxLen:4
    char ReferringPhysicians[64]; // VR:PN MaxLen:64
    char StudyInstanceID[128];
    char SeriesInstanceID[128];
    char History[1024];
    char Diagnosis[1024];
} DcmSrInfo_tag, *PDcmSrInfo_tag;

#define DCMSRINFO_FILE_NAME "dcmsrinfo"
#define DCMSR_FILE_NAME "sr.dcm"

#endif
