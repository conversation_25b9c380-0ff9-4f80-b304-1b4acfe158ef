#include "jpegencoderbaseline.h"
#include "dcmtk/config/osconfig.h"
#include "dcmtk/dcmjpeg/djencbas.h"
#include "dcmtk/dcmjpeg/djcparam.h"
#include "dcmtk/dcmjpeg/djrploss.h"
#include "djcompressijgturbo8bit.h"
#include "dcmtk/dcmjpeg/djrplol.h" /* for DJ_RPLossless */

#include "dcmtk/dcmjpeg/djeijg8.h"

JpegEncoderBaseline::JpegEncoderBaseline()
    : JpegCodecEncoder()
{
}

JpegEncoderBaseline::~JpegEncoderBaseline()
{
}

E_TransferSyntax JpegEncoderBaseline::supportedTransferSyntax() const
{
    return EXS_JPEGProcess1TransferSyntax;
}

OFBool JpegEncoderBaseline::isLosslessProcess() const
{
    return OFFalse;
}

void JpegEncoderBaseline::createDerivationDescription(const DcmRepresentationParameter* toRepParam,
                                                      const DJCodecParameter* /* cp */, Uint8 /* bitsPerSample */,
                                                      double ratio, OFString& derivationDescription) const
{
    DJ_RPLossy defaultRP;
    const DJ_RPLossy* rp = toRepParam ? (const DJ_RPLossy*)toRepParam : &defaultRP;
    char buf[64];

    derivationDescription = "Lossy compression with JPEG baseline, IJG quality factor ";
    sprintf(buf, "%u", rp->getQuality());
    derivationDescription += buf;
    derivationDescription += ", compression ratio ";
    appendCompressionRatio(derivationDescription, ratio);
}

DJEncoder* JpegEncoderBaseline::createEncoderInstance(const DcmRepresentationParameter* toRepParam,
                                                      const DJCodecParameter* cp, Uint8 /* bitsPerSample */) const
{
    DJ_RPLossy defaultRP;
    const DJ_RPLossy* rp = toRepParam ? (const DJ_RPLossy*)toRepParam : &defaultRP;
    DJCompressIJGTurbo8Bit* result = new DJCompressIJGTurbo8Bit(*cp, EJM_baseline, rp->getQuality());

    //  DJCompressIJG8Bit *result = new DJCompressIJG8Bit(*cp, EJM_baseline, rp->getQuality());

    return result;
}
