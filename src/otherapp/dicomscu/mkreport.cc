/*
 *
 *  Copyright (C) 2000-2005, OFFIS
 *
 *  This software and supporting documentation were developed by
 *
 *    Kuratorium OFFIS e.V.
 *    Healthcare Information and Communication Systems
 *    Escherweg 2
 *    D-26121 Oldenburg, Germany
 *
 *  THIS SOFTWARE IS MADE AVAILABLE,  AS IS,  AND OFFIS MAKES NO  WARRANTY
 *  REGARDING  THE  SOFTWARE,  ITS  PERFORMANCE,  ITS  MERCHANTABILITY  OR
 *  FITNESS FOR ANY PARTICULAR USE, FREEDOM FROM ANY COMPUTER DISEASES  OR
 *  ITS CONFORMITY TO ANY SPECIFICATION. THE ENTIRE RISK AS TO QUALITY AND
 *  PERFORMANCE OF THE SOFTWARE IS WITH THE USER.
 *
 *  Module: dcmsr
 *
 *  Author: <PERSON><PERSON>
 *
 *  Purpose: Create sample structured reports using the dcmsr API
 *
 *  Last Update:      $Author: meichel $
 *  Update Date:      $Date: 2005/12/08 15:48:27 $
 *  CVS/RCS Revision: $Revision: 1.25 $
 *  Status:           $State: Exp $
 *
 *  CVS/RCS Log at end of file
 *
 */

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#include "dcmtk/ofstd/ofstream.h"
#include "dcmtk/dcmsr/dsrdoc.h"
#include "dcmtk/dcmdata/dcuid.h"
#include "dcmtk/dcmdata/dcfilefo.h"

#include <stdio.h>
#include "dcmcmd.h"
#include "dcmfilemake.h"
#include "mkreport.h"
#include "dcmstorescu.h"

static int mkreport();
static int ReadDcmSrInfoTag(PDcmSrInfo_tag pSrInfo_tag, const char* filename);
static int generate_12(DSRDocument* doc);

extern OFList<OFString> SOPInstanceUIDList;
int main_mkreport(int argc, char* argv[])
{
    int res = 0;

    if ((res = DcmFileMake(argc, argv)) == 0)
        if ((res = mkreport()) == 0)
            if ((res = storescu(argc, argv)) == 0)
            {
                // we can sure that the length of argv[sr_para_count_but_filename] is large than  DCMSR_FILE_NAME's;
                // the element, which is on argv[sr_para_count_but_filename], has such suffix: ".dcm",
                // and its prefix always is "..._jpg" or "..._img"
                // while DCMSR_FILE_NAME's value is sr.dcm
                strcpy(argv[sr_para_count_but_filename], DCMSR_FILE_NAME);

                res = storescu(sr_para_count_but_filename + 1, argv);
            }
    return res;
}

static int mkreport()
{
    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        CERR << "Warning: no data dictionary loaded, "
             << "check environment variable: " << DCM_DICT_ENVIRONMENT_VARIABLE << endl;
    }

    DSRDocument* doc = new DSRDocument();
    if (doc != NULL)
    {
        OFString studyUID_ki, studyUID_01;
        OFBool writeFile = OFTrue;
        /* check whether to create all reports or only selected ones */

        doc->setLogStream(&ofConsole);
        writeFile = OFTrue;

        generate_12(doc);

        if (writeFile)
        {
            doc->print(COUT, DSRTypes::PF_shortenLongItemValues);
            COUT << endl;

            DcmFileFormat* fileformat = new DcmFileFormat();
            DcmDataset* dataset = NULL;
            if (fileformat != NULL)
                dataset = fileformat->getDataset();
            if (dataset != NULL)
            {
                doc->getCodingSchemeIdentification().addPrivateDcmtkCodingScheme();
                if (doc->write(*dataset).good())
                {
                    OFString filename = DCMSR_FILE_NAME;
                    fileformat->saveFile(filename.c_str(), EXS_LittleEndianExplicit);
                }
                else
                {
                    CERR << "ERROR: could not write SR document into dataset" << endl;
                    delete fileformat;
                    return -1;
                }
            }
            delete fileformat;
        }
    }
    else
        return -1;
    delete doc;
    return 0;
}

static int ReadDcmSrInfoTag(PDcmSrInfo_tag pSrInfo_tag, const char* filename)
{
    FILE* pFile;
    if ((pFile = fopen(filename, "rb")) == NULL)
    {
        printf("can not open file %s!\n", filename);
        return -1;
    }
    else
    {
        fread(pSrInfo_tag, sizeof(DcmSrInfo_tag), 1, pFile);
        fclose(pFile);
        remove(filename);
    }
    return 0;
}

static int generate_12(DSRDocument* doc)
{
    DcmSrInfo_tag SrInfo_tag;

    //    if((pFile = fopen(SRUID_FILE_NAME, "a")) != 0)
    //    {
    //        fwrite(&sr_uids, sizeof(SR_UIDs), 1, pFile);
    //    }

    //    char  *g_StrSOPInstanceTempUID;
    //    char  g_StrSOPInstanceCpyUID[128];

    if (ReadDcmSrInfoTag(&SrInfo_tag, DCMSRINFO_FILE_NAME) != 0)
        return -1;
    //    strcpy(g_StrSOPInstanceCpyUID,g_StrSOPInstanceUID);
    //    g_StrSOPInstanceTempUID = strrchr(g_StrSOPInstanceCpyUID, '.');
    //    *g_StrSOPInstanceTempUID = '\0';

    doc->createNewDocument(DSRTypes::DT_BasicTextSR);
    doc->createNewSeriesInStudy(SrInfo_tag.StudyInstanceID);
    doc->setStudyDescription("Ultrasound Structured Reporting");
    doc->setSeriesDescription("Series, US");

    doc->setPatientsName(SrInfo_tag.PatientsName);
    doc->setPatientsSex(SrInfo_tag.PatientsSex);
    // doc->setPatientsBirthDate("19500420");
    doc->setPatientID(SrInfo_tag.PatientID);
    doc->setReferringPhysiciansName(SrInfo_tag.ReferringPhysicians);

    doc->getTree().addContentItem(DSRTypes::RT_isRoot, DSRTypes::VT_Container);
    doc->getTree().getCurrentContentItem().setConceptName(
        DSRCodedEntryValue("DT.03", OFFIS_CODING_SCHEME_DESIGNATOR, "Ultrasound Report"));

    doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Text, DSRTypes::AM_belowCurrent);
    // doc->getTree().getCurrentContentItem().setConceptName(DSRCodedEntryValue("RE.02", OFFIS_CODING_SCHEME_DESIGNATOR,
    // "Request")); doc->getTree().getCurrentContentItem().setStringValue("Ultrasound: Right lower quadrant");

    doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Text);
    doc->getTree().getCurrentContentItem().setConceptName(
        DSRCodedEntryValue("RE.01", OFFIS_CODING_SCHEME_DESIGNATOR, "History"));
    doc->getTree().getCurrentContentItem().setStringValue(SrInfo_tag.History);

    doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Container);
    doc->getTree().getCurrentContentItem().setConceptName(
        DSRCodedEntryValue("SH.06", OFFIS_CODING_SCHEME_DESIGNATOR, "Findings"));

    doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Text, DSRTypes::AM_belowCurrent);
    doc->getTree().getCurrentContentItem().setConceptName(
        DSRCodedEntryValue("RE.05", OFFIS_CODING_SCHEME_DESIGNATOR, "Finding"));
    doc->getTree().getCurrentContentItem().setStringValue(SrInfo_tag.Diagnosis);

    /*doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Text);
    doc->getTree().getCurrentContentItem().setConceptName(DSRCodedEntryValue("RE.05", OFFIS_CODING_SCHEME_DESIGNATOR,
    "Finding")); doc->getTree().getCurrentContentItem().setStringValue("The liver, spleen, kidneys, gallbladder, and
    pancreas are normal.");

    doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Text);
    doc->getTree().getCurrentContentItem().setConceptName(DSRCodedEntryValue("RE.05", OFFIS_CODING_SCHEME_DESIGNATOR,
    "Finding")); doc->getTree().getCurrentContentItem().setStringValue("Thickening of appendix with surrounding
    inflammation consistent with appendicitis.");*/

    doc->getTree().goUp();

    size_t num = SOPInstanceUIDList.size();
    OFIterator<OFString> itera = SOPInstanceUIDList.begin();
    OFIterator<OFString> end_itera = SOPInstanceUIDList.end();

    for (itera; itera != end_itera; ++itera)
    {

        doc->getTree().addContentItem(DSRTypes::RT_contains, DSRTypes::VT_Image);
        doc->getTree().getCurrentContentItem().setConceptName(
            DSRCodedEntryValue("IR.02", OFFIS_CODING_SCHEME_DESIGNATOR, "Best illustration of finding"));
        doc->getTree().getCurrentContentItem().setImageReference(
            DSRImageReferenceValue(UID_RETIRED_UltrasoundImageStorage, *itera));

        doc->getCurrentRequestedProcedureEvidence().addItem(SrInfo_tag.StudyInstanceID, doc->getSeriesInstanceUID(),
                                                            UID_RETIRED_UltrasoundImageStorage, *itera);
    }
    //    doc->getCurrentRequestedProcedureEvidence().addItem(SrInfo_tag.StudyInstanceID, SrInfo_tag.SeriesInstanceID,
    //    UID_RETIRED_UltrasoundImageStorage, g_StrSOPInstanceUID);
    return 0;
}

/*
 *  CVS/RCS Log:
 *  $Log: mkreport.cc,v $
 *  Revision 1.25  2005/12/08 15:48:27  meichel
 *  Changed include path schema for all DCMTK header files
 *
 *  Revision 1.24  2004/01/05 14:38:05  joergr
 *  Removed acknowledgements with e-mail addresses from CVS log.
 *
 *  Revision 1.23  2003/10/09 17:48:35  joergr
 *  Added identification information on UCUM coding scheme (see CP 372).
 *
 *  Revision 1.22  2003/09/10 13:19:05  joergr
 *  Replaced PrivateCodingSchemeUID by new CodingSchemeIdenticationSequence as
 *  required by CP 324.
 *
 *  Revision 1.21  2003/08/07 14:19:12  joergr
 *  Adapted for use of OFListConstIterator, needed for compiling with HAVE_STL.
 *
 *  Revision 1.20  2002/05/14 08:17:47  joergr
 *  Added new command line option to create "all" reports with one call.
 *
 *  Revision 1.19  2002/05/07 13:02:12  joergr
 *  Added support for the Current Requested Procedure Evidence Sequence and the
 *  Pertinent Other Evidence Sequence to the dcmsr module.
 *
 *  Revision 1.18  2002/04/16 13:51:59  joergr
 *  Added configurable support for C++ ANSI standard includes (e.g. streams).
 *
 *  Revision 1.17  2002/04/11 13:06:08  joergr
 *  Use the new loadFile() and saveFile() routines from the dcmdata library.
 *
 *  Revision 1.16  2001/12/14 10:37:53  joergr
 *  Re-structured test program to "co-operate" with gcc on Irix 5.
 *
 *  Revision 1.15  2001/10/10 15:31:46  joergr
 *  Additonal adjustments for new OFCondition class.
 *
 *  Revision 1.14  2001/09/28 14:15:01  joergr
 *  Replaced "cerr" by "CERR". Replaced "cout" by "COUT".
 *
 *  Revision 1.13  2001/09/26 13:19:11  meichel
 *  Adapted dcmsr to class OFCondition
 *
 *  Revision 1.12  2001/09/26 13:04:34  meichel
 *  Adapted dcmsr to class OFCondition
 *
 *  Revision 1.11  2001/07/02 12:58:04  joergr
 *  Replaced non-standard characters in report "05" and "15".
 *
 *  Revision 1.10  2001/06/13 13:44:47  joergr
 *  Added check for data dictionary to command line tool.
 *
 *  Revision 1.9  2001/06/13 10:09:55  joergr
 *  Added SpecificCharacterSet attribute to report "05".
 *
 *  Revision 1.8  2001/03/28 09:05:44  joergr
 *  Added new sample report (valid structured report with cycle/loop).
 *
 */
