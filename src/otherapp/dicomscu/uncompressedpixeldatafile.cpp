#include "uncompressedpixeldatafile.h"
#include "dcmutilitydef.h"
#include "pixeldatafilehead.h"
#include <new>
using namespace std;

UncompressedPixelDataFile::UncompressedPixelDataFile(const PixelDataFileHead& fileHead, const char* paletteFileName,
                                                     const char* sonoparameterFile, bool opt_verbose, bool opt_debug)
    : PixelDataFile(fileHead, opt_verbose, opt_debug)
    , m_PixelData(NULL)
    , m_Palette(NULL)
    , m_SonoParameters(NULL)
{
    if ((m_PixelData = (Uint8*)tryAllocateMemory(m_NumberOfFrames, fileHead.imageSize())) == NULL)
    {
        printf("Allocate memory failed!\n");
    }
    else
    {
        if (fileHead.isNeedPalette())
        {
            if ((m_Palette = readPalette(paletteFileName)) == NULL)
            {
                freeMem();
                return;
            }
        }

        if (fileHead.isMovie())
        {
            if ((m_SonoParameters = readSonoParameters(sonoparameterFile)) == NULL)
            {
                printf("SonoparameterFile: read CustomImageData error\n");

                freeMem();
                return;
            }
        }

        if (!skipStartingFrames())
        {
            printf("UncompressedPixelDataFile: skip starting frames error\n");
            freeMem();
            return;
        }

        Uint32 frameSize = fileHead.frameSize();
        int fileIndex = 0;
        if (fileHead.isNeedPalette() && fileHead.photometricInterpretationType() == rgb)
        {
            // do palette map frame by frame
            Uint8* frameBuffer = new (std::nothrow) Uint8[frameSize];
            if (frameBuffer == NULL)
            {
                printf("UncompressedPixelDataFile: new frameData memory failed!\n");
                freeMem();
                return;
            }

            Uint32 i = 0;
            for (i = 0; i < m_NumberOfFrames; i++)
            {
                Uint32 sonoParametersIndex = fileIndex;
                if (sonoParametersIndex >= m_NumberOfSrcFrames)
                {
                    sonoParametersIndex = m_NumberOfSrcFrames - 1;
                    //防止最后一帧图超过SonoParameters最大帧
                }
                if (readFrame(i, fileIndex, (void*)frameBuffer, frameSize) != frameSize)
                {
                    printf("read %d Frame error\n", i);
                    break;
                }

                doGray8BitTo24Bit(frameBuffer, frameSize, m_Palette, m_SonoParameters[sonoParametersIndex],
                                  m_PixelData + i * fileHead.imageSize());
            }
            m_NumberOfFramesReaded = i;

            if (m_Palette != NULL)
            {
                delete[] m_Palette;
                m_Palette = NULL;
            }

            if (frameBuffer != NULL)
            {
                delete[] frameBuffer;
                frameBuffer = NULL;
            }

            if (m_SonoParameters != NULL)
            {
                delete[] m_SonoParameters;
                m_SonoParameters = NULL;
            }
        }
        else
        {
            if (m_NumberOfFrames == 1 || !fileHead.isNeedSample())
            {
                Uint32 readedSize = 0;
                if ((readedSize = fread(m_PixelData, sizeof(unsigned char), m_NumberOfFrames * frameSize,
                                        fileHead.pFile())) != m_NumberOfFrames * frameSize)
                {
                    printf("fread error\n");
                }
                else
                {
                    m_NumberOfFramesReaded = readedSize / frameSize;
                }
            }
            else
            {
                Uint32 i = 0;
                for (i = 0; i < m_NumberOfFrames; i++)
                {
                    if (readFrame(i, fileIndex, (void*)(m_PixelData + i * frameSize), frameSize) != frameSize)
                    {
                        printf("read %d Frame error\n", i);
                        break;
                    }
                }
                m_NumberOfFramesReaded = i;
            }
        }

        if (fileHead.photometricInterpretationType() != palette_color)
        {
            if (m_Palette != NULL)
            {
                delete[] m_Palette;
                m_Palette = NULL;
            }
        }

        if (opt_verbose)
        {
            printf("org frames: %u, org read frames: %u, readed frames: %u\n", m_FileHead.numberOfFrames(),
                   m_FileHead.numberOfReadFrames(), m_NumberOfFramesReaded);
        }
    }
}

UncompressedPixelDataFile::~UncompressedPixelDataFile()
{
    freeMem();
}

bool UncompressedPixelDataFile::isValid() const
{
    return PixelDataFile::isValid() && m_PixelData != NULL;
}

const Uint8* UncompressedPixelDataFile::palette() const
{
    return m_Palette;
}

const Uint8* UncompressedPixelDataFile::pixelData()
{
    return m_PixelData;
}

void UncompressedPixelDataFile::freeMem()
{
    if (m_PixelData != NULL)
    {
        delete[] m_PixelData;
        m_PixelData = NULL;
    }

    if (m_Palette != NULL)
    {
        delete[] m_Palette;
        m_Palette = NULL;
    }

    if (m_SonoParameters != NULL)
    {
        delete[] m_SonoParameters;
        m_SonoParameters = NULL;
    }
}

Uint8* UncompressedPixelDataFile::tryAllocateMemory(Uint32& frame, int frameSize)
{
    return new (std::nothrow) Uint8[frame * frameSize];
}
