#ifndef DJCOMPRESSIJGTURBO8BIT_H
#define DJCOMPRESSIJGTURBO8BIT_H

#include "dcmtk/config/osconfig.h"
#include "dcmtk/ofstd/oflist.h"
#include "dcmtk/dcmjpeg/djencabs.h"

extern "C"
{
    struct jpeg_compress_struct;
}

class DJCodecParameter;

/** this class encapsulates the compression routines of the
 *  IJG JPEG Turbo library configured for 8 bits/sample.
 */
class DJCompressIJGTurbo8Bit : public DJEncoder
{
public:
    /** constructor for lossy JPEG
     *  @param cp codec parameters
     *  @param mode mode of operation
     *  @param quality compression quality
     */
    DJCompressIJGTurbo8Bit(const DJCodecParameter& cp, EJ_Mode mode, Uint8 quality);

    /// destructor
    virtual ~DJCompressIJGTurbo8Bit();

    /** single frame compression routine for 16-bit raw pixel data.
     *  May only be called if bytesPerSample() == 2.
     *  @param columns columns of frame
     *  @param rows rows of frame
     *  @param interpr photometric interpretation of input frame
     *  @param samplesPerPixel samples per pixel of input frame
     *  @param image_buffer pointer to frame buffer
     *  @param to compressed frame returned in this parameter upon success
     *  @param length length of compressed frame (in bytes) returned in this parameter
     *    upon success; length guaranteed to be always even.
     *  @return EC_Normal if successful, an error code otherwise.
     */
    virtual OFCondition encode(Uint16 columns, Uint16 rows, EP_Interpretation interpr, Uint16 samplesPerPixel,
                               Uint16* image_buffer, Uint8*& to, Uint32& length);

    /** single frame compression routine for 8-bit raw pixel data.
     *  May only be called if bytesPerSample() == 1.
     *  @param columns columns of frame
     *  @param rows rows of frame
     *  @param interpr photometric interpretation of input frame
     *  @param samplesPerPixel samples per pixel of input frame
     *  @param image_buffer pointer to frame buffer
     *  @param to compressed frame returned in this parameter upon success
     *  @param length length of compressed frame (in bytes) returned in this parameter
     *    upon success; length guaranteed to be always even.
     *  @return EC_Normal if successful, an error code otherwise.
     */
    virtual OFCondition encode(Uint16 columns, Uint16 rows, EP_Interpretation interpr, Uint16 samplesPerPixel,
                               Uint8* image_buffer, Uint8*& to, Uint32& length);

    /** returns the number of bytes per sample that will be expected when encoding.
     */
    virtual Uint16 bytesPerSample() const
    {
        return 1;
    }

    /** returns the number of bits per sample that will be expected when encoding.
     */
    virtual Uint16 bitsPerSample() const
    {
        return 8;
    }

    /** callback for IJG compress destination manager.
     *  Internal use only, not to be called by client code.
     *  @param cinfo pointer to compress info
     */
    void initDestination(jpeg_compress_struct* cinfo);

    /** callback for IJG compress destination manager.
     *  Internal use only, not to be called by client code.
     *  @param cinfo pointer to compress info
     */
    int emptyOutputBuffer(jpeg_compress_struct* cinfo);

    /** callback for IJG compress destination manager.
     *  Internal use only, not to be called by client code.
     *  @param cinfo pointer to compress info
     */
    void termDestination(jpeg_compress_struct* cinfo);

    /** callback function used to report warning messages and the like.
     *  Should not be called by user code directly.
     *  @param arg opaque pointer to JPEG compress structure
     */
    virtual void outputMessage(void* arg) const;

private:
    /// private undefined copy constructor
    DJCompressIJGTurbo8Bit(const DJCompressIJGTurbo8Bit&);

    /// private undefined copy assignment operator
    DJCompressIJGTurbo8Bit& operator=(const DJCompressIJGTurbo8Bit&);

    /// cleans up pixelDataList, called from destructor and error handlers
    void cleanup();

    /// codec parameters
    const DJCodecParameter* cparam;

    /// for lossy compression, defines compression quality factor
    Uint8 quality;

    /// for lossless compression, defines selection value
    int psv;

    /// for lossless compression, defines point transform
    int pt;

    /// enum for mode of operation (baseline, sequential, progressive etc.)
    EJ_Mode modeofOperation;

    /// list of compressed pixel data blocks
    OFList<unsigned char*> pixelDataList;

    /// filled number of bytes in last block in pixelDataList
    size_t bytesInLastBlock;
};

#endif // DJCOMPRESSIJGTURBO8BIT_H
