#ifndef JPEGCODECENCODER_H
#define JPEGCODECENCODER_H

#include "dcmtk/config/osconfig.h"
#include "dcmtk/ofstd/oftypes.h"
#include "dcmtk/dcmdata/dccodec.h" /* for class DcmCodec */
#include "dcmtk/dcmjpeg/djutils.h" /* for enums */
#include "dcmtk/ofstd/oflist.h"
#include "dcmtk/ofstd/ofstring.h" /* for class OFString */

class DataInterface;
class DJEncoder;
class DcmDataset;
class DJCodecParameter;
class DJDecoder;
class DcmItem;
class DcmPixelItem;
class DicomImage;
class DcmTagKey;

class LinePixelDataFile;

/** abstract codec class for JPEG encoders.
 *  This abstract class contains most of the application logic
 *  needed for a dcmdata codec object that implements a JPEG encoder
 *  using the DJEncoder interface to the underlying JPEG implementation.
 *  This class only supports compression, it neither implements
 *  decoding nor transcoding.
 */
class JpegCodecEncoder
{
public:
    /// default constructor
    JpegCodecEncoder();

    /// destructor
    virtual ~JpegCodecEncoder();

    /** compresses the given uncompressed DICOM image and stores
     *  the result in the given pixSeq element.
     *  @param pixelData pointer to the uncompressed image data in OW format
     *    and local byte order
     *  @param length of the pixel data field in bytes
     *  @param toRepParam representation parameter describing the desired
     *    compressed representation (e.g. JPEG quality)
     *  @param pixSeq compressed pixel sequence (pointer to new DcmPixelSequence object
     *    allocated on heap) returned in this parameter upon success.
     *  @param cp codec parameters for this codec
     *  @param objStack stack pointing to the location of the pixel data
     *    element in the current dataset.
     *  @return EC_Normal if successful, an error code otherwise.
     */
    virtual OFCondition encode(DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                               const DcmRepresentationParameter* toRepParam, const DcmCodecParameter* cp) const;

    /** returns the transfer syntax that this particular codec
     *  is able to encode and decode.
     *  @return supported transfer syntax
     */
    virtual E_TransferSyntax supportedTransferSyntax() const = 0;

protected:
    /** format compression ratio as string and append to given string object.
     *  @param arg string to append to
     *  @param ratio compression ratio
     */
    static void appendCompressionRatio(OFString& arg, double ratio);

    /** toggles Planar Configuration of 8-bit pixel data from "by pixel" to
     *  "by plane" and vice versa.
     *  @param pixelData - [in/out] Original pixel data (input), contains
     *                     pixel data with toggled Planar configuration after
     *                     returning (output).
     *  @param numValues - [in] The number of 8 bit values in pixelData
     *  @param samplesPerPixel - [in] Number of components for one pixel
     *  @param oldPlanarConfig - [in] The old Planar Configuration, that should
     *                           be toggled. 0 means "by pixel", 1 "by color"
     *  @return EC_Normal, if conversion was successful; error else
     */
    static OFCondition togglePlanarConfiguration8(Uint8* pixelData, const unsigned long numValues,
                                                  const Uint16 samplesPerPixel, const Uint16 oldPlanarConfig);

    /** toggles Planar Configuration of 16-bit pixel data from "by pixel" to
     *  "by plane" and vice versa.
     *  @param pixelData - [in/out] Original pixel data (input), contains
     *                     pixel data with toggled Planar configuration after
     *                     returning (output).
     *  @param numValues - [in] The number of 16 bit values in pixelData
     *  @param samplesPerPixel - [in] Number of components for one pixel
     *  @param oldPlanarConfig - [in] The old Planar Configuration, that should
     *                           be toggled. 0 means "by pixel", 1 "by color"
     *  @return EC_Normal, if conversion was successful; error else
     */
    static OFCondition togglePlanarConfiguration16(Uint16* pixelData, const unsigned long numValues,
                                                   const Uint16 samplesPerPixel, const Uint16 oldPlanarConfig);

private:
    /** compresses the given uncompressed DICOM color image and stores
     *  the result in the given pixSeq element.
     *  @param YBRmode true if the source image has YBR_FULL or YBR_FULL_422
     *    photometric interpretation and can thus be compressed without color
     *    space conversion.
     *  @param dataset DICOM dataset containing the pixel data as well as
     *    descriptive attributes
     *  @param toRepParam representation parameter describing the desired
     *    compressed representation (e.g. JPEG quality)
     *  @param cp codec parameters for this codec
     *  @param compressionRatio compression ratio returned in this parameter
     *    if successful.
     *  @return EC_Normal if successful, an error code otherwise.
     */
    virtual OFCondition encodeColorImage(OFBool YBRmode, DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                                         const DcmRepresentationParameter* toRepParam, const DJCodecParameter* cp,
                                         double& compressionRatio) const;

    /** compresses the given uncompressed DICOM image and stores
     *  the result in the given pixSeq element. No colorspace
     *  conversions, modality or windowing transformations are applied
     *  to guarantee, that the quality of the source image is fully preserved.
     *  @param toRepParam - [in] representation parameter describing the desired
     *  @param cp - [in] codec parameters for this codec
     *  @param objStack - [in/out] stack pointing to the location of the pixel data
     *    element in the current dataset.
     *  @return EC_Normal if successful, an error code otherwise.
     */
    virtual OFCondition encodeTrueLossless(DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                                           const DcmRepresentationParameter* toRepParam,
                                           const DcmCodecParameter* cp) const;

    /** create Lossy Image Compression and Lossy Image Compression Ratio.
     *  @param dataset dataset to be modified
     *  @param ratio image compression ratio > 1. This is not the "quality factor"
     *    but the real effective ratio between compressed and uncompressed image,
     *    i. e. 30 means a 30:1 lossy compression.
     *  @return EC_Normal if successful, an error code otherwise
     */
    virtual OFCondition updateLossyCompressionRatio(DcmItem* dataset, double ratio) const;

    /** create Derivation Description.
     *  @param dataset dataset to be modified
     *  @param toRepParam representation parameter passed to encode()
     *  @param cp codec parameter passed to encode()
     *  @param bitsPerSample bits per sample of the original image data prior to compression
     *  @param ratio image compression ratio. This is not the "quality factor"
     *    but the real effective ratio between compressed and uncompressed image,
     *    i. e. 30 means a 30:1 lossy compression.
     *  @return EC_Normal if successful, an error code otherwise
     */
    virtual OFCondition updateDerivationDescription(DcmItem* dataset, const DcmRepresentationParameter* toRepParam,
                                                    const DJCodecParameter* cp, Uint8 bitsPerSample,
                                                    double ratio) const;

    /** returns true if the transfer syntax supported by this
     *  codec is lossless.
     *  @return lossless flag
     */
    virtual OFBool isLosslessProcess() const = 0;

    /** creates 'derivation description' string after encoding.
     *  @param toRepParam representation parameter passed to encode()
     *  @param cp codec parameter passed to encode()
     *  @param bitsPerSample bits per sample of the original image data prior to compression
     *  @param ratio image compression ratio. This is not the "quality factor"
     *    but the real effective ratio between compressed and uncompressed image,
     *    i. e. 30 means a 30:1 lossy compression.
     *  @param derivation description returned in this
     *    parameter which is initially empty
     */
    virtual void createDerivationDescription(const DcmRepresentationParameter* toRepParam, const DJCodecParameter* cp,
                                             Uint8 bitsPerSample, double ratio,
                                             OFString& derivationDescription) const = 0;

    /** creates an instance of the compression library to be used
     *  for encoding/decoding.
     *  @param toRepParam representation parameter passed to encode()
     *  @param cp codec parameter passed to encode()
     *  @param bitsPerSample bits per sample for the image data
     *  @return pointer to newly allocated codec object
     */
    virtual DJEncoder* createEncoderInstance(const DcmRepresentationParameter* toRepParam, const DJCodecParameter* cp,
                                             Uint8 bitsPerSample) const = 0;

    OFCondition updatePlanarConfiguration(DcmItem* item, const Uint16 newPlanConf) const;
};

#endif // JPEGCODECENCODER_H
