#include "dcmsrscu.h"
#include "dcmtk/ofstd/ofstdinc.h"

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#include "dcmtk/ofstd/ofstd.h"
#include "dcmtk/ofstd/ofstring.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/ofstd/ofdatime.h"
#include "dcmtk/ofstd/ofthread.h"

#include "dcmtk/dcmdata/dctk.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/dcmdata/dcuid.h" /* for dcmtk version name */
#include "dcmtk/dcmdata/dccodec.h"
#include "dcmtk/dcmdata/dcdebug.h"
#include "dcmtk/dcmsr/dsrdoc.h"
#include "dcmtk/ofstd/ofstream.h"

#include "dcmtk/dcmimage/diqtctab.h"
#include "dcmtk/dcmimgle/dcmimage.h"
#ifdef WITH_OPENSSL
#include "dcmtk/dcmtls/tlstrans.h"
#include "dcmtk/dcmtls/tlslayer.h"
#endif
#include "dcmtk/dcmimage/diqtpix.h"

#ifdef WITH_ZLIB
#include "zlib.h" /* for zlibVersion() */
#endif
#include "dcmcmd.h"
#include "dcmfilemake.h"
#include "timelogger.h"
#include "dcmstorescu.h"

#ifdef DICOM_DEBUG
static OFBool opt_verbose = OFTrue;
static OFBool opt_debug = OFTrue;
#else
static OFBool opt_verbose = OFFalse;
static OFBool opt_debug = OFFalse;
#endif

//#ifdef WITH_LIBXML

#include <libxml/parser.h>

#define OFFIS_CONSOLE_APPLICATION "xml2dsr"
#define OFFIS_CONSOLE_DESCRIPTION "Convert XML document to DICOM SR file"

// static char rcsid[] = "$dcmtk: " OFFIS_CONSOLE_APPLICATION " v"
//  OFFIS_DCMTK_VERSION " " OFFIS_DCMTK_RELEASEDATE " $";

#define SHORTCOL 3
#define LONGCOL 21

int xml2dsr(const char* ifname, const char* ofname);

/**
 * @brief main_mkdcmsr xml 生成 sr
 *
 * ./chison_dcmsrscu -dir destdir
 * ./dicom_running_temp/sr_0.xml
 * ./dicom_running_temp/sr_2.xml
 *
 * @param argc
 * @param argv
 * @return
 */
int main_mkdcmsr(int argc, char* argv[])
{
    if (argc == 1)
    {
        printf("mkdcmsr: make sr xml file to sr dcm file\n");
        printf("usage: mkdcmsr [options] ./dicom_running_temp/sr_0.xml ./dicom_running_temp/sr_2.xml ...xml\n"
               "default dest file name is ./dicom_running_temp/sr_0.dcm ./dicom_running_temp/sr_2.dcm ...dcm\n");
        printf("general options:\n  -dir      modify dest dir, ./dicom_running_temp/sr_0.xml to [dest_dir]/sr_0.dcm\n");
        return 0;
    }
    OFList<OFString> xmlfiles;
    bool containsImage = false;

    OFString outputDir;
    for (int i = 0; i < argc; ++i)
    {
        OFString arg(argv[i]);

        if (arg == "-dir")
        {
            //-dir 可以控制输出dcm文件的路径，如果没设置，则为源文件路径
            if ((i + 1) < argc)
            {
                outputDir = OFString(argv[i + 1]);
                if (outputDir.at(outputDir.size() - 1) != '/')
                {
                    outputDir.append("/");
                }

                i++;
            }
        }
        else if (arg.rfind(OFString(".xml")) != OFString_npos)
        {
            xmlfiles.push_back(arg);
        }
        else if (arg.rfind(OFString(".dcm")) != OFString_npos)
        {
            containsImage = true;
        }
    }

    OFIterator<OFString> itera = xmlfiles.begin();
    OFIterator<OFString> end_itera = xmlfiles.end();

    // convert sr_x.xml to sr_x.dcm
    for (itera; itera != end_itera; ++itera)
    {
        OFString curXmlFileName = *itera;
        OFString curDsrFileName = OFString(*itera).replace(curXmlFileName.size() - 3, 3, "dcm");

        OFString dcmPath = outputDir;

        if (!dcmPath.empty())
        {
            size_t pos = curDsrFileName.rfind("/");
            if (pos == OFString_npos)
            {
                dcmPath.append(curDsrFileName);
            }
            else
            {
                dcmPath.append(curDsrFileName.substr(pos + 1, curDsrFileName.size() - (pos + 1)));
            }
        }
        else
        {
            dcmPath = curDsrFileName;
        }

        if (xml2dsr(curXmlFileName.c_str(), dcmPath.c_str()) != 0)
        {
            return -1;
        }
    }

    // write image dcm
    if (containsImage)
    {
        memset(&g_DcmStorage_Option, 0, sizeof(DcmStorage_Option));
        if (DcmFileMake(argc, argv) != 0)
        {
            return -1;
        }
    }

    return 0;
}

/**
 * @brief main_dcmsrscu xml 生成 sr，image 生成 dcm，并且发送生成的所有dcm
 *
 * ./chison_dcmsrscu -d -v -xy -to 10 -aet AE_SCU -aec AE_SR 192.168.30.20 104
 * ./dicom_running_temp/e4fd7240d4bbe2028348cd735236ebf9.dcm
 * ./dicom_running_temp/1a0a3e5e0c3d53d8db771018098be5e0.dcm
 * ./dicom_running_temp/48e6dfac4b5da0a8f466bc966c085ae1.dcm
 * ./dicom_running_temp/3cc02855a2563fe4696a15b35d7a86cd.dcm
 * ./dicom_running_temp/sr_0.xml
 * ./dicom_running_temp/sr_2.xml
 *
 * @param argc
 * @param argv
 * @return
 */
int main_dcmsrscu(int argc, char* argv[])
{
    OFList<OFString> xmlfiles;
    bool containsImage = false;

    for (int i = 0; i < argc; ++i)
    {
        OFString arg(argv[i]);

        if (arg.rfind(OFString(".xml")) != OFString_npos)
        {
            xmlfiles.push_back(arg);
        }
        else if (arg.rfind(OFString(".dcm")) != OFString_npos)
        {
            containsImage = true;
        }
    }

    OFIterator<OFString> itera = xmlfiles.begin();
    OFIterator<OFString> end_itera = xmlfiles.end();

    // convert sr_x.xml to sr_x.dcm
    for (itera; itera != end_itera; ++itera)
    {
        OFString curXmlFileName = *itera;
        OFString curDsrFileName = OFString(*itera).replace(curXmlFileName.size() - 3, 3, "dcm");

        if (xml2dsr(curXmlFileName.c_str(), curDsrFileName.c_str()) != 0)
        {
            return -1;
        }
    }

    // write image dcm
    if (containsImage)
    {
        memset(&g_DcmStorage_Option, 0, sizeof(DcmStorage_Option));
        if (DcmFileMake(argc, argv) != 0)
        {
            return -1;
        }
    }

    // replace .xml .dcm in argv paras
    for (int i = 0; i < argc; ++i)
    {
        OFString arg(argv[i]);

        if (arg.rfind(OFString(".xml")) != OFString_npos)
        {
            strncpy(argv[i] + (strlen(argv[i]) - 3), "dcm", 3);
        }
    }

    // store all image.dcm sr.dcm
    return storescu(argc, argv);
}

int xml2dsr(const char* ifname, const char* ofname)
{
#ifndef WIN32
    int opt_sr_debug = 5;
    OFBool opt_sr_verbose = OFTrue;
    OFBool opt_dataset = OFTrue;
    size_t opt_readFlags = 0;
    E_TransferSyntax opt_xfer = EXS_LittleEndianExplicit;
    E_EncodingType opt_enctype = EET_ExplicitLength;
    E_GrpLenEncoding opt_glenc = EGL_recalcGL;
    E_PaddingEncoding opt_padenc = EPD_withoutPadding;
    OFCmdUnsignedInt opt_filepad = 0;
    OFCmdUnsignedInt opt_itempad = 0;

    SetDebugLevel((opt_sr_debug));

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        CERR << "Warning: no data dictionary loaded, "
             << "check environment variable: " << DCM_DICT_ENVIRONMENT_VARIABLE << endl;
    }

    /* check for compatible libxml version */
    LIBXML_TEST_VERSION
    /* initialize the XML library (only required for MT-safety) */
    xmlInitParser();

    OFCondition result = EC_Normal;

    /* check filenames */
    if ((ifname == NULL) || (strlen(ifname) == 0))
    {
        CERR << OFFIS_CONSOLE_APPLICATION << ": invalid input filename: <empty string>" << endl;
        result = EC_IllegalParameter;
    }
    if ((ofname == NULL) || (strlen(ofname) == 0))
    {
        CERR << OFFIS_CONSOLE_APPLICATION << ": invalid output filename: <empty string>" << endl;
        result = EC_IllegalParameter;
    }

    if (result.good())
    {
        /* create new SR document */
        DSRDocument* dsrdoc = new DSRDocument();
        if (dsrdoc != NULL)
        {
            DcmFileFormat fileformat;
            if (opt_sr_debug)
                dsrdoc->setLogStream(&ofConsole);
            if (opt_sr_verbose)
            {
                COUT << "reading ";
#ifdef LIBXML_SCHEMAS_ENABLED
                if (opt_readFlags & DSRTypes::XF_validateSchema)
                    COUT << "and validating ";
#endif
                COUT << "XML input file: " << ifname << endl;
            }
            /* read XML file and feed data into DICOM fileformat */
            result = dsrdoc->readXML(ifname, opt_readFlags);
            if (result.good())
            {
                if (opt_sr_verbose)
                    COUT << "writing DICOM SR output file: " << ofname << endl;
                /* write SR document to dataset */
                result = dsrdoc->write(*fileformat.getDataset());
                /* write DICOM file */
                if (result.good())
                    result = fileformat.saveFile(ofname, opt_xfer, opt_enctype, opt_glenc, opt_padenc,
                                                 OFstatic_cast(Uint32, opt_filepad), OFstatic_cast(Uint32, opt_itempad),
                                                 opt_dataset);
                if (result.bad())
                    CERR << "Error: " << result.text() << ": writing file: " << ofname << endl;
            }
            else
                CERR << "Error: " << result.text() << ": reading file: " << ifname << endl;
        }
        delete dsrdoc;
    }

    /* clean up XML library before quitting */
    xmlCleanupParser();

    return result.status();
#else
    OFString argv("xml2dsr");
    argv.append(" -d -v ");
    argv.append(ifname);
    argv.append(" ");
    argv.append(ofname);
    return system(argv.c_str());
#endif
}
