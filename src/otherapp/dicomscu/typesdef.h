#ifndef TYPESDEF_H
#define TYPESDEF_H

#ifdef HAVE_CONFIG_H

#include "dcmtk/ofstd/oftypes.h"

#else

typedef char Sint8;

typedef unsigned char Uint8;

typedef signed long Sint32;
typedef unsigned long Uint32;

typedef signed short Sint16;
typedef unsigned short Uint16;

typedef float Float32;  /* 32 Bit Floating Point Single */
typedef double Float64; /* 64 Bit Floating Point Double */

// Definition of type OFBool
#define OFBool bool
#define OFTrue true
#define OFFalse false

#endif

#endif // TYPESDEF_H
