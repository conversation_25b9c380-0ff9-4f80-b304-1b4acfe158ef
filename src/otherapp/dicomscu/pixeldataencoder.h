#ifndef PIXELDATAENCODER_H
#define PIXELDATAENCODER_H

#include "ipixeldataencoder.h"
#include "dcmutilitydef.h"

class IPixelDataFileHead;

class PixelDataEncoder : public IPixelDataEncoder
{
public:
    PixelDataEncoder(const char* fileName, const char* paletteFileName, const char* sonoparameterFile, bool isPrinttask,
                     const DcmStorage_Option& gStorageOption, int defFps, bool opt_verbose = false,
                     bool opt_debug = false);
    ~PixelDataEncoder();
    virtual Uint32 numberOfFrames() const;
    virtual Uint32 pixelSize() const;
    virtual E_TransferSyntax supportedTransferSyntax() const;
    virtual OFCondition encode(DcmItem* dataset) const;

private:
    IPixelDataFileHead* m_FileHead;
    IPixelDataEncoder* m_Encoder;
};

#endif // PIXELDATAENCODER_H
