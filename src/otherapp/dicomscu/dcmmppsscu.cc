/*
**
** Author: sijihu		Created: 06-04-2020
**
** Module: dcmnet
**
** Purpose: MPPS Service Class User(N-CREATE and N-SET operation)
**
*/

#include "dcmmppsscu.h"

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#define INCLUDE_CSTDLIB
#define INCLUDE_CSTDIO
#define INCLUDE_CSTRING
#define INCLUDE_CSTDARG
#include "dcmtk/ofstd/ofstdinc.h"

#ifdef HAVE_GUSI_H
#include <GUSI.h>
#endif

#include "dcmtk/dcmnet/dimse.h"
#include "dcmtk/dcmnet/diutil.h"
#include "dcmtk/dcmdata/dcfilefo.h"
#include "dcmtk/dcmdata/dcdebug.h"
#include "dcmtk/dcmdata/dcdict.h"
#include "dcmtk/dcmdata/dcuid.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/dcmdata/dcuid.h" /* for dcmtk version name */
#include "dcmtk/dcmdata/dcdicent.h"
#include "dcmtk/dcmdata/dcdeftag.h"
#include "dcmtk/ofstd/ofdatime.h"

#ifdef WITH_OPENSSL
#include "dcmtk/dcmtls/tlstrans.h"
#include "dcmtk/dcmtls/tlslayer.h"
#endif

#ifdef WITH_ZLIB
#include <zlib.h> /* for zlibVersion() */
#endif

#define OFFIS_CONSOLE_APPLICATION "mppsscu"

static char rcsid[] = "$dcmtk: " OFFIS_CONSOLE_APPLICATION " v" OFFIS_DCMTK_VERSION " " OFFIS_DCMTK_RELEASEDATE " $";
/* default application titles */
#define APPLICATIONTITLE "MPPSSCU"
#define PEERAPPLICATIONTITLE "ANY-SCP"

static OFBool opt_verbose = OFFalse;
static OFBool opt_debug = OFFalse;
static OFBool opt_abortAssociation = OFFalse;
static OFCmdUnsignedInt opt_maxReceivePDULength = ASC_DEFAULTMAXPDU;
static OFCmdUnsignedInt opt_maxSendPDULength = 0;
static DcmDataset* overrideKeys = NULL;
static E_TransferSyntax opt_networkTransferSyntax = EXS_Unknown;
static T_DIMSE_BlockingMode opt_blockMode = DIMSE_BLOCKING;
static int opt_dimse_timeout = 0;
static int opt_acse_timeout = 30;
T_DIMSE_MPPSSAction opt_mppsaction = DIMSE_NCreate;
static const char* opt_peerTitle = PEERAPPLICATIONTITLE;
static const char* opt_ourTitle = APPLICATIONTITLE;
static OFString opt_scheduledModality = "US";

#define IN_PROGRESS "IN PROGRESS"
#define COMPLETED "COMPLETED"
#define DISCONTINUED "DISCONTINUED"

static void errmsg(const char* msg, ...)
{
    va_list args;

    fprintf(stderr, "%s: ", OFFIS_CONSOLE_APPLICATION);
    va_start(args, msg);
    vfprintf(stderr, msg, args);
    va_end(args);
    fprintf(stderr, "\n");
}

static void addOverrideKey(OFConsoleApplication& app, const char* s)
{
    unsigned int g = 0xffff;
    unsigned int e = 0xffff;
    int n = 0;
    char val[1024];
    OFString dicName, valStr;
    OFString msg;
    char msg2[200];
    val[0] = '\0';

    // try to parse group and element number
    n = sscanf(s, "%x,%x=%s", &g, &e, val);
    OFString toParse = s;
    size_t eqPos = toParse.find('=');
    if (n < 2) // if at least no tag could be parsed
    {
        // if value is given, extract it (and extrect dictname)
        if (eqPos != OFString_npos)
        {
            dicName = toParse.substr(0, eqPos).c_str();
            valStr = toParse.substr(eqPos + 1, toParse.length());
        }
        else             // no value given, just dictionary name
            dicName = s; // only dictionary name given (without value)
        // try to lookup in dictionary
        DcmTagKey key(0xffff, 0xffff);
        const DcmDataDictionary& globalDataDict = dcmDataDict.rdlock();
        const DcmDictEntry* dicent = globalDataDict.findEntry(dicName.c_str());
        dcmDataDict.unlock();
        if (dicent != NULL)
        {
            // found dictionary name, copy group and element number
            key = dicent->getKey();
            g = key.getGroup();
            e = key.getElement();
        }
        else
        {
            // not found in dictionary
            msg = "bad key format or dictionary name not found in dictionary: ";
            msg += dicName;
            app.printError(msg.c_str());
        }
    } // tag could be parsed, copy value if it exists
    else
    {
        if (eqPos != OFString_npos)
            valStr = toParse.substr(eqPos + 1, toParse.length());
    }
    DcmTag tag(g, e);
    if (tag.error() != EC_Normal)
    {
        sprintf(msg2, "unknown tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
    DcmElement* elem = newDicomElement(tag);
    if (elem == NULL)
    {
        sprintf(msg2, "cannot create element for tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
    if (valStr.length() > 0)
    {
        if (elem->putString(valStr.c_str()).bad())
        {
            sprintf(msg2, "cannot put tag value: (%04x,%04x)=\"", g, e);
            msg = msg2;
            msg += valStr;
            msg += "\"";
            app.printError(msg.c_str());
        }
    }

    if (overrideKeys == NULL)
        overrideKeys = new DcmDataset;
    if (overrideKeys->insert(elem, OFTrue).bad())
    {
        sprintf(msg2, "cannot insert tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
}

static OFCondition addPresentationContext(T_ASC_Parameters* params);

static OFCondition mppsscu(T_ASC_Association* assoc, const char* fname);

static void updateAttributeValue(DcmItem* src, DcmItem* item, const DcmTagKey& seqTagKey, int verifyLength);
static OFCondition getAttributesForNCreate(DcmDataset* src, DcmDataset* dest);
static OFCondition getAttributesForNSet(DcmDataset* src, DcmDataset* dest);
static bool IsNullOrEmpty(DcmItem* item, const DcmTagKey& tagKey);
static bool checkRequestedProcedureCodeSequenceItem(DcmItem* item);

#define SHORTCOL 4
#define LONGCOL 16

int main_mppsscu(int argc, char* argv[])
{
    T_ASC_Network* net;
    T_ASC_Parameters* params;
    const char* opt_peer = NULL;
    OFCmdUnsignedInt opt_port = 104;
    DIC_NODENAME localHost;
    DIC_NODENAME peerHost;
    T_ASC_Association* assoc;
    OFList<OFString> fileNameList;

#ifdef WITH_OPENSSL
    int opt_keyFileFormat = SSL_FILETYPE_PEM;
    OFBool opt_doAuthenticate = OFFalse;
    const char* opt_privateKeyFile = NULL;
    const char* opt_certificateFile = NULL;
    const char* opt_passwd = NULL;
#if OPENSSL_VERSION_NUMBER >= 0x0090700fL
    OFString opt_ciphersuites(TLS1_TXT_RSA_WITH_AES_128_SHA ":" SSL3_TXT_RSA_DES_192_CBC3_SHA);
#else
    OFString opt_ciphersuites(SSL3_TXT_RSA_DES_192_CBC3_SHA);
#endif
    const char* opt_readSeedFile = NULL;
    const char* opt_writeSeedFile = NULL;
    DcmCertificateVerification opt_certVerification = DCV_requireCertificate;
    const char* opt_dhparam = NULL;
#endif

#ifdef HAVE_GUSI_H
    /* needed for Macintosh */
    GUSISetup(GUSIwithSIOUXSockets);
    GUSISetup(GUSIwithInternetSockets);
#endif

#ifdef HAVE_WINSOCK_H
    WSAData winSockData;
    /* we need at least version 1.1 */
    WORD winSockVersionNeeded = MAKEWORD(1, 1);
    WSAStartup(winSockVersionNeeded, &winSockData);
#endif

    char tempstr[20];
    OFConsoleApplication app(OFFIS_CONSOLE_APPLICATION, "DICOM mpps (N-CREATE or N-SET) SCU", rcsid);
    OFCommandLine cmd;

    cmd.setParamColumn(LONGCOL + SHORTCOL + 4);
    cmd.addParam("peer", "hostname of DICOM peer");
    cmd.addParam("port", "tcp/ip port number of peer");
    cmd.addParam("dcmfile_in", "N-CREATE or N-SET dataset to be sent (optional)", OFCmdParam::PM_MultiOptional);
    cmd.setOptionColumns(LONGCOL, SHORTCOL);
    cmd.addGroup("general options:", LONGCOL, SHORTCOL + 2);
    cmd.addOption("--help", "-h", "print this help text and exit");
    cmd.addOption("--version", "print version information and exit", OFTrue /* exclusive */);
    cmd.addOption("--verbose", "-v", "verbose mode, print processing details");
    cmd.addOption("--debug", "-d", "debug mode, print debug information");
    cmd.addGroup("mpps options:");
    cmd.addOption("--nauto", "-na", "auto-detect N-CREATE or N-SET (default)");
    cmd.addOption("--ncreate", "-nc", "send N-CRERATE message");
    cmd.addOption("--nset", "-ns", "send N-SET message");

    cmd.addGroup("network options:");
    cmd.addSubGroup("override keys:");
    cmd.addOption("--key", "-k", 1, "[k]ey: gggg,eeee=\"str\" path or dictionary name=\"str\"", "override key");
    cmd.addSubGroup("application entity titles:");
    OFString opt1 = "set my calling AE title (default: ";
    opt1 += APPLICATIONTITLE;
    opt1 += ")";
    cmd.addOption("--aetitle", "-aet", 1, "[a]etitle: string", opt1.c_str());
    OFString opt2 = "set called AE title of peer (default: ";
    opt2 += PEERAPPLICATIONTITLE;
    opt2 += ")";
    cmd.addOption("--call", "-aec", 1, "[a]etitle: string", opt2.c_str());
    cmd.addSubGroup("proposed transmission transfer syntaxes:");
    cmd.addOption("--propose-uncompr", "-x=",
                  "propose all uncompressed transfer syntaxes,\nexplicit VR with local byte ordering first (default)");
    cmd.addOption("--propose-little", "-xe",
                  "propose all uncompressed transfer syntaxes,\nexplicit VR little endian first");
    cmd.addOption("--propose-big", "-xb", "propose all uncompressed transfer syntaxes,\nexplicit VR big endian first");
    cmd.addOption("--propose-implicit", "-xi", "propose implicit VR little endian transfer syntaxes only");
    cmd.addSubGroup("other network options:");
    OFString opt3 = "set max receive pdu to n bytes (default: ";
    sprintf(tempstr, "%ld", (long)ASC_DEFAULTMAXPDU);
    opt3 += tempstr;
    opt3 += ")";
    OFString opt4 = "[n]umber of bytes: integer [";
    sprintf(tempstr, "%ld", (long)ASC_MINIMUMPDUSIZE);
    opt4 += tempstr;
    opt4 += "..";
    sprintf(tempstr, "%ld", (long)ASC_MAXIMUMPDUSIZE);
    opt4 += tempstr;
    opt4 += "]";
    cmd.addOption("--timeout", "-to", 1, "[s]econds: integer (default: unlimited)", "timeout for connection requests");
    cmd.addOption("--acse-timeout", "-ta", 1, "[s]econds: integer (default: 30)", "timeout for ACSE messages");
    cmd.addOption("--dimse-timeout", "-td", 1, "[s]econds: integer (default: unlimited)", "timeout for DIMSE messages");
    cmd.addOption("--max-pdu", "-pdu", 1, opt4.c_str(), opt3.c_str());
    cmd.addOption("--max-send-pdu", 1, opt4.c_str(), "restrict max send pdu to n bytes");
    cmd.addOption("--abort", "abort association instead of releasing it");

    /* evaluate command line */
    prepareCmdLineArgs(argc, argv, OFFIS_CONSOLE_APPLICATION);
    if (app.parseCommandLine(cmd, argc, argv, OFCommandLine::ExpandWildcards))
    {
        /* check exclusive options first */

        if (cmd.getParamCount() == 0)
        {
            if (cmd.findOption("--version"))
            {
                app.printHeader(OFTrue /*print host identifier*/); // uses ofConsole.lockCerr()
                CERR << endl << "External libraries used:";
#if !defined(WITH_ZLIB) && !defined(WITH_OPENSSL)
                CERR << " none" << endl;
#else
                CERR << endl;
#endif
#ifdef WITH_ZLIB
                CERR << "- ZLIB, Version " << zlibVersion() << endl;
#endif
#ifdef WITH_OPENSSL
                CERR << "- " << OPENSSL_VERSION_TEXT << endl;
#endif
                return 0;
            }
        }

        /* command line parameters */

        cmd.getParam(1, opt_peer);
        app.checkParam(cmd.getParamAndCheckMinMax(2, opt_port, 1, 65535));

        if (cmd.findOption("--verbose"))
            opt_verbose = OFTrue;
        if (cmd.findOption("--debug"))
        {
            opt_debug = OFTrue;
            DUL_Debug(OFTrue);
            DIMSE_debug(OFTrue);
            SetDebugLevel(3);
        }

        if (cmd.findOption("--ncreate"))
        {
            opt_mppsaction = DIMSE_NCreate;
        }
        else if (cmd.findOption("--nset"))
        {
            opt_mppsaction = DIMSE_NSet;
        }
        else
        {
            printf("Service type is not specified, please add --ncreate or --nset option\n");
            return 0;
        }

        if (cmd.findOption("--key", 0, OFCommandLine::FOM_First))
        {
            const char* ovKey = NULL;
            do
            {
                app.checkValue(cmd.getValue(ovKey));
                addOverrideKey(app, ovKey);
            } while (cmd.findOption("--key", 0, OFCommandLine::FOM_Next));
        }

        cmd.beginOptionBlock();
        if (cmd.findOption("--propose-uncompr"))
            opt_networkTransferSyntax = EXS_Unknown;
        if (cmd.findOption("--propose-little"))
            opt_networkTransferSyntax = EXS_LittleEndianExplicit;
        if (cmd.findOption("--propose-big"))
            opt_networkTransferSyntax = EXS_BigEndianExplicit;
        if (cmd.findOption("--propose-implicit"))
            opt_networkTransferSyntax = EXS_LittleEndianImplicit;
        cmd.endOptionBlock();

        if (cmd.findOption("--aetitle"))
            app.checkValue(cmd.getValue(opt_ourTitle));
        if (cmd.findOption("--call"))
            app.checkValue(cmd.getValue(opt_peerTitle));
        if (cmd.findOption("--timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            dcmConnectionTimeout.set((Sint32)opt_timeout);
        }

        if (cmd.findOption("--acse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_acse_timeout = OFstatic_cast(int, opt_timeout);
        }

        if (cmd.findOption("--dimse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_dimse_timeout = OFstatic_cast(int, opt_timeout);
            opt_blockMode = DIMSE_NONBLOCKING;
        }
        if (cmd.findOption("--max-pdu"))
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxReceivePDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
        if (cmd.findOption("--max-send-pdu"))
        {
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxSendPDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
            dcmMaxOutgoingPDUSize.set((Uint32)opt_maxSendPDULength);
        }
        if (cmd.findOption("--abort"))
            opt_abortAssociation = OFTrue;

        /* finally parse filenames */
        int paramCount = cmd.getParamCount();
        const char* currentFilename = NULL;
        OFString errormsg;

        for (int i = 3; i <= paramCount; i++)
        {
            cmd.getParam(i, currentFilename);
            if (access(currentFilename, R_OK) < 0)
            {
                errormsg = "cannot access file: ";
                errormsg += currentFilename;
                app.printError(errormsg.c_str());
            }
            fileNameList.push_back(currentFilename);
        }

        if ((fileNameList.empty()) && (overrideKeys == NULL))
        {
            app.printError("either query file or override keys (or both) must be specified");
        }
    }

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        fprintf(stderr, "Warning: no data dictionary loaded, check environment variable: %s\n",
                DCM_DICT_ENVIRONMENT_VARIABLE);
    }

    /* initialize network, i.e. create an instance of T_ASC_Network*. */
    OFCondition cond = ASC_initializeNetwork(NET_REQUESTOR, 0, opt_acse_timeout, &net);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        exit(1);
    }

    /* initialize asscociation parameters, i.e. create an instance of T_ASC_Parameters*. */
    cond = ASC_createAssociationParameters(&params, opt_maxReceivePDULength);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        exit(1);
    }

    /* sets this application's title and the called application's title in the params */
    /* structure. The default values to be set here are "STORESCU" and "ANY-SCP". */
    ASC_setAPTitles(params, opt_ourTitle, opt_peerTitle, NULL);

    //    /* Set the transport layer type (type of network connection) in the params */
    //    /* strucutre. The default is an insecure connection; where OpenSSL is  */
    //    /* available the user is able to request an encrypted,secure connection. */
    //    cond = ASC_setTransportLayerType(params, opt_secureConnection);
    //    if (cond.bad()) {
    //        DimseCondition::dump(cond);
    //        return 1;
    //    }

    /* Figure out the presentation addresses and copy the */
    /* corresponding values into the association parameters.*/
    gethostname(localHost, sizeof(localHost) - 1);
    sprintf(peerHost, "%s:%d", opt_peer, (int)opt_port);
    ASC_setPresentationAddresses(params, localHost, peerHost);

    /* Set the presentation contexts which will be negotiated */
    /* when the network connection will be established */
    cond = addPresentationContext(params);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        exit(1);
    }

    /* dump presentation contexts if required */
    if (opt_debug)
    {
        printf("Request Parameters:\n");
        ASC_dumpParameters(params, COUT);
    }

    /* create association, i.e. try to establish a network connection to another */
    /* DICOM application. This call creates an instance of T_ASC_Association*. */
    if (opt_verbose)
        printf("Requesting Association\n");
    cond = ASC_requestAssociation(net, params, &assoc);
    if (cond.bad())
    {
        if (cond == DUL_ASSOCIATIONREJECTED)
        {
            T_ASC_RejectParameters rej;

            ASC_getRejectParameters(params, &rej);
            errmsg("Association Rejected:");
            ASC_printRejectParameters(stderr, &rej);
            exit(1);
        }
        else
        {
            errmsg("Association Request Failed:");
            DimseCondition::dump(cond);
            exit(1);
        }
    }

    /* dump the presentation contexts which have been accepted/refused */
    if (opt_debug)
    {
        printf("Association Parameters Negotiated:\n");
        ASC_dumpParameters(params, COUT);
    }

    /* count the presentation contexts which have been accepted by the SCP */
    /* If there are none, finish the execution */
    if (ASC_countAcceptedPresentationContexts(params) == 0)
    {
        errmsg("No Acceptable Presentation Contexts");
        exit(1);
    }

    /* dump general information concerning the establishment of the network connection if required */
    if (opt_verbose)
    {
        printf("Association Accepted (Max Send PDV: %lu)\n", assoc->sendPDVLength);
    }

    /* do the real work, i.e. for all files which were specified in the command line, send a */
    /* N-CREATE/N-SET to the other DICOM application and receive corresponding response messages. */
    cond = EC_Normal;
    if (fileNameList.empty())
    {
        /* no files provided on command line */
        cond = mppsscu(assoc, NULL);
    }
    else
    {
        OFListIterator(OFString) iter = fileNameList.begin();
        OFListIterator(OFString) enditer = fileNameList.end();
        while ((iter != enditer) &&
               (cond == EC_Normal)) // compare with EC_Normal since DUL_PEERREQUESTEDRELEASE is also good()
        {
            cond = mppsscu(assoc, (*iter).c_str());
            ++iter;
        }
    }
    /* tear down association, i.e. terminate network connection to SCP */
    bool succeed = true;
    if (cond == EC_Normal)
    {
        if (opt_abortAssociation)
        {
            if (opt_verbose)
                printf("Aborting Association\n");
            cond = ASC_abortAssociation(assoc);
            if (cond.bad())
            {
                errmsg("Association Abort Failed:");
                DimseCondition::dump(cond);
                exit(1);
            }
        }
        else
        {
            /* release association */
            if (opt_verbose)
                printf("Releasing Association\n");
            cond = ASC_releaseAssociation(assoc);
            if (cond.bad())
            {
                errmsg("Association Release Failed:");
                DimseCondition::dump(cond);
                exit(1);
            }
        }
    }
    else if (cond == DUL_PEERREQUESTEDRELEASE)
    {
        errmsg("Protocol Error: peer requested release (Aborting)");
        if (opt_verbose)
            printf("Aborting Association\n");
        cond = ASC_abortAssociation(assoc);
        if (cond.bad())
        {
            errmsg("Association Abort Failed:");
            DimseCondition::dump(cond);
            exit(1);
        }
    }
    else if (cond == DUL_PEERABORTEDASSOCIATION)
    {
        if (opt_verbose)
            printf("Peer Aborted Association\n");
    }
    else
    {
        if (opt_verbose)
            printf("Aborting Association\n");
        cond = ASC_abortAssociation(assoc);
        if (cond.bad())
        {
            errmsg("Association Abort Failed:");
            DimseCondition::dump(cond);
            exit(1);
        }
        succeed = false;
    }

    /* destroy the association, i.e. free memory of T_ASC_Association* structure. This */
    /* call is the counterpart of ASC_requestAssociation(...) which was called above. */
    cond = ASC_destroyAssociation(&assoc);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        exit(1);
    }

    /* drop the network, i.e. free memory of T_ASC_Network* structure. This call */
    /* is the counterpart of ASC_initializeNetwork(...) which was called above. */
    cond = ASC_dropNetwork(&net);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        exit(1);
    }

#ifdef HAVE_WINSOCK_H
    WSACleanup();
#endif

    delete overrideKeys;

    if (!succeed)
    {
        exit(1);
    }
    return 0;
}

static void substituteOverrideKeys(DcmDataset* dset)
{
    if (overrideKeys == NULL)
    {
        return; /* nothing to do */
    }

    /* copy the override keys */
    DcmDataset keys(*overrideKeys);

    /* put the override keys into dset replacing existing tags */
    unsigned long elemCount = keys.card();
    for (unsigned long i = 0; i < elemCount; i++)
    {
        DcmElement* elem = keys.remove((unsigned long)0);

        dset->insert(elem, OFTrue);
    }
}

static OFCondition mppsscu(T_ASC_Association* assoc, const char* fname)
{
    DIC_US msgId = assoc->nextMsgID++;
    DcmDataset* statusDetail = NULL;
    DcmFileFormat dest;
    DcmFileFormat dcmff;

    /* if there is a valid filename */
    if (fname != NULL)
    {

        /* read information from file (this information specifies a search mask). After the */
        /* call to DcmFileFormat::read(...) the information which is encapsulated in the file */
        /* will be available through the DcmFileFormat object. In detail, it will be available */
        /* through calls to DcmFileFormat::getMetaInfo() (for meta header information) and */
        /* DcmFileFormat::getDataset() (for data set information). */
        OFCondition cond = dcmff.loadFile(fname);

        /* figure out if an error occured while the file was read*/
        if (cond.bad())
        {
            errmsg("Bad DICOM file: %s: %s", fname, cond.text());
            return cond;
        }
    }

    /* replace specific keys by those in overrideKeys */

    substituteOverrideKeys(dcmff.getDataset());
    if (opt_mppsaction == DIMSE_NCreate)
    {
        getAttributesForNCreate(dcmff.getDataset(), dest.getDataset());
    }
    else
    {
        getAttributesForNSet(dcmff.getDataset(), dest.getDataset());
    }

    const char* ppsStatus = NULL;
    dest.getDataset()->findAndGetString(DCM_PerformedProcedureStepStatus, ppsStatus);

    if (ppsStatus == NULL)
    {
        printf("PerformedProcedureStepStatus should not be empty\n");
        return EC_IllegalParameter;
    }
    else if (strcmp(ppsStatus, IN_PROGRESS) != 0 && strcmp(ppsStatus, COMPLETED) != 0 &&
             strcmp(ppsStatus, DISCONTINUED) != 0)
    {
        printf("PerformedProcedureStepStatus dose not be set correctly\n");
        return EC_IllegalParameter;
    }

    const char* sopInstanceUID = NULL;

    dcmff.getDataset()->findAndGetString(DCM_SOPInstanceUID, sopInstanceUID);

    if (sopInstanceUID == NULL)
    {
        printf("sopInstanceUID should not be empty\n");
        return EC_IllegalParameter;
    }

    /* if required, dump some more general information */
    if (opt_verbose)
    {
        printf("MPPS SCU RQ: MsgID %d\n", msgId);
        printf("REQUEST:\n");
        dest.getDataset()->print(COUT);
        printf("--------\n");
    }

    OFCondition cond = EC_Normal;
    if (opt_mppsaction == DIMSE_NCreate)
    {

        T_DIMSE_N_CreateRQ req;
        T_DIMSE_N_CreateRSP rsp;

        bzero((char*)&req, sizeof(req));

        req.MessageID = msgId;
        strcpy(req.AffectedSOPClassUID, UID_ModalityPerformedProcedureStepSOPClass);
        strcpy(req.AffectedSOPInstanceUID, sopInstanceUID);
        req.DataSetType = DIMSE_DATASET_PRESENT;

        req.opts = O_NCREATE_AFFECTEDSOPINSTANCEUID;

        /* finally conduct transmission of data */
        cond = DIMSE_nCreateUser(assoc, &req, dest.getDataset(), opt_blockMode, opt_dimse_timeout, &rsp, &statusDetail);

        if (opt_verbose)
        {
            DIMSE_printNCreateRSP(stdout, &rsp);
        }

        /* depending on if a response was received, dump some information */
        if (cond.bad())
        {
            errmsg("N-CREATE Failed:");
            DimseCondition::dump(cond);
        }
    }
    else if (opt_mppsaction == DIMSE_NSet)
    {

        T_DIMSE_N_SetRQ req;
        T_DIMSE_N_SetRSP rsp;

        bzero((char*)&req, sizeof(req));

        req.MessageID = msgId;
        strcpy(req.RequestedSOPClassUID, UID_ModalityPerformedProcedureStepSOPClass);
        strcpy(req.RequestedSOPInstanceUID, sopInstanceUID);

        req.DataSetType = DIMSE_DATASET_PRESENT;

        /* finally conduct transmission of data */
        cond = DIMSE_nSetUser(assoc, &req, dest.getDataset(), opt_blockMode, opt_dimse_timeout, &rsp, &statusDetail);

        if (opt_verbose)
        {
            DIMSE_printNSetRSP(stdout, &rsp);
        }

        /* depending on if a response was received, dump some information */
        if (cond.bad())
        {
            errmsg("N-SET Failed:");
            DimseCondition::dump(cond);
        }
    }

    /* dump status detail information if there is some */
    if (statusDetail != NULL)
    {
        printf("  Status Detail:\n");
        statusDetail->print(COUT);
        delete statusDetail;
    }
    /* return result value */
    return cond;
}

static OFCondition addPresentationContext(T_ASC_Parameters* params)
{
    /*
    ** We prefer to use Explicitly encoded transfer syntaxes.
    ** If we are running on a Little Endian machine we prefer
    ** LittleEndianExplicitTransferSyntax to BigEndianTransferSyntax.
    ** Some SCP implementations will just select the first transfer
    ** syntax they support (this is not part of the standard) so
    ** organise the proposed transfer syntaxes to take advantage
    ** of such behaviour.
    **
    ** The presentation contexts proposed here are only used for
    ** N-CREATE and N-SET, so there is no need to support compressed
    ** transmission.
    */

    const char* transferSyntaxes[] = {NULL, NULL, NULL};
    int numTransferSyntaxes = 0;

    switch (opt_networkTransferSyntax)
    {
    case EXS_LittleEndianImplicit:
        /* we only support Little Endian Implicit */
        transferSyntaxes[0] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 1;
        break;
    case EXS_LittleEndianExplicit:
        /* we prefer Little Endian Explicit */
        transferSyntaxes[0] = UID_LittleEndianExplicitTransferSyntax;
        transferSyntaxes[1] = UID_BigEndianExplicitTransferSyntax;
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    case EXS_BigEndianExplicit:
        /* we prefer Big Endian Explicit */
        transferSyntaxes[0] = UID_BigEndianExplicitTransferSyntax;
        transferSyntaxes[1] = UID_LittleEndianExplicitTransferSyntax;
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    default:
        /* We prefer explicit transfer syntaxes.
         * If we are running on a Little Endian machine we prefer
         * LittleEndianExplicitTransferSyntax to BigEndianTransferSyntax.
         */
        if (gLocalByteOrder == EBO_LittleEndian) /* defined in dcxfer.h */
        {
            transferSyntaxes[0] = UID_LittleEndianExplicitTransferSyntax;
            transferSyntaxes[1] = UID_BigEndianExplicitTransferSyntax;
        }
        else
        {
            transferSyntaxes[0] = UID_BigEndianExplicitTransferSyntax;
            transferSyntaxes[1] = UID_LittleEndianExplicitTransferSyntax;
        }
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    }

    return ASC_addPresentationContext(params, 1, UID_ModalityPerformedProcedureStepSOPClass, transferSyntaxes,
                                      numTransferSyntaxes);
}

static void updateAttributeValue(DcmItem* src, DcmItem* item, const DcmTagKey& seqTagKey, int verifyLength)
{
    const char* context = NULL;

    if (src->findAndGetString(seqTagKey, context).good())
    {
        if (verifyLength > 0 && strlen(context) > (size_t)verifyLength)
        {
            char* destContext = new char[verifyLength];
            strncpy(destContext, context, verifyLength);
            item->putAndInsertString(seqTagKey, destContext);

            delete[] destContext;
        }
        else
        {
            item->putAndInsertString(seqTagKey, context);
        }
    }
    else
    {
        item->putAndInsertString(seqTagKey, "");
    }
}

static OFCondition getAttributesForNCreate(DcmDataset* src, DcmDataset* dest)
{
    DcmSequenceOfItems* scheduledStepAttributesSequence = new DcmSequenceOfItems(DCM_ScheduledStepAttributesSequence);
    DcmItem* scheduledStepAttributeSequenceItem = NULL;
    if (src->findAndGetSequenceItem(DCM_ScheduledStepAttributesSequence, scheduledStepAttributeSequenceItem, 0).good())
    {
        scheduledStepAttributesSequence->insert(scheduledStepAttributeSequenceItem);
    }
    else
    {
        scheduledStepAttributeSequenceItem = new DcmItem;

        scheduledStepAttributesSequence->insert(scheduledStepAttributeSequenceItem);

        updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_StudyInstanceUID, 0);

        DcmSequenceOfItems* referencedStudySequence = NULL;

        if (src->findAndGetSequence(DCM_ReferencedStudySequence, referencedStudySequence, true).good())
        {
            scheduledStepAttributeSequenceItem->insert(referencedStudySequence);
        }
        else
        {
            referencedStudySequence = new DcmSequenceOfItems(DCM_ReferencedStudySequence);
        }
        scheduledStepAttributeSequenceItem->insert(referencedStudySequence);

        updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_AccessionNumber, 0);
        updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_RequestedProcedureID, 16);

        DcmSequenceOfItems* requestedProcedureCodeSequence = NULL;

        if (src->findAndGetSequence(DCM_RequestedProcedureCodeSequence, requestedProcedureCodeSequence, true).good())
        {
            scheduledStepAttributeSequenceItem->insert(referencedStudySequence);
        }
        else
        {
            requestedProcedureCodeSequence = new DcmSequenceOfItems(DCM_RequestedProcedureCodeSequence);
        }

        scheduledStepAttributeSequenceItem->insert(requestedProcedureCodeSequence);

        updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_RequestedProcedureDescription, 0);

        DcmItem* scheduledProcedureStepSequenceItem = NULL;
        if (src->findAndGetSequenceItem(DCM_ScheduledProcedureStepSequence, scheduledProcedureStepSequenceItem, 0)
                .good())
        {
            const char* modaliity = NULL;
            updateAttributeValue(scheduledProcedureStepSequenceItem, scheduledStepAttributeSequenceItem,
                                 DCM_ScheduledProcedureStepID, 16);
            updateAttributeValue(scheduledProcedureStepSequenceItem, scheduledStepAttributeSequenceItem,
                                 DCM_ScheduledProcedureStepDescription, 0);
            if (scheduledProcedureStepSequenceItem->findAndGetString(DCM_Modality, modaliity).good())
            {
                opt_scheduledModality = modaliity;
            }
        }
        else
        {
            updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_ScheduledProcedureStepID, 16);
            updateAttributeValue(src, scheduledStepAttributeSequenceItem, DCM_ScheduledProcedureStepDescription, 16);
        }

        DcmSequenceOfItems* scheduledProtocolCodeSequence = NULL;
        DcmItem* scheduledProtocolCodeSequenceItem = NULL;
        if (scheduledProcedureStepSequenceItem != NULL)
        {
            scheduledProtocolCodeSequence = new DcmSequenceOfItems(DCM_ScheduledProtocolCodeSequence);
            if (scheduledProcedureStepSequenceItem
                    ->findAndGetSequenceItem(DCM_ScheduledProtocolCodeSequence, scheduledProtocolCodeSequenceItem, 0)
                    .good())
            {
                scheduledProtocolCodeSequence->insert(scheduledProtocolCodeSequenceItem);
            }
        }
        else
        {
            if (src->findAndGetSequence(DCM_ScheduledProtocolCodeSequence, scheduledProtocolCodeSequence, true).bad())
            {
                scheduledProtocolCodeSequence = new DcmSequenceOfItems(DCM_ScheduledProtocolCodeSequence);
            }
        }

        scheduledStepAttributeSequenceItem->insert(scheduledProtocolCodeSequence);
    }

    dest->insert(scheduledStepAttributesSequence);

    updateAttributeValue(src, dest, DCM_PatientsName, 0);
    updateAttributeValue(src, dest, DCM_PatientID, 0);
    updateAttributeValue(src, dest, DCM_PatientsBirthDate, 0);
    updateAttributeValue(src, dest, DCM_PatientsSex, 0);

    DcmSequenceOfItems* referencedPatientSequence = new DcmSequenceOfItems(DCM_ReferencedPatientSequence);
    DcmItem* referencedPatientSequenceItem = NULL;
    if (src->findAndGetSequenceItem(DCM_ReferencedPatientSequence, referencedPatientSequenceItem, 0).good())
    {
        referencedPatientSequence->insert(referencedPatientSequenceItem);
    }
    dest->insert(referencedPatientSequence);

    OFDateTime dateTime;
    dateTime.setCurrentDateTime();

    OFString currentDateTime;
    OFString currentDate;
    OFString currentTime;
    dateTime.getISOFormattedDateTime(currentDateTime, true, false, false, false);
    dateTime.getDate().getISOFormattedDate(currentDate, false);
    dateTime.getTime().getISOFormattedTime(currentTime, true, false, false, false);

    dest->putAndInsertOFStringArray(DCM_PerformedProcedureStepID, currentDateTime);
    dest->putAndInsertString(DCM_PerformedStationAETitle, opt_ourTitle);
    dest->putAndInsertString(DCM_PerformedStationName, "");
    dest->putAndInsertString(DCM_PerformedLocation, "");
    dest->putAndInsertOFStringArray(DCM_PerformedProcedureStepStartDate, currentDate);
    dest->putAndInsertOFStringArray(DCM_PerformedProcedureStepStartTime, currentTime);
    dest->putAndInsertString(DCM_PerformedProcedureStepStatus, IN_PROGRESS);
    updateAttributeValue(src, dest, DCM_PerformedProcedureStepDescription, 0);
    dest->putAndInsertString(DCM_PerformedProcedureTypeDescription, "");

    DcmSequenceOfItems* procedureCodeSequence = new DcmSequenceOfItems(DCM_ProcedureCodeSequence);
    int itemIndex = 0;

    DcmItem* item = NULL;

    while (src->findAndGetSequenceItem(DCM_RequestedProcedureCodeSequence, item, itemIndex++).good())
    {
        if (checkRequestedProcedureCodeSequenceItem(item))
        {
            procedureCodeSequence->insert(item);
        }
    }

    dest->insert(procedureCodeSequence);

    dest->putAndInsertString(DCM_PerformedProcedureStepEndDate, "");
    dest->putAndInsertString(DCM_PerformedProcedureStepEndTime, "");

    DcmSequenceOfItems* performedProcedureStepDiscontinuationReasonCodeSequence =
        new DcmSequenceOfItems(DCM_PerformedProcedureStepDiscontinuationReasonCodeSequence);
    DcmItem* performedProcedureStepDiscontinuationReasonCodeSequenceItem = NULL;
    if (src->findAndGetSequenceItem(DCM_PerformedProcedureStepDiscontinuationReasonCodeSequence,
                                    performedProcedureStepDiscontinuationReasonCodeSequenceItem, 0)
            .good())
    {
        performedProcedureStepDiscontinuationReasonCodeSequence->insert(
            performedProcedureStepDiscontinuationReasonCodeSequenceItem);
    }
    dest->insert(performedProcedureStepDiscontinuationReasonCodeSequence);

    dest->putAndInsertOFStringArray(DCM_Modality, opt_scheduledModality);

    updateAttributeValue(src, dest, DCM_StudyID, 0);

    DcmSequenceOfItems* PerformedProtocolCodeSequence = new DcmSequenceOfItems(DCM_PerformedProtocolCodeSequence);
    DcmItem* PerformedProtocolCodeSequenceItem = NULL;
    if (src->findAndGetSequenceItem(DCM_PerformedProtocolCodeSequence, PerformedProtocolCodeSequenceItem, 0).good())
    {
        PerformedProtocolCodeSequence->insert(PerformedProtocolCodeSequenceItem);
    }
    dest->insert(PerformedProtocolCodeSequence);

    DcmSequenceOfItems* performedSeriesSequence = new DcmSequenceOfItems(DCM_PerformedSeriesSequence);
    DcmItem* performedSeriesSequenceItem = NULL;
    if (src->findAndGetSequenceItem(DCM_PerformedSeriesSequence, performedSeriesSequenceItem, 0).good())
    {
        performedSeriesSequence->insert(performedSeriesSequenceItem);
    }
    dest->insert(performedSeriesSequence);

    return EC_Normal;
}

static OFCondition getAttributesForNSet(DcmDataset* src, DcmDataset* dest)
{
    DcmSequenceOfItems* procedureCodeSequence = new DcmSequenceOfItems(DCM_ProcedureCodeSequence);
    int itemIndex = 0;

    DcmItem* item = NULL;

    while (src->findAndGetSequenceItem(DCM_RequestedProcedureCodeSequence, item, itemIndex++).good())
    {
        if (checkRequestedProcedureCodeSequenceItem(item))
        {
            procedureCodeSequence->insert(item);
        }
    }

    dest->insert(procedureCodeSequence);
    dest->putAndInsertString(DCM_PerformedProcedureStepStatus, COMPLETED);

    OFDateTime dateTime;
    dateTime.setCurrentDateTime();

    OFString currentDateTime;
    OFString currentDate;
    OFString currentTime;

    dateTime.getISOFormattedDateTime(currentDateTime, true, false, false, false);
    dateTime.getDate().getISOFormattedDate(currentDate, false);
    dateTime.getTime().getISOFormattedTime(currentTime, true, false, false, false);

    dest->putAndInsertOFStringArray(DCM_PerformedProcedureStepEndDate, currentDate);
    dest->putAndInsertOFStringArray(DCM_PerformedProcedureStepEndTime, currentTime);

    dest->putAndInsertString(DCM_PerformedProcedureStepDescription, "", 0);

    DcmSequenceOfItems* performedSeriesSequence = new DcmSequenceOfItems(DCM_PerformedSeriesSequence);
    if (performedSeriesSequence)
    {
        DcmItem* item = new DcmItem;
        if (item)
        {
            performedSeriesSequence->insert(item);
            updateAttributeValue(src, item, DCM_PerformingPhysiciansName, 0);
            item->putAndInsertString(DCM_ProtocolName, "Test");
            item->putAndInsertString(DCM_OperatorsName, "");
            item->putAndInsertString(DCM_SeriesInstanceUID, "1");
            item->putAndInsertString(DCM_SeriesDescription, "");
            item->putAndInsertString(DCM_RetrieveAETitle, "");
        }
        item->insert(new DcmSequenceOfItems(DCM_ReferencedImageSequence));
    }
    dest->insert(performedSeriesSequence);

    return EC_Normal;
}

static bool IsNullOrEmpty(DcmItem* item, const DcmTagKey& tagKey)
{
    const char* context = NULL;
    return (item->findAndGetString(tagKey, context).bad() || strlen(context) <= 0);
}

static bool checkRequestedProcedureCodeSequenceItem(DcmItem* item)
{
    if (IsNullOrEmpty(item, DCM_CodeMeaning))
    {
        return false;
    }
    if (!IsNullOrEmpty(item, DCM_CodeValue) && IsNullOrEmpty(item, DCM_CodingSchemeDesignator))
    {
        return false;
    }

    if (!IsNullOrEmpty(item, DCM_CodingSchemeDesignator) && IsNullOrEmpty(item, DCM_CodingSchemeVersion))
    {
        return false;
    }
    return true;
}
