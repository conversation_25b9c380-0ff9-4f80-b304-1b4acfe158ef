#ifndef PIXELDATAFILEHEAD_H
#define PIXELDATAFILEHEAD_H

#include "typesdef.h"
#include "dcmutilitydef.h"
#include <stdio.h>

class PixelDataFileHead
{
public:
    PixelDataFileHead(const char* fileName, PhotometricInterpretationType rawInterpretationType,
                      PhotometricInterpretationType screenInterpretationType, const DcmStorage_Option& gStorageOption,
                      int defFps, bool opt_verbose);
    ~PixelDataFileHead();
    bool isValid() const;
    FILE* pFile() const;
    Uint16 fps() const;
    bool isNeedPalette() const;
    bool isMovie() const;
    PhotometricInterpretationType photometricInterpretationType() const;
    Uint16 samplesPerPixel() const;
    Uint16 columns() const;
    Uint16 rows() const;
    Uint32 bytesPerLine() const;
    Uint32 frameSize() const;
    Uint32 imageSize() const;
    Uint32 numberOfFrames() const;
    Uint32 numberOfReadFrames() const;
    /**
     * @brief quality 1...100, 0:lossless
     * @return
     */
    Uint8 quality() const;
    Uint8 colorspace() const;
    bool isNeedCompress() const;
    bool isNeedSample() const;
    float sampleInterval() const;
    bool isValidFps() const;
    const DcmStorage_Option& storageOption() const;

private:
    FILE* m_pFile;
    FileStruct_head m_FileHead;
    int m_Fps;
    PhotometricInterpretationType m_InterpretationType;
    Uint32 m_SamplesPerPixel;
    Uint32 m_FrameSize;
    Uint32 m_ImageSize;
    Uint32 m_NumberOfReadFrames;
    float m_SampleInterval;
    DcmStorage_Option m_StorageOption;
};

#endif // PIXELDATAFILEHEAD_H
