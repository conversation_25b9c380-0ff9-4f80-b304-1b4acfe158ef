#include "dcmtk/ofstd/ofstdinc.h"

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#include "dcmtk/ofstd/ofstd.h"
#include "dcmtk/ofstd/ofstring.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/ofstd/ofdatime.h"
#include "dcmtk/ofstd/ofthread.h"

#include "dcmtk/dcmdata/dctk.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/dcmdata/dcuid.h" /* for dcmtk version name */
#include "dcmtk/dcmdata/dccodec.h"
#include "dcmtk/dcmdata/dcdebug.h"

#ifdef WITH_OPENSSL
#include "dcmtk/dcmtls/tlstrans.h"
#include "dcmtk/dcmtls/tlslayer.h"
#endif

#ifdef WITH_ZLIB
#include "zlib.h" /* for zlibVersion() */
#endif

#include "dcmwritersp.h"

const char* RequestKey[] = {
    "0010,0010", "0010,0020", "0010,0040", "0008,0050", "0008,0090", "0032,1032", "0008,0060",
};

const char* ResponseKey[] = {
    "0010,0010",
    "0010,0020",
    "0010,0030",
    "0010,0040",
    //"0040,0006",
    "0032,1032",
};

static void copyString(char* dest, int destSize, const char* src)
{
    if (src == NULL)
    {
        dest[0] = '\0';
    }
    else
    {
        strncpy(dest, src, destSize);
        dest[destSize - 1] = '\0';
    }
}

int writeRspToFile(FILE* fp, DcmDataset* dataset)
{
    /* write out as a file format */
    const char* SOPClassText = NULL;
    DcmResponse_Tag ResponseData;
    DcmFileFormat fileformat(dataset); // copies dataset
    OFCondition ec = fileformat.error();
    if (ec.bad())
    {
        printf("error writing file: %s", ec.text());
        return -1;
    }

    fileformat.getDataset()->findAndGetString(DCM_PatientsName, SOPClassText);
    copyString(ResponseData.PatientsName, sizeof(ResponseData.PatientsName), SOPClassText);
    // printf("%s\n",SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_PatientID, SOPClassText);
    copyString(ResponseData.PatientID, sizeof(ResponseData.PatientID), SOPClassText);
    // printf("%s\n",SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_PatientsBirthDate, SOPClassText);
    copyString(ResponseData.PatientsBirthDate, sizeof(ResponseData.PatientsBirthDate), SOPClassText);
    // printf("%s\n",SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_PatientsSex, SOPClassText);
    copyString(ResponseData.PatientsSex, sizeof(ResponseData.PatientsSex), SOPClassText);
    // printf("%s\n",SOPClassText);
    // fileformat.getDataset()->findAndGetString(DCM_ScheduledPerformingPhysiciansName, SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_RequestingPhysician, SOPClassText);
    copyString(ResponseData.ScheduledPerformingPhysiciansName, sizeof(ResponseData.ScheduledPerformingPhysiciansName),
               SOPClassText);
    // printf("%s\n",SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_AccessionNumber, SOPClassText);
    copyString(ResponseData.AccessionNumber, sizeof(ResponseData.AccessionNumber), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyInstanceUID, SOPClassText);
    copyString(ResponseData.StudyInstanceID, sizeof(ResponseData.StudyInstanceID), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyDescription, SOPClassText);
    copyString(ResponseData.StudyDescription, sizeof(ResponseData.StudyDescription), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_RequestedProcedureDescription, SOPClassText);
    copyString(ResponseData.RequestedProcedureDescription, sizeof(ResponseData.RequestedProcedureDescription),
               SOPClassText);
    // ScheduledProcedureStepDescription
    DcmItem* spssItem = NULL;
    if (fileformat.getDataset()->findAndGetString(DCM_ScheduledProcedureStepDescription, SOPClassText).bad())
    {
        if (fileformat.getDataset()->findAndGetSequenceItem(DCM_ScheduledProcedureStepSequence, spssItem, 0).good())
        {
            spssItem->findAndGetString(DCM_ScheduledProcedureStepDescription, SOPClassText);
        }
    }
    copyString(ResponseData.ScheduledProcedureStepDescription, sizeof(ResponseData.ScheduledProcedureStepDescription),
               SOPClassText);

    // CodeMeaning
    {
        bool isCodeMeaningBad = fileformat.getDataset()->findAndGetString(DCM_CodeMeaning, SOPClassText).bad();
        bool isEmpty = true;
        if (SOPClassText != NULL)
        {
            size_t len = strlen(SOPClassText);
            isEmpty = len <= 0;
        }

        if (isCodeMeaningBad || isEmpty)
        {
            if (spssItem != NULL)
            {
                DcmItem* spcsItem = NULL;
                if (spssItem->findAndGetSequenceItem(DCM_ScheduledProtocolCodeSequence, spcsItem, 0).good())
                {
                    isCodeMeaningBad = spcsItem->findAndGetString(DCM_CodeMeaning, SOPClassText).bad();
                }
            }
        }

        isEmpty = true;
        if (SOPClassText != NULL)
        {
            size_t len = strlen(SOPClassText);
            isEmpty = len <= 0;
        }

        if (isCodeMeaningBad || isEmpty)
        {
            DcmItem* rpcsItem = NULL;
            if (fileformat.getDataset()->findAndGetSequenceItem(DCM_RequestedProcedureCodeSequence, rpcsItem, 0).good())
            {
                rpcsItem->findAndGetString(DCM_CodeMeaning, SOPClassText);
            }
        }

        copyString(ResponseData.CodeMeaning, sizeof(ResponseData.CodeMeaning), SOPClassText);
    }

    fileformat.getDataset()->findAndGetString(DCM_PatientsSize, SOPClassText);
    copyString(ResponseData.PatientSize, sizeof(ResponseData.PatientSize), SOPClassText);
    fileformat.getDataset()->findAndGetString(DCM_PatientsWeight, SOPClassText);
    copyString(ResponseData.PatientWeight, sizeof(ResponseData.PatientWeight), SOPClassText);

    // printf("%s\n",SOPClassText);
    /*
    fileformat.getDataset()->findAndGetString(DCM_PatientsName,OFconst_cast(char *&, ResponseData.PatientsName));
    fileformat.getDataset()->findAndGetString(DCM_PatientID, OFconst_cast(char *&, ResponseData.PatientID));
    fileformat.getDataset()->findAndGetString(DCM_PatientsBirthDate, OFconst_cast(char *&,
    ResponseData.PatientsBirthDate)); fileformat.getDataset()->findAndGetString(DCM_PatientsSex, OFconst_cast(char *&,
    ResponseData.PatientsSex)); fileformat.getDataset()->findAndGetString(DCM_ScheduledPerformingPhysiciansName,
    OFconst_cast(char *&, ResponseData.ScheduledPerformingPhysiciansName));
     */

    if (fp)
        fwrite(&ResponseData, sizeof(DcmResponse_Tag), 1, fp);
    else
        return -1;

    return 0;
}

int writeQRRspToFile(FILE* fp, DcmDataset* dataset)
{
    const char* SOPClassText = NULL;
    DcmQueryResponse_Tag responseData;

    memset(&responseData, 0, sizeof(DcmQueryResponse_Tag));
    DcmFileFormat fileformat(dataset); // copies dataset
    OFCondition ec = fileformat.error();
    if (ec.bad())
    {
        printf("error writing file: %s", ec.text());
        return -1;
    }

    fileformat.getDataset()->findAndGetString(DCM_PatientsName, SOPClassText);
    copyString(responseData.PatientsName, sizeof(responseData.PatientsName), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientID, SOPClassText);
    copyString(responseData.PatientID, sizeof(responseData.PatientID), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientsSex, SOPClassText);
    copyString(responseData.PatientsSex, sizeof(responseData.PatientsSex), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientsBirthDate, SOPClassText);
    copyString(responseData.PatientsBirthDate, sizeof(responseData.PatientsBirthDate), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientsBirthTime, SOPClassText);
    copyString(responseData.PatientsBirthTime, sizeof(responseData.PatientsBirthTime), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyDate, SOPClassText);
    copyString(responseData.StudyDate, sizeof(responseData.StudyDate), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyTime, SOPClassText);
    copyString(responseData.StudyTime, sizeof(responseData.StudyTime), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyID, SOPClassText);
    copyString(responseData.StudyID, sizeof(responseData.StudyID), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_AccessionNumber, SOPClassText);
    copyString(responseData.AccessionNumber, sizeof(responseData.AccessionNumber), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_ReferringPhysiciansName, SOPClassText);
    copyString(responseData.ReferringPhysiciansName, sizeof(responseData.ReferringPhysiciansName), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_SpecificCharacterSet, SOPClassText);
    copyString(responseData.SpecificCharacterSet, sizeof(responseData.SpecificCharacterSet), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyInstanceUID, SOPClassText);
    copyString(responseData.StudyInstanceID, sizeof(responseData.StudyInstanceID), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_StudyDescription, SOPClassText);
    copyString(responseData.StudyDescription, sizeof(responseData.StudyDescription), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_ModalitiesInStudy, SOPClassText);
    copyString(responseData.Modality, sizeof(responseData.Modality), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_NumberOfStudyRelatedSeries, SOPClassText);
    copyString(responseData.NumberOfStudyRelatedSeries, sizeof(responseData.NumberOfStudyRelatedSeries), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_NumberOfStudyRelatedInstances, SOPClassText);
    copyString(responseData.NumberOfStudyRelatedInstances, sizeof(responseData.NumberOfStudyRelatedInstances),
               SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientsSize, SOPClassText);
    copyString(responseData.PatientSize, sizeof(responseData.PatientSize), SOPClassText);

    fileformat.getDataset()->findAndGetString(DCM_PatientsWeight, SOPClassText);
    copyString(responseData.PatientWeight, sizeof(responseData.PatientWeight), SOPClassText);

    if (fp)
    {
        fwrite(&responseData, sizeof(DcmQueryResponse_Tag), 1, fp);
        return 0;
    }
    else
    {
        return -1;
    }
}
