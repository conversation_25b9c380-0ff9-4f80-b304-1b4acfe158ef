#include "basepixeldataencoder.h"
#include "dcmtk/dcmdata/dcdeftag.h"
#include "dcmtk/dcmdata/dcitem.h"
#include "dcmtk/dcmdata/dcuid.h"
#include "dcmtk/dcmimage/diqtctab.h"
#include "dcmtk/ofstd/ofconsol.h"
#include "dcmtk/dcmimage/diqtpbox.h"
#include "dcmtk/dcmdata/dcvrus.h"
#include "dcmtk/dcmdata/dcswap.h"
#include "dcmtk/dcmdata/dcvrobow.h"
#include "ipixeldatafilehead.h"
#include "linepixeldatafile.h"

static void CreatePaletteColorMap(const Uint8* palette, DcmQuantHistogramItemPointer*& array, size_t numColors);
static OFCondition WritePaletteColorMap(DcmItem& target, size_t numColors, DcmQuantHistogramItemPointer* array,
                                        bool writeAsOW, bool write16BitEntries);

BasePixelDataEncoder::~BasePixelDataEncoder()
{
    freeMem();
}

Uint32 BasePixelDataEncoder::numberOfFrames() const
{
    return m_PixelDataFile->numberOfFramesReaded();
}

Uint32 BasePixelDataEncoder::pixelSize() const
{
    return m_PixelDataFile->pixelSizeReaded();
}

BasePixelDataEncoder::BasePixelDataEncoder(LinePixelDataFile* basePixelDataFile)
    : m_PixelDataFile(basePixelDataFile)
{
}

OFCondition BasePixelDataEncoder::insertFrameInfo(DcmItem* dataset, LinePixelDataFile* pixelDataFile) const
{
    OFCondition result = EC_Normal;
    const IPixelDataFileHead& fileHead = pixelDataFile->fileHead();
    Uint32 numOfFrames = numberOfFrames();
    dataset->putAndInsertString(DCM_SOPClassUID,
                                numOfFrames <= 1 ? UID_UltrasoundImageStorage : UID_UltrasoundMultiframeImageStorage);

    // only for multi frame
    // part03 CINE MODULE ATTRIBUTES (C.7-13)
    if (numOfFrames > 1)
    {
        // changed 2011-4-08
        char strVar[20];
        int fps = fileHead.fps();

        float frameDurationMs = 1000.0f / fps;
        float effectiveDuration = numOfFrames / (float)fps;

        sprintf(strVar, "%d", numOfFrames);
        if (result.good())
            result = dataset->putAndInsertString(DCM_NumberOfFrames, strVar); // DCM_NumberOfFrames
        sprintf(strVar, "%d", 1);
        if (result.good())
            result = dataset->putAndInsertString(DCM_StartTrim, strVar);
        sprintf(strVar, "%d", numOfFrames);
        if (result.good())
            result = dataset->putAndInsertString(DCM_StopTrim, strVar);

        sprintf(strVar, "%d", fps);
        if (result.good())
            result = dataset->putAndInsertString(DCM_RecommendedDisplayFrameRate, strVar);
        if (result.good())
            result = dataset->putAndInsertString(DCM_CineRate, strVar);
        sprintf(strVar, "%f", effectiveDuration);
        if (result.good())
            result = dataset->putAndInsertString(DCM_EffectiveDuration, strVar);
        if (result.good())
            result = dataset->putAndInsertString(DCM_FrameDelay, "0");
        sprintf(strVar, "%d", (int)effectiveDuration);
        if (result.good())
            result = dataset->putAndInsertString(DCM_ActualFrameDuration, strVar);
        sprintf(strVar, "%f", frameDurationMs);
        if (result.good())
            result = dataset->putAndInsertString(DCM_FrameTime, strVar);
        if (result.good())
            result =
                dataset->putAndInsertString(DCM_FrameIncrementPointer, "(0018,1063)"); //(0018,1063) is DCM_FrameTime
        if (result.good())
            result = dataset->putAndInsertString(DCM_PreferredPlaybackSequencing, "0");
    }

    if (result.good())
        result = insertImageType(dataset);

    //设置像素数据相关信息
    if (result.good())
        result = dataset->putAndInsertUint16(
            DCM_BitsAllocated, pixelDataFile->bitsAllocated()); //与DCM_SamplesPerPixel的乘积确定一个像素的位数(bits)
    if (result.good())
        result = dataset->putAndInsertUint16(DCM_BitsStored, pixelDataFile->bitsStored());
    if (result.good())
        result = dataset->putAndInsertUint16(DCM_HighBit, pixelDataFile->bitsStored() - 1);
    if (result.good())
        result = dataset->putAndInsertUint16(DCM_PixelRepresentation, 0); // 0:unsigned 1: signed
    if (result.good())
        result = dataset->putAndInsertUint16(DCM_Rows, pixelDataFile->rows());
    if (result.good())
        result = dataset->putAndInsertUint16(DCM_Columns, pixelDataFile->columns());

    if (result.good())
        result = dataset->putAndInsertUint16(DCM_SamplesPerPixel, pixelDataFile->samplesPerPixel());
    if (result.good())
    {
        if (pixelDataFile->colorspace() == 2)
        {
            result = dataset->putAndInsertString(DCM_PhotometricInterpretation, "YBR_FULL");
        }
        else
        {
            result =
                dataset->putAndInsertString(DCM_PhotometricInterpretation, pixelDataFile->photometricInterpretation());
        }
    }

    if (fileHead.photometricInterpretationType() == rgb)
    {
        if (result.good())
            result = dataset->putAndInsertUint16(DCM_PlanarConfiguration,
                                                 pixelDataFile->planarConfiguration()); // DCM_SamplesPerPixel > 1时设置
    }

    //    if(fileHead.photometricInterpretationType() == palette_color)
    //    {
    //        if(result.good()) result = dataset->putAndInsertUint16(DCM_SmallestImagePixelValue, 0);//color
    //        table的索引最大值, if(result.good()) result = dataset->putAndInsertUint16(DCM_LargestImagePixelValue,
    //        255);//color table的索引最大值，与color table的r或b或g的位数有关

    //        if(pixelDataFile->palette() != NULL)
    //        {
    //            size_t numColors = 256;
    //            DcmQuantHistogramItemPointer *array = NULL;//color map
    //            CreatePaletteColorMap(pixelDataFile->palette(), array, numColors);

    //            if (array != NULL)
    //            {
    //                WritePaletteColorMap(*dataset, numColors, array, true, true);// OW and 16bits

    //                for (size_t i=0; i < numColors; i++) delete array[i];
    //                delete [] array;
    //                array = NULL;
    //            }
    //            else
    //            {
    //                result = EC_MemoryExhausted;
    //            }
    //        }
    //        else
    //        {
    //            printf("palette is NULL\n");
    //            result = EC_IllegalParameter;
    //        }
    //    }

    return result;
}

void BasePixelDataEncoder::freeMem()
{
    if (m_PixelDataFile != NULL)
    {
        delete m_PixelDataFile;
        m_PixelDataFile = NULL;
    }
}

static void CreatePaletteColorMap(const Uint8* palette, DcmQuantHistogramItemPointer*& array, size_t numColors)
{
    array = new DcmQuantHistogramItemPointer[numColors];

    if (array != NULL)
    {
        for (size_t i = 0; i < numColors; ++i)
        {
            // printf("r is: %d g is: %d b is: %d a is: %d\n", destMap[i * 4 + 0], destMap[i * 4 + 1], destMap[i * 4 +
            // 2]);
            DcmQuantPixel pixel;

            DcmQuantComponent r = OFstatic_cast(DcmQuantComponent, palette[i * 4 + 0]);
            DcmQuantComponent g = OFstatic_cast(DcmQuantComponent, palette[i * 4 + 1]);
            DcmQuantComponent b = OFstatic_cast(DcmQuantComponent, palette[i * 4 + 2]);
            pixel.assign(r, g, b);

            array[i] = new DcmQuantHistogramItem(pixel, i);
        }
    }
    else
    {
        printf("create palette color map error\n");
        array = NULL;
    }
}

static OFCondition WritePaletteColorMap(DcmItem& target, size_t numColors, DcmQuantHistogramItemPointer* array,
                                        bool writeAsOW, bool write16BitEntries)
{
    if (numColors == 0)
        return EC_IllegalCall;

    // if we're using 16 bit per sample anyway, force 16 bit palette entries
    if (sizeof(DcmQuantComponent) > 1)
        write16BitEntries = OFTrue;

    OFCondition result = EC_Normal;
    if (array)
    {
        // create palette color LUT descriptor
        Uint16 descriptor[3];
        descriptor[0] = (numColors > 65535) ? 0 : OFstatic_cast(Uint16, numColors); // number of entries
        descriptor[1] = 0;                                                          // first pixel value mapped
        descriptor[2] = write16BitEntries ? 16 : 8;                                 // bits per entry, must be 8 or 16.

        // if we're writing a 16-bit LUT with 64K entries, we must write as OW because
        // otherwise the US length field will overflow when writing in explicit VR!
        if ((descriptor[0] == 0) && write16BitEntries)
            writeAsOW = OFTrue;

        DcmElement* elem;
        if (result.good())
        {
            elem = new DcmUnsignedShort(DCM_RedPaletteColorLookupTableDescriptor);
            if (elem)
            {
                result = elem->putUint16Array(descriptor, 3);
                if (result.good())
                    result = target.insert(elem, OFTrue);
                if (result.bad())
                    delete elem;
            }
            else
                result = EC_MemoryExhausted;
        }

        if (result.good())
        {
            elem = new DcmUnsignedShort(DCM_GreenPaletteColorLookupTableDescriptor);
            if (elem)
            {
                result = elem->putUint16Array(descriptor, 3);
                if (result.good())
                    result = target.insert(elem, OFTrue);
                if (result.bad())
                    delete elem;
            }
            else
                result = EC_MemoryExhausted;
        }

        if (result.good())
        {
            elem = new DcmUnsignedShort(DCM_BluePaletteColorLookupTableDescriptor);
            if (elem)
            {
                result = elem->putUint16Array(descriptor, 3);
                if (result.good())
                    result = target.insert(elem, OFTrue);
                if (result.bad())
                    delete elem;
            }
            else
                result = EC_MemoryExhausted;
        }

        // now create the LUT content
        if (result.good())
        {
            Uint16* rLUT = NULL;
            Uint16* gLUT = NULL;
            Uint16* bLUT = NULL;
            size_t numWords = 0;
            double factor = 1.0;
            if (write16BitEntries)
            {
                numWords = numColors;
                rLUT = new Uint16[numWords];
                gLUT = new Uint16[numWords];
                bLUT = new Uint16[numWords];

                if (rLUT && gLUT && bLUT)
                {
                    for (size_t i = 0; i < numColors; i++)
                    {
                        rLUT[i] = OFstatic_cast(Uint16, OFstatic_cast(double, array[i]->getRed()) * factor);
                        gLUT[i] = OFstatic_cast(Uint16, OFstatic_cast(double, array[i]->getGreen()) * factor);
                        bLUT[i] = OFstatic_cast(Uint16, OFstatic_cast(double, array[i]->getBlue()) * factor);

                        // if the source data is only 8 bits per entry, replicate high and low bytes
                        if (sizeof(DcmQuantComponent) == 1)
                        {
                            rLUT[i] |= rLUT[i] << 8;
                            gLUT[i] |= gLUT[i] << 8;
                            bLUT[i] |= bLUT[i] << 8;
                        }
                    }
                }
                else
                    result = EC_MemoryExhausted;
            }
            else
            {
                // number of Uint16 words needed to store numColors Uint8 values plus padding
                numWords = (numColors + 1) / 2;
                rLUT = new Uint16[numWords];
                gLUT = new Uint16[numWords];
                bLUT = new Uint16[numWords];
                rLUT[numWords - 1] = 0;
                gLUT[numWords - 1] = 0;
                bLUT[numWords - 1] = 0;

                if (rLUT && gLUT && bLUT)
                {
                    Uint8* rLUT8 = OFreinterpret_cast(Uint8*, rLUT);
                    Uint8* gLUT8 = OFreinterpret_cast(Uint8*, gLUT);
                    Uint8* bLUT8 = OFreinterpret_cast(Uint8*, bLUT);
                    for (size_t i = 0; i < numColors; i++)
                    {
                        //                        printf("r is: %d g is: %d b is: %d \n", array[i]->getRed(),
                        //                        array[i]->getGreen(),array[i]->getBlue());
                        rLUT8[i] = OFstatic_cast(Uint8, OFstatic_cast(double, array[i]->getRed()) * factor);
                        gLUT8[i] = OFstatic_cast(Uint8, OFstatic_cast(double, array[i]->getGreen()) * factor);
                        bLUT8[i] = OFstatic_cast(Uint8, OFstatic_cast(double, array[i]->getBlue()) * factor);
                    }
                    // we have written the byte array in little endian order, now swap to US/OW if neccessary
                    swapIfNecessary(gLocalByteOrder, EBO_LittleEndian, rLUT, numWords * sizeof(Uint16), sizeof(Uint16));
                    swapIfNecessary(gLocalByteOrder, EBO_LittleEndian, gLUT, numWords * sizeof(Uint16), sizeof(Uint16));
                    swapIfNecessary(gLocalByteOrder, EBO_LittleEndian, bLUT, numWords * sizeof(Uint16), sizeof(Uint16));
                }
                else
                    result = EC_MemoryExhausted;
            }

            // the LUT data is prepared, now create the corresponding DICOM elements
            if (result.good())
            {
                if (writeAsOW)
                    elem = new DcmOtherByteOtherWord(DcmTag(DCM_RedPaletteColorLookupTableData, EVR_OW));
                else
                    elem = new DcmUnsignedShort(DcmTag(DCM_RedPaletteColorLookupTableData, EVR_US));
                if (elem)
                {
                    result = elem->putUint16Array(rLUT, numWords);
                    if (result.good())
                        result = target.insert(elem, OFTrue);
                    if (result.bad())
                        delete elem;
                }
                else
                    result = EC_MemoryExhausted;
            }

            if (result.good())
            {
                if (writeAsOW)
                    elem = new DcmOtherByteOtherWord(DcmTag(DCM_GreenPaletteColorLookupTableData, EVR_OW));
                else
                    elem = new DcmUnsignedShort(DcmTag(DCM_GreenPaletteColorLookupTableData, EVR_US));
                if (elem)
                {
                    result = elem->putUint16Array(gLUT, numWords);
                    if (result.good())
                        result = target.insert(elem, OFTrue);
                    if (result.bad())
                        delete elem;
                }
                else
                    result = EC_MemoryExhausted;
            }

            if (result.good())
            {
                if (writeAsOW)
                    elem = new DcmOtherByteOtherWord(DcmTag(DCM_BluePaletteColorLookupTableData, EVR_OW));
                else
                    elem = new DcmUnsignedShort(DcmTag(DCM_BluePaletteColorLookupTableData, EVR_US));
                if (elem)
                {
                    result = elem->putUint16Array(bLUT, numWords);
                    if (result.good())
                        result = target.insert(elem, OFTrue);
                    if (result.bad())
                        delete elem;
                }
                else
                    result = EC_MemoryExhausted;
            }
            delete[] rLUT;
            delete[] gLUT;
            delete[] bLUT;
        }
    }
    else
        result = EC_IllegalCall;

    return result;
}
