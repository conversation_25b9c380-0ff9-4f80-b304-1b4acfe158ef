if(WIN32)
    include_depends(usf.com.core.utilitymodel usf.exam.com.bs usf.preset.bs usf.exam.license.ui)
else()
    include_depends(usf.com.core.utilitymodel usf.exam.com.bs usf.preset.bs custompixeldata usf.exam.license.ui)
endif(WIN32)
add_definitions(
	-DHAVE_CONFIG_H 
	-DNDEBUG 
	-D_REENTRANT 
	-D_XOPEN_SOURCE_EXTENDED 
	-D_XOPEN_SOURCE=500 
	-D_DEFAULT_SOURCE
	-D_BSD_COMPAT 
	-D_OSF_SOURCE 
	-D_POSIX_C_SOURCE=200809C)


include_directories(${DCMTK_ROOT}/dcmtk/include ${DCMTK_ROOT}/support/include ${DCMTK_ROOT}/support/include/libxml2)
link_directories(${DCMTK_ROOT}/dcmtk/lib ${DCMTK_ROOT}/support/lib)


set(DCMTKSTATICLIBS
	libdcmdsig.a
	libdcmimage.a
	libdcmimgle.a
	libdcmjpeg.a
	libdcmnet.a
	libdcmpstat.a
	libdcmqrdb.a
	libdcmsr.a
	libdcmtls.a
	libdcmwlm.a
	libijg12.a
	libijg16.a
	libijg8.a
	libofstd.a
	libdcmdata.a
	libcharset.a
	libpng.a
	libtiff.a
	libtiffxx.a
	libxml2.a
        libz.a
	)

	include_directories(/usr/include)
	link_directories(/usr/lib)
	set(DCMTKSHAREDLIBS jpeg)
 
set(dicomscu_src 
	basepixeldataencoder.cpp
	compressedpixeldatafile.cpp
	dcmcjpeg.cc
	dcmechoscu.cc
	dcmfilemake.cc
	dcmfindscu.cc
	dcmj2pnm.cc
	dcmprscu.cc
	dcmpsprt.cc
	dcmsrscu.cc
	dcmstorescu.cc
	dcmutilitydef.cpp
	dcmwritersp.cc
	dicomscu.cc
	djcompressijgturbo8bit.cpp
	ipixeldataencoder.cpp
	jpegcodecencoder.cpp
	jpegencoderbaseline.cpp
	jpegencoder.cpp
	jpegencoderp14sv1.cpp
	mkreport.cc
	pixeldataencoder.cpp
	pixeldatafile.cpp
	pixeldatafilehead.cpp
	rawdataencoder.cpp
	timelogger.cpp
	uncompressedpixeldatafile.cpp
	dcmmovescu.cc
	dcmmppsscu.cc
	)


set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin")

add_executable_qt(dicomscu ${dicomscu_src})

if(WIN32)
    target_link_libraries(dicomscu usf.com.core.utilitymodel usf.exam.com.bs usf.preset.bs usf.exam.license.ui ${DCMTKSHAREDLIBS} ${DCMTKSTATICLIBS} pthread)
else()
    target_link_libraries(dicomscu usf.com.core.utilitymodel usf.exam.com.bs usf.preset.bs custompixeldata usf.exam.license.ui ${DCMTKSHAREDLIBS} ${DCMTKSTATICLIBS} pthread)
endif(WIN32)
