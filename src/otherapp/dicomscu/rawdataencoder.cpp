#include "rawdataencoder.h"
#include "ipixeldatafilehead.h"
#include "lineuncompressedpixeldatafile.h"
#include "dcmtk/dcmdata/dcitem.h"
#include "dcmtk/dcmdata/dcdeftag.h"

RawDataEncoder::RawDataEncoder(const IPixelDataFileHead& fileHead, bool opt_verbose, bool opt_debug)
    : BasePixelDataEncoder(new LineUncompressedPixelDataFile(fileHead, opt_verbose, opt_debug))
{
}

RawDataEncoder::~RawDataEncoder()
{
    freeMem();
}

E_TransferSyntax RawDataEncoder::supportedTransferSyntax() const
{
    return EXS_LittleEndianImplicit;
}

OFCondition RawDataEncoder::encode(DcmItem* dataset) const
{
    if (m_PixelDataFile == NULL)
    {
        return EC_MemoryExhausted;
    }

    if (!m_PixelDataFile->isValid())
    {
        return EC_IllegalParameter;
    }

    OFCondition result = dataset->putAndInsertUint8Array(DCM_PixelData, m_PixelDataFile->pixelData(),
                                                         m_PixelDataFile->pixelSizeReaded());
    if (result.good())
    {
        result = insertFrameInfo(dataset, m_PixelDataFile);
    }

    return result;
}

OFCondition RawDataEncoder::insertImageType(DcmItem* dataset) const
{
    return dataset->putAndInsertString(DCM_ImageType, "ORIGINAL\\PRIMARY");
}
