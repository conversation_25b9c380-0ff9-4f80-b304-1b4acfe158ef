#ifndef UNCOMPRESSEDPIXELDATAFILE_H
#define UNCOMPRESSEDPIXELDATAFILE_H

#include "pixeldatafile.h"

class UncompressedPixelDataFile : public PixelDataFile
{
public:
    UncompressedPixelDataFile(const PixelDataFileHead& fileHead, const char* paletteFileName,
                              const char* sonoparameterFile, bool opt_verbose = false, bool opt_debug = false);
    ~UncompressedPixelDataFile();
    virtual bool isValid() const;
    virtual const Uint8* palette() const;
    virtual const Uint8* pixelData();

private:
    void freeMem();
    Uint8* tryAllocateMemory(Uint32& frame, int frameSize);

private:
    Uint8* m_PixelData;
    Uint8* m_Palette;
    DicomSonoParameters* m_SonoParameters;
};

#endif // UNCOMPRESSEDPIXELDATAFILE_H
