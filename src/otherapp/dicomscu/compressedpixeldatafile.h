#ifndef COMPRESSEDPIXELDATAFILE_H
#define COMPRESSEDPIXELDATAFILE_H

#include "pixeldatafile.h"

class CompressedPixelDataFile : public PixelDataFile
{
public:
    CompressedPixelDataFile(const PixelDataFileHead& fileHead, const char* paletteFileName,
                            const char* sonoparameterFile, bool switchRB = false, bool opt_verbose = false,
                            bool opt_debug = false);
    ~CompressedPixelDataFile();
    virtual bool isValid() const;
    virtual const Uint8* framePixelData(int curIndex);
    virtual const Uint8* palette() const;
    virtual const Uint8* pixelData();

private:
    void freeMem();

private:
    Uint32 m_FrameSize;
    Uint32 m_ImageSize;
    Uint16 m_SamplesPerPixel;
    Uint8* m_FrameBuffer;
    Uint8* m_ImageBuffer;
    Uint8* m_Palette;
    DicomSonoParameters* m_SonoParameters;
    int m_FileIndex;
};

#endif // COMPRESSEDPIXELDATAFILE_H
