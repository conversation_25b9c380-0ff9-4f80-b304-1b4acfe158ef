#include "compressedpixeldatafile.h"
#include "pixeldatafilehead.h"
#include <new>
#include <string.h>
using namespace std;

CompressedPixelDataFile::CompressedPixelDataFile(const PixelDataFileHead& fileHead, const char* paletteFileName,
                                                 const char* sonoparameterFile, bool switchRB, bool opt_verbose,
                                                 bool opt_debug)
    : PixelDataFile(fileHead, opt_verbose, opt_debug)
    , m_FrameSize(fileHead.frameSize())
    , m_ImageSize(fileHead.imageSize())
    , m_SamplesPerPixel(fileHead.samplesPerPixel())
    , m_FrameBuffer(NULL)
    , m_ImageBuffer(NULL)
    , m_Palette(NULL)
    , m_SonoParameters(NULL)
    , m_FileIndex(0)
{
    if (m_ImageSize != m_FrameSize) // if equal,this file is rgb bmp data file
    {
        m_ImageBuffer = (Uint8*)new (std::nothrow) int[fileHead.imageSize() / sizeof(int)];
        if (m_ImageBuffer == NULL)
        {
            printf("CompressedPixelDataFile: new image buffer error\n");
            return;
        }
        memset(m_ImageBuffer, 0, fileHead.bytesPerLine());
    }

    m_FrameBuffer = (Uint8*)new (std::nothrow) unsigned char[fileHead.frameSize()];
    if (m_FrameBuffer == NULL)
    {
        printf("CompressedPixelDataFile: new frame buffer error\n");

        freeMem();
        return;
    }

    if (fileHead.isNeedPalette())
    {
        if ((m_Palette = readPalette(paletteFileName, switchRB)) == NULL)
        {
            printf("CompressedPixelDataFile: read palette error\n");

            freeMem();
            return;
        }
    }
    else
    {
        if (fileHead.photometricInterpretationType() != rgb)
        {
            printf("CompressedPixelDataFile: photometricInterpretationType %d is error\n",
                   fileHead.photometricInterpretationType());
            freeMem();
            return;
        }
    }

    if (fileHead.isMovie())
    {
        if ((m_SonoParameters = readSonoParameters(sonoparameterFile)) == NULL)
        {
            printf("SonoparameterFile: read CustomImageData error\n");

            freeMem();
            return;
        }
    }

    if (!skipStartingFrames())
    {
        printf("CompressedPixelDataFile: skip starting frames error\n");

        freeMem();

        return;
    }
}

CompressedPixelDataFile::~CompressedPixelDataFile()
{
    freeMem();
}

bool CompressedPixelDataFile::isValid() const
{
    bool ret = PixelDataFile::isValid() && m_FrameBuffer != NULL;

    if ((m_FrameSize != m_ImageSize))
    {
        ret = ret && m_ImageBuffer != NULL;
    }

    if (m_FileHead.isNeedPalette())
    {
        ret = ret && m_Palette != NULL;
    }

    return ret;
}

const Uint8* CompressedPixelDataFile::framePixelData(int curIndex)
{
    Uint32 sonoParametersIndex = m_FileIndex;
    if (sonoParametersIndex >= m_NumberOfSrcFrames)
    {
        sonoParametersIndex = m_NumberOfSrcFrames - 1;
        //防止最后一帧图超过SonoParameters最大帧
    }

    if (readFrame(curIndex, m_FileIndex, (void*)m_FrameBuffer, m_FrameSize) != m_FrameSize)
    {
        printf("read %d Frame error\n", curIndex);
        return NULL;
    }

    if (m_FrameSize != m_ImageSize)
    {
        doGray8BitTo24Bit(m_FrameBuffer, m_FrameSize, m_Palette, m_SonoParameters[sonoParametersIndex], m_ImageBuffer);
        m_NumberOfFramesReaded++;
        return m_ImageBuffer;
    }
    else
    {
        m_NumberOfFramesReaded++;
        return m_FrameBuffer;
    }
}

const Uint8* CompressedPixelDataFile::palette() const
{
    return m_Palette;
}

const Uint8* CompressedPixelDataFile::pixelData()
{
    return (m_FrameSize == m_ImageSize) ? m_FrameBuffer : m_ImageBuffer;
}

void CompressedPixelDataFile::freeMem()
{
    if (m_ImageBuffer != NULL)
    {
        delete[] m_ImageBuffer;
        m_ImageBuffer = NULL;
    }

    if (m_FrameBuffer != NULL)
    {
        delete[] m_FrameBuffer;
        m_FrameBuffer = NULL;
    }

    if (m_Palette != NULL)
    {
        delete[] m_Palette;
        m_Palette = NULL;
    }

    if (m_SonoParameters != NULL)
    {
        delete[] m_SonoParameters;
        m_SonoParameters = NULL;
    }
}
