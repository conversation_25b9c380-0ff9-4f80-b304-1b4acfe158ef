#include "pixeldatafile.h"
#include "pixeldatafilehead.h"
#include "dcmutilitydef.h"
#include <new>
#include <sys/stat.h>
using namespace std;
PixelDataFile::PixelDataFile(const PixelDataFileHead& fileHead, bool opt_verbose, bool opt_debug)
    : m_opt_verbose(opt_verbose)
    , m_opt_debug(opt_debug)
    , m_FileHead(fileHead)
    , m_NumberOfFrames(fileHead.numberOfReadFrames())
    , m_NumberOfFramesReaded(0)
    , m_NumberOfSrcFrames(0)
{
}

PixelDataFile::~PixelDataFile()
{
}

const PixelDataFileHead& PixelDataFile::fileHead() const
{
    return m_FileHead;
}

bool PixelDataFile::isValid() const
{
    return m_FileHead.isValid();
}

const Uint8* PixelDataFile::framePixelData(int curIndex)
{
    return NULL;
}

const Uint8* PixelDataFile::palette() const
{
    return NULL;
}

Uint16 PixelDataFile::bitsStored() const
{
    return 8;
}

Uint16 PixelDataFile::bitsAllocated() const
{
    return 8;
}

Uint16 PixelDataFile::bytesAllocated() const
{
    return 1;
}

Uint16 PixelDataFile::samplesPerPixel() const
{
    return m_FileHead.samplesPerPixel();
}

Uint16 PixelDataFile::planarConfiguration() const
{
    return 0;
}

const char* PixelDataFile::photometricInterpretation() const
{
    return PhotometricInterpretationStr[m_FileHead.photometricInterpretationType()];
}

Uint16 PixelDataFile::columns() const
{
    return m_FileHead.columns();
}

Uint16 PixelDataFile::rows() const
{
    return m_FileHead.rows();
}

Uint32 PixelDataFile::numberOfFrames() const
{
    return m_NumberOfFrames;
}

Uint32 PixelDataFile::numberOfFramesReaded() const
{
    return m_NumberOfFramesReaded;
}

Uint32 PixelDataFile::pixelSizeReaded() const
{
    return m_FileHead.imageSize() * numberOfFramesReaded();
}

Uint8 PixelDataFile::colorspace() const
{
    return m_FileHead.colorspace();
}

bool PixelDataFile::skipStartingFrames()
{
    m_NumberOfSrcFrames = adjustedSrcReadFrame(m_NumberOfFrames);

    const DcmStorage_Option& storageOption = m_FileHead.storageOption();
    if (m_opt_verbose)
    {
        printf("CineFps:%d DcmFps:%d fInterval:%f numberOfFrames:%u adjSrcReadFrame:%u\n", storageOption.CineFramerate,
               storageOption.DcmFramerate, m_FileHead.sampleInterval(), m_NumberOfFrames, m_NumberOfSrcFrames);
    }

    if (m_FileHead.numberOfFrames() > m_NumberOfSrcFrames)
    {
        if (fseek(m_FileHead.pFile(), (m_FileHead.numberOfFrames() - m_NumberOfSrcFrames) * m_FileHead.frameSize(),
                  SEEK_CUR) == -1)
        {
            perror("fseek");
            return false;
        }
    }

    return true;
}

Uint32 PixelDataFile::readFrame(int index, int& fileIndex, void* ptr, Uint32 size) const
{
    //以下使用最近邻抽帧的方法
    FILE* pFile = m_FileHead.pFile();

    float fIndex = index * m_FileHead.sampleInterval();
    int pre = (int)fIndex;
    int next = pre + 1;
    //获取最近邻index
    int readIndex = 0;
    if (fIndex >= (pre + 0.5f))
    {
        readIndex = next;
    }
    else
    {
        readIndex = pre;
    }

    if (readIndex > fileIndex)
    {
        if (fseek(pFile, m_FileHead.frameSize() * (readIndex - fileIndex), SEEK_CUR) == -1)
        {
            return -1;
        }
    }

    if (fileIndex <= readIndex)
    {
        Uint32 readedSize = 0;
        if ((readedSize = fread(ptr, sizeof(unsigned char), size, pFile)) == size)
        {
            fileIndex = readIndex + 1;
        }
        else
        {
            printf("fread error:size %d readedsize %d index %d fileIndex %d readIndex %d\n", size, readedSize, index,
                   fileIndex, readIndex);
        }
        return readedSize;
    }
    else
    {
        return size;
    }
}

static int fileSize(const char* filename)
{
    struct stat statbuf;
    if (stat(filename, &statbuf) == 0)
    {
        return statbuf.st_size;
    }
    else
    {
        perror("get file size failed");
        return 0;
    }
}

Uint8* PixelDataFile::readPalette(const char* fileName, bool switchRB)
{
    if (fileName == NULL)
    {
        printf("Palette fileName is NULL!\n");
        return NULL;
    }

    Uint8* palette = NULL;
    FILE* paletteFile = NULL;
    int fSize = fileSize(fileName);
    if ((paletteFile = fopen(fileName, "rb")) != NULL)
    {
        if (fSize == 1024)
        {
            //兼容老的版本，只考虑一块区域，一个palette，老的palette文件没有PaletteFile_head，只有1024字节
            m_PaletteHead.ImageRegionType = ImageRegionTypeEnum::Region_Single;
            m_PaletteHead.PaletteColorType = PaletteColorTypeEnum::Type_RGB;
            m_PaletteHead.LeftOrUpsideWidth = m_FileHead.columns();
            m_PaletteHead.LeftOrUpsideHeight = m_FileHead.rows();
            m_PaletteHead.RoiLeft = 0;
            m_PaletteHead.RoiTop = 0;
            m_PaletteHead.RoiWidth = 0;
            m_PaletteHead.RoiHeight = 0;
        }
        else
        {
            if (fread(&m_PaletteHead, sizeof(PaletteFile_head), 1, paletteFile) != 1)
            {
                printf("Read Palette fileHead Fail!\n");
                return NULL;
            }
        }

        int paletteCount = 1;
        if (m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_LR ||
            m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_UD ||
            m_PaletteHead.ImageRegionType == ImageRegionTypeEnum::Region_LRElasto)
        {
            paletteCount = 2;
        }
        m_PaletteHead.RoiWidth += 2; //增加2个像素，防止边缘没有被处理.（这里为了速度是2个像素一起被处理的）
        palette = (Uint8*)new (std::nothrow) Uint32[256 * paletteCount];
        if (palette != NULL)
        {
            if (fread(palette, sizeof(Uint8), 1024 * paletteCount, paletteFile) == (size_t)(1024 * paletteCount))
            {
                if (m_PaletteHead.PaletteColorType == PaletteColorTypeEnum::Type_RGB && switchRB)
                {
                    Uint8 value = 0;
                    for (int i = 0; i < 256 * paletteCount; i++)
                    {
                        value = palette[i * 4 + 0];
                        palette[i * 4 + 0] = palette[i * 4 + 2];
                        palette[i * 4 + 2] = value;
                    }
                }

                fclose(paletteFile);
                return palette;
            }
            else
            {
                printf("palette size isn't correct!\n");
                delete[] palette;
                fclose(paletteFile);
            }
        }
        else
        {
            printf("new memory failed!\n");
            fclose(paletteFile);
        }
    }
    else
    {
        printf("can not open file %s!\n", fileName);
    }

    return NULL;
}

DicomSonoParameters* PixelDataFile::readSonoParameters(const char* fileName)
{
    if (fileName == NULL)
    {
        printf("sonoparameter fileName is NULL!\n");
        return NULL;
    }

    DicomSonoParameters* sonoParameters = NULL;
    FILE* customImageDataFile = NULL;
    if ((customImageDataFile = fopen(fileName, "rb")) != NULL)
    {
        sonoParameters = new (std::nothrow) DicomSonoParameters[256];

        if (sonoParameters != NULL)
        {
            if (fread(sonoParameters, sizeof(DicomSonoParameters), 256, customImageDataFile) == 256)
            {

                fclose(customImageDataFile);
                return sonoParameters;
            }
            else
            {
                printf("sonoParameters size isn't correct!\n");
                delete[] sonoParameters;
                fclose(customImageDataFile);
            }
        }
        else
        {
            printf("new memory failed!\n");
            fclose(customImageDataFile);
        }
    }
    else
    {
        printf("can not open file %s!\n", fileName);
    }

    return NULL;
}

void PixelDataFile::doGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette,
                                      DicomSonoParameters sonoParameters, Uint8* imageBuffer)
{
    switch (m_PaletteHead.ImageRegionType)
    {
    case ImageRegionTypeEnum::Region_LR:
        RegionLRGray8BitTo24Bit(frameBuffer, frameSize, palette, imageBuffer);
        break;
    case ImageRegionTypeEnum::Region_UD:
        RegionUDGray8BitTo24Bit(frameBuffer, frameSize, palette, imageBuffer);
        break;
    case ImageRegionTypeEnum::Region_LRElasto:
        RegionLRRoiGray8BitTo24Bit(frameBuffer, frameSize, palette, sonoParameters, imageBuffer);
        break;
    case ImageRegionTypeEnum::Region_Single:
    default:
        RegionSingleGray8BitTo24Bit(frameBuffer, frameSize, palette, imageBuffer);
        break;
    }
}

Uint32 PixelDataFile::adjustedSrcReadFrame(int numberOfFrames) const
{
    const DcmStorage_Option& storageOption = m_FileHead.storageOption();
    Uint32 adjSrcReadFrame = numberOfFrames;
    if (numberOfFrames > 1 && m_FileHead.isNeedSample())
    {
        if (storageOption.CineFramerate > storageOption.DcmFramerate)
        {
            //抽帧取整
            adjSrcReadFrame = numberOfFrames * storageOption.CineFramerate / storageOption.DcmFramerate;
        }
        else
        {
            //插帧要+0.5
            adjSrcReadFrame =
                (Uint32)((float)numberOfFrames * storageOption.CineFramerate / storageOption.DcmFramerate + 0.5f);
        }
    }

    return adjSrcReadFrame;
}

void PixelDataFile::convertRegionalPixels(Uint8* frameBuffer, Uint32 begin, Uint32 end, Uint8* palette,
                                          Uint8* imageBuffer, int& index, bool roi, bool elastoColor)
{
    int io = 0;
    int indexP1 = 0, indexP2 = 0, indexP3 = 0, indexP4 = 0;

    if (roi == false)
    {
        for (Uint32 i = begin; i < end; i++)
        {
            io = i << 1;
            indexP1 = (*(frameBuffer + io)) << 2;
            indexP2 = (*(frameBuffer + io + 1)) << 2;

            // two pixel
            // rg/bg
            *((Uint16*)(imageBuffer + index)) = *((Uint16*)(palette + indexP1));
            // br/rb
            *((Uint16*)(imageBuffer + index + 2)) =
                (((Uint16)(*(palette + indexP2))) << 8) | (*(palette + indexP1 + 2));
            // gb/gr
            *((Uint16*)(imageBuffer + index + 4)) =
                (((Uint16)(*(palette + indexP2 + 2))) << 8) | (*(palette + indexP2 + 1));

            index += 6; // m_SamplesPerPixel * 2
        }
    }
    else
    {
        if (elastoColor)
        {
            Uint8* palette2 = palette + 1024;
            for (Uint32 i = begin; i < end; i++)
            {
                io = i << 1;
                indexP1 = (*(frameBuffer + io)) << 2;
                indexP2 = (*(frameBuffer + io + 1)) << 2;
                indexP3 = (*(frameBuffer + m_FileHead.columns() / 2 + io)) << 2;
                indexP4 = (*(frameBuffer + m_FileHead.columns() / 2 + io + 1)) << 2;

                // two pixel
                // rg/bg
                *((Uint16*)(imageBuffer + index)) =
                    ((Uint16)(qBound(0, *(palette2 + indexP1 + 1) + *(palette2 + indexP3 + 1), 255)) << 8) |
                    (qBound(0, *(palette2 + indexP1) + *(palette2 + indexP3), 255));
                // br/rb
                *((Uint16*)(imageBuffer + index + 2)) =
                    ((Uint16)(qBound(0, *(palette2 + indexP2) + *(palette2 + indexP4), 255)) << 8) |
                    (qBound(0, *(palette2 + indexP1 + 2) + *(palette2 + indexP3 + 2), 255));
                // gb/gr
                *((Uint16*)(imageBuffer + index + 4)) =
                    ((Uint16)(qBound(0, *(palette2 + indexP2 + 2) + *(palette2 + indexP4 + 2), 255)) << 8) |
                    (qBound(0, *(palette2 + indexP2 + 1) + *(palette2 + indexP4 + 1), 255));

                index += 6; // m_SamplesPerPixel * 2
            }
        }
        else
        {
            for (Uint32 i = begin; i < end; i++)
            {
                io = i << 1;
                indexP1 = (*(frameBuffer + m_FileHead.columns() / 2 + io)) << 2;
                indexP2 = (*(frameBuffer + m_FileHead.columns() / 2 + io + 1)) << 2;

                // two pixel
                // rg/bg
                *((Uint16*)(imageBuffer + index)) = *((Uint16*)(palette + indexP1));
                // br/rb
                *((Uint16*)(imageBuffer + index + 2)) =
                    (((Uint16)(*(palette + indexP2))) << 8) | (*(palette + indexP1 + 2));
                // gb/gr
                *((Uint16*)(imageBuffer + index + 4)) =
                    (((Uint16)(*(palette + indexP2 + 2))) << 8) | (*(palette + indexP2 + 1));

                index += 6; // m_SamplesPerPixel * 2
            }
        }
    }
}

void PixelDataFile::RegionSingleGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette,
                                                Uint8* imageBuffer)
{
    int index = m_FileHead.bytesPerLine();
    Uint32 begin = m_FileHead.columns();
    Uint32 end = frameSize / 2;
    convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);
}

void PixelDataFile::RegionLRGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, Uint8* imageBuffer)
{
    int index = m_FileHead.bytesPerLine();

    Uint32 begin = 0;
    Uint32 end = 0;
    int height = m_PaletteHead.LeftOrUpsideHeight;
    int leftWidth = m_PaletteHead.LeftOrUpsideWidth;
    for (int i = 1; i < height; i++)
    {
        // left
        begin = i * (m_FileHead.columns() / 2);
        end = begin + leftWidth / 2;
        convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);

        // right
        begin = end;
        end = (m_FileHead.columns() / 2) * (i + 1);
        convertRegionalPixels(frameBuffer, begin, end, (palette + 1024), imageBuffer, index);
    }
}

void PixelDataFile::RegionUDGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, Uint8* imageBuffer)
{
    int index = m_FileHead.bytesPerLine();

    // up
    Uint32 begin = m_FileHead.columns();
    Uint32 end = (m_PaletteHead.LeftOrUpsideWidth * m_PaletteHead.LeftOrUpsideHeight) / 2;
    convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);

    // down
    begin = end;
    end = frameSize / 2;
    convertRegionalPixels(frameBuffer, begin, end, (palette + 1024), imageBuffer, index);
}

void PixelDataFile::RegionLRRoiGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette,
                                               DicomSonoParameters sonoParameters, Uint8* imageBuffer)
{
    int index = m_FileHead.bytesPerLine();

    // up
    Uint32 begin = m_FileHead.columns();
    Uint32 end = (m_FileHead.columns() * m_PaletteHead.RoiTop) / 2;
    convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);

    // middle
    for (int i = 0; i < m_PaletteHead.RoiHeight; i++)
    {
        // left
        begin = end;
        end = begin + m_PaletteHead.RoiLeft / 2;
        convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);

        // middle with roi
        begin = end;
        end = begin + m_PaletteHead.RoiWidth / 2;
        convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index, true,
                              (sonoParameters.elastoColor == 1));

        // right
        begin = end;
        end = begin + m_FileHead.columns() / 2 - m_PaletteHead.RoiLeft / 2 - m_PaletteHead.RoiWidth / 2;
        convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);
    }

    // down
    begin = end;
    end = frameSize / 2;
    convertRegionalPixels(frameBuffer, begin, end, palette, imageBuffer, index);
}
