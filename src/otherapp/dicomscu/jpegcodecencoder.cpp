#include "jpegcodecencoder.h"
#include "dcmtk/config/osconfig.h"
#include "dcmtk/dcmjpeg/djcodece.h"

// ofstd includes
#include "dcmtk/ofstd/oflist.h"
#include "dcmtk/ofstd/ofstd.h"

// dcmdata includes
#include "dcmtk/dcmdata/dcdatset.h" /* for class DcmDataset */
#include "dcmtk/dcmdata/dcdeftag.h" /* for tag constants */
#include "dcmtk/dcmdata/dcovlay.h"  /* for class DcmOverlayData */
#include "dcmtk/dcmdata/dcpixseq.h" /* for class DcmPixelSequence */
#include "dcmtk/dcmdata/dcpxitem.h" /* for class DcmPixelItem */
#include "dcmtk/dcmdata/dcuid.h"    /* for dcmGenerateUniqueIdentifer()*/
#include "dcmtk/dcmdata/dcvrcs.h"   /* for class DcmCodeString */
#include "dcmtk/dcmdata/dcvrds.h"   /* for class DcmDecimalString */
#include "dcmtk/dcmdata/dcvrlt.h"   /* for class DcmLongText */
#include "dcmtk/dcmdata/dcvrst.h"   /* for class DcmShortText */
#include "dcmtk/dcmdata/dcvrus.h"   /* for class DcmUnsignedShort */
#include "dcmtk/dcmdata/dcswap.h"   /* for swapIfNecessary */

// dcmjpeg includes
#include "dcmtk/dcmjpeg/djcparam.h" /* for class DJCodecParameter */
#include "dcmtk/dcmjpeg/djencabs.h" /* for class DJEncoder */

// dcmimgle includes
#include "dcmtk/dcmimgle/dcmimage.h" /* for class DicomImage */

#define INCLUDE_CMATH
#include "dcmtk/ofstd/ofstdinc.h"

#include "linepixeldatafile.h"
#include "timelogger.h"

JpegCodecEncoder::JpegCodecEncoder()
{
}

JpegCodecEncoder::~JpegCodecEncoder()
{
}

OFCondition JpegCodecEncoder::encode(DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                                     const DcmRepresentationParameter* toRepParam, const DcmCodecParameter* cp) const
{
    if (!pixelDataFile->isValid())
    {
        return EC_IllegalParameter;
    }

    OFCondition result = EC_Normal;

    DcmElement* element = NULL;
    result = dataset->findAndGetElement(DCM_PixelData, element);
    if (result.good())
    {
        CERR << "Jpeg Codec Encoder: Dataset has pixel data element." << endl;
        return EC_IllegalCall;
    }

    // assume we can cast the codec parameter to what we need
    const DJCodecParameter* djcp = (const DJCodecParameter*)cp;

    // if true lossless mode is enabled, and we're supposed to do lossless compression,
    // call the "true lossless encoding"-engine
    if (isLosslessProcess() && (djcp->getTrueLosslessMode()))
        return encodeTrueLossless(dataset, pixelDataFile, toRepParam, cp);

    if ((!dataset) || ((dataset->ident() != EVR_dataset) && (dataset->ident() != EVR_item)))
        result = EC_InvalidTag;
    else
    {
        double compressionRatio = 0.0;
        EP_Interpretation interpr = EPI_Unknown;
        OFString photometricInterpretation = pixelDataFile->photometricInterpretation();

        if (photometricInterpretation == "PALETTE COLOR")
            interpr = EPI_PaletteColor;
        else if (photometricInterpretation == "RGB")
            interpr = EPI_RGB;
        else if (photometricInterpretation == "YBR_FULL")
            interpr = EPI_YBR_Full;
        // some photometric interpretations are not supported:
        else
        {
            CERR << "Jpeg Codec Encoder: photometric interpretation not supported: " << photometricInterpretation
                 << endl;
            return EC_IllegalParameter;
        }

        switch (interpr)
        {
        case EPI_PaletteColor:
        case EPI_RGB:
        case EPI_ARGB:
            // color image except YCbCr which receives special treatment
            result = encodeColorImage(OFFalse, (DcmItem*)dataset, pixelDataFile, toRepParam, djcp, compressionRatio);
            break;
        case EPI_HSV:
        case EPI_CMYK:
        case EPI_YBR_Partial_422:
        case EPI_Monochrome1:
        case EPI_Monochrome2:
        case EPI_YBR_Full:
        case EPI_YBR_Full_422:
        case EPI_Unknown:
            // unknown color model - bail out
            result = EJ_UnsupportedPhotometricInterpretation;
            break;
        }

        // the following operations do not affect the Image Pixel Module
        // but other modules such as SOP Common.  We only perform these
        // changes if we're on the main level of the dataset,
        // which should always identify itself as dataset, not as item.
        if (dataset->ident() == EVR_dataset)
        {

            // update image type
            if (result.good())
                result = DcmCodec::updateImageType((DcmItem*)dataset);

            // determine compressed bit depth passed to JPEG codec
            Uint16 compressedBits = djcp->getForcedBitDepth();
            if (result.good())
            {
                if (compressedBits == 0)
                {
                    compressedBits = pixelDataFile->bitsStored();
                }
            }

            // update derivation description
            if (result.good())
                result = updateDerivationDescription((DcmItem*)dataset, toRepParam, djcp, (Uint8)compressedBits,
                                                     compressionRatio);

            if (result.good())
            {
                if (isLosslessProcess())
                {
                    // lossless process - create new UID if mode is EUC_always or if we're converting to Secondary
                    // Capture
                    if (djcp->getConvertToSC() || (djcp->getUIDCreation() == EUC_always))
                        result = DcmCodec::newInstance((DcmItem*)dataset, "DCM", "121320", "Uncompressed predecessor");
                }
                else
                {
                    // lossy process - create new UID unless mode is EUC_never and we're not converting to Secondary
                    // Capture
                    if (djcp->getConvertToSC() || (djcp->getUIDCreation() != EUC_never))
                        result = DcmCodec::newInstance((DcmItem*)dataset, "DCM", "121320", "Uncompressed predecessor");

                    // update lossy compression ratio
                    if (result.good())
                        result = updateLossyCompressionRatio((DcmItem*)dataset, compressionRatio);
                }
            }

            // convert to Secondary Capture if requested by user.
            // This method creates a new SOP class UID, so it should be executed
            // after the call to newInstance() which creates a Source Image Sequence.
            if (result.good() && djcp->getConvertToSC())
                result = DcmCodec::convertToSecondaryCapture((DcmItem*)dataset);
        }
    }
    return result;
}

OFCondition JpegCodecEncoder::encodeColorImage(OFBool YBRmode, DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                                               const DcmRepresentationParameter* toRepParam, const DJCodecParameter* cp,
                                               double& compressionRatio) const
{
    if (!pixelDataFile->isValid())
    {
        return EC_IllegalParameter;
    }

    OFCondition result = EC_Normal;
    DcmOffsetList offsetList;
    DcmPixelSequence* pixelSequence = NULL;
    DcmPixelItem* offsetTable = NULL;
    unsigned short bitsPerSample = 0;
    compressionRatio = 0.0; // initialize if something goes wrong
    unsigned long compressedSize = 0;
    double uncompressedSize = 0.0;
    Uint16 compressedBits = cp->getForcedBitDepth();

    // initialize settings with defaults for RGB mode
    OFBool monochromeMode = OFFalse;
    EP_Interpretation interpr = EPI_RGB;
    Uint16 samplesPerPixel = 3;

    // check mode and adjust settings
    if (cp->getCompressionColorSpaceConversion() == ECC_monochrome)
    {
        result = EC_IllegalParameter;
    }
    else if (YBRmode)
    {
        result = EC_IllegalParameter;
    }

    // create initial pixel sequence
    if (result.good())
    {
        pixelSequence = new DcmPixelSequence(DcmTag(DCM_PixelData, EVR_OB));
        if (pixelSequence == NULL)
            result = EC_MemoryExhausted;
        else
        {
            // create empty offset table
            offsetTable = new DcmPixelItem(DcmTag(DCM_Item, EVR_OB));
            if (offsetTable == NULL)
                result = EC_MemoryExhausted;
            else
                pixelSequence->insert(offsetTable);
        }
    }

    // select bit depth
    if (result.good())
    {
        if (compressedBits == 0)
        {
            compressedBits = pixelDataFile->bitsStored();
        }
    }

    // create codec instance
    if (result.good())
    {
        DJEncoder* jpeg = createEncoderInstance(toRepParam, cp, (Uint8)compressedBits);
        if (jpeg)
        {
            // render and compress each frame
            bitsPerSample = jpeg->bitsPerSample();
            unsigned long frameCount = pixelDataFile->numberOfFrames();
            unsigned short bytesPerSample = jpeg->bytesPerSample();
            unsigned short columns = (unsigned short)pixelDataFile->columns();
            unsigned short rows = (unsigned short)pixelDataFile->rows();
            Uint8* jpegData = NULL;
            Uint32 jpegLen = 0;
            const void* frame = NULL;

            TimeLogger* tl = NULL;
            TimeLogger* tlpx = NULL;
            TimeLogger* tlj = NULL;
            if (cp->isVerbose())
            {
                tl = new TimeLogger();
                tlpx = new TimeLogger();
                tlj = new TimeLogger();
            }

            double totalTime = 0.0;
            double totalTimePixelData = 0.0;

            // compute original image size in bytes, ignoring any padding bits.
            uncompressedSize = columns * rows * pixelDataFile->bitsAllocated() * frameCount * samplesPerPixel / 8.0;
            for (unsigned long i = 0; (i < frameCount) && (result.good()); i++)
            {
                if (cp->isVerbose())
                {
                    tlpx->restart();
                }
                frame = pixelDataFile->framePixelData(i);
                if (cp->isVerbose())
                {
                    totalTimePixelData += tlpx->logger(NULL, false);
                }
                if (frame == NULL && i == 0)
                    result = EC_MemoryExhausted;
                else if (frame == NULL)
                {
                    frameCount = i;
                    uncompressedSize =
                        columns * rows * pixelDataFile->bitsAllocated() * frameCount * samplesPerPixel / 8.0;
                }
                else
                {
                    if (cp->isVerbose())
                    {
                        tlj->restart();
                    }
                    // compress frame
                    jpegData = NULL;
                    if (bytesPerSample == 1)
                    {
                        result =
                            jpeg->encode(columns, rows, interpr, samplesPerPixel, (Uint8*)frame, jpegData, jpegLen);
                    }
                    else
                    {
                        result =
                            jpeg->encode(columns, rows, interpr, samplesPerPixel, (Uint16*)frame, jpegData, jpegLen);
                    }

                    if (cp->isVerbose())
                    {
                        totalTime += tlj->logger(NULL, false);
                    }

                    // store frame
                    if (result.good())
                    {
                        result =
                            pixelSequence->storeCompressedFrame(offsetList, jpegData, jpegLen, cp->getFragmentSize());
                    }

                    // delete block of JPEG data
                    delete[] jpegData;
                    compressedSize += jpegLen;
                }
            }
            delete jpeg;

            if (cp->isVerbose())
            {
                char info[256];
                sprintf(info,
                        "compressed uncompressedSize %d compressedSize %d totalTime %3f ms totalTimePixelData %3f ms",
                        (int)uncompressedSize, (int)compressedSize, totalTime, totalTimePixelData);
                tl->logger(info);

                delete tl;
                delete tlpx;
                delete tlj;
            }
        }
        else
            result = EC_MemoryExhausted;
    }

    // store pixel sequence if everything was successful
    if (result.good())
    {
        result = dataset->insert(pixelSequence, OFFalse, OFFalse);
        if (result.bad())
        {
            CERR << "Jpeg Codec Encoder: Unable to get relevant attributes from dataset" << endl;
        }
    }
    else
    {
        delete pixelSequence;
    }

    if ((result.good()) && (cp->getCreateOffsetTable()))
    {
        // create offset table
        result = offsetTable->createOffsetTable(offsetList);
    }

    //    if (result.good())
    //    {
    //        // adapt attributes in image pixel module
    //        if (result.good()) result = dataset->putAndInsertUint16(DCM_SamplesPerPixel, samplesPerPixel);
    //        if (result.good()) result = dataset->putAndInsertString(DCM_PhotometricInterpretation,
    //        photometricInterpretation); if (result.good())
    //        {
    //            if (bitsPerSample > 8)
    //                result = dataset->putAndInsertUint16(DCM_BitsAllocated, 16);
    //            else
    //                result = dataset->putAndInsertUint16(DCM_BitsAllocated, 8);
    //        }
    //        if (result.good()) result = dataset->putAndInsertUint16(DCM_BitsStored, bitsPerSample);
    //        if (result.good()) result = dataset->putAndInsertUint16(DCM_HighBit, bitsPerSample-1);
    //        if (result.good()) result = dataset->putAndInsertUint16(DCM_PixelRepresentation, 0);
    //        if (result.good())
    //        {
    //            if (monochromeMode) delete dataset->remove(DCM_PlanarConfiguration);
    //            else result = dataset->putAndInsertUint16(DCM_PlanarConfiguration, 0);
    //        }
    //        delete dataset->remove(DCM_SmallestImagePixelValue);
    //        delete dataset->remove(DCM_LargestImagePixelValue);
    //        delete dataset->remove(DCM_RedPaletteColorLookupTableDescriptor);
    //        delete dataset->remove(DCM_GreenPaletteColorLookupTableDescriptor);
    //        delete dataset->remove(DCM_BluePaletteColorLookupTableDescriptor);
    //        delete dataset->remove(DCM_RedPaletteColorLookupTableData);
    //        delete dataset->remove(DCM_GreenPaletteColorLookupTableData);
    //        delete dataset->remove(DCM_BluePaletteColorLookupTableData);
    //        delete dataset->remove(DCM_PixelPaddingValue);
    //        delete dataset->remove(DCM_SmallestPixelValueInSeries);
    //        delete dataset->remove(DCM_LargestPixelValueInSeries);
    //        delete dataset->remove(DCM_PaletteColorLookupTableUID);
    //        delete dataset->remove(DCM_SegmentedRedPaletteColorLookupTableData);
    //        delete dataset->remove(DCM_SegmentedGreenPaletteColorLookupTableData);
    //        delete dataset->remove(DCM_SegmentedBluePaletteColorLookupTableData);
    //    }
    if (compressedSize > 0)
        compressionRatio = uncompressedSize / compressedSize;

    return result;
}

OFCondition JpegCodecEncoder::encodeTrueLossless(DcmItem* dataset, LinePixelDataFile* pixelDataFile,
                                                 const DcmRepresentationParameter* toRepParam,
                                                 const DcmCodecParameter* cp) const
{
    if (!pixelDataFile->isValid())
    {
        return EC_IllegalParameter;
    }

    OFCondition result = EC_Normal;
    // assume we can cast the codec parameter to what we need
    DJCodecParameter* djcp = (DJCodecParameter*)cp;

    // check whether dataset was on top of the stack
    if ((!dataset) || ((dataset->ident() != EVR_dataset) && (dataset->ident() != EVR_item)))
        return EC_InvalidTag;
    else
    {
        DcmItem* datsetItem = (DcmItem*)dataset;
        double compressionRatio = 0.0;
        unsigned long length = 0;
        Uint16 bitsAllocated = 0;
        Uint16 bitsStored = 0;
        Uint16 bytesAllocated = 0;
        Uint16 samplesPerPixel = 0;
        Uint16 planarConfiguration = 0;
        Uint16 columns = 0;
        Uint16 rows = 0;
        Sint32 numberOfFrames = 1;
        EP_Interpretation interpr = EPI_Unknown;
        Uint8* jpegData = NULL;
        Uint32 jpegLen = 0;
        DcmOffsetList offsetList;
        OFString photometricInterpretation;

        // get relevant attributes for encoding from dataset
        OFCondition result = EC_Normal;
        bitsStored = pixelDataFile->bitsStored();
        bitsAllocated = pixelDataFile->bitsAllocated();
        samplesPerPixel = pixelDataFile->samplesPerPixel();
        columns = pixelDataFile->columns();
        rows = pixelDataFile->rows();
        photometricInterpretation = pixelDataFile->photometricInterpretation();

        numberOfFrames = pixelDataFile->numberOfFrames();
        if (numberOfFrames < 1)
            numberOfFrames = 1;

        if (result.bad())
        {
            CERR << "Jpeg Codec Encoder: Unable to get relevant attributes from dataset" << endl;
            return result;
        }

        // check, whether bit depth is supported
        if (bitsAllocated == 8)
            bytesAllocated = 1;
        else if (bitsAllocated == 16)
            bytesAllocated = 2;
        else
        {
            CERR << "Jpeg Codec Encoder: only 8 or 16 bits allocated supported" << endl;
            return EC_IllegalParameter;
        }

        // make sure that all the descriptive attributes have sensible values
        if ((columns < 1) || (rows < 1) || (samplesPerPixel < 1))
        {
            CERR << "Invalid attribute values in pixel module" << endl;
            return EC_CannotChangeRepresentation;
        }

        planarConfiguration = pixelDataFile->planarConfiguration();

        /* Set and check photometric interpretation (up to now: EPI_RGB)
         * Only photometric interpretations, that are explicetly "supported" by the
         * IJG lib are set. For all others "unknown" is set. Some are even rejected here.
         */
        if (photometricInterpretation == "MONOCHROME1")
            interpr = EPI_Monochrome1;
        else if (photometricInterpretation == "MONOCHROME2")
            interpr = EPI_Monochrome2;
        else if (photometricInterpretation == "YBR_FULL")
            interpr = EPI_YBR_Full;
        // some photometric interpretations are not supported:
        else if ((photometricInterpretation == "YBR_FULL_422") || (photometricInterpretation == "YBR_PARTIAL_422") ||
                 (photometricInterpretation == "YBR_PARTIAL_420") || (photometricInterpretation == "YBR_ICT") ||
                 (photometricInterpretation == "YBR_RCT"))
        {
            CERR << "Jpeg Codec Encoder: photometric interpretation not supported: " << photometricInterpretation
                 << endl;
            return EC_IllegalParameter;
        }
        else // Palette, HSV, ARGB, CMYK
            interpr = EPI_Unknown;

        // create initial pixel sequence with empty offset table
        DcmPixelSequence* pixelSequence = NULL;
        DcmPixelItem* offsetTable = NULL;
        if (result.good())
        {
            pixelSequence = new DcmPixelSequence(DcmTag(DCM_PixelData, EVR_OB));
            if (pixelSequence == NULL)
                result = EC_MemoryExhausted;
            else
            {
                // create empty offset table
                offsetTable = new DcmPixelItem(DcmTag(DCM_Item, EVR_OB));
                if (offsetTable == NULL)
                    result = EC_MemoryExhausted;
                else
                    pixelSequence->insert(offsetTable);
            }
        }

        // prepare some variables for encoding
        unsigned long frameCount = OFstatic_cast(unsigned long, numberOfFrames);
        unsigned long frameSize = columns * rows * samplesPerPixel * bytesAllocated;
        const Uint8* framePointer = NULL;
        unsigned long compressedSize = 0;

        // create encoder corresponding to bit depth (8 or 16 bit)
        DJEncoder* jpeg = createEncoderInstance(toRepParam, djcp, OFstatic_cast(Uint8, bitsAllocated));
        if (jpeg)
        {
            // main loop for compression: compress each frame
            for (unsigned int i = 0; i < frameCount && result.good(); i++)
            {
                framePointer = pixelDataFile->framePixelData(i);

                if (framePointer == NULL && i == 0)
                {
                    result = EC_MemoryExhausted;
                }
                else if (framePointer == NULL)
                {
                    frameCount = OFstatic_cast(unsigned long, i);
                    numberOfFrames = frameCount;
                }
                else
                {
                    // IJG libs need "color by pixel", transform if required
                    if ((samplesPerPixel > 1))
                    {
                        if ((planarConfiguration == 1))
                        {
                            if (bytesAllocated == 1)
                                result = togglePlanarConfiguration8((Uint8*)framePointer, frameSize, samplesPerPixel,
                                                                    (Uint16)1 /* switch to "by pixel"*/);
                            else
                                result =
                                    togglePlanarConfiguration16((Uint16*)framePointer, frameSize / 2 /*16 bit*/,
                                                                samplesPerPixel, (Uint16)1 /* switch to "by pixel"*/);
                        }
                    }
                    if (result.bad())
                    {
                        CERR << "Jpeg Codec Encoder: Unable to change Planar Configuration for encoding" << endl;
                        return result;
                    }

                    // byte swap pixel data to little endian if bits allocated is 8
                    if ((gLocalByteOrder == EBO_BigEndian) && (bitsAllocated == 8))
                    {
                        swapIfNecessary(EBO_LittleEndian, gLocalByteOrder,
                                        OFstatic_cast(void*, OFconst_cast(Uint8*, framePointer)), length,
                                        sizeof(Uint16));
                    }

                    if (bitsAllocated == 8)
                    {
                        jpeg->encode(columns, rows, interpr, samplesPerPixel, (Uint8*)framePointer, jpegData, jpegLen);
                    }
                    else if (bitsAllocated == 16)
                    {
                        jpeg->encode(columns, rows, interpr, samplesPerPixel, (Uint16*)framePointer, jpegData, jpegLen);
                    }
                    // update variables
                    compressedSize += jpegLen;
                    if (jpegLen == 0)
                    {
                        CERR << "Jpeg Codec Encoder: Error encoding frame" << endl;
                        result = EC_CannotChangeRepresentation;
                    }
                    else
                    {
                        result =
                            pixelSequence->storeCompressedFrame(offsetList, jpegData, jpegLen, djcp->getFragmentSize());
                    }
                    // free memory
                    delete[] jpegData;
                }
            }
        }
        else
        {
            CERR << "Jpeg Codec Encoder: Cannot allocate encoder instance" << endl;
            result = EC_IllegalCall;
        }
        if (result.good())
        {
            compressionRatio = ((double)bytesAllocated * samplesPerPixel * columns * rows *
                                OFstatic_cast(unsigned long, numberOfFrames)) /
                               compressedSize;
            result = dataset->insert(pixelSequence, OFFalse, OFFalse);
            if (result.bad())
            {
                CERR << "Jpeg Codec Encoder: Unable to get relevant attributes from dataset" << endl;
            }
        }
        else
            delete pixelSequence;
        delete jpeg; // encoder no longer in use
        // the following operations do not affect the Image Pixel Module
        // but other modules such as SOP Common.  We only perform these
        // changes if we're on the main level of the datsetItem,
        // which should always identify itself as datsetItem, not as item.
        if (result.good())
            result = updateDerivationDescription(datsetItem, toRepParam, djcp, OFstatic_cast(Uint8, bitsAllocated),
                                                 compressionRatio);

        if ((datsetItem->ident() == EVR_item) && result.good())
        {
            // convert to Secondary Capture if requested by user.
            // This method creates a new SOP class UID, so it should be executed
            // after the call to newInstance() which creates a Source Image Sequence.
            if (djcp->getConvertToSC() || (djcp->getUIDCreation() == EUC_always))
            {
                result = DcmCodec::convertToSecondaryCapture(datsetItem);
                // update image type (set to DERIVED)
                if (result.good())
                    result = DcmCodec::updateImageType(datsetItem);
                result = DcmCodec::newInstance((DcmItem*)datsetItem, "DCM", "121320", "Uncompressed predecessor");
            }
        }
    }
    return result;
}

void JpegCodecEncoder::appendCompressionRatio(OFString& arg, double ratio)
{
    char buf[64];
    OFStandard::ftoa(buf, sizeof(buf), ratio, OFStandard::ftoa_uppercase, 0, 5);
    arg += buf;
}

OFCondition JpegCodecEncoder::updateLossyCompressionRatio(DcmItem* dataset, double ratio) const
{
    if (dataset == NULL)
        return EC_IllegalCall;

    // set Lossy Image Compression to "01" (see DICOM part 3, C.7.6.1.1.5)
    OFCondition result = dataset->putAndInsertString(DCM_LossyImageCompression, "01");
    if (result.bad())
        return result;

    // set Lossy Image Compression Ratio
    OFString s;
    const char* oldRatio = NULL;
    if ((dataset->findAndGetString(DCM_LossyImageCompressionRatio, oldRatio)).good() && oldRatio)
    {
        s = oldRatio;
        s += "\\";
    }
    appendCompressionRatio(s, ratio);

    result = dataset->putAndInsertString(DCM_LossyImageCompressionRatio, s.c_str());
    if (result.bad())
        return result;

    // count VM of lossy image compression ratio
    size_t i;
    size_t s_vm = 0;
    size_t s_sz = s.size();
    for (i = 0; i < s_sz; ++i)
        if (s[i] == '\\')
            ++s_vm;

    // set Lossy Image Compression Method
    const char* oldMethod = NULL;
    OFString m;
    if ((dataset->findAndGetString(DCM_LossyImageCompressionMethod, oldMethod)).good() && oldMethod)
    {
        m = oldMethod;
        m += "\\";
    }

    // count VM of lossy image compression method
    size_t m_vm = 0;
    size_t m_sz = m.size();
    for (i = 0; i < m_sz; ++i)
        if (m[i] == '\\')
            ++m_vm;

    // make sure that VM of Compression Method is not smaller than  VM of Compression Ratio
    while (m_vm++ < s_vm)
        m += "\\";

    m += "ISO_10918_1";
    return dataset->putAndInsertString(DCM_LossyImageCompressionMethod, m.c_str());
}

OFCondition JpegCodecEncoder::updateDerivationDescription(DcmItem* dataset,
                                                          const DcmRepresentationParameter* toRepParam,
                                                          const DJCodecParameter* cp, Uint8 bitsPerSample,
                                                          double ratio) const
{
    OFString derivationDescription;

    // create new Derivation Description
    createDerivationDescription(toRepParam, cp, bitsPerSample, ratio, derivationDescription);

    // append old Derivation Description, if any
    const char* oldDerivation = NULL;
    if ((dataset->findAndGetString(DCM_DerivationDescription, oldDerivation)).good() && oldDerivation)
    {
        derivationDescription += " [";
        derivationDescription += oldDerivation;
        derivationDescription += "]";
        if (derivationDescription.length() > 1024)
        {
            // ST is limited to 1024 characters, cut off tail
            derivationDescription.erase(1020);
            derivationDescription += "...]";
        }
    }

    OFCondition result = dataset->putAndInsertString(DCM_DerivationDescription, derivationDescription.c_str());
    if (result.good())
    {
        // assume we can cast the codec parameter to what we need
        DJCodecParameter* djcp = (DJCodecParameter*)cp;

        if (djcp->getTrueLosslessMode())
            result = DcmCodec::insertCodeSequence(dataset, DCM_DerivationCodeSequence, "DCM", "121327",
                                                  "Full fidelity image, uncompressed or lossless compressed");
        else
            result =
                DcmCodec::insertCodeSequence(dataset, DCM_DerivationCodeSequence, "DCM", "113040", "Lossy Compression");
    }
    return result;
}

OFCondition JpegCodecEncoder::togglePlanarConfiguration8(Uint8* pixelData, const unsigned long numValues,
                                                         const Uint16 samplesPerPixel, const Uint16 oldPlanarConfig)
{
    if ((pixelData == NULL) || (numValues % samplesPerPixel != 0))
        return EC_IllegalParameter;
    // allocate target buffer
    Uint8* px8 = new Uint8[numValues];
    if (!px8)
        return EC_MemoryExhausted;
    unsigned long numPixels = numValues / samplesPerPixel;
    if (oldPlanarConfig == 1) // change from "by plane" to "by pixel"
    {
        for (unsigned long n = 0; n < numPixels; n++)
        {
            for (Uint16 s = 0; s < samplesPerPixel; s++)
                px8[n * samplesPerPixel + s] = pixelData[n + numPixels * s];
        }
    }
    else // change from "by pixel" to "by plane"
    {
        for (unsigned long n = 0; n < numPixels; n++)
        {
            for (Uint16 s = 0; s < samplesPerPixel; s++)
                px8[n + numPixels * s] = pixelData[n * samplesPerPixel + s];
        }
    }
    // copy filled buffer to pixel data and free memory
    memcpy(pixelData, px8, OFstatic_cast(size_t, numValues));
    delete[] px8;
    return EC_Normal;
}

OFCondition JpegCodecEncoder::togglePlanarConfiguration16(Uint16* pixelData,
                                                          const unsigned long numValues, // number of 16-bit components
                                                          const Uint16 samplesPerPixel, const Uint16 oldPlanarConfig)
{
    if ((pixelData == NULL) || (numValues % samplesPerPixel != 0))
        return EC_IllegalParameter;
    // allocate target buffer
    Uint16* px16 = new Uint16[numValues];
    if (!px16)
        return EC_MemoryExhausted;
    unsigned long numPixels = numValues / samplesPerPixel;
    if (oldPlanarConfig == 1) // change from "by plane" to "by pixel"
    {
        for (unsigned long n = 0; n < numPixels; n++)
        {
            for (Uint16 s = 0; s < samplesPerPixel; s++)
                px16[n * samplesPerPixel + s] = pixelData[n + numPixels * s];
        }
    }
    else // change from "by pixel" to "by plane"
    {
        for (unsigned long n = 0; n < numPixels; n++)
        {
            for (Uint16 s = 0; s < samplesPerPixel; s++)
                px16[n + numPixels * s] = pixelData[n * samplesPerPixel + s];
        }
    }
    // copy filled buffer to pixel data and free memory
    memcpy(pixelData, px16, OFstatic_cast(size_t, numValues * 2));
    delete[] px16;
    return EC_Normal;
}

OFCondition JpegCodecEncoder::updatePlanarConfiguration(DcmItem* item, const Uint16 newPlanConf) const
{
    if ((item == NULL) || (newPlanConf) > 1)
        return EC_IllegalParameter;
    return item->putAndInsertUint16(DCM_PlanarConfiguration, newPlanConf);
}
