#ifndef BASEPIXELDATAENCODER_H
#define BASEPIXELDATAENCODER_H

#include "ipixeldataencoder.h"

class LinePixelDataFile;

class BasePixelDataEncoder : public IPixelDataEncoder
{
public:
    virtual ~BasePixelDataEncoder();
    virtual Uint32 numberOfFrames() const;
    virtual Uint32 pixelSize() const;

protected:
    BasePixelDataEncoder(LinePixelDataFile* basePixelDataFile);
    OFCondition insertFrameInfo(DcmItem* dataset, LinePixelDataFile* pixelDataFile) const;
    virtual OFCondition insertImageType(DcmItem* dataset) const = 0;
    void freeMem();

protected:
    LinePixelDataFile* m_PixelDataFile;
};

#endif // BASEPIXELDATAENCODER_H
