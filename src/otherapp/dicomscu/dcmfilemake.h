#ifndef DCMFILEMAKE_H
#define DCMFILEMAKE_H

#include "cmdcfg.h"
#include "dcmtk/ofstd/oftypes.h"
#include "dcmtk/dcmdata/dcxfer.h"
#include "dcmutilitydef.h"

//#pragma pack(2)  // arm gcc 不支持该指令
typedef struct tagBITMAPFILEHEADER // size :14
{
    unsigned short bfType;
    unsigned long bfSize;
    unsigned short bfReserved1;
    unsigned short bfReserved2;
    unsigned long bfOffBits;
} BITMAPFILEHEADER, *PBITMAPFILEHEADER;
//#pragma pack()

typedef struct tagBITMAPINFOHEADER
{
    unsigned long biSize;
    long biWidth;
    long biHeight;
    unsigned short biPlanes;
    unsigned short biBitCount;
    unsigned long biCompression;
    unsigned long biSizeImage;
    long biXPelsPerMeter;
    long biYPelsPerMeter;
    unsigned long biClrUsed;
    unsigned long biClrImportant;
} BITMAPINFOHEADER, *PBITMAPINFOHEADER;

#define WIDTHBYTES(i) (((i) + 31) / 32 * 4)

#define BMPFRAMES_FILE_NAME "bmpframes"
#define BMP_FILE_NAME "dcmpict.bmp"
//#define DCMTAG_FILE_NAME      "dcmtag"
#define BMPDCM_FILE_NAME "bmpdcmfile.dcm"
#define DCMSTORAGE_OPTION_FILE_NAME "dcmstorage.option"

extern int sr_para_count_but_filename; // argc subtracts the num of dcm filename in cmdLine
extern DcmStorage_Option g_DcmStorage_Option;

class DcmDataset;
class DcmMetaInfo;

int DcmFileMake(int argc, char* argv[]);
int FillDataSet(DcmMetaInfo* metaInfo, DcmDataset* dataset, const char* fname, bool isPrinttask,
                unsigned long* aboutSize, E_TransferSyntax& transferSyntax);
int ReadDcmStorageOption(const char* filename, PDcmStorage_Option pDcmStorage_Option);
void ClearDcmStorageOption(PDcmStorage_Option pDcmStorage_Option);
void SetFileMakeVerbose(bool value);
void SetFileMakeDebug(bool value);

#endif
