/*
#ifdef HAVE_GUSI_H
#include <GUSI.h>
#endif


BEGIN_EXTERN_C
#ifdef HAVE_FCNTL_H
#include <fcntl.h>       / * for O_RDONLY * /
#endif
#ifdef HAVE_SYS_TYPES_H
#include <sys/types.h>   / * required for sys/stat.h * /
#endif
#ifdef HAVE_SYS_STAT_H
#include <sys/stat.h>    / * for stat, fstat * /
#endif
END_EXTERN_C*/

#include "dcmtk/ofstd/ofstdinc.h"

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#include "dcmtk/ofstd/ofstd.h"
#include "dcmtk/ofstd/ofstring.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/ofstd/ofdatime.h"
#include "dcmtk/ofstd/ofthread.h"

#include "dcmtk/dcmdata/dctk.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/dcmdata/dcuid.h" /* for dcmtk version name */
#include "dcmtk/dcmdata/dccodec.h"
#include "dcmtk/dcmdata/dcdebug.h"

#include "dcmtk/dcmimage/diqtctab.h"
#include "dcmtk/dcmimgle/dcmimage.h"
#ifdef WITH_OPENSSL
#include "dcmtk/dcmtls/tlstrans.h"
#include "dcmtk/dcmtls/tlslayer.h"
#endif
#include "dcmtk/dcmimage/diqtpix.h"

#ifdef WITH_ZLIB
#include "zlib.h" /* for zlibVersion() */
#endif
#include "dcmcmd.h"
#include "dcmfilemake.h"
#include "pixeldataencoder.h"
#include "timelogger.h"
#include "generalinfo.h"

#ifdef DICOM_DEBUG
static OFBool opt_verbose = OFTrue;
static OFBool opt_debug = OFTrue;
#else
static OFBool opt_verbose = OFFalse;
static OFBool opt_debug = OFFalse;
#endif

// read patient diagnose information
static int ReadDcmDatasetTag(const char* filename, PDcmDataset_Tag pDataset_Tag);

// char *pDCM_FileName = NULL;//用于控制设定生成dicom文件名
char g_StrSOPInstanceUID[128];
int sr_para_count_but_filename;
//全局配置,一次storescu只load一次
DcmStorage_Option g_DcmStorage_Option;
//每次filldataset，都会和全局配置同步一次,然后会对里面的参数做调整
static DcmStorage_Option s_DcmStorage_Option;

OFList<OFString> SOPInstanceUIDList;

int main_mkdcmfile(int argc, char* argv[])
{
    int hasOption = 0;
    memset(&g_DcmStorage_Option, 0, sizeof(DcmStorage_Option));
    if (ReadDcmStorageOption(DCMSTORAGE_OPTION_FILE_NAME, &g_DcmStorage_Option) == 0)
    {
        hasOption = 1;
    }

    int ret = DcmFileMake(argc, argv);

    if (hasOption == 1)
    {
        ClearDcmStorageOption(&g_DcmStorage_Option);
    }

    return ret;
}

#define UNPIXEL_SIZE 1164

int FillDataSet(DcmMetaInfo* metaInfo, DcmDataset* dataset, const char* fname, bool isPrinttask,
                unsigned long* aboutSize, E_TransferSyntax& transferSyntax)
{
    OFString currentDcmFileName = fname;

    // we want to get this element's prefix for some purposes
    OFString temp = currentDcmFileName;

    size_t stringsize = currentDcmFileName.size(); // not calculate \0
    size_t suffixPos = stringsize - 4;
    OFString prefix = temp.erase(suffixPos, 4); // removes ".dcm"

    OFString pixelFileName = prefix;
    OFString colorMapFileName = prefix + ".colormap";
    OFString dcmtagfilename = prefix + ".dcmtag";
    OFString sonoParametersfilename = prefix + ".sonoParameters";

    DcmDataset_Tag Dataset_Tag;
    memset(&Dataset_Tag, 0, sizeof(Dataset_Tag));
    if (ReadDcmDatasetTag(dcmtagfilename.c_str(), &Dataset_Tag) != 0)
    {
        return -1;
    }

    OFCondition status = EC_Normal;

    if (metaInfo != NULL)
    {
        metaInfo->putAndInsertString(DCM_SourceApplicationEntityTitle, Dataset_Tag.SourceApplicationEntityTitle);
    }

    dataset->putAndInsertString(DCM_SpecificCharacterSet, "ISO_IR 100");
    dataset->putAndInsertString(DCM_SOPInstanceUID, Dataset_Tag.SopInstanceID);
    SOPInstanceUIDList.push_back(OFString(Dataset_Tag.SopInstanceID));
    dataset->putAndInsertString(DCM_StudyInstanceUID, Dataset_Tag.StudyInstanceID);
    dataset->putAndInsertString(DCM_SeriesInstanceUID, Dataset_Tag.SeriesInstanceID);

    // PerformedStationAETitle
    // DCM_SourceApplicationEntityTitle. dvtk validate failed:cann't use to be additional attributes
    // dataset->putAndInsertString(DCM_SourceApplicationEntityTitle,Dataset_Tag.SourceApplicationEntityTitle); //added
    // 2014-11-19
    dataset->putAndInsertString(DCM_PerformedStationAETitle,
                                Dataset_Tag.SourceApplicationEntityTitle); // added 2011-12-7
    dataset->putAndInsertString(DCM_StudyDate, Dataset_Tag.StudyDate);
    dataset->putAndInsertString(DCM_StudyTime, Dataset_Tag.StudyTime);
    dataset->putAndInsertString(DCM_StudyID, Dataset_Tag.StudyID);
    dataset->putAndInsertString(DCM_PatientsName, Dataset_Tag.PatientsName);
    dataset->putAndInsertString(DCM_PatientID, Dataset_Tag.PatientID);
    dataset->putAndInsertString(DCM_PatientsWeight, Dataset_Tag.StudyWeight);
    dataset->putAndInsertString(DCM_PatientsSize, Dataset_Tag.StudyHeight);
    if (strlen(Dataset_Tag.PatientsAge))
    {
        dataset->putAndInsertString(DCM_PatientsAge, Dataset_Tag.PatientsAge); // formats: days:"xxxD"
    }
    dataset->putAndInsertString(DCM_PatientsBirthDate, Dataset_Tag.PatientsBirthDate);
    dataset->putAndInsertString(DCM_AccessionNumber, Dataset_Tag.AccessionNumber); // added 2010-07-13
    if (strlen(Dataset_Tag.PatientsSex) == 0)
    {
        strcpy(Dataset_Tag.PatientsSex, "O");
    }
    dataset->putAndInsertString(DCM_PatientsSex, Dataset_Tag.PatientsSex);
    dataset->putAndInsertString(DCM_ReferringPhysiciansName, Dataset_Tag.ReferringPhysiciansName);
    dataset->putAndInsertString(DCM_StudyDescription, Dataset_Tag.StudyDescription);
    dataset->putAndInsertString(DCM_InstitutionName, Dataset_Tag.InstitutionName);

    dataset->putAndInsertString(DCM_PatientOrientation, ""); // added 2011-12-7

    dataset->putAndInsertString(DCM_SeriesDate, Dataset_Tag.StudyDate);
    dataset->putAndInsertString(DCM_SeriesTime, Dataset_Tag.StudyTime);
    // dataset->putAndInsertString(DCM_ManufacturersModelName, "SonoTouch");
    dataset->putAndInsertString(DCM_PixelAspectRatio, "1\\1");
    dataset->putAndInsertString(DCM_PatientsBirthTime, "000000");
    // dataset->putAndInsertString(DCM_DeviceSerialNumber, "sn112233");
    dataset->putAndInsertString(DCM_SoftwareVersions,
                                GeneralInfo::instance().softWareVersion().trimmed().toStdString().c_str());
    dataset->putAndInsertString(DCM_ProtocolName, "");
    // dataset->putAndInsertString(DCM_DepthOfScanField, "24");

    dataset->putAndInsertString(DCM_SeriesNumber, "1"); // DCM_SeriesNumber,  к aded 2011-12-7
    dataset->putAndInsertString(DCM_Modality, Dataset_Tag.Modality);

    dataset->putAndInsertString(DCM_Manufacturer, Dataset_Tag.Manufacturer);
    dataset->putAndInsertString(DCM_InstanceNumber, Dataset_Tag.ImageNumber); // ImageNum

    if (strlen(Dataset_Tag.PixelSpacing) != 0)
    {
        dataset->putAndInsertString(DCM_PixelSpacing, Dataset_Tag.PixelSpacing);
    }
    DcmItem* regionItem;
    int regions = atoi(Dataset_Tag.SequenceOfUltrasoundRegions);
    printf("the region number is: %d\n", regions);
    fflush(stdout);
    if (regions > 0)
    {
        for (int i = 0; i < regions; ++i)
        {
            dataset->findOrCreateSequenceItem(DCM_SequenceOfUltrasoundRegions, regionItem, i);
            regionItem->putAndInsertUint16(DCM_RegionSpatialFormat,
                                           atoi(Dataset_Tag.RegionArrays[i].RegionSpatialFormat));
            regionItem->putAndInsertUint16(DCM_RegionDataType, atoi(Dataset_Tag.RegionArrays[i].RegionDataType));
            regionItem->putAndInsertUint32(DCM_RegionFlags, atol(Dataset_Tag.RegionArrays[i].RegionFlags));
            regionItem->putAndInsertUint32(DCM_RegionLocationMinX0,
                                           atol(Dataset_Tag.RegionArrays[i].RegionLocationMinX0));
            regionItem->putAndInsertUint32(DCM_RegionLocationMinY0,
                                           atol(Dataset_Tag.RegionArrays[i].RegionLocationMinY0));
            regionItem->putAndInsertUint32(DCM_RegionLocationMaxX1,
                                           atol(Dataset_Tag.RegionArrays[i].RegionLocationMaxX1));
            regionItem->putAndInsertUint32(DCM_RegionLocationMaxY1,
                                           atol(Dataset_Tag.RegionArrays[i].RegionLocationMaxY1));
            if (strlen(Dataset_Tag.RegionArrays[i].ReferencePixelX0) != 0)
            {
                regionItem->putAndInsertSint32(DCM_ReferencePixelX0,
                                               atol(Dataset_Tag.RegionArrays[i].ReferencePixelX0));
                regionItem->putAndInsertSint32(DCM_ReferencePixelY0,
                                               atol(Dataset_Tag.RegionArrays[i].ReferencePixelY0));
                regionItem->putAndInsertFloat64(DCM_ReferencePixelPhysicalValueX,
                                                atof(Dataset_Tag.RegionArrays[i].ReferencePixelPhysicalValueX));
                regionItem->putAndInsertFloat64(DCM_ReferencePixelPhysicalValueY,
                                                atof(Dataset_Tag.RegionArrays[i].ReferencePixelPhysicalValueY));
            }
            regionItem->putAndInsertUint16(DCM_PhysicalUnitsXDirection,
                                           atoi(Dataset_Tag.RegionArrays[i].PhysicalUnitsXDirection));
            regionItem->putAndInsertUint16(DCM_PhysicalUnitsYDirection,
                                           atoi(Dataset_Tag.RegionArrays[i].PhysicalUnitsYDirection));
            regionItem->putAndInsertFloat64(DCM_PhysicalDeltaX, atof(Dataset_Tag.RegionArrays[i].PhysicalDeltaX));
            regionItem->putAndInsertFloat64(DCM_PhysicalDeltaY, atof(Dataset_Tag.RegionArrays[i].PhysicalDeltaY));
            regionItem->putAndInsertUint32(DCM_TransducerFrequency,
                                           atol(Dataset_Tag.RegionArrays[i].TransducerFrequency));
            if (strlen(Dataset_Tag.RegionArrays[i].PulseRepetitionFrequency) != 0)
            {
                regionItem->putAndInsertUint32(DCM_PulseRepetitionFrequency,
                                               atol(Dataset_Tag.RegionArrays[i].PulseRepetitionFrequency));
            }

            //            regionItem->putAndInsertString(DCM_RegionSpatialFormat,
            //            Dataset_Tag.RegionArrays[i].RegionSpatialFormat);
            //            regionItem->putAndInsertString(DCM_RegionDataType,
            //            Dataset_Tag.RegionArrays[i].RegionDataType); regionItem->putAndInsertString(DCM_RegionFlags,
            //            Dataset_Tag.RegionArrays[i].RegionFlags);
            //            regionItem->putAndInsertString(DCM_RegionLocationMinX0,
            //            Dataset_Tag.RegionArrays[i].RegionLocationMinX0);
            //            regionItem->putAndInsertString(DCM_RegionLocationMinY0,
            //            Dataset_Tag.RegionArrays[i].RegionLocationMinY0);
            //            regionItem->putAndInsertString(DCM_RegionLocationMaxX1,
            //            Dataset_Tag.RegionArrays[i].RegionLocationMaxX1);
            //            regionItem->putAndInsertString(DCM_RegionLocationMaxY1,
            //            Dataset_Tag.RegionArrays[i].RegionLocationMaxY1);
            //            regionItem->putAndInsertString(DCM_ReferencePixelX0,
            //            Dataset_Tag.RegionArrays[i].ReferencePixelX0);
            //            regionItem->putAndInsertString(DCM_ReferencePixelY0,
            //            Dataset_Tag.RegionArrays[i].ReferencePixelY0);
            //            regionItem->putAndInsertString(DCM_PhysicalUnitsXDirection,
            //            Dataset_Tag.RegionArrays[i].PhysicalUnitsXDirection);
            //            regionItem->putAndInsertString(DCM_PhysicalUnitsYDirection,
            //            Dataset_Tag.RegionArrays[i].PhysicalUnitsYDirection);
            //            regionItem->putAndInsertString(DCM_ReferencePixelPhysicalValueX,
            //            Dataset_Tag.RegionArrays[i].ReferencePixelPhysicalValueX);
            //            regionItem->putAndInsertString(DCM_ReferencePixelPhysicalValueY,
            //            Dataset_Tag.RegionArrays[i].ReferencePixelPhysicalValueY);
            //            regionItem->putAndInsertString(DCM_PhysicalDeltaX,
            //            Dataset_Tag.RegionArrays[i].PhysicalDeltaX);
            //            regionItem->putAndInsertString(DCM_PhysicalDeltaY,
            //            Dataset_Tag.RegionArrays[i].PhysicalDeltaY);
            //            regionItem->putAndInsertString(DCM_TransducerFrequency,
            //            Dataset_Tag.RegionArrays[i].TransducerFrequency);
            //            regionItem->putAndInsertString(DCM_PulseRepetitionFrequency,
            //            Dataset_Tag.RegionArrays[i].PulseRepetitionFrequency);
        }
    }

    //    //表示像素文件来源于图片非原始数据
    //    PhotometricInterpretationType screenInterpretationType = rgb;
    //    //若是打印则修改图像的解释为灰度(monochrome)
    //    if(isPrinttask)
    //    {
    //        //对于打印任务，不论其像素数据是原始的数据还是来自图片
    //        //其像素数据位宽均为8bits。
    //        screenInterpretationType = monochrome;
    //    }

    {
        TimeLogger* tl = NULL;
        if (opt_verbose)
        {
            tl = new TimeLogger();
        }

        PixelDataEncoder pixelDataEncoder(pixelFileName.c_str(), colorMapFileName.c_str(),
                                          sonoParametersfilename.c_str(), isPrinttask, g_DcmStorage_Option,
                                          atoi(Dataset_Tag.Framerate), opt_verbose, opt_debug);

        transferSyntax = pixelDataEncoder.supportedTransferSyntax();
        status = pixelDataEncoder.encode(dataset);

        if (opt_verbose)
        {
            double time = tl->logger(NULL, false);

            printf("PixelData Encoder: ret %d time %3f numberOfFrames %d pixelSize %d TransferSyntax %d.\n",
                   status.good(), time, pixelDataEncoder.numberOfFrames(), pixelDataEncoder.pixelSize(),
                   transferSyntax);

            delete tl;
            tl = NULL;
        }
    }

    if (status == EC_Normal)
    {
        *aboutSize = dataset->calcElementLength(transferSyntax, EET_UndefinedLength);
        printf("FillDataSet succeed.\n");
        return 0;
    }
    else
    {
        printf("FillDataSet failed.Insert pixeldata failed:%s\n", status.text());
        return -1;
    }
}

/**
 * @brief DcmFileMake make dcm file
 *
 * if has DcmStorage_Option, will use DcmStorage_Option
 * else will use uncompress option
 *
 * sr print use uncompress option
 * @param argc
 * @param argv
 * @return
 */
int DcmFileMake(int argc, char* argv[])
{
    OFList<OFString> dcmfilenameList;

    OFString outputDir;
    for (int i = 0; i < argc; ++i)
    {
        OFString arg(argv[i]);

        if (arg == "-v" || arg == "--verbose")
        {
            opt_verbose = OFTrue;
        }
        else if (arg == "-d" || arg == "--debug")
        {
            opt_debug = OFTrue;
        }
        else if (arg == "-dir")
        {
            //-dir 可以控制输出dcm文件的路径，如果没设置，则为源文件路径
            if ((i + 1) < argc)
            {
                outputDir = OFString(argv[i + 1]);
                if (outputDir.at(outputDir.size() - 1) != '/')
                {
                    outputDir.append("/");
                }

                i++;
            }
        }
        else if (arg.rfind(OFString(".dcm")) != OFString_npos)
        {
            dcmfilenameList.push_back(arg);
        }
    }

    char* taskname = argv[0];

    size_t count = dcmfilenameList.size();

    sr_para_count_but_filename = argc - count;

    SOPInstanceUIDList.clear();

    OFIterator<OFString> itera = dcmfilenameList.begin();
    OFIterator<OFString> end_itera = dcmfilenameList.end();

    for (itera; itera != end_itera; ++itera)
    {
        OFString currentDcmFileName = *itera;

        OFCondition status = EC_Normal;
        DcmFileFormat fileformat;
        bool isPrinttask = strstr(taskname, "chison_dcmprscu") != NULL || strstr(taskname, "chison_dcmpsprt") != NULL;
        unsigned long aboutSize = 0;
        E_TransferSyntax xfer;
        if (FillDataSet(fileformat.getMetaInfo(), fileformat.getDataset(), currentDcmFileName.c_str(), isPrinttask,
                        &aboutSize, xfer) == 0)
        {
            OFString dcmPath = outputDir;

            if (!dcmPath.empty())
            {
                size_t pos = currentDcmFileName.rfind("/");
                if (pos == OFString_npos)
                {
                    dcmPath.append(currentDcmFileName);
                }
                else
                {
                    dcmPath.append(currentDcmFileName.substr(pos + 1, currentDcmFileName.size() - (pos + 1)));
                }
            }
            else
            {
                dcmPath = currentDcmFileName;
            }

            const char* dcmfile = dcmPath.c_str();

            status = fileformat.saveFile(dcmfile, xfer, EET_UndefinedLength, EGL_recalcGL);

            if (status == EC_Normal)
            {
                printf("save %s succeed .\n", opt_verbose ? dcmfile : "dcm");
            }
            else
            {
                printf("save %s failed:%s\n", opt_verbose ? dcmfile : "dcm", status.text());
                if (!opt_debug)
                {
                    remove(BMPDCM_FILE_NAME);
                }
                return -1;
            }
        }
        else
        {
            return -1;
        }
    }
    return 0;
}

int ReadDcmStorageOption(const char* filename, PDcmStorage_Option pDcmStorage_Option)
{
    FILE* pFile;
    if ((pFile = fopen(filename, "rb")) == NULL)
    {
        printf("can not open file %s!\n", filename);
        return -1;
    }
    else
    {
        size_t readLen = 0;
        readLen = fread(pDcmStorage_Option, sizeof(DcmStorage_Option), 1, pFile);
        fclose(pFile);

        if (readLen == 0)
        {
            return -1;
        }
    }
    return 0;
}

static int ReadDcmDatasetTag(const char* filename, PDcmDataset_Tag pDataset_Tag)
{
    FILE* pFile;
    if ((pFile = fopen(filename, "rb")) == NULL)
    {
        printf("can not open file %s!\n", filename);
        return -1;
    }
    else
    {
        size_t readLen = 0;

        readLen = fread(pDataset_Tag,
                        (unsigned char*)(pDataset_Tag->InstitutionName) - (unsigned char*)pDataset_Tag +
                            sizeof(pDataset_Tag->InstitutionName),
                        1, pFile);

        if (fread(pDataset_Tag->Manufacturer, sizeof(pDataset_Tag->Manufacturer), 1, pFile) == 0)
        {
            strcpy(pDataset_Tag->Manufacturer, "CHISON");
        }

        // SopInstanceID 20161017 新增的，为了保持与上层APP的兼容性，做特殊控制，Tiger app还未写入此字段
        if (fread(pDataset_Tag->SopInstanceID, sizeof(pDataset_Tag->SopInstanceID), 1, pFile) == 0)
        {
            dcmGenerateUniqueIdentifier(pDataset_Tag->SopInstanceID, SITE_INSTANCE_UID_ROOT);
        }

        if (fread(pDataset_Tag->ImageNumber, sizeof(pDataset_Tag->ImageNumber), 1, pFile) == 0)
        {
            strcpy(pDataset_Tag->ImageNumber, "1");
        }

        fread(pDataset_Tag->PixelSpacing, sizeof(pDataset_Tag->PixelSpacing), 1, pFile);

        // 填充SequenceOfUltrasoundRegions数据
        if (fread(pDataset_Tag->SequenceOfUltrasoundRegions, sizeof(pDataset_Tag->SequenceOfUltrasoundRegions), 1,
                  pFile) == 0)
        {
            strncpy(pDataset_Tag->SequenceOfUltrasoundRegions, "0", sizeof(pDataset_Tag->SequenceOfUltrasoundRegions));
        }
        else
        {
            int regions = atoi(pDataset_Tag->SequenceOfUltrasoundRegions);
            if (regions > 0)
            {
                for (int i = 0; i < regions; ++i)
                {
                    fread(&pDataset_Tag->RegionArrays[i], sizeof(CalibrationRegions_Tag), 1, pFile);
                }
            }
        }

        fclose(pFile);
        //        remove(filename);

        if (readLen == 0)
        {
            return -1;
        }
    }
    return 0;
}

void ClearDcmStorageOption(PDcmStorage_Option pDcmStorage_Option)
{
    memset(pDcmStorage_Option, 0, sizeof(DcmStorage_Option));
    if (!opt_debug)
    {
        remove(DCMSTORAGE_OPTION_FILE_NAME);
    }
}

void SetFileMakeVerbose(bool value)
{
    opt_verbose = value;
}

void SetFileMakeDebug(bool value)
{
    opt_debug = value;
}
