#ifndef PIXELDATAFILE_H
#define PIXELDATAFILE_H

#include "typesdef.h"
#include "dcmutilitydef.h"

class PixelDataFileHead;

typedef struct dicomSonoParameters
{
    unsigned char elastoColor;
    unsigned char dummy;
    dicomSonoParameters()
    {
        elastoColor = 0;
        dummy = 0;
    }
} DicomSonoParameters;

class PixelDataFile
{
public:
    PixelDataFile(const PixelDataFileHead& fileHead, bool opt_verbose = false, bool opt_debug = false);
    virtual ~PixelDataFile();
    const PixelDataFileHead& fileHead() const;
    virtual bool isValid() const;
    virtual const Uint8* framePixelData(int curIndex);
    virtual const Uint8* palette() const;
    virtual const Uint8* pixelData() = 0;
    Uint16 bitsStored() const;
    Uint16 bitsAllocated() const;
    Uint16 bytesAllocated() const;
    Uint16 samplesPerPixel() const;
    Uint16 planarConfiguration() const;
    const char* photometricInterpretation() const;
    Uint16 columns() const;
    Uint16 rows() const;
    Uint32 numberOfFrames() const;
    Uint32 numberOfFramesReaded() const;
    Uint32 pixelSizeReaded() const;
    Uint8 colorspace() const;

protected:
    /**
     * @brief skipStartingFrame 因为有抽帧或者有最大可读帧的限制，需要尝试
     * 跳过一开始的一些帧
     *
     * @return
     */
    bool skipStartingFrames();
    Uint32 readFrame(int index, int& fileIndex, void* ptr, Uint32 size) const;
    /**
     * @brief readPalette 读取rgba*256格式的palette文件(修改为根据palette文件头信息决定256的倍数)
     * @param fileName
     * @param switchRB 是否要交换R和B的位置，目前只有在生成AVI时需要使用gbr格式，所以要设成true
     * @return
     */
    Uint8* readPalette(const char* fileName, bool switchRB = false);
    DicomSonoParameters* readSonoParameters(const char* fileName);
    void doGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, DicomSonoParameters sonoParameters,
                           Uint8* imageBuffer);

private:
    Uint32 adjustedSrcReadFrame(int numberOfFrames) const;

    void convertRegionalPixels(Uint8* frameBuffer, Uint32 begin, Uint32 end, Uint8* palette, Uint8* imageBuffer,
                               int& index, bool roi = false, bool elastoColor = false);

    void RegionSingleGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, Uint8* imageBuffer);
    void RegionLRGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, Uint8* imageBuffer);
    void RegionUDGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette, Uint8* imageBuffer);
    void RegionLRRoiGray8BitTo24Bit(Uint8* frameBuffer, Uint32 frameSize, Uint8* palette,
                                    DicomSonoParameters sonoParameters, Uint8* imageBuffer);

protected:
    bool m_opt_verbose;
    bool m_opt_debug;
    const PixelDataFileHead& m_FileHead;
    Uint32 m_NumberOfFrames;
    Uint32 m_NumberOfSrcFrames;
    Uint32 m_NumberOfFramesReaded;
    PaletteFile_head m_PaletteHead;
};

#endif // PIXELDATAFILE_H
