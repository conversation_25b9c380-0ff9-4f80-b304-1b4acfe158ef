/*
 *
 *  Copyright (C) 1994-2005, OFFIS
 *
 *  This software and supporting documentation were developed by
 *
 *    Kuratorium OFFIS e.V.
 *    Healthcare Information and Communication Systems
 *    Escherweg 2
 *    D-26121 Oldenburg, Germany
 *
 *  THIS SOFTWARE IS MADE AVAILABLE,  AS IS,  AND OFFIS MAKES NO  WARRANTY
 *  REGARDING  THE  SOFTWARE,  ITS  PERFORMANCE,  ITS  MERCHANTABILITY  OR
 *  FITNESS FOR ANY PARTICULAR USE, FREEDOM FROM ANY COMPUTER DISEASES  OR
 *  ITS CONFORMITY TO ANY SPECIFICATION. THE ENTIRE RISK AS TO QUALITY AND
 *  PERFORMANCE OF THE SOFTWARE IS WITH THE USER.
 *
 *  Module:  dcmnet
 *
 *  Author:  <PERSON>
 *
 *  Purpose: Query/Retrieve Service Class User (C-FIND operation)
 *
 *  Last Update:      $Author: meichel $
 *  Update Date:      $Date: 2005/12/08 15:44:19 $
 *  Source File:      $Source: /share/dicom/cvs-depot/dcmtk/dcmnet/apps/findscu.cc,v $
 *  CVS/RCS Revision: $Revision: 1.47 $
 *  Status:           $State: Exp $
 *
 *  CVS/RCS Log at end of file
 *
 */

#include "dcmtk/config/osconfig.h" /* make sure OS specific configuration is included first */

#define INCLUDE_CSTDLIB
#define INCLUDE_CSTDIO
#define INCLUDE_CSTRING
#define INCLUDE_CSTDARG
#define INCLUDE_CERRNO
#include "dcmtk/ofstd/ofstdinc.h"

BEGIN_EXTERN_C
#ifdef HAVE_SYS_FILE_H
#include <sys/file.h>
#endif
END_EXTERN_C

#ifdef HAVE_GUSI_H
#include <GUSI.h>
#endif

#include "dcmtk/dcmnet/dimse.h"
#include "dcmtk/dcmnet/diutil.h"
#include "dcmtk/dcmdata/dcfilefo.h"
#include "dcmtk/dcmdata/dcdebug.h"
#include "dcmtk/dcmdata/dcuid.h"
#include "dcmtk/dcmdata/dcdict.h"
#include "dcmtk/dcmdata/cmdlnarg.h"
#include "dcmtk/ofstd/ofconapp.h"
#include "dcmtk/dcmdata/dcuid.h" /* for dcmtk version name */
#include "dcmtk/dcmdata/dcdicent.h"
#include "dcmtk/dcmdata/dcdeftag.h"

#ifdef WITH_ZLIB
#include <zlib.h> /* for zlibVersion() */
#endif

#ifdef WITH_OPENSSL
#include "dcmtk/dcmtls/tlstrans.h"
#include "dcmtk/dcmtls/tlslayer.h"
#endif

#include "dcmcmd.h"
#include "dcmwritersp.h"

#define OFFIS_CONSOLE_APPLICATION "findscu"

static char rcsid[] = "$dcmtk: " OFFIS_CONSOLE_APPLICATION " v" OFFIS_DCMTK_VERSION " " OFFIS_DCMTK_RELEASEDATE " $";

/* default application titles */
#define APPLICATIONTITLE "FINDSCU"
#define PEERAPPLICATIONTITLE "ANY-SCP"

static OFBool opt_verbose = OFFalse;
static OFBool opt_debug = OFFalse;
static OFBool opt_abortAssociation = OFFalse;
static OFCmdUnsignedInt opt_maxReceivePDULength = ASC_DEFAULTMAXPDU;
static OFCmdUnsignedInt opt_repeatCount = 1;
static OFBool opt_extractResponsesToFile = OFFalse;
static const char* opt_abstractSyntax = UID_FINDModalityWorklistInformationModel;
static OFCmdSignedInt opt_cancelAfterNResponses = -1;
static DcmDataset* overrideKeys = NULL;
static E_TransferSyntax opt_networkTransferSyntax = EXS_Unknown;
static T_DIMSE_BlockingMode opt_blockMode = DIMSE_BLOCKING;
static int opt_dimse_timeout = 0;
int opt_acse_timeout = 30;

static OFString opt_scheduledModality = "US";
static OFString opt_scheduledStationAETitle;
static OFString opt_scheduledProcedureStepStartDate;
static OFBool opt_worklist = OFFalse;
static OFBool opt_mppsOpen = OFFalse;

FILE* fprsp = NULL;
int rsp_count = 0;
#define MAX_RSP_COUNT 500

typedef struct
{
    T_ASC_Association* assoc;
    T_ASC_PresentationContextID presId;
} MyCallbackInfo;

static void errmsg(const char* msg, ...)
{
    va_list args;

    fprintf(stderr, "%s: ", OFFIS_CONSOLE_APPLICATION);
    va_start(args, msg);
    vfprintf(stderr, msg, args);
    va_end(args);
    fprintf(stderr, "\n");
}

static OFCondition fillScheduledProcedureStepSequence(DcmDataset* dataset, const char* modality, const char* aet,
                                                      const char* startDate);

static void addOverrideKey(OFConsoleApplication& app, const char* s)
{
    unsigned int g = 0xffff;
    unsigned int e = 0xffff;
    int n = 0;
    char val[1024];
    OFString dicName, valStr;
    OFString msg;
    char msg2[200];
    val[0] = '\0';

    // try to parse group and element number
    n = sscanf(s, "%x,%x=%s", &g, &e, val);
    OFString toParse = s;
    size_t eqPos = toParse.find('=');
    if (n < 2) // if at least no tag could be parsed
    {
        // if value is given, extract it (and extrect dictname)
        if (eqPos != OFString_npos)
        {
            dicName = toParse.substr(0, eqPos).c_str();
            valStr = toParse.substr(eqPos + 1, toParse.length());
        }
        else             // no value given, just dictionary name
            dicName = s; // only dictionary name given (without value)
        // try to lookup in dictionary
        DcmTagKey key(0xffff, 0xffff);
        const DcmDataDictionary& globalDataDict = dcmDataDict.rdlock();
        const DcmDictEntry* dicent = globalDataDict.findEntry(dicName.c_str());
        dcmDataDict.unlock();
        if (dicent != NULL)
        {
            // found dictionary name, copy group and element number
            key = dicent->getKey();
            g = key.getGroup();
            e = key.getElement();
        }
        else
        {
            // not found in dictionary
            msg = "bad key format or dictionary name not found in dictionary: ";
            msg += dicName;
            app.printError(msg.c_str());
        }
    } // tag could be parsed, copy value if it exists
    else
    {
        if (eqPos != OFString_npos)
            valStr = toParse.substr(eqPos + 1, toParse.length());
    }
    DcmTag tag(g, e);
    if (tag.error() != EC_Normal)
    {
        sprintf(msg2, "unknown tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
    DcmElement* elem = newDicomElement(tag);
    if (elem == NULL)
    {
        sprintf(msg2, "cannot create element for tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
    if (valStr.length() > 0)
    {
        if (elem->putString(valStr.c_str()).bad())
        {
            sprintf(msg2, "cannot put tag value: (%04x,%04x)=\"", g, e);
            msg = msg2;
            msg += valStr;
            msg += "\"";
            app.printError(msg.c_str());
        }
    }

    if (overrideKeys == NULL)
        overrideKeys = new DcmDataset;
    if (overrideKeys->insert(elem, OFTrue).bad())
    {
        sprintf(msg2, "cannot insert tag: (%04x,%04x)", g, e);
        app.printError(msg2);
    }
}

void trimmed(const char* strIn, char* strOut)
{
    if (strIn == NULL || *strIn == '\0')
    {
        return;
    }
    int head = 0;
    int tail = strlen(strIn) - 1;
    while (isspace(strIn[head]))
    {
        ++head;
    }
    while (isspace(strIn[tail]))
    {
        --tail;
    }

    strncpy(strOut, strIn + head, tail - head + 1);
    strOut[tail - head + 1] = '\0';
}

static OFCondition addPresentationContext(T_ASC_Parameters* params);

static OFCondition cfind(T_ASC_Association* assoc, const char* fname);

#define SHORTCOL 4
#define LONGCOL 16

int main_findscu(int argc, char* argv[])
{
    T_ASC_Network* net;
    T_ASC_Parameters* params;
    const char* opt_peer;
    OFCmdUnsignedInt opt_port = 104;
    DIC_NODENAME localHost;
    DIC_NODENAME peerHost;
    T_ASC_Association* assoc;
    const char* opt_peerTitle = PEERAPPLICATIONTITLE;
    const char* opt_ourTitle = APPLICATIONTITLE;
    OFList<OFString> fileNameList;
    OFBool opt_secureConnection = OFFalse; /* default: no secure connection */

#ifdef WITH_OPENSSL
    int opt_keyFileFormat = SSL_FILETYPE_PEM;
    OFBool opt_doAuthenticate = OFFalse;
    const char* opt_privateKeyFile = NULL;
    const char* opt_certificateFile = NULL;
    const char* opt_passwd = NULL;
#if OPENSSL_VERSION_NUMBER >= 0x0090700fL
    OFString opt_ciphersuites(TLS1_TXT_RSA_WITH_AES_128_SHA ":" SSL3_TXT_RSA_DES_192_CBC3_SHA);
#else
    OFString opt_ciphersuites(SSL3_TXT_RSA_DES_192_CBC3_SHA);
#endif
    const char* opt_readSeedFile = NULL;
    const char* opt_writeSeedFile = NULL;
    DcmCertificateVerification opt_certVerification = DCV_requireCertificate;
    const char* opt_dhparam = NULL;
#endif

    /*
    ** Don't let dcmdata remove tailing blank padding or perform other
    ** maipulations.  We want to see the real data.
    */
    dcmEnableAutomaticInputDataCorrection.set(OFFalse);

#ifdef HAVE_GUSI_H
    GUSISetup(GUSIwithSIOUXSockets);
    GUSISetup(GUSIwithInternetSockets);
#endif

#ifdef HAVE_WINSOCK_H
    WSAData winSockData;
    /* we need at least version 1.1 */
    WORD winSockVersionNeeded = MAKEWORD(1, 1);
    WSAStartup(winSockVersionNeeded, &winSockData);
#endif

    char tempstr[20];
    OFConsoleApplication app(OFFIS_CONSOLE_APPLICATION, "DICOM query (C-FIND) SCU", rcsid);
    OFCommandLine cmd;

    cmd.setParamColumn(LONGCOL + SHORTCOL + 4);
    cmd.addParam("peer", "hostname of DICOM peer");
    cmd.addParam("port", "tcp/ip port number of peer");
    cmd.addParam("dcmfile_in", "DICOM query file(s)", OFCmdParam::PM_MultiOptional);

    cmd.setOptionColumns(LONGCOL, SHORTCOL);
    cmd.addGroup("general options:", LONGCOL, SHORTCOL + 2);
    cmd.addOption("--help", "-h", "print this help text and exit");
    cmd.addOption("--version", "print version information and exit", OFTrue /* exclusive */);
    cmd.addOption("--verbose", "-v", "verbose mode, print processing details");
    cmd.addOption("--debug", "-d", "debug mode, print debug information");
    cmd.addGroup("network options:");
    cmd.addSubGroup("override matching keys:");
    cmd.addOption("--key", "-k", 1, "key: gggg,eeee=\"str\" or data dictionary name=\"str\"", "override matching key");

    cmd.addSubGroup("scheduled procedure step sequence keys:");
    cmd.addOption("--sq-modality", "-sm", 1, "modality: string (default: US)", "scheduled modality");
    cmd.addOption("--sq-aetitle", "-saet", 1, "aetitle: string", "scheduled aetitle");
    cmd.addOption("--sq-date", "-sd", 1, "start date: date",
                  "scheduled start date, like 20150212, date range is 20150112-20150212 , 20150112- , -20150212");

    cmd.addSubGroup("query information model:");
    cmd.addOption("--worklist", "-W", "use modality worklist information model (default)");
    cmd.addOption("--patient", "-P", "use patient root information model");
    cmd.addOption("--study", "-S", "use study root information model");
    cmd.addOption("--psonly", "-O", "use patient/study only information model");
    cmd.addSubGroup("application entity titles:");
    OFString opt1 = "set my calling AE title (default: ";
    opt1 += APPLICATIONTITLE;
    opt1 += ")";
    cmd.addOption("--aetitle", "-aet", 1, "aetitle: string", opt1.c_str());
    OFString opt2 = "set called AE title of peer (default: ";
    opt2 += PEERAPPLICATIONTITLE;
    opt2 += ")";
    cmd.addOption("--call", "-aec", 1, "aetitle: string", opt2.c_str());
    cmd.addSubGroup("post-1993 value representations:");
    cmd.addOption("--enable-new-vr", "+u", "enable support for new VRs (UN/UT) (default)");
    cmd.addOption("--disable-new-vr", "-u", "disable support for new VRs, convert to OB");
    cmd.addSubGroup("proposed transmission transfer syntaxes:");
    cmd.addOption("--propose-uncompr",
                  "-x=", "propose all uncompressed TS, explicit VR\nwith local byte ordering first (default)");
    cmd.addOption("--propose-little", "-xe", "propose all uncompressed TS, explicit VR\nlittle endian first");
    cmd.addOption("--propose-big", "-xb", "propose all uncompressed TS, explicit VR\nbig endian first");
    cmd.addOption("--propose-implicit", "-xi", "propose implicit VR little endian TS only");
    cmd.addSubGroup("other network options:");
    OFString opt3 = "set max receive pdu to n bytes (default: ";
    sprintf(tempstr, "%ld", (long)ASC_DEFAULTMAXPDU);
    opt3 += tempstr;
    opt3 += ")";
    OFString opt4 = "[n]umber of bytes: integer [";
    sprintf(tempstr, "%ld", (long)ASC_MINIMUMPDUSIZE);
    opt4 += tempstr;
    opt4 += "..";
    sprintf(tempstr, "%ld", (long)ASC_MAXIMUMPDUSIZE);
    opt4 += tempstr;
    opt4 += "]";
    cmd.addOption("--timeout", "-to", 1, "[s]econds: integer (default: unlimited)", "timeout for connection requests");
    cmd.addOption("--acse-timeout", "-ta", 1, "[s]econds: integer (default: 30)", "timeout for ACSE messages");
    cmd.addOption("--dimse-timeout", "-td", 1, "[s]econds: integer (default: unlimited)", "timeout for DIMSE messages");
    cmd.addOption("--max-pdu", "-pdu", 1, opt4.c_str(), opt3.c_str());
    cmd.addOption("--repeat", 1, "[n]umber: integer", "repeat n times");
    cmd.addOption("--abort", "abort association instead of releasing it");
    cmd.addOption("--cancel", 1, "[n]umber: integer", "cancel after n responses (default: never)");
    cmd.addOption("--extract", "-X", "extract responses to file (rsp0001.dcm, ...)");

    cmd.addOption("--mpps", "-M", "extract responses to file for mpps(StudyInstanceUID.dcm, ...)");

#ifdef WITH_OPENSSL
    cmd.addGroup("transport layer security (TLS) options:");
    cmd.addSubGroup("transport protocol stack options:");
    cmd.addOption("--disable-tls", "-tls", "use normal TCP/IP connection (default)");
    cmd.addOption("--enable-tls", "+tls", 2, "[p]rivate key file, [c]ertificate file: string",
                  "use authenticated secure TLS connection");
    cmd.addOption("--anonymous-tls", "+tla", "use secure TLS connection without certificate");
    cmd.addSubGroup("private key password options (only with --enable-tls):");
    cmd.addOption("--std-passwd", "+ps", "prompt user to type password on stdin (default)");
    cmd.addOption("--use-passwd", "+pw", 1, "[p]assword: string ", "use specified password");
    cmd.addOption("--null-passwd", "-pw", "use empty string as password");
    cmd.addSubGroup("key and certificate file format options:");
    cmd.addOption("--pem-keys", "-pem", "read keys and certificates as PEM file (default)");
    cmd.addOption("--der-keys", "-der", "read keys and certificates as DER file");
    cmd.addSubGroup("certification authority options:");
    cmd.addOption("--add-cert-file", "+cf", 1, "[c]ertificate filename: string",
                  "add certificate file to list of certificates");
    cmd.addOption("--add-cert-dir", "+cd", 1, "[c]ertificate directory: string",
                  "add certificates in d to list of certificates");
    cmd.addSubGroup("ciphersuite options:");
    cmd.addOption("--cipher", "+cs", 1, "[c]iphersuite name: string", "add ciphersuite to list of negotiated suites");
    cmd.addOption("--dhparam", "+dp", 1, "[f]ilename: string", "read DH parameters for DH/DSS ciphersuites");
    cmd.addSubGroup("pseudo random generator options:");
    cmd.addOption("--seed", "+rs", 1, "[f]ilename: string", "seed random generator with contents of f");
    cmd.addOption("--write-seed", "+ws", "write back modified seed (only with --seed)");
    cmd.addOption("--write-seed-file", "+wf", 1, "[f]ilename: string (only with --seed)",
                  "write modified seed to file f");
    cmd.addSubGroup("peer authentication options:");
    cmd.addOption("--require-peer-cert", "-rc", "verify peer certificate, fail if absent (default)");
    cmd.addOption("--verify-peer-cert", "-vc", "verify peer certificate if present");
    cmd.addOption("--ignore-peer-cert", "-ic", "don't verify peer certificate");
#endif

    /* evaluate command line */
    prepareCmdLineArgs(argc, argv, OFFIS_CONSOLE_APPLICATION);
    if (app.parseCommandLine(cmd, argc, argv, OFCommandLine::ExpandWildcards))
    {
        /* check exclusive options first */

        if (cmd.getParamCount() == 0)
        {
            if (cmd.findOption("--version"))
            {
                app.printHeader(OFTrue /*print host identifier*/); // uses ofConsole.lockCerr()
                CERR << endl << "External libraries used:";
#if !defined(WITH_ZLIB) && !defined(WITH_OPENSSL)
                CERR << " none" << endl;
#else
                CERR << endl;
#endif
#ifdef WITH_ZLIB
                CERR << "- ZLIB, Version " << zlibVersion() << endl;
#endif
#ifdef WITH_OPENSSL
                CERR << "- " << OPENSSL_VERSION_TEXT << endl;
#endif
                return 0;
            }
        }

        /* command line parameters */

        cmd.getParam(1, opt_peer);
        app.checkParam(cmd.getParamAndCheckMinMax(2, opt_port, 1, 65535));

        if (cmd.findOption("--verbose"))
            opt_verbose = OFTrue;
        if (cmd.findOption("--debug"))
        {
            opt_debug = OFTrue;
            DUL_Debug(OFTrue);
            DIMSE_debug(OFTrue);
            SetDebugLevel(3);
        }

        if (cmd.findOption("--key", 0, OFCommandLine::FOM_First))
        {
            const char* ovKey = NULL;
            do
            {
                app.checkValue(cmd.getValue(ovKey));
                addOverrideKey(app, ovKey);
            } while (cmd.findOption("--key", 0, OFCommandLine::FOM_Next));

            printf("%s", ovKey);
        }

        if (cmd.findOption("--sq-modality"))
        {
            const char* value = NULL;
            app.checkValue(cmd.getValue(value));
            opt_scheduledModality = value;
        }

        if (cmd.findOption("--sq-aetitle"))
        {
            const char* value = NULL;
            app.checkValue(cmd.getValue(value));
            opt_scheduledStationAETitle = value;
        }

        if (cmd.findOption("--sq-date"))
        {
            const char* value = NULL;
            app.checkValue(cmd.getValue(value));
            opt_scheduledProcedureStepStartDate = value;
        }

        cmd.beginOptionBlock();
        if (cmd.findOption("--propose-uncompr"))
            opt_networkTransferSyntax = EXS_Unknown;
        if (cmd.findOption("--propose-little"))
            opt_networkTransferSyntax = EXS_LittleEndianExplicit;
        if (cmd.findOption("--propose-big"))
            opt_networkTransferSyntax = EXS_BigEndianExplicit;
        if (cmd.findOption("--propose-implicit"))
            opt_networkTransferSyntax = EXS_LittleEndianImplicit;
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--worklist"))
        {
            opt_abstractSyntax = UID_FINDModalityWorklistInformationModel;
            opt_worklist = true;
        }
        if (cmd.findOption("--patient"))
            opt_abstractSyntax = UID_FINDPatientRootQueryRetrieveInformationModel;
        if (cmd.findOption("--study"))
            opt_abstractSyntax = UID_FINDStudyRootQueryRetrieveInformationModel;
        if (cmd.findOption("--psonly"))
            opt_abstractSyntax = UID_FINDPatientStudyOnlyQueryRetrieveInformationModel;
        cmd.endOptionBlock();
        if (cmd.findOption("--aetitle"))
            app.checkValue(cmd.getValue(opt_ourTitle));
        if (cmd.findOption("--call"))
            app.checkValue(cmd.getValue(opt_peerTitle));
        cmd.beginOptionBlock();
        if (cmd.findOption("--enable-new-vr"))
        {
            dcmEnableUnknownVRGeneration.set(OFTrue);
            dcmEnableUnlimitedTextVRGeneration.set(OFTrue);
        }
        if (cmd.findOption("--disable-new-vr"))
        {
            dcmEnableUnknownVRGeneration.set(OFFalse);
            dcmEnableUnlimitedTextVRGeneration.set(OFFalse);
        }
        cmd.endOptionBlock();

        if (cmd.findOption("--timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            dcmConnectionTimeout.set((Sint32)opt_timeout);
        }

        if (cmd.findOption("--acse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_acse_timeout = OFstatic_cast(int, opt_timeout);
        }

        if (cmd.findOption("--dimse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_dimse_timeout = OFstatic_cast(int, opt_timeout);
            opt_blockMode = DIMSE_NONBLOCKING;
        }

        if (cmd.findOption("--max-pdu"))
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxReceivePDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
        if (cmd.findOption("--repeat"))
            app.checkValue(cmd.getValueAndCheckMin(opt_repeatCount, 1));
        if (cmd.findOption("--abort"))
            opt_abortAssociation = OFTrue;
        if (cmd.findOption("--cancel"))
            app.checkValue(cmd.getValueAndCheckMin(opt_cancelAfterNResponses, 0));
        if (cmd.findOption("--extract"))
            opt_extractResponsesToFile = OFTrue;

        /* finally parse filenames */
        int paramCount = cmd.getParamCount();
        const char* currentFilename = NULL;
        OFString errormsg;

        for (int i = 3; i <= paramCount; i++)
        {
            cmd.getParam(i, currentFilename);
            if (access(currentFilename, R_OK) < 0)
            {
                errormsg = "cannot access file: ";
                errormsg += currentFilename;
                app.printError(errormsg.c_str());
            }
            fileNameList.push_back(currentFilename);
        }

        if ((fileNameList.empty()) && (overrideKeys == NULL))
        {
            app.printError("either query file or override keys (or both) must be specified");
        }

        if (cmd.findOption("--mpps"))
            opt_mppsOpen = OFTrue;
#ifdef WITH_OPENSSL

#ifdef DEBUG
        /* prevent command line code from moaning that --add-cert-dir and --add-cert-file have not been checked */
        if (cmd.findOption("--add-cert-dir", 0, OFCommandLine::FOM_First)) /* nothing */
            ;
        if (cmd.findOption("--add-cert-file", 0, OFCommandLine::FOM_First)) /* nothing */
            ;
#endif

        cmd.beginOptionBlock();
        if (cmd.findOption("--disable-tls"))
            opt_secureConnection = OFFalse;
        if (cmd.findOption("--enable-tls"))
        {
            opt_secureConnection = OFTrue;
            opt_doAuthenticate = OFTrue;
            app.checkValue(cmd.getValue(opt_privateKeyFile));
            app.checkValue(cmd.getValue(opt_certificateFile));
        }
        if (cmd.findOption("--anonymous-tls"))
        {
            opt_secureConnection = OFTrue;
        }
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--std-passwd"))
        {
            if (!opt_doAuthenticate)
                app.printError("--std-passwd only with --enable-tls");
            opt_passwd = NULL;
        }
        if (cmd.findOption("--use-passwd"))
        {
            if (!opt_doAuthenticate)
                app.printError("--use-passwd only with --enable-tls");
            app.checkValue(cmd.getValue(opt_passwd));
        }
        if (cmd.findOption("--null-passwd"))
        {
            if (!opt_doAuthenticate)
                app.printError("--null-passwd only with --enable-tls");
            opt_passwd = "";
        }
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--pem-keys"))
            opt_keyFileFormat = SSL_FILETYPE_PEM;
        if (cmd.findOption("--der-keys"))
            opt_keyFileFormat = SSL_FILETYPE_ASN1;
        cmd.endOptionBlock();

        if (cmd.findOption("--dhparam"))
        {
            app.checkValue(cmd.getValue(opt_dhparam));
        }

        if (cmd.findOption("--seed"))
        {
            app.checkValue(cmd.getValue(opt_readSeedFile));
        }

        cmd.beginOptionBlock();
        if (cmd.findOption("--write-seed"))
        {
            if (opt_readSeedFile == NULL)
                app.printError("--write-seed only with --seed");
            opt_writeSeedFile = opt_readSeedFile;
        }
        if (cmd.findOption("--write-seed-file"))
        {
            if (opt_readSeedFile == NULL)
                app.printError("--write-seed-file only with --seed");
            app.checkValue(cmd.getValue(opt_writeSeedFile));
        }
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--require-peer-cert"))
            opt_certVerification = DCV_requireCertificate;
        if (cmd.findOption("--verify-peer-cert"))
            opt_certVerification = DCV_checkCertificate;
        if (cmd.findOption("--ignore-peer-cert"))
            opt_certVerification = DCV_ignoreCertificate;
        cmd.endOptionBlock();

        const char* current = NULL;
        const char* currentOpenSSL;
        if (cmd.findOption("--cipher", 0, OFCommandLine::FOM_First))
        {
            opt_ciphersuites.clear();
            do
            {
                app.checkValue(cmd.getValue(current));
                if (NULL == (currentOpenSSL = DcmTLSTransportLayer::findOpenSSLCipherSuiteName(current)))
                {
                    CERR << "ciphersuite '" << current << "' is unknown. Known ciphersuites are:" << endl;
                    unsigned long numSuites = DcmTLSTransportLayer::getNumberOfCipherSuites();
                    for (unsigned long cs = 0; cs < numSuites; cs++)
                    {
                        CERR << "    " << DcmTLSTransportLayer::getTLSCipherSuiteName(cs) << endl;
                    }
                    return 1;
                }
                else
                {
                    if (opt_ciphersuites.length() > 0)
                        opt_ciphersuites += ":";
                    opt_ciphersuites += currentOpenSSL;
                }
            } while (cmd.findOption("--cipher", 0, OFCommandLine::FOM_Next));
        }

#endif
    }

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        fprintf(stderr, "Warning: no data dictionary loaded, check environment variable: %s\n",
                DCM_DICT_ENVIRONMENT_VARIABLE);
    }

    /* initialize network, i.e. create an instance of T_ASC_Network*. */
    OFCondition cond = ASC_initializeNetwork(NET_REQUESTOR, 0, opt_acse_timeout, &net);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

#ifdef WITH_OPENSSL

    DcmTLSTransportLayer* tLayer = NULL;
    if (opt_secureConnection)
    {
        tLayer = new DcmTLSTransportLayer(DICOM_APPLICATION_REQUESTOR, opt_readSeedFile);
        if (tLayer == NULL)
        {
            app.printError("unable to create TLS transport layer");
        }

        if (cmd.findOption("--add-cert-file", 0, OFCommandLine::FOM_First))
        {
            const char* current = NULL;
            do
            {
                app.checkValue(cmd.getValue(current));
                if (TCS_ok != tLayer->addTrustedCertificateFile(current, opt_keyFileFormat))
                {
                    CERR << "warning unable to load certificate file '" << current << "', ignoring" << endl;
                }
            } while (cmd.findOption("--add-cert-file", 0, OFCommandLine::FOM_Next));
        }

        if (cmd.findOption("--add-cert-dir", 0, OFCommandLine::FOM_First))
        {
            const char* current = NULL;
            do
            {
                app.checkValue(cmd.getValue(current));
                if (TCS_ok != tLayer->addTrustedCertificateDir(current, opt_keyFileFormat))
                {
                    CERR << "warning unable to load certificates from directory '" << current << "', ignoring" << endl;
                }
            } while (cmd.findOption("--add-cert-dir", 0, OFCommandLine::FOM_Next));
        }

        if (opt_dhparam && !(tLayer->setTempDHParameters(opt_dhparam)))
        {
            CERR << "warning unable to load temporary DH parameter file '" << opt_dhparam << "', ignoring" << endl;
        }

        if (opt_doAuthenticate)
        {
            if (opt_passwd)
                tLayer->setPrivateKeyPasswd(opt_passwd);

            if (TCS_ok != tLayer->setPrivateKeyFile(opt_privateKeyFile, opt_keyFileFormat))
            {
                CERR << "unable to load private TLS key from '" << opt_privateKeyFile << "'" << endl;
                return 1;
            }
            if (TCS_ok != tLayer->setCertificateFile(opt_certificateFile, opt_keyFileFormat))
            {
                CERR << "unable to load certificate from '" << opt_certificateFile << "'" << endl;
                return 1;
            }
            if (!tLayer->checkPrivateKeyMatchesCertificate())
            {
                CERR << "private key '" << opt_privateKeyFile << "' and certificate '" << opt_certificateFile
                     << "' do not match" << endl;
                return 1;
            }
        }

        if (TCS_ok != tLayer->setCipherSuites(opt_ciphersuites.c_str()))
        {
            CERR << "unable to set selected cipher suites" << endl;
            return 1;
        }

        tLayer->setCertificateVerification(opt_certVerification);

        cond = ASC_setTransportLayer(net, tLayer, 0);
        if (cond.bad())
        {
            DimseCondition::dump(cond);
            return 1;
        }
    }

#endif

    /* initialize asscociation parameters, i.e. create an instance of T_ASC_Parameters*. */
    cond = ASC_createAssociationParameters(&params, opt_maxReceivePDULength);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

    /* sets this application's title and the called application's title in the params */
    /* structure. The default values to be set here are "STORESCU" and "ANY-SCP". */
    ASC_setAPTitles(params, opt_ourTitle, opt_peerTitle, NULL);

    /* Set the transport layer type (type of network connection) in the params */
    /* structure. The default is an insecure connection; where OpenSSL is  */
    /* available the user is able to request an encrypted,secure connection. */
    cond = ASC_setTransportLayerType(params, opt_secureConnection);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
        return 1;
    }

    /* Figure out the presentation addresses and copy the */
    /* corresponding values into the association parameters.*/
    gethostname(localHost, sizeof(localHost) - 1);
    sprintf(peerHost, "%s:%d", opt_peer, (int)opt_port);
    ASC_setPresentationAddresses(params, localHost, peerHost);

    /* Set the presentation contexts which will be negotiated */
    /* when the network connection will be established */
    cond = addPresentationContext(params);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

    /* dump presentation contexts if required */
    if (opt_debug)
    {
        printf("Request Parameters:\n");
        ASC_dumpParameters(params, COUT);
    }

    /* create association, i.e. try to establish a network connection to another */
    /* DICOM application. This call creates an instance of T_ASC_Association*. */
    if (opt_verbose)
        printf("Requesting Association\n");
    cond = ASC_requestAssociation(net, params, &assoc);
    if (cond.bad())
    {
        if (cond == DUL_ASSOCIATIONREJECTED)
        {
            T_ASC_RejectParameters rej;

            ASC_getRejectParameters(params, &rej);
            errmsg("Association Rejected:");
            ASC_printRejectParameters(stderr, &rej);
#if BUILD_LIB
            return 1;
#else
            exit(1);
#endif
        }
        else
        {
            errmsg("Association Request Failed:");
            DimseCondition::dump(cond);
#if BUILD_LIB
            return 1;
#else
            exit(1);
#endif
        }
    }

    /* dump the presentation contexts which have been accepted/refused */
    if (opt_debug)
    {
        printf("Association Parameters Negotiated:\n");
        ASC_dumpParameters(params, COUT);
    }

    /* count the presentation contexts which have been accepted by the SCP */
    /* If there are none, finish the execution */
    if (ASC_countAcceptedPresentationContexts(params) == 0)
    {
        errmsg("No Acceptable Presentation Contexts");
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

    /* dump general information concerning the establishment of the network connection if required */
    if (opt_verbose)
    {
        printf("Association Accepted (Max Send PDV: %lu)\n", assoc->sendPDVLength);
    }

    /* do the real work, i.e. for all files which were specified in the command line, send a */
    /* C-FIND-RQ to the other DICOM application and receive corresponding response messages. */
    cond = EC_Normal;
    if (fileNameList.empty())
    {
        /* no files provided on command line */
        cond = cfind(assoc, NULL);
    }
    else
    {
        OFListIterator(OFString) iter = fileNameList.begin();
        OFListIterator(OFString) enditer = fileNameList.end();
        while ((iter != enditer) &&
               (cond == EC_Normal)) // compare with EC_Normal since DUL_PEERREQUESTEDRELEASE is also good()
        {
            cond = cfind(assoc, (*iter).c_str());
            ++iter;
        }
    }

    /* tear down association, i.e. terminate network connection to SCP */
    if (cond == EC_Normal)
    {
        if (opt_abortAssociation)
        {
            if (opt_verbose)
                printf("Aborting Association\n");
            cond = ASC_abortAssociation(assoc);
            if (cond.bad())
            {
                errmsg("Association Abort Failed:");
                DimseCondition::dump(cond);
#if BUILD_LIB
                return 1;
#else
                exit(1);
#endif
            }
        }
        else
        {
            /* release association */
            if (opt_verbose)
                printf("Releasing Association\n");
            cond = ASC_releaseAssociation(assoc);
            if (cond.bad())
            {
                errmsg("Association Release Failed:");
                DimseCondition::dump(cond);
#if BUILD_LIB
                return 1;
#else
                exit(1);
#endif
            }
        }
    }
    else if (cond == DUL_PEERREQUESTEDRELEASE)
    {
        errmsg("Protocol Error: peer requested release (Aborting)");
        if (opt_verbose)
            printf("Aborting Association\n");
        cond = ASC_abortAssociation(assoc);
        if (cond.bad())
        {
            errmsg("Association Abort Failed:");
            DimseCondition::dump(cond);
#if BUILD_LIB
            return 1;
#else
            exit(1);
#endif
        }
    }
    else if (cond == DUL_PEERABORTEDASSOCIATION)
    {
        if (opt_verbose)
            printf("Peer Aborted Association\n");
    }
    else
    {
        errmsg("SCU Failed:");
        DimseCondition::dump(cond);
        if (opt_verbose)
            printf("Aborting Association\n");
        cond = ASC_abortAssociation(assoc);
        if (cond.bad())
        {
            errmsg("Association Abort Failed:");
            DimseCondition::dump(cond);
#if BUILD_LIB
            return 1;
#else
            exit(1);
#endif
        }
    }

    /* destroy the association, i.e. free memory of T_ASC_Association* structure. This */
    /* call is the counterpart of ASC_requestAssociation(...) which was called above. */
    cond = ASC_destroyAssociation(&assoc);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

    /* drop the network, i.e. free memory of T_ASC_Network* structure. This call */
    /* is the counterpart of ASC_initializeNetwork(...) which was called above. */
    cond = ASC_dropNetwork(&net);
    if (cond.bad())
    {
        DimseCondition::dump(cond);
#if BUILD_LIB
        return 1;
#else
        exit(1);
#endif
    }

#ifdef HAVE_WINSOCK_H
    WSACleanup();
#endif

#ifdef WITH_OPENSSL
    if (tLayer && opt_writeSeedFile)
    {
        if (tLayer->canWriteRandomSeed())
        {
            if (!tLayer->writeRandomSeed(opt_writeSeedFile))
            {
                CERR << "Error while writing random seed file '" << opt_writeSeedFile << "', ignoring." << endl;
            }
        }
        else
        {
            CERR << "Warning: cannot write random seed, ignoring." << endl;
        }
    }
    delete tLayer;
#endif

    delete overrideKeys;
    overrideKeys = NULL;
    return 0;
}

static OFCondition addPresentationContext(T_ASC_Parameters* params)
{
    /*
    ** We prefer to use Explicitly encoded transfer syntaxes.
    ** If we are running on a Little Endian machine we prefer
    ** LittleEndianExplicitTransferSyntax to BigEndianTransferSyntax.
    ** Some SCP implementations will just select the first transfer
    ** syntax they support (this is not part of the standard) so
    ** organise the proposed transfer syntaxes to take advantage
    ** of such behaviour.
    **
    ** The presentation contexts proposed here are only used for
    ** C-FIND and C-MOVE, so there is no need to support compressed
    ** transmission.
    */

    const char* transferSyntaxes[] = {NULL, NULL, NULL};
    int numTransferSyntaxes = 0;

    switch (opt_networkTransferSyntax)
    {
    case EXS_LittleEndianImplicit:
        /* we only support Little Endian Implicit */
        transferSyntaxes[0] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 1;
        break;
    case EXS_LittleEndianExplicit:
        /* we prefer Little Endian Explicit */
        transferSyntaxes[0] = UID_LittleEndianExplicitTransferSyntax;
        transferSyntaxes[1] = UID_BigEndianExplicitTransferSyntax;
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    case EXS_BigEndianExplicit:
        /* we prefer Big Endian Explicit */
        transferSyntaxes[0] = UID_BigEndianExplicitTransferSyntax;
        transferSyntaxes[1] = UID_LittleEndianExplicitTransferSyntax;
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    default:
        /* We prefer explicit transfer syntaxes.
         * If we are running on a Little Endian machine we prefer
         * LittleEndianExplicitTransferSyntax to BigEndianTransferSyntax.
         */
        if (gLocalByteOrder == EBO_LittleEndian) /* defined in dcxfer.h */
        {
            transferSyntaxes[0] = UID_LittleEndianExplicitTransferSyntax;
            transferSyntaxes[1] = UID_BigEndianExplicitTransferSyntax;
        }
        else
        {
            transferSyntaxes[0] = UID_BigEndianExplicitTransferSyntax;
            transferSyntaxes[1] = UID_LittleEndianExplicitTransferSyntax;
        }
        transferSyntaxes[2] = UID_LittleEndianImplicitTransferSyntax;
        numTransferSyntaxes = 3;
        break;
    }

    return ASC_addPresentationContext(params, 1, opt_abstractSyntax, transferSyntaxes, numTransferSyntaxes);
}

static void substituteOverrideKeys(DcmDataset* dset)
{
    if (overrideKeys == NULL)
    {
        return; /* nothing to do */
    }

    /* copy the override keys */
    DcmDataset keys(*overrideKeys);

    /* put the override keys into dset replacing existing tags */
    unsigned long elemCount = keys.card();
    for (unsigned long i = 0; i < elemCount; i++)
    {
        DcmElement* elem = keys.remove((unsigned long)0);

        dset->insert(elem, OFTrue);
    }
}

static OFBool writeToFile(const char* ofname, DcmDataset* dataset)
{
    /* write out as a file format */

    DcmFileFormat fileformat(dataset); // copies dataset
    OFCondition ec = fileformat.error();
    if (ec.bad())
    {
        errmsg("error writing file: %s: %s", ofname, ec.text());
        return OFFalse;
    }

    ec = fileformat.saveFile(ofname, dataset->getOriginalXfer());
    if (ec.bad())
    {
        errmsg("error writing file: %s: %s", ofname, ec.text());
        return OFFalse;
    }

    return OFTrue;
}

static void progressCallback(void* callbackData, T_DIMSE_C_FindRQ* request, int responseCount, T_DIMSE_C_FindRSP* rsp,
                             DcmDataset* responseIdentifiers)
/*
 * This function.is used to indicate progress when findscu receives search results over the
 * network. This function will simply cause some information to be dumped to stdout.
 *
 * Parameters:
 *   callbackData        - [in] data for this callback function
 *   request             - [in] The original find request message.
 *   responseCount       - [in] Specifies how many C-FIND-RSP were received including the current one.
 *   rsp                 - [in] the C-FIND-RSP message which was received shortly before the call to
 *                              this function.
 *   responseIdentifiers - [in] Contains the record which was received. This record matches the search
 *                              mask of the C-FIND-RQ which was sent.
 */
{
    if (opt_verbose)
    {
        /* dump response number */
        printf("RESPONSE: %d (%s)\n", responseCount, DU_cfindStatusString(rsp->DimseStatus));

        /* dump data set which was received */
        responseIdentifiers->print(COUT);

        /* dump delimiter */
        printf("--------\n");
    }

    /* in case opt_extractResponsesToFile is set the responses shall be extracted to a certain file */
    if (opt_extractResponsesToFile && fprsp)
    {
        //        char rspIdsFileName[1024];
        //        sprintf(rspIdsFileName, "rsp%04d.dcm", responseCount);
        //        writeToFile(rspIdsFileName, responseIdentifiers);
        if (rsp_count++ < MAX_RSP_COUNT)
        {
            opt_worklist ? writeRspToFile(fprsp, responseIdentifiers) : writeQRRspToFile(fprsp, responseIdentifiers);
        }

        if (!opt_worklist || opt_mppsOpen)
        {
            char rspIdsFileName[1024];
            const char* context = NULL;
            char studyInstanceIDStr[64] = {0};

            responseIdentifiers->findAndGetString(DCM_StudyInstanceUID, context);
            trimmed(context, studyInstanceIDStr);

            sprintf(rspIdsFileName, "./dicom_response_temp/%s.dcm", studyInstanceIDStr);
            writeToFile(rspIdsFileName, responseIdentifiers);
        }
    }

    MyCallbackInfo* myCallbackData = OFstatic_cast(MyCallbackInfo*, callbackData);

    /* should we send a cancel back ?? */
    if (opt_cancelAfterNResponses == responseCount)
    {
        if (opt_verbose)
        {
            printf("Sending Cancel RQ, MsgId: %d, PresId: %d\n", request->MessageID, myCallbackData->presId);
        }
        OFCondition cond = DIMSE_sendCancelRequest(myCallbackData->assoc, myCallbackData->presId, request->MessageID);
        if (cond.bad())
        {
            errmsg("Cancel RQ Failed:");
            DimseCondition::dump(cond);
        }
    }
}

static OFCondition findSCU(T_ASC_Association* assoc, const char* fname)
/*
 * This function will read all the information from the given file
 * (this information specifies a search mask), figure out a corresponding
 * presentation context which will be used to transmit a C-FIND-RQ message
 * over the network to the SCP, and it will finally initiate the transmission
 * of data to the SCP.
 *
 * Parameters:
 *   assoc - [in] The association (network connection to another DICOM application).
 *   fname - [in] Name of the file which shall be processed.
 */
{
    DIC_US msgId = assoc->nextMsgID++;
    T_ASC_PresentationContextID presId;
    T_DIMSE_C_FindRQ req;
    T_DIMSE_C_FindRSP rsp;
    DcmDataset* statusDetail = NULL;
    MyCallbackInfo callbackData;
    DcmFileFormat dcmff;

    /* if there is a valid filename */
    if (fname != NULL)
    {

        /* read information from file (this information specifies a search mask). After the */
        /* call to DcmFileFormat::read(...) the information which is encapsulated in the file */
        /* will be available through the DcmFileFormat object. In detail, it will be available */
        /* through calls to DcmFileFormat::getMetaInfo() (for meta header information) and */
        /* DcmFileFormat::getDataset() (for data set information). */
        OFCondition cond = dcmff.loadFile(fname);

        /* figure out if an error occured while the file was read*/
        if (cond.bad())
        {
            errmsg("Bad DICOM file: %s: %s", fname, cond.text());
            return cond;
        }
    }

    /* fill Scheduled Procedure Step Sequence */
    if (opt_worklist && (opt_scheduledModality.length() > 0 || opt_scheduledStationAETitle.length() > 0 ||
                         opt_scheduledProcedureStepStartDate.length() > 0))
    {
        fillScheduledProcedureStepSequence(dcmff.getDataset(), opt_scheduledModality.c_str(),
                                           opt_scheduledStationAETitle.c_str(),
                                           opt_scheduledProcedureStepStartDate.c_str());
    }

    /* replace specific keys by those in overrideKeys */
    substituteOverrideKeys(dcmff.getDataset());

    /* figure out which of the accepted presentation contexts should be used */
    presId = ASC_findAcceptedPresentationContextID(assoc, opt_abstractSyntax);
    if (presId == 0)
    {
        errmsg("No presentation context");
        return DIMSE_NOVALIDPRESENTATIONCONTEXTID;
    }

    /* prepare the transmission of data */
    bzero((char*)&req, sizeof(req));
    req.MessageID = msgId;
    strcpy(req.AffectedSOPClassUID, opt_abstractSyntax);
    req.DataSetType = DIMSE_DATASET_PRESENT;
    req.Priority = DIMSE_PRIORITY_LOW;

    /* prepare the callback data */
    callbackData.assoc = assoc;
    callbackData.presId = presId;

    /* if required, dump some more general information */
    if (opt_verbose)
    {
        printf("Find SCU RQ: MsgID %d\n", msgId);
        printf("REQUEST:\n");
        dcmff.getDataset()->print(COUT);
        printf("--------\n");
    }

    if ((fprsp = fopen(FINDSCURSP_FILE_NAME, "wb")) == NULL)
    {
        printf("can not open file %s!\n", FINDSCURSP_FILE_NAME);
    }
    rsp_count = 0;
    /* finally conduct transmission of data */
    OFCondition cond = DIMSE_findUser(assoc, presId, &req, dcmff.getDataset(), progressCallback, &callbackData,
                                      opt_blockMode, opt_dimse_timeout, &rsp, &statusDetail);

    if (fprsp)
        fclose(fprsp);

    /* dump some more general information */
    if (cond == EC_Normal)
    {
        if (opt_verbose)
        {
            DIMSE_printCFindRSP(stdout, &rsp);
        }
        else
        {
            if (rsp.DimseStatus != STATUS_Success)
            {
                printf("Response: %s\n", DU_cfindStatusString(rsp.DimseStatus));
            }
        }
    }
    else
    {
        if (fname)
        {
            errmsg("Find Failed, file: %s:", fname);
        }
        else
        {
            errmsg("Find Failed, query keys:");
            dcmff.getDataset()->print(COUT);
        }
        DimseCondition::dump(cond);
    }

    /* dump status detail information if there is some */
    if (statusDetail != NULL)
    {
        printf("  Status Detail:\n");
        statusDetail->print(COUT);
        delete statusDetail;
    }

    /* return */
    return cond;
}

static OFCondition cfind(T_ASC_Association* assoc, const char* fname)
/*
 * This function will process the given file as often as is specified by opt_repeatCount.
 * "Process" in this case means "read file, send C-FIND-RQ, receive C-FIND-RSP messages".
 *
 * Parameters:
 *   assoc - [in] The association (network connection to another DICOM application).
 *   fname - [in] Name of the file which shall be processed (contains search mask information).
 */
{
    OFCondition cond = EC_Normal;

    /* opt_repeatCount specifies how many times a certain file shall be processed */
    int n = (int)opt_repeatCount;

    /* as long as no error occured and the counter does not equal 0 */
    while (cond == EC_Normal && n--)
    {
        /* process file (read file, send C-FIND-RQ, receive C-FIND-RSP messages) */
        cond = findSCU(assoc, fname);
    }

    /* return result value */
    return cond;
}

static OFCondition fillScheduledProcedureStepSequence(DcmDataset* dataset, const char* modality, const char* aet,
                                                      const char* startDate)
{
    DcmSequenceOfItems* dseq = new DcmSequenceOfItems(DCM_ScheduledProcedureStepSequence);
    if (dseq)
    {
        DcmItem* ditem = new DcmItem;
        if (ditem)
        {
            dseq->insert(ditem);
            ditem->putAndInsertString(DCM_Modality, modality != NULL ? modality : "");
            ditem->putAndInsertString(DCM_ScheduledStationAETitle, aet != NULL ? aet : "");
            ditem->putAndInsertString(DCM_ScheduledProcedureStepStartDate, startDate != NULL ? startDate : "");
            ditem->putAndInsertString(DCM_ScheduledProcedureStepStartTime, "000000-235959");
            OFCondition status = dataset->insert(dseq, OFTrue);

            if (status != EC_Normal)
            {
                printf("fill fillScheduledProcedureStepSequence failed:%s\n", status.text());
            }

            return status;
        }
        else
        {
            return EC_MemoryExhausted;
        }
    }
    else
    {
        return EC_MemoryExhausted;
    }
}

static OFCondition createQueryFile(const char* modality, const char* aet, const char* startDate)
{
    DcmFileFormat queryfile;
    DcmDataset* dataset = queryfile.getDataset();
    OFCondition status = fillScheduledProcedureStepSequence(dataset, modality, aet, startDate);
    if (status == EC_Normal)
    {
        OFCondition status =
            queryfile.saveFile("querytest.dcm", EXS_LittleEndianExplicit, EET_ExplicitLength, EGL_recalcGL);
        if (status != EC_Normal)
        {
            printf("save queryfile dcm failed:%s\n", status.text());
        }
    }

    return status;
}

/*
** CVS Log
** $Log: findscu.cc,v $
** Revision 1.47  2005/12/08 15:44:19  meichel
** Changed include path schema for all DCMTK header files
**
** Revision 1.46  2005/11/23 16:10:23  meichel
** Added support for AES ciphersuites in TLS module. All TLS-enabled
**   tools now support the "AES TLS Secure Transport Connection Profile".
**
** Revision 1.45  2005/11/17 13:45:16  meichel
** Added command line options for DIMSE and ACSE timeouts
**
** Revision 1.44  2005/11/16 14:58:07  meichel
** Set association timeout in ASC_initializeNetwork to 30 seconds. This improves
**   the responsiveness of the tools if the peer blocks during assoc negotiation.
**
** Revision 1.43  2005/11/14 09:06:50  onken
** Added data dictionary name support for "--key" option
**
** Revision 1.42  2005/11/03 17:39:41  meichel
** Added transfer syntax selection options to findscu.
**
** Revision 1.41  2004/02/27 12:51:51  meichel
** Added --cancel option to findscu, similar to the option available in movescu.
**
** Revision 1.40  2004/02/26 17:17:43  meichel
** Fixed minor memory leak in findscu
**
** Revision 1.39  2003/03/14 15:57:34  meichel
** Enabled TLS network connections in findscu
**
** Revision 1.38  2002/11/29 09:15:49  meichel
** Introduced new command line option --timeout for controlling the
**   connection request timeout.
**
** Revision 1.37  2002/11/27 13:04:30  meichel
** Adapted module dcmnet to use of new header file ofstdinc.h
**
** Revision 1.36  2002/11/26 08:43:20  meichel
** Replaced all includes for "zlib.h" with <zlib.h>
**   to avoid inclusion of zlib.h in the makefile dependencies.
**
** Revision 1.35  2002/09/23 17:53:46  joergr
** Added new command line option "--version" which prints the name and version
** number of external libraries used (incl. preparation for future support of
** 'config.guess' host identifiers).
**
** Revision 1.34  2002/08/20 12:21:21  meichel
** Adapted code to new loadFile and saveFile methods, thus removing direct
**   use of the DICOM stream classes.
**
** Revision 1.33  2001/11/09 15:56:23  joergr
** Renamed some of the getValue/getParam methods to avoid ambiguities reported
** by certain compilers.
**
** Revision 1.32  2001/11/01 14:38:58  wilkens
** Added lots of comments.
**
** Revision 1.31  2001/10/12 10:18:20  meichel
** Replaced the CONDITION types, constants and functions in the dcmnet module
**   by an OFCondition based implementation which eliminates the global condition
**   stack.  This is a major change, caveat emptor!
**
** Revision 1.30  2001/09/26 12:28:54  meichel
** Implemented changes in dcmnet required by the adaptation of dcmdata
**   to class OFCondition.  Removed some unused code.
**
** Revision 1.29  2001/06/01 15:50:01  meichel
** Updated copyright header
**
** Revision 1.28  2000/06/07 13:56:16  meichel
** Output stream now passed as mandatory parameter to ASC_dumpParameters.
**
** Revision 1.27  2000/04/14 16:29:26  meichel
** Removed default value from output stream passed to print() method.
**   Required for use in multi-thread environments.
**
** Revision 1.26  2000/03/08 16:43:15  meichel
** Updated copyright header.
**
** Revision 1.25  2000/03/03 14:11:11  meichel
** iImplemented library support for redirecting error messages into memory
**   instead of printing them to stdout/stderr for GUI applications.
**
** Revision 1.24  2000/02/29 11:49:49  meichel
** Removed support for VS value representation. This was proposed in CP 101
**   but never became part of the standard.
**
** Revision 1.23  2000/02/23 15:12:19  meichel
** Corrected macro for Borland C++ Builder 4 workaround.
**
** Revision 1.22  2000/02/01 10:24:02  meichel
** Avoiding to include <stdlib.h> as extern "C" on Borland C++ Builder 4,
**   workaround for bug in compiler header files.
**
** Revision 1.21  1999/04/30 16:40:21  meichel
** Minor code purifications to keep Sun CC 2.0.1 quiet
**
** Revision 1.20  1999/04/29 10:02:45  meichel
** Adapted findscu to new command line option scheme
**
** Revision 1.19  1999/04/19 08:43:53  meichel
** Added new option to findscu which allows to extract all
**   C-FIND-RSP messages to file as received over network.
**
** Revision 1.18  1999/03/29 11:19:53  meichel
** Cleaned up dcmnet code for char* to const char* assignments.
**
** Revision 1.17  1998/08/10 08:53:34  meichel
** renamed member variable in DIMSE structures from "Status" to
**   "DimseStatus". This is required if dcmnet is used together with
**   <X11/Xlib.h> where Status is #define'd as int.
**
** Revision 1.16  1998/01/14 14:35:53  hewett
** Modified existing -u command line option to also disable generation
** of UT and VS (previously just disabled generation of UN).
**
** Revision 1.15  1997/08/05 07:46:20  andreas
** - Change needed version number of WINSOCK to 1.1
**   to support WINDOWS 95
**
** Revision 1.14  1997/07/21 08:37:02  andreas
** - Replace all boolean types (BOOLEAN, CTNBOOLEAN, DICOM_BOOL, BOOL)
**   with one unique boolean type OFBool.
**
** Revision 1.13  1997/05/30 07:33:21  meichel
** Added space characters around comments and simplified
** some inlining code (needed for SunCC 2.0.1).
**
** Revision 1.12  1997/05/29 15:52:56  meichel
** Added constant for dcmtk release date in dcuid.h.
** All dcmtk applications now contain a version string
** which is displayed with the command line options ("usage" message)
** and which can be queried in the binary with the "ident" command.
**
** Revision 1.11  1997/05/22 13:29:58  hewett
** Modified the test for presence of a data dictionary to use the
** method DcmDataDictionary::isDictionaryLoaded().
**
** Revision 1.10  1997/04/18 08:40:11  andreas
** - The put/get-methods for all VRs did not conform to the C++-Standard
**   draft. Some Compilers (e.g. SUN-C++ Compiler, Metroworks
**   CodeWarrier, etc.) create many warnings concerning the hiding of
**   overloaded get methods in all derived classes of DcmElement.
**   So the interface of all value representation classes in the
**   library are changed rapidly, e.g.
**   OFCondition get(Uint16 & value, const unsigned long pos);
**   becomes
**   OFCondition getUint16(Uint16 & value, const unsigned long pos);
**   All (retired) "returntype get(...)" methods are deleted.
**   For more information see dcmdata/include/dcelem.h
**
** Revision 1.9  1997/03/27 16:11:26  hewett
** Added command line switches allowing generation of UN to
** be disabled (it is enabled by default).
**
** Revision 1.8  1996/12/16 15:13:59  hewett
** Added bugfix for WINSOCK support.  The required WINSOCK version
** number was being incorrectly set to version 0.1.  The fixed
** WINSOCK initialisation now uses the MAKEWORD macro to correctly
** set the required version number. This bugfix was contributed
** by Dr. Yongjian Bao of Innomed GmbH, Germany.
**
** Revision 1.7  1996/09/27 14:05:03  hewett
** Added calls to initialise WINSOCK library for Win32 environment.  Only
** compiled in if HAVE_WINSOCK_H
**
** Revision 1.6  1996/09/27 08:24:28  hewett
** System header files now enclosed with BEGIN_EXTERN_C/END_EXTERN_C
**
** Revision 1.5  1996/09/24 16:21:49  hewett
** Added preliminary support for the Macintosh environment (GUSI library).
**
** Revision 1.4  1996/08/29 16:24:07  hewett
** Added copyright header.
**
** Revision 1.3  1996/06/20 07:23:40  hewett
** Corrected typo in printf.
**
** Revision 1.2  1996/04/25 16:19:17  hewett
** Added char* parameter casts to bzero() calls.
**
** Revision 1.1  1996/04/22 09:58:16  hewett
** Initial release.
**
**
*/
