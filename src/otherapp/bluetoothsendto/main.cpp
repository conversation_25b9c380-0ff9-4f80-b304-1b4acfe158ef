#include <QElapsedTimer>
#include <QString>
#include <QApplication>
#include <QEventLoop>
#include <QObject>
#include <QDebug>
#include <usbttransferlocal.h>

int main(int argc, char* argv[])
{
    if (argc != 3)
    {
        return -1;
    }
    QApplication app(argc, argv);

    QString remoteDeviceAddr = argv[1];
    QString filepath = argv[2];

    USBtTransferLocal trans(remoteDeviceAddr, filepath, &app);
    QObject::connect(&trans, &USBtTransferLocal::exit, QCoreApplication::instance(), &QCoreApplication::exit,
                     Qt::QueuedConnection);
    trans.run();
    return app.exec();
}
