#ifndef AVITYPE_H
#define AVITYPE_H
 
#include <string.h>
#include <stdio.h>
#include <sys/types.h>
 
typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;

/* for use in AVI_avih.flags */
#define AVIF_HASINDEX 0x00000010;	/* index at end of file */
#define AVIF_MUSTUSEINDEX 0x00000020;
#define AVIF_ISINTERLEAVED 0x00000100;
#define AVIF_TRUSTCKTYPE 0x00000800;
#define AVIF_WASCAPTUREFILE 0x00010000;
#define AVIF_COPYRIGHTED 0x00020000;

// RIFF文件头
typedef struct{
    char pRiff[4];               // 4个字节 ”RIFF”
    u32 uiSize;                  // RIFF文件大小
    char pRiffType[4];           // RIFF文件类型  "avi"
}stRiffHead;

//hdrl头部
typedef struct {
    char pList[4];               // 4个字节 "LIST"
    u32 uiSize;                  // list大小
    char pType[4];               // 4个字节list类型
}stHeadList;

// AVI主头部
typedef struct
{
    char pAvih[4];             // 必须为 "avih"
    u32 uiSize;                // 本数据结构的大小，不包括最初的8个字节（fcc和cb两个域）
    u32 uiMicroSecPerFrame;    // 视频帧间隔时间（以microsec为单位）
    u32 uiMaxBytesPerSec;      // 这个AVI文件的最大数据率 (total size/ uiTotalFrames )/ uiMicroSecPerFrame)
    u32 uiPaddingGranularity;  // 数据填充的粒度 (pad = 0)
    u32 uiFlags;               // AVI文件的全局标记，比如是否含有索引块等  e.g. AVIF_HASINDEX
    u32 uiTotalFrames;         // 总帧数
    u32 uiInitialFrames;       // 为交互格式指定初始帧数（非交互格式应该指定为0）
    u32 uiStreams;             // 本文件包含的流的个数   (0)
    u32 uiSuggestedBufferSize; // 建议读取本文件的缓存大小（应能容纳最大的块）
    u32 uiWidth;               // 视频图像的宽（以像素为单位）
    u32 uiHeight;              // 视频图像的高（以像素为单位）
    u32 pReserved[4];          // 保留
} stAVIMainHeader;

//idx1块
typedef struct {
    char pIdx1[4];               // 4个字节 "idx1"
    u32  uiSize;                 // idx1大小
}stIdx1Head;

typedef struct {
    char pDb[4];
    u32  uiSize;
} stDbHead;

typedef struct {
    char pDmlh[4];
    u32  uiSize;
    u32  uiFrames;
} stHeadDmlh;

typedef struct {
    char strh[4];               // chunk type = "strh"
    u32 size;                   // chunk size
    char vids[4];               // stream type = "vids"d
    char codec[4];              // codec name (for us, = "MJPG")
    u32 flags;                  // contains AVIT_F* flags
    u16 priority;               // = 0
    u16 language;               // = 0
    u32 initialframes;          // = 0
    u32 scale;                  // = usec per frame
    u32 rate;                   // 1e6
    u32 start;                  // = 0
    u32 length;                 // number of frames
    u32 suggested_bufsize;      // = 0
    u32 quality;                // = 0 ?
    u32 samplesize;             // = 0 ?
} stream_head;

// AVI流头部
typedef struct
{
    char pStrh[4];            // 必须为 strh
    u32  uiSize;              // 本数据结构的大小,不包括最初的8个字节(fcc和cb两个域)
    char pVids[4];            // 流的类型: auds(音频流) vids(视频流) mids(MIDI流) txts(文字流)
    char pCodec[4];           // 指定流的处理者，对于音视频来说就是解码器
    u32 uiFlags;              // 标记：是否允许这个流输出？调色板是否变化？
    u16 usPriority;           // 流的优先级（当有多个相同类型的流时优先级最高的为默认流）
    u16 usLanguage;           // 语言
    u32 uiInitialFrames;      // 为交互格式指定初始帧数
    u32 uiScale;              // 每帧视频大小或者音频采样大小
    u32 uiRate;               // dwScale/dwRate，每秒采样率
    u32 uiStart;              // 流的开始时间
    u32 uiLength;             // 流的长度（单位与dwScale和dwRate的定义有关）
    u32 uiSuggestedBufferSize;// 读取这个流数据建议使用的缓存大小
    u32 uiQuality;            // 流数据的质量指标（0 ~ 10,000）
    u32 uiSampleSize;         // Sample的大小
} stAVIStreamHeader;


typedef struct  {
    char pStrf[4];
    u32 uiSize;
    u32 uiSize2;
    u32 uiWidth;
    u32 uiHeight;
    u16 usPlanes;
    u16 usBitcount;
    char pCodec[4];
    u32 unpackedsize;
    u32 uiR1;
    u32 uiR2;
    u32 uiClrUsed;
    u32 uiClrImportant;
} stFrameHead;




#endif

