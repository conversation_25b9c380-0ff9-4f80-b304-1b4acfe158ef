#ifndef _TOOLS_JPEGCODEC_H_
#define _TOOLS_JPEGCODEC_H_

#include <vector>

using namespace std;

class JPEGCodec
{
public:
    class IJPEGDestination;
    class IJPEGSource;

    typedef std::vector<unsigned char> Buffer;

    explicit JPEGCodec(int quality = 100);
    ~JPEGCodec();
    void setQuality(int quality);
    bool encode(unsigned char const* data, size_t size, unsigned int width, unsigned int height, unsigned int stride,
                Buffer& buffer) const;

    bool jpegToRle(Buffer buffer, Buffer& outBuffer);

private:
    JPEGCodec(JPEGCodec const&);
    JPEGCodec& operator=(JPEGCodec const&);
    bool encode(unsigned char const* data, size_t size, unsigned int width, unsigned int height, unsigned int stride,
                IJPEGDestination& destination) const;

    int iBytesPerPixelRGB24;
    int quality_;
};

#endif // _TOOLS_JPEGCODEC_H_
