#ifndef BMPCODE_H
#define BMPCODE_H

class bmpcode
{
public:
    bmpcode();
    ~bmpcode();
    static bool createBMP(const char* filename, const unsigned char *pRGB, int iLen, int iWidth, int iHeight);
    static unsigned char* createBMP(const unsigned char *pRGBT, int iRGBSize, int iWidth, int iHeight, unsigned int& iLen);

private:
    unsigned char *m_BMP;
    int m_Width;
    int m_Height;
};

#endif // BMPCODE_H
