#include "bmpcode.h"
#include "basememory.h"
#include "basetype.h"
#include "basefile.h"

unsigned char* rgbtorgba(const unsigned char *pRGB, int iWidth, int iHeight)
{
    if(NULL == pRGB)
    {
        return NULL;
    }
    int iSize = iWidth * iHeight * 3 + 54;
    unsigned char* pBGRA = (unsigned char*)baseMalloc(iSize);

    if(NULL == pBGRA)
    {
        return NULL;
    }
    baseMemset(pBGRA, 0, iSize);

    baseMemcpy(pBGRA, pRGB, 54, iSize -54);

//    for(int iH = 0 ; iH < iHeight; ++iH)
//    {
//        for(int iW = 0 ; iW < iWidth; ++iW)
//        {
//            int iBit1 = ((iHeight - iH - 1)*iWidth + iW) *3;
//            int iBit = ((iH)*iWidth + iW) *3 + 54;
//            pBGRA[iBit] = pRGB[iBit1 + 2];
//            pBGRA[iBit + 1] = pRGB[iBit1 + 1];
//            pBGRA[iBit + 2] = pRGB[iBit1];
//        }
//    }

    return pBGRA;
}

bmpcode::bmpcode()
    :m_BMP(NULL)
    ,m_Width(0)
    ,m_Height(0)
{

}

bool bmpcode::createBMP(const char* filename, const unsigned char *pRGBT, int iRGBSize, int iWidth, int iHeight)
{
    if(NULL == pRGBT)
    {
        return false;
    }

    char bmpfileheader[14];
    char bmpinfoheader[40];
    int headersize, bfSize;
    int bits_per_pixel, cmap_entries;

    bits_per_pixel = 24;
    cmap_entries = 0;
    /* File size */
    headersize = 14 + 40 + cmap_entries * 4; /* Header and colormap */
    bfSize = headersize + iWidth * iHeight * 3;

    /* Set unused fields of header to 0 */
    baseMemset(bmpfileheader, 0, sizeof(bmpfileheader));
    baseMemset(bmpinfoheader, 0, sizeof(bmpinfoheader));

    /* Fill the file header */
    bmpfileheader[0] = 0x42;	/* first 2 bytes are ASCII 'B', 'M' */
    bmpfileheader[1] = 0x4D;
    PUT_4B(bmpfileheader, 2, bfSize); /* bfSize */
    /* we leave bfReserved1 & bfReserved2 = 0 */
    PUT_4B(bmpfileheader, 10, headersize); /* bfOffBits */

    PUT_2B(bmpinfoheader, 0, 40);	/* bmpinfoheader size */
    PUT_4B(bmpinfoheader, 4, iWidth); /* biWidth */
    PUT_4B(bmpinfoheader, 8, iHeight); /* biHeight */
    PUT_2B(bmpinfoheader, 12, 1);	/* biPlanes - must be 1 */
    PUT_2B(bmpinfoheader, 14, bits_per_pixel); /* biBitCount */
    PUT_2B(bmpinfoheader, 32, cmap_entries); /* biClrUsed */

    if(baseIsFileExist(filename))
    {
        baseDeleteFile(filename);
    }

    if((bfSize - iRGBSize) != headersize)
    {
        return false;
    }

    unsigned char* pBGRA = rgbtorgba(pRGBT, iWidth, iHeight);
    if(NULL == pBGRA)
    {
        return false;
    }

    int iFileID = baseOpenFile(filename, 02);
    baseMemcpy(pBGRA, bmpfileheader, 0, 14);
    baseMemcpy(pBGRA + 14, bmpinfoheader, 0, 40);
    baseWriteFileBuf(iFileID, pBGRA, bfSize);
    baseCloseFile(iFileID);

    if(NULL != pBGRA)
    {
        free(pBGRA);
        pBGRA = NULL;
    }
    return true;
}

unsigned char* bmpcode::createBMP(const unsigned char *pRGBT, int iRGBSize, int iWidth, int iHeight, unsigned int& iLen)
{
    if(NULL == pRGBT)
    {
        return NULL;
    }

    char bmpfileheader[14];
    char bmpinfoheader[40];
    int headersize, bfSize;
    int bits_per_pixel, cmap_entries;

    bits_per_pixel = 24;
    cmap_entries = 0;
    /* File size */
    headersize = 14 + 40 + cmap_entries * 4; /* Header and colormap */
    bfSize = headersize + iWidth * iHeight * 3;

    /* Set unused fields of header to 0 */
    baseMemset(bmpfileheader, 0, sizeof(bmpfileheader));
    baseMemset(bmpinfoheader, 0, sizeof(bmpinfoheader));

    /* Fill the file header */
    bmpfileheader[0] = 0x42;	/* first 2 bytes are ASCII 'B', 'M' */
    bmpfileheader[1] = 0x4D;
    PUT_4B(bmpfileheader, 2, bfSize); /* bfSize */
    /* we leave bfReserved1 & bfReserved2 = 0 */
    PUT_4B(bmpfileheader, 10, headersize); /* bfOffBits */

    PUT_2B(bmpinfoheader, 0, 40);	/* bmpinfoheader size */
    PUT_4B(bmpinfoheader, 4, iWidth); /* biWidth */
    PUT_4B(bmpinfoheader, 8, iHeight); /* biHeight */
    PUT_2B(bmpinfoheader, 12, 1);	/* biPlanes - must be 1 */
    PUT_2B(bmpinfoheader, 14, bits_per_pixel); /* biBitCount */
    PUT_2B(bmpinfoheader, 32, cmap_entries); /* biClrUsed */

    if((bfSize - iRGBSize) != headersize)
    {
        return NULL;
    }

    unsigned char* pBGRA = rgbtorgba(pRGBT, iWidth, iHeight);
    if(NULL == pBGRA)
    {
        return NULL;
    }

    baseMemcpy(pBGRA, bmpfileheader, 0, 14);
    baseMemcpy(pBGRA + 14, bmpinfoheader, 0, 40);
    iLen = bfSize;
    return pBGRA;
}

bmpcode::~bmpcode()
{
    if(NULL != m_BMP)
    {
        free(m_BMP);
        m_BMP = NULL;
    }
}

