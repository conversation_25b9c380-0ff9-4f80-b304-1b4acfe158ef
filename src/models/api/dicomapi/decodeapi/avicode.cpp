#include <vector>
#include "basefile.h"
#include "basedebug.h"
#include "basetype.h"
#include "avitype.h"
#include "avicode.h"

using namespace std;

static unsigned int m_AllFrames = 0;
static unsigned int m_TotalSize = 0;
static vector<unsigned int> m_AllImageSizeVector;

static void writeFileBuf(int iFileID, unsigned int iFleg)
{
    char pBuf[4] = {0};
    pBuf[0] = (char) iFleg%0x100;
    iFleg /= 0x100;
    pBuf[1] = (char) iFleg%0x100;
    iFleg /= 0x100;
    pBuf[2] = (char) iFleg%0x100;
    iFleg /= 0x100;
    pBuf[3] = (char) iFleg%0x100;
    baseWriteFileBuf(iFileID, &pBuf, 4);
}

void resetAllImageStatus()
{
    m_AllFrames = 0;
    m_TotalSize = 0;
    m_AllImageSizeVector.clear();
}

int startCreateAviFile(const char* pFilePath)
{
    int iFileID = baseOpenFile(pFilePath, 02);
    if(-1 == iFileID)
    {
        ErrorPrintf("file open is wrong!");
        return iFileID;
    }

    int iAllHeadSize = sizeof(stRiffHead) + sizeof(stHeadList) + sizeof(stAVIMainHeader) + sizeof(stHeadList) + sizeof(stAVIStreamHeader)+
        sizeof(stFrameHead) + sizeof(stHeadList) + sizeof(stHeadDmlh) + sizeof(stHeadList);

    baseLseekFileBuf(iFileID, iAllHeadSize, SEEK_SET);
    resetAllImageStatus();
    return iFileID;
}

void addJpgDataToAviFile(int iFileID, unsigned char *pImageValue, int iImageSize)
{
    if(-1 == iFileID)
    {
        ErrorPrintf("iFileID is wrong!");
        return;
    }

    if((NULL == pImageValue) ||
            (iImageSize < 1))
    {
        ErrorPrintf("image value is wrong!");
        return;
    }

    stDbHead dbHead  = {{'0','0','d','b'}, (unsigned int)iImageSize};
    int iRet = baseWriteFileBuf(iFileID, &dbHead, sizeof(dbHead));
    if(-1 == iRet)
    {
        ErrorPrintf("write dbHead is wrong!");
        return;
    }
    iRet = baseWriteFileBuf(iFileID, pImageValue, iImageSize);
    if(-1 == iRet)
    {
        ErrorPrintf("write ImagValue is wrong!");
        return;
    }
    m_AllImageSizeVector.push_back(iImageSize);
    ++m_AllFrames;
    m_TotalSize += iImageSize;
}

void saveAviFile(int iFileID, int iWidth, int iHeight, int iVideoFps)
{
    if(-1 == iFileID)
    {
        ErrorPrintf("iFileID is wrong!");
        return;
    }

    stRiffHead riffHead = { {'R','I','F','F'}, 0, {'A','V','I',' '}};
    stAVIMainHeader aviMainHeader = {{0}, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {0}};
    stHeadList headList1 = {{'L','I','S','T'}, 0, {'h','d','r','l'}};
    stHeadList headList2 = {{'L','I','S','T'}, 0, {'s','t','r','l'}};
    stHeadList headList3 = {{'L','I','S','T'}, 0, {'o','d','m','l'}};
    stHeadList headList4 = {{'L','I','S','T'}, 0, {'m','o','v','i'}};
    stIdx1Head idx1Head = {{'i','d','x','1'}, 16 * m_AllFrames};
    stDbHead dbHead = {{'0','0','d','b'}, 0};
    stHeadDmlh headDmlh = {{'d','m','l','h'}, 4, m_AllFrames};
    stAVIStreamHeader aviStreamHeader = {{0}, 0, {0}, {0}, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    stFrameHead frameHead = {{0}, 0, 0, 0, 0, 0, 0, {0}, 0, 0, 0, 0, 0};

    unsigned int offset = 4;
    int iRet = baseWriteFileBuf(iFileID, &idx1Head, sizeof(idx1Head));
    if(-1 == iRet)
    {
        ErrorPrintf("write idx1Head is wrong!");
        return;
    }
    for (int i = 0; i < m_AllFrames; ++i)
    {
        baseWriteFileBuf(iFileID, &dbHead, 4);
        writeFileBuf(iFileID, 18 );
        writeFileBuf(iFileID, offset );
        writeFileBuf(iFileID, m_AllImageSizeVector[i]);
        offset += m_AllImageSizeVector[i] + 8;
    }
    m_AllImageSizeVector.clear();

    int iPerUsec = 0xf4240 / iVideoFps;
    strncpy(aviMainHeader.pAvih, "avih", sizeof(aviMainHeader.pAvih));
    aviMainHeader.uiMicroSecPerFrame = iPerUsec;
    aviMainHeader.uiMaxBytesPerSec = 0xf4240 * (m_TotalSize / m_AllFrames) / iPerUsec;
    aviMainHeader.uiTotalFrames = m_AllFrames;
    aviMainHeader.uiStreams = 1;
    aviMainHeader.uiFlags = AVIF_HASINDEX;
    aviMainHeader.uiWidth = iWidth;
    aviMainHeader.uiHeight = iHeight;

    strncpy(aviStreamHeader.pStrh, "strh", sizeof(aviStreamHeader.pStrh));
    strncpy(aviStreamHeader.pVids, "vids", sizeof(aviStreamHeader.pVids));
    strncpy(aviStreamHeader.pCodec, "MJPG", sizeof(aviStreamHeader.pCodec));
    aviStreamHeader.uiScale = iPerUsec;
    aviStreamHeader.uiRate = 0xf4240;
    aviStreamHeader.uiStart = 0;
    aviStreamHeader.uiLength = m_AllFrames;

    strncpy(frameHead.pStrf, "strf", sizeof(frameHead.pStrf));
    frameHead.uiWidth = iWidth;
    frameHead.uiHeight = iHeight;
    frameHead.usPlanes = 1;
    frameHead.usBitcount = 24;
    strcpy(frameHead.pCodec, "MJPG");
    frameHead.unpackedsize = 3 * iWidth * iHeight;

    riffHead.uiSize = sizeof(headList1) + sizeof(aviMainHeader) + sizeof(headList2) + sizeof(aviStreamHeader) +
        sizeof(frameHead) + sizeof(headList3) + sizeof(headDmlh) + sizeof(headList4) + m_AllFrames * sizeof(stDbHead) +
        m_TotalSize + sizeof(stIdx1Head) + (16 * m_AllFrames) + 4;

    headList1.uiSize = 4 + sizeof(aviMainHeader) + sizeof(headList2)
            + sizeof(aviStreamHeader) + sizeof(frameHead) + sizeof(headList3) + sizeof(headDmlh);

    aviMainHeader.uiSize = sizeof(aviMainHeader) - 8;
    headList2.uiSize = 4 + sizeof(aviStreamHeader) + sizeof(frameHead) + sizeof(headList3) + sizeof(headDmlh);
    aviStreamHeader.uiSize = sizeof(aviStreamHeader) - 8;
    frameHead.uiSize = sizeof(frameHead) - 8;
    frameHead.uiSize2 = frameHead.uiSize;
    headList3.uiSize = 4 + sizeof(headDmlh);
    headList4.uiSize = 4 + m_AllFrames * sizeof(stDbHead) + m_TotalSize;

    baseLseekFileBuf(iFileID, 0, SEEK_SET);
    iRet = baseWriteFileBuf(iFileID, &riffHead, sizeof(riffHead));
    iRet = baseWriteFileBuf(iFileID, &headList1, sizeof(headList1));
    iRet = baseWriteFileBuf(iFileID, &aviMainHeader, sizeof(aviMainHeader));
    iRet = baseWriteFileBuf(iFileID, &headList2, sizeof(headList2));
    iRet = baseWriteFileBuf(iFileID, &aviStreamHeader, sizeof(aviStreamHeader));
    iRet = baseWriteFileBuf(iFileID, &frameHead, sizeof(frameHead));
    iRet = baseWriteFileBuf(iFileID, &headList3, sizeof(headList3));
    iRet = baseWriteFileBuf(iFileID, &headDmlh, sizeof(headDmlh));
    iRet = baseWriteFileBuf(iFileID, &headList4, sizeof(headList4));
    baseCloseFile(iFileID);
}
