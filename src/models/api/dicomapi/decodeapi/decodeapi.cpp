#include "basedefine.h"
#include "dicomparameters.h"
#include "basefile.h"
#include "basetime.h"
#include "avicode.h"
#include "decodeapi.h"

static JPEGCodec st_jpegc;
static JPEGCodec::Buffer st_buffer;

int startCreateAvi(const char* pPath)
{
    st_jpegc.setQuality(95);
    return startCreateAviFile(pPath);
}

bool stopAndSaveAvi(int iFd, int iW, int iH, int iFps)
{
    saveAviFile(iFd, iW, iH, iFps);
    return true;
}

bool startAddToAvi(int iFd, const unsigned char* pRGB, int iLen, int iW, int iH)
{
    st_jpegc.encode(pRGB, iLen, iW, iH, iW*3, st_buffer);
    addJpgDataToAviFile(iFd, (unsigned char *)st_buffer.data(), st_buffer.size());
    return true;
}

JPEGCodec::Buffer startRGBToJPEG(const unsigned char* pRGB, int iLen, int iW, int iH)
{
    st_jpegc.encode(pRGB, iLen, iW, iH, iW*3, st_buffer);
    return st_buffer;
}
