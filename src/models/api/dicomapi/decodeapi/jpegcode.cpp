#include "jpegcode.h"
#include <assert.h>
#include <stddef.h>
#include <stdio.h>

extern "C" {
#include "jpeglib.h"
}


class JPEGCodec::IJPEGDestination
{
public:
    virtual ~IJPEGDestination() {}
    virtual bool bind(j_compress_ptr cinfo) = 0;
};

class JPEGCodec::IJPEGSource
{
public:
    virtual ~IJPEGSource() {}
    virtual bool bind(j_decompress_ptr cinfo) = 0;
};

class MemoryDestination: public JPEGCodec::IJPEGDestination
{
public:
    MemoryDestination(JPEGCodec::Buffer& buffer);
    virtual ~MemoryDestination();

    virtual bool bind(j_compress_ptr cinfo);

private:
    MemoryDestination(MemoryDestination const&);
    MemoryDestination& operator=(MemoryDestination const&);

    static void    onInitialize       (j_compress_ptr cinfo);
    static boolean onEmptyOutputBuffer(j_compress_ptr cinfo);
    static void    onTermination      (j_compress_ptr cinfo);

    static int const s_initialBufferSize_;

    jpeg_destination_mgr    manager_;
    JPEGCodec::Buffer&        buffer_;
};

class FileDestination: public JPEGCodec::IJPEGDestination
{
public:
    FileDestination(std::string const& path);
    virtual ~FileDestination();

    virtual bool bind(j_compress_ptr cinfo);

private:
    FileDestination(FileDestination const&);
    FileDestination& operator=(FileDestination const&);

    FILE *file_;
};

class MemorySource: public JPEGCodec::IJPEGSource
{
public:
    MemorySource(uint8_t const *data, size_t size);
    virtual ~MemorySource();

    virtual bool bind(j_decompress_ptr cinfo);

private:
    MemorySource(MemorySource const&);
    MemorySource& operator=(MemorySource const&);

    static boolean onFillInputBuffer(j_decompress_ptr cinfo);
    static void    onSkipInputData  (j_decompress_ptr cinfo, long num_bytes);
    static void    onInitialize     (j_decompress_ptr cinfo);
    static void    onTermination    (j_decompress_ptr cinfo);

    jpeg_source_mgr    manager_;
};

class FileSource: public JPEGCodec::IJPEGSource
{
public:
    FileSource(std::string const& path);
    virtual ~FileSource();

    virtual bool bind(j_decompress_ptr cinfo);

private:
    FileSource(FileSource const&);
    FileSource& operator=(FileSource const&);

    FILE *file_;
};


JPEGCodec::JPEGCodec(int quality)
    : quality_(quality)
    , iBytesPerPixelRGB24(3)
{
}

JPEGCodec::~JPEGCodec()
{
}

void JPEGCodec::setQuality(int quality)
{
    assert(0 <= quality && quality <= 100);
    quality_ = quality;
}

bool JPEGCodec::encode(uint8_t const *data, size_t size, uint32_t width, uint32_t height, uint32_t stride, Buffer& buffer) const
{
    MemoryDestination dest(buffer);
    return this->encode(data, size, width, height, stride, dest);
}

bool JPEGCodec::encode(uint8_t const *data, size_t size, uint32_t width, uint32_t height, uint32_t stride, IJPEGDestination& destination) const
{
    jpeg_compress_struct cinfo;
    jpeg_error_mgr jerr;

    cinfo.err = ::jpeg_std_error(&jerr);

    ::jpeg_create_compress(&cinfo);

    if (!destination.bind(&cinfo)) {
        ::jpeg_destroy_compress(&cinfo);
        return false;
    }

    cinfo.image_width        = width;
    cinfo.image_height        = height;
    cinfo.input_components    = JPEGCodec::iBytesPerPixelRGB24;
    cinfo.in_color_space    = JCS_RGB;

    ::jpeg_set_defaults(&cinfo);
    ::jpeg_set_quality(&cinfo, quality_, TRUE);
    ::jpeg_start_compress(&cinfo, TRUE);

    JSAMPROW row_ptr[1];

    while (cinfo.next_scanline < cinfo.image_height) {
        row_ptr[0] = const_cast<JSAMPROW>(data) + cinfo.next_scanline * stride;
        ::jpeg_write_scanlines(&cinfo, row_ptr, 1);
    }

    ::jpeg_finish_compress(&cinfo);
    ::jpeg_destroy_compress(&cinfo);

    return true;
}

FileDestination::FileDestination(std::string const& path)
    : file_(NULL)
{
}

FileDestination::~FileDestination()
{
    if (file_) {
        ::fclose(file_);
        file_ = NULL;
    }
}

bool FileDestination::bind(j_compress_ptr cinfo)
{
    if (file_ != NULL) {
        ::jpeg_stdio_dest(cinfo, file_);
    }
    return (file_ != NULL);
}

int const MemoryDestination::s_initialBufferSize_ = 16 * 1024;

MemoryDestination::MemoryDestination(JPEGCodec::Buffer& buffer)
    : buffer_(buffer)
{
    manager_.init_destination     = MemoryDestination::onInitialize;
    manager_.empty_output_buffer = MemoryDestination::onEmptyOutputBuffer;
    manager_.term_destination     = MemoryDestination::onTermination;
}

MemoryDestination::~MemoryDestination()
{
}

bool MemoryDestination::bind(j_compress_ptr cinfo)
{
    cinfo->dest = &manager_;
    return true;
}

void MemoryDestination::onInitialize(j_compress_ptr cinfo)
{
    MemoryDestination *self = reinterpret_cast<MemoryDestination *>(reinterpret_cast<char *>(cinfo->dest) - offsetof(MemoryDestination, manager_));
    self->buffer_.resize(MemoryDestination::s_initialBufferSize_);
    self->manager_.next_output_byte = self->buffer_.data();
    self->manager_.free_in_buffer    = self->buffer_.size();
}

boolean MemoryDestination::onEmptyOutputBuffer(j_compress_ptr cinfo)
{
    MemoryDestination *self = reinterpret_cast<MemoryDestination *>(reinterpret_cast<char *>(cinfo->dest) - offsetof(MemoryDestination, manager_));
    size_t prevSize = self->buffer_.size();
    self->buffer_.resize(prevSize + s_initialBufferSize_);
    self->manager_.next_output_byte = self->buffer_.data() + prevSize;
    self->manager_.free_in_buffer = s_initialBufferSize_;
    return TRUE;
}

void MemoryDestination::onTermination(j_compress_ptr cinfo)
{
    MemoryDestination *self = reinterpret_cast<MemoryDestination *>(reinterpret_cast<char *>(cinfo->dest) - offsetof(MemoryDestination, manager_));
    self->buffer_.resize(self->buffer_.size() - self->manager_.free_in_buffer);
}

FileSource::FileSource(std::string const& path)
    : file_(NULL)
{
}

FileSource::~FileSource()
{
    if (file_) {
        ::fclose(file_);
        file_ = NULL;
    }
}

bool FileSource::bind(j_decompress_ptr cinfo)
{
    if (file_ != NULL) {
        ::jpeg_stdio_src(cinfo, file_);
    }
    return (file_ != NULL);
}

MemorySource::MemorySource(uint8_t const *data, size_t size)
{
    manager_.init_source        = MemorySource::onInitialize;
    manager_.fill_input_buffer    = MemorySource::onFillInputBuffer;
    manager_.skip_input_data    = MemorySource::onSkipInputData;
    manager_.resync_to_restart    = ::jpeg_resync_to_restart;
    manager_.term_source        = MemorySource::onTermination;

    manager_.bytes_in_buffer = size;
    manager_.next_input_byte = data;
}

MemorySource::~MemorySource()
{
}

bool MemorySource::bind(j_decompress_ptr cinfo)
{
    if(cinfo)
        cinfo->src = &manager_;
    else
        return false;
    return true;
}

boolean MemorySource::onFillInputBuffer(j_decompress_ptr cinfo)
{
    MemorySource *self = reinterpret_cast<MemorySource *>(reinterpret_cast<char *>(cinfo->src) - offsetof(MemorySource, manager_));
    static JOCTET const EOI_BUFFER[2] = { 0xFF, JPEG_EOI };
    self->manager_.next_input_byte = EOI_BUFFER;
    self->manager_.bytes_in_buffer = sizeof EOI_BUFFER;
    return TRUE;
}

void MemorySource::onSkipInputData(j_decompress_ptr cinfo, long num_bytes)
{
    MemorySource *self = reinterpret_cast<MemorySource *>(reinterpret_cast<char *>(cinfo->src) - offsetof(MemorySource, manager_));

    if (num_bytes < 1) {
        return;
    }

    if (size_t(num_bytes) < self->manager_.bytes_in_buffer) {
        self->manager_.bytes_in_buffer -= num_bytes;
        self->manager_.next_input_byte += num_bytes;
    } else {
        self->manager_.bytes_in_buffer = 0;
    }
}

void MemorySource::onInitialize(j_decompress_ptr cinfo)
{
}

void MemorySource::onTermination(j_decompress_ptr cinfo)
{
}

