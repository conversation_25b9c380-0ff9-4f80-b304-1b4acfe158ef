#include "dcmapi.h"
#include "dcmtk/config/osconfig.h"  /* make sure OS specific configuration is included first */
#include "dcmtk/dcmdata/cmdlnarg.h" /* for prepareCmdLineArgs */
#include "dcmtk/dcmdata/dcdict.h"   /* for global data dictionary */
#include "dcmtk/dcmdata/dcostrmz.h" /* for dcmZlibCompressionLevel */
#include "dcmtk/dcmdata/dcuid.h"    /* for dcmtk version name */
#include "dcmtk/dcmnet/assoc.h"
#include "dcmtk/dcmnet/dstorscu.h" /* for DcmStorageSCU */
#include "dcmtk/ofstd/ofconapp.h"  /* for OFConsoleApplication */
#include "dcmtk/ofstd/ofstd.h"     /* for OFStandard functions */
#include "dcmtk/ofstd/ofstream.h"  /* for OFStringStream et al. */

#include "dcmtk/dcmdata/dcddirif.h" /* for class DicomDirInterface */
#include "dcmtk/dcmdata/dcrledrg.h" /* for RLE decoder */
#include "dcmtk/dcmdata/libi2d/i2dbmps.h"
#include "dcmtk/dcmimgle/dcmimage.h"
#include "dcmtk/dcmjpeg/dipijpeg.h" /* for dcmimage JPEG plugin */
#include "dcmtk/dcmjpeg/djdecode.h" /* for JPEG decoders */
#include "dcmtk/dcmjpls/djdecode.h" /* for JPEG-LS decoders */
#include "dcmtk/dcmnet/diutil.h"

#include "dcmtk/dcmdata/dcpixel.h"
#include "dcmtk/dcmdata/dcpixseq.h"
#include "dcmtk/dcmdata/dcpxitem.h"
#include "dcmtk/dcmdata/libi2d/i2djpgs.h"
#include "dcmtk/dcmdata/libi2d/i2doutpl.h"

#include "dcmtk/dcmnet/dfindscu.h"
#include "dcmtk/dcmtls/tlsopt.h" /* for DcmTLSOptions */

#include "dcmtk/dcmdata/dcistrmz.h" /* for dcmZlibExpectRFC1950Encoding */
#include "dcmtk/dcmdata/dcuid.h"    /* for dcmtk version name */
#include "echoscu.h"

#include "basedefine.h"
#include "basefile.h"
#include "basejson.h"
#include "basetime.h"
#include "bmpcode.h"
#include "dicomparameters.h"
#include "jpegcode.h"

#ifdef WITH_ZLIB
#include <zlib.h> /* for zlibVersion() */
#endif

#if defined(HAVE_WINDOWS_H) || defined(HAVE_FNMATCH_H)
#define PATTERN_MATCHING_AVAILABLE
#endif

/* general definitions */
#define OFFIS_CONSOLE_APPLICATION "dcmsend"

static OFLogger dcmsendLogger = OFLog::getLogger("dcmtk.apps." OFFIS_CONSOLE_APPLICATION);

/* default application entity titles */
#define APPLICATIONTITLE "DCMSEND"
#define PEERAPPLICATIONTITLE "ANY-SCP"
// output file errors
#define EXITCODE_CANNOT_WRITE_REPORT_FILE 43

// network errors
#define EXITCODE_CANNOT_INITIALIZE_NETWORK 60
#define EXITCODE_CANNOT_NEGOTIATE_ASSOCIATION 61
#define EXITCODE_CANNOT_SEND_REQUEST 62
#define EXITCODE_CANNOT_ADD_PRESENTATION_CONTEXT 65

/* helper macro for converting stream output to a string */
#define CONVERT_TO_STRING(output, string)                                                                              \
    optStream.str("");                                                                                                 \
    optStream.clear();                                                                                                 \
    optStream << output << OFStringStream_ends;                                                                        \
    OFSTRINGSTREAM_GETOFSTRING(optStream, string)

#define adStrValue(a, b) mydatasete->putAndInsertString(a, b)

#define adU16Value(a, b) mydatasete->putAndInsertUint16(a, b)

#define addCCVALUE(a, b, c)                                                                                            \
    ;                                                                                                                  \
    {                                                                                                                  \
        char pTC[56] = {0};                                                                                            \
        baseSnprintf(pTC, 56, "%0.8f", CDicomParameter::instance().getCalibrationRegionValue(b, c));                   \
        pDcmItem->putAndInsertString(a, pTC);                                                                          \
    }

static void cleanup()
{
    // deregister JPEG decoder
    DJDecoderRegistration::cleanup();
    // deregister JPEG-LS decoder
    DJLSDecoderRegistration::cleanup();
    // deregister RLE decoder
    DcmRLEDecoderRegistration::cleanup();
#ifdef DEBUG
    /* useful for debugging with dmalloc */
    dcmDataDict.clear();
#endif
}

#define SHORTCOL 4
#define LONGCOL 21
#define DEFAULT_FILESETID "DCMTK_MEDIA_DEMO"
#define DEFAULT_DESCRIPTOR_CHARSET "ISO_IR 100"
#define DEFAULT_TMP_DFM "/tmpDW.bmp"

static char rcsid[] = "$dcmtk: " OFFIS_CONSOLE_APPLICATION " v" OFFIS_DCMTK_VERSION " " OFFIS_DCMTK_RELEASEDATE " $";

/* default application titles */
#define PEERAPPLICATIONTITLE "ANY-SCP"

#define SHORTCOL 4
#define LONGCOL 20

void AddPatientElementsToDicom(DcmDataset*& mydatasete)
{
    adStrValue(DCM_AccessionNumber, CDicomParameter::instance().getAccessionNumber());
    adStrValue(DCM_PatientName, CDicomParameter::instance().getPatientsName());
    adStrValue(DCM_PatientID, CDicomParameter::instance().getPatientID());
    adU16Value(DCM_TypeOfPatientID, 0);
    adStrValue(DCM_PatientAge, CDicomParameter::instance().getPatientsAge());
    adStrValue(DCM_PatientBirthDate, CDicomParameter::instance().getPatientsBirthDate());
    adStrValue(DCM_PatientSex, CDicomParameter::instance().getPatientsSex());
    adStrValue(DCM_ReferringPhysicianName, CDicomParameter::instance().getReferringPhysicianName());
}

void AddStudyElementsToDicom(DcmDataset*& mydatasete)
{
    adStrValue(DCM_StudyID, CDicomParameter::instance().getStudyID());
    adStrValue(DCM_StudyDate, CDicomParameter::instance().getStudyDate());
    adStrValue(DCM_StudyTime, CDicomParameter::instance().getStudyTime());
    adStrValue(DCM_StudyDescription, CDicomParameter::instance().getStudyDescription());
}

void AddParameters(DcmDataset*& mydatasete)
{
    FILEPARAMETERSLIST m_InputFileListParameters = CDicomParameter::instance().getFunctionParameters();
    for (FILEPARAMETERSLIST::iterator it = m_InputFileListParameters.begin(); it != m_InputFileListParameters.end();
         ++it)
    {
        Dcm_InputFileParameters* mbi = *it;
        if (NULL != mbi)
        {
            if (0x0010 == mbi->StartTypeParameters && 0x1002 == mbi->EndTypeParameters)
            {
                DcmSequenceOfItems* ReferencedStudySequence = new DcmSequenceOfItems(DCM_OtherPatientIDsSequence);
                DcmItem* pDcmItem = new DcmItem;
                pDcmItem->putAndInsertString(DCM_PatientID, mbi->pParameters);
                pDcmItem->putAndInsertString(DCM_TypeOfPatientID, "TEXT");
                ReferencedStudySequence->insert(pDcmItem);
                mydatasete->insert(ReferencedStudySequence);
            }
            else
            {
                adStrValue(DcmTagKey(mbi->StartTypeParameters, mbi->EndTypeParameters), mbi->pParameters);
            }
        }
    }
}

void AddSeriesElementsToDicom(DcmDataset*& mydatasete)
{
    char pUid[128] = {0};
    adStrValue(DCM_SeriesDate, CDicomParameter::instance().getSeriesDate());
    adStrValue(DCM_SeriesTime, CDicomParameter::instance().getSeriesTime());
    adStrValue(DCM_SeriesNumber, "1");
    //    adStrValue(DCM_PerformedStationAETitle, CDicomParameter::instance().getSourceApplicationEntityTitle());
    adStrValue(DCM_Modality, CDicomParameter::instance().getModality());
    // adStrValue(DCM_SourceApplicationEntityTitle, CDicomParameter::instance().getSourceApplicationEntityTitle());
    adStrValue(DCM_ImageType, "ORIGINAL\\PRIMARY\\\\");

    adStrValue(DCM_StudyInstanceUID, CDicomParameter::instance().getStudyInstanceUID());
    adStrValue(DCM_SeriesInstanceUID, CDicomParameter::instance().getSeriesInstanceUID());
    adStrValue(DCM_InstitutionName, CDicomParameter::instance().getInstitutionName());
    adStrValue(DCM_Manufacturer, CDicomParameter::instance().getManufacturer());
    adStrValue(DCM_SOPInstanceUID, CDicomParameter::instance().getSOPInstanceUID());
}

void AddImageElementsToDicom(DcmDataset*& mydatasete)
{
    adStrValue(DCM_FrameTime, CDicomParameter::instance().getFrameTime());
}

void addSSequence(DcmDataset*& mydatasete)
{
    int iValue = CDicomParameter::instance().getCalibrationRegionValue(0, 0);
    if (iValue != -0xFFFF)
    {
        DcmSequenceOfItems* ReferencedStudySequence = new DcmSequenceOfItems(DCM_SequenceOfUltrasoundRegions);

        for (int i = 0; i < 10; ++i)
        {
            iValue = CDicomParameter::instance().getCalibrationRegionValue(i, 0);
            if (-0xFFFF == iValue)
            {
                break;
            }
            DcmItem* pDcmItem = new DcmItem;
            addCCVALUE(DCM_RegionSpatialFormat, i, EDICOM_PARAMETERS_Sequence_RegionSpatialFormat);
            addCCVALUE(DCM_RegionDataType, i, EDICOM_PARAMETERS_Sequence_RegionDataType);
            addCCVALUE(DCM_RegionFlags, i, EDICOM_PARAMETERS_Sequence_RegionFlags);
            addCCVALUE(DCM_RegionLocationMinX0, i, EDICOM_PARAMETERS_Sequence_RegionLocationMinX0);
            addCCVALUE(DCM_RegionLocationMinY0, i, EDICOM_PARAMETERS_Sequence_RegionLocationMinY0);
            addCCVALUE(DCM_RegionLocationMaxX1, i, EDICOM_PARAMETERS_Sequence_RegionLocationMaxX1);
            addCCVALUE(DCM_RegionLocationMaxY1, i, EDICOM_PARAMETERS_Sequence_RegionLocationMaxY1);
            //            addCCVALUE(DCM_ReferencePixelX0, i, EDICOM_PARAMETERS_Sequence_ReferencePixelX0);
            //            addCCVALUE(DCM_ReferencePixelY0, i, EDICOM_PARAMETERS_Sequence_ReferencePixelY0);
            addCCVALUE(DCM_PhysicalUnitsXDirection, i, EDICOM_PARAMETERS_Sequence_PhysicalUnitsXDirection);
            addCCVALUE(DCM_PhysicalUnitsYDirection, i, EDICOM_PARAMETERS_Sequence_PhysicalUnitsYDirection);
            //            addCCVALUE(DCM_ReferencePixelPhysicalValueX, i,
            //            EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueX);
            //            addCCVALUE(DCM_ReferencePixelPhysicalValueY, i,
            //            EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueY);
            addCCVALUE(DCM_PhysicalDeltaX, i, EDICOM_PARAMETERS_Sequence_PhysicalDeltaX);
            addCCVALUE(DCM_PhysicalDeltaY, i, EDICOM_PARAMETERS_Sequence_PhysicalDeltaY);
            //            addCCVALUE(DCM_TransducerFrequency, i, EDICOM_PARAMETERS_Sequence_TransducerFrequency);
            //            addCCVALUE(DCM_PulseRepetitionFrequency, i,
            //            EDICOM_PARAMETERS_Sequence_PulseRepetitionFrequency);

            addCCVALUE(DCM_PixelSpacing, i, EDICOM_PARAMETERS_Sequence_PixelSpacing);
            ReferencedStudySequence->insert(pDcmItem);
            mydatasete->insert(ReferencedStudySequence);
        }
    }

    CDicomParameter::instance().resetCalibrationRegionValue();
}

void resetElementsID()
{
    char pUid[128] = {0};
    CDicomParameter::instance().setStudyInstanceUID(dcmGenerateUniqueIdentifier(pUid, SITE_STUDY_UID_ROOT));
    CDicomParameter::instance().setSeriesInstanceUID(dcmGenerateUniqueIdentifier(pUid, SITE_SERIES_UID_ROOT));
    CDicomParameter::instance().setSOPInstanceUID(dcmGenerateUniqueIdentifier(pUid, SITE_INSTANCE_UID_ROOT));
}

void createFrameLabelVector(OFString& s, unsigned long numFrames, OFBool isColor)
{
    s = "";
    char buf[100];
    for (unsigned long i = 0; i < numFrames; ++i)
    {
        if (i > 0)
            s += "\\";
        if (isColor)
            OFStandard::snprintf(buf, 100, "F%03luR\\F%03luG\\F%03luB", i + 1, i + 1, i + 1);
        else
            OFStandard::snprintf(buf, 100, "F%03lu", i + 1);
        s += buf;
    }
    if (s.length() > 65534)
    {
        s.erase(65534);
    }
}

int createDicomFile(const char* pSavePath)
{
    int Iret = 0;
    int iSendFileType = atoi(CDicomParameter::instance().getSendDicomDataType());
    switch (iSendFileType)
    {
    case 0:
    {
        Iret = createRGBFileMake(pSavePath);
        break;
    }
    case 1:
    {
        Iret = createBMPFileMake(pSavePath);
        break;
    }
    case 2:
    {
        Iret = createRLEFileMake(pSavePath);
        break;
    }
    default:
    {
        break;
    }
    }
    CDicomParameter::instance().resetCalibrationRegionValue();
    return Iret;
}

int createRGBFileMake(const char* pSaveStoragePath)
{
    int iNumberOfFrames = CDicomParameter::instance().getFileCounts();
    if (0 == iNumberOfFrames)
    {
        return EXITCODE_NO_INPUT_FILES;
    }
    char pSavePath[128] = {0};
    snprintf(pSavePath, 128, "%s/%lld_rgb.dcm", CDicomParameter::instance().getStoragePath(), getCurrentTimestamp());
    if (baseIsFileExist(pSavePath))
    {
        return sendDicomFile(pSavePath);
    }

    DcmFileFormat fileformat;
    DcmDataset* mydatasete = fileformat.getDataset();
    DcmPixelSequence* seq = new DcmPixelSequence(DcmTag(DCM_PixelData, EVR_OB));
    seq->insert(new DcmPixelItem(DcmTag(DCM_Item, EVR_OB)));
    Uint16 cols = 0;
    Uint16 rows = 0;

    int iCount = 0;

    for (int i = 0; i < iNumberOfFrames; ++i)
    {
        Dcm_InputFile* mdi = CDicomParameter::instance().getRGBDicomFile();
        if (NULL == mdi || mdi->pRGB == NULL)
        {
            break;
        }
        cols = mdi->iW;
        rows = mdi->iH;

        DcmPixelItem* newItem = new DcmPixelItem(DcmTag(DCM_Item, EVR_OB));
        if (newItem != NULL)
        {
            newItem->putUint8Array((Uint8*)mdi->pRGB, mdi->iLen);
            seq->insert(newItem, iCount);
            ++iCount;
        }
        if (mdi != NULL)
        {
            if (mdi->pRGB != NULL)
            {
                free(mdi->pRGB);
                mdi->pRGB = NULL;
            }
            delete mdi;
            mdi = NULL;
        }
    }
    if (0 == iCount)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pCount[128] = {0};
    sprintf(pCount, "%d", iCount);
    if (iCount > 1)
    {
        adStrValue(DCM_SOPClassUID, UID_UltrasoundMultiframeImageStorage);
        adStrValue(DCM_NumberOfFrames, pCount);
        adStrValue(DCM_StartTrim, "1");
        adStrValue(DCM_StopTrim, pCount);

        adStrValue(DCM_RecommendedDisplayFrameRate, CDicomParameter::instance().getFrameRate());
        adStrValue(DCM_CineRate, CDicomParameter::instance().getFrameRate());

        char strVar[48] = {0};
        int fps = atoi(CDicomParameter::instance().getFrameRate());

        float frameDurationMs = 1000.0f / fps;
        float effectiveDuration = iCount / (float)fps;
        float aActualFrameDuration = frameDurationMs + 0.5;

        sprintf(strVar, "%0.6f", effectiveDuration);
        adStrValue(DCM_EffectiveDuration, strVar);
        adStrValue(DCM_FrameDelay, strVar);
        sprintf(strVar, "%d", (int)aActualFrameDuration);
        adStrValue(DCM_ActualFrameDuration, strVar);
        sprintf(strVar, "%0.6f", frameDurationMs);
        adStrValue(DCM_FrameTime, strVar);
        adStrValue(DCM_FrameIncrementPointer, "(0018,1063)");
        adStrValue(DCM_PreferredPlaybackSequencing, "0");
    }
    else
    {
        adStrValue(DCM_SOPClassUID, UID_UltrasoundImageStorage);
    }
    AddPatientElementsToDicom(mydatasete);
    AddStudyElementsToDicom(mydatasete);
    AddParameters(mydatasete);
    AddSeriesElementsToDicom(mydatasete);
    addSSequence(mydatasete);

    adU16Value(DCM_PixelRepresentation, 0);
    adU16Value(DCM_SamplesPerPixel, 3);
    adU16Value(DCM_Rows, rows);
    adU16Value(DCM_Columns, cols);
    adU16Value(DCM_BitsAllocated, 8);
    adU16Value(DCM_BitsStored, 8);
    adU16Value(DCM_HighBit, 7);
    adStrValue(DCM_PhotometricInterpretation, "YBR_FULL_422");

    mydatasete->insert(seq, OFFalse, OFFalse);

    OFCondition status;
    if (NULL == pSaveStoragePath)
    {
        status = fileformat.saveFile(pSavePath, EXS_JPEGProcess1);
        if (status.bad())
        {
            DCMNET_DEBUG("Error");
            return EXITCODE_INVALID_INPUT_FILE;
        }
        return sendDicomFile(pSavePath);
    }
    else
    {
        status = fileformat.saveFile(pSaveStoragePath, EXS_JPEGProcess1);
        if (status.bad())
        {
            DCMNET_DEBUG("Error");
            return EXITCODE_INVALID_INPUT_FILE;
        }
    }
    return EXITCODE_INVALID_INPUT_FILE;
}

int createFileAndSendToDicom()
{
    int Iret = 0;
    int iSendFileType = atoi(CDicomParameter::instance().getSendDicomDataType());
    switch (iSendFileType)
    {
    case 0:
    {
        Iret = createRGBFileMake(NULL);
        break;
    }
    case 1:
    {
        Iret = createBMPFileMake(NULL);
        break;
    }
    case 2:
    {
        DcmRLEDecoderRegistration::registerCodecs(false, false);
        Iret = createRLEFileMake(NULL);
        DcmRLEDecoderRegistration::cleanup();
        break;
    }
    default:
    {
        break;
    }
    }
    CDicomParameter::instance().resetCalibrationRegionValue();
    return Iret;
}

int createBMPFileMake(const char* pSaveStoragePath)
{
    int iNumberOfFrames = CDicomParameter::instance().getFileCounts();
    if (0 == iNumberOfFrames)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pSavePath[128] = {0};
    snprintf(pSavePath, 128, "%s/%lld_bmp.dcm", CDicomParameter::instance().getStoragePath(), getCurrentTimestamp());
    if (baseIsFileExist(pSavePath))
    {
        return sendDicomFile(pSavePath);
    }

    Uint16 rows, cols;
    unsigned int length;

    DcmFileFormat fileformat;
    DcmDataset* mydatasete = fileformat.getDataset();

    unsigned char* currentData = NULL;
    unsigned char* pImageDat = NULL;
    int iDateLen = 0;
    int iCount = 0;

    for (int i = 0; i < iNumberOfFrames; ++i)
    {
        Dcm_InputFile* mdi = CDicomParameter::instance().getRGBDicomFile();
        if (NULL == mdi || mdi->pRGB == NULL)
        {
            break;
        }

        cols = mdi->iW;
        rows = mdi->iH;

        if (NULL == pImageDat)
        {
            pImageDat = (unsigned char*)malloc(rows * cols * 10 * iNumberOfFrames);
            if (NULL == pImageDat)
            {
                return EXITCODE_SETUID_FAILED;
            }
            memset(pImageDat, 0x0, rows * cols * 10 * iNumberOfFrames);
            currentData = pImageDat;
        }
        memcpy(currentData, mdi->pRGB, mdi->iLen);
        currentData += mdi->iLen;
        iDateLen += mdi->iLen;

        if (mdi != NULL)
        {
            if (mdi->pRGB != NULL)
            {
                free(mdi->pRGB);
                mdi->pRGB = NULL;
            }
            delete mdi;
            mdi = NULL;
        }
        ++iCount;
    };

    if (0 == iCount)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    if (pImageDat != NULL)
    {
        char pCount[128] = {0};
        sprintf(pCount, "%d", iCount);
        if (iCount > 1)
        {
            adStrValue(DCM_SOPClassUID, UID_UltrasoundMultiframeImageStorage);
            adStrValue(DCM_NumberOfFrames, pCount);
            adStrValue(DCM_StartTrim, "1");
            adStrValue(DCM_StopTrim, pCount);

            adStrValue(DCM_RecommendedDisplayFrameRate, CDicomParameter::instance().getFrameRate());
            adStrValue(DCM_CineRate, CDicomParameter::instance().getFrameRate());

            char strVar[48] = {0};
            int fps = atoi(CDicomParameter::instance().getFrameRate());

            float frameDurationMs = 1000.0f / fps;
            float effectiveDuration = iCount / (float)fps;
            float aActualFrameDuration = frameDurationMs + 0.5;

            sprintf(strVar, "%0.6f", effectiveDuration);
            adStrValue(DCM_EffectiveDuration, strVar);
            adStrValue(DCM_FrameDelay, strVar);
            sprintf(strVar, "%d", (int)aActualFrameDuration);
            adStrValue(DCM_ActualFrameDuration, strVar);
            sprintf(strVar, "%0.6f", frameDurationMs);
            adStrValue(DCM_FrameTime, strVar);
            adStrValue(DCM_FrameIncrementPointer, "(0018,1063)");
            adStrValue(DCM_PreferredPlaybackSequencing, "0");
        }
        else
        {
            adStrValue(DCM_SOPClassUID, UID_UltrasoundImageStorage);
        }

        AddPatientElementsToDicom(mydatasete);
        AddParameters(mydatasete);
        AddStudyElementsToDicom(mydatasete);
        AddSeriesElementsToDicom(mydatasete);
        addSSequence(mydatasete);
        adU16Value(DCM_PixelRepresentation, 0);
        adU16Value(DCM_SamplesPerPixel, 3);
        adU16Value(DCM_Rows, rows);
        adU16Value(DCM_Columns, cols);
        adU16Value(DCM_BitsAllocated, 8);
        adU16Value(DCM_BitsStored, 8);
        adU16Value(DCM_HighBit, 7);
        adU16Value(DCM_PlanarConfiguration, 0);

        mydatasete->putAndInsertUint8Array(DCM_PixelData, pImageDat, iDateLen);
        mydatasete->putAndInsertOFStringArray(DCM_PhotometricInterpretation, "RGB");

        OFCondition status;
        if (NULL == pSaveStoragePath)
        {
            status = fileformat.saveFile(pSavePath, EXS_LittleEndianExplicit);
            free(pImageDat) pImageDat = NULL;
            if (status.bad())
            {
                DCMNET_DEBUG("Error");
                return EXITCODE_INVALID_INPUT_FILE;
            }
            return sendDicomFile(pSavePath);
        }
        else
        {
            status = fileformat.saveFile(pSaveStoragePath, EXS_LittleEndianExplicit);
            free(pImageDat) pImageDat = NULL;
            if (status.bad())
            {
                DCMNET_DEBUG("Error");
                return EXITCODE_INVALID_INPUT_FILE;
            }
        }
    }
    return EXITCODE_INVALID_INPUT_FILE;
}

int createRLEFileMake(const char* pSaveStoragePath)
{
    int iNumberOfFrames = CDicomParameter::instance().getFileCounts();
    if (0 == iNumberOfFrames)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pSavePath[128] = {0};
    snprintf(pSavePath, 128, "%s/%lld_bmp.dcm", CDicomParameter::instance().getStoragePath(), getCurrentTimestamp());
    if (baseIsFileExist(pSavePath))
    {
        return sendDicomFile(pSavePath);
    }

    Uint16 rows, cols;
    unsigned int length;

    DcmFileFormat fileformat;
    DcmDataset* mydatasete = fileformat.getDataset();

    unsigned char* currentData = NULL;
    unsigned char* pImageDat = NULL;
    int iDateLen = 0;
    int iCount = 0;

    for (int i = 0; i < iNumberOfFrames; ++i)
    {
        Dcm_InputFile* mdi = CDicomParameter::instance().getRGBDicomFile();
        if (NULL == mdi || mdi->pRGB == NULL)
        {
            break;
        }

        rows = mdi->iW;
        cols = mdi->iH;

        if (NULL == pImageDat)
        {
            pImageDat = (unsigned char*)malloc(rows * cols * 10 * iNumberOfFrames);
            if (NULL == pImageDat)
            {
                return EXITCODE_SETUID_FAILED;
            }
            memset(pImageDat, 0x0, rows * cols * 10 * iNumberOfFrames);
            currentData = pImageDat;
        }
        memcpy(currentData, mdi->pRGB, mdi->iLen);
        currentData += mdi->iLen;
        iDateLen += mdi->iLen;

        if (mdi != NULL)
        {
            if (mdi->pRGB != NULL)
            {
                free(mdi->pRGB);
                mdi->pRGB = NULL;
            }
            delete mdi;
            mdi = NULL;
        }
        ++iCount;
    };

    if (0 == iCount)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    if (pImageDat != NULL)
    {
        char pCount[128] = {0};
        sprintf(pCount, "%d", iCount);

        if (iCount > 1)
        {
            adStrValue(DCM_SOPClassUID, UID_UltrasoundMultiframeImageStorage);
            adStrValue(DCM_NumberOfFrames, pCount);
            adStrValue(DCM_StartTrim, "1");
            adStrValue(DCM_StopTrim, pCount);

            adStrValue(DCM_RecommendedDisplayFrameRate, CDicomParameter::instance().getFrameRate());
            adStrValue(DCM_CineRate, CDicomParameter::instance().getFrameRate());

            char strVar[48] = {0};
            int fps = atoi(CDicomParameter::instance().getFrameRate());

            float frameDurationMs = 1000.0f / fps;
            float effectiveDuration = iCount / (float)fps;
            float aActualFrameDuration = frameDurationMs + 0.5;

            sprintf(strVar, "%0.6f", effectiveDuration);
            adStrValue(DCM_EffectiveDuration, strVar);
            adStrValue(DCM_FrameDelay, strVar);
            sprintf(strVar, "%d", (int)aActualFrameDuration);
            adStrValue(DCM_ActualFrameDuration, strVar);
            sprintf(strVar, "%0.6f", frameDurationMs);
            adStrValue(DCM_FrameTime, strVar);
            adStrValue(DCM_FrameIncrementPointer, "(0018,1063)");
            adStrValue(DCM_PreferredPlaybackSequencing, "0");
        }
        else
        {
            adStrValue(DCM_SOPClassUID, UID_UltrasoundImageStorage);
        }

        AddPatientElementsToDicom(mydatasete);
        AddParameters(mydatasete);
        AddStudyElementsToDicom(mydatasete);
        AddSeriesElementsToDicom(mydatasete);
        addSSequence(mydatasete);
        adU16Value(DCM_PixelRepresentation, 0);
        adU16Value(DCM_SamplesPerPixel, 3);
        adU16Value(DCM_Rows, rows);
        adU16Value(DCM_Columns, cols);
        adU16Value(DCM_BitsAllocated, 8);
        adU16Value(DCM_BitsStored, 8);
        adU16Value(DCM_HighBit, 7);
        adU16Value(DCM_PlanarConfiguration, 0);

        mydatasete->putAndInsertUint8Array(DCM_PixelData, pImageDat, iDateLen);
        mydatasete->putAndInsertOFStringArray(DCM_PhotometricInterpretation, "RGB");

        OFCondition status;
        if (NULL == pSaveStoragePath)
        {
            mydatasete->chooseRepresentation(EXS_RLELossless, NULL);
            if (mydatasete->canWriteXfer(EXS_RLELossless))
            {
                status = fileformat.saveFile(pSavePath, EXS_RLELossless);
            }
            else
            {
                status = fileformat.saveFile(pSavePath, EXS_LittleEndianExplicit);
            }
            delete pImageDat;
            pImageDat = NULL;
            return sendDicomFile(pSavePath);
        }
        else
        {
            mydatasete->chooseRepresentation(EXS_RLELossless, NULL);
            if (mydatasete->canWriteXfer(EXS_RLELossless))
            {
                status = fileformat.saveFile(pSavePath, EXS_RLELossless);
            }
            else
            {
                status = fileformat.saveFile(pSavePath, EXS_LittleEndianExplicit);
            }
            delete pImageDat;
            pImageDat = NULL;
            if (status.bad())
            {
                DCMNET_DEBUG("Error");
                return EXITCODE_INVALID_INPUT_FILE;
            }
        }
    }
    return EXITCODE_INVALID_INPUT_FILE;
}

int createBMPMake()
{
    int iNumberOfFrames = CDicomParameter::instance().getFileCounts();
    if (0 == iNumberOfFrames)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pSavePath[128] = {0};
    snprintf(pSavePath, 128, "%s/%lld_bmp.dcm", CDicomParameter::instance().getStoragePath(), getCurrentTimestamp());
    if (baseIsFileExist(pSavePath))
    {
        return sendDicomFile(pSavePath);
    }

    Uint16 rows, cols, samplePerPixel, bitsAlloc, bitsStored, highBit, pixelRpr, planConf, pixAspectH, pixAspectV;
    OFString photoMetrInt;
    Uint32 length;
    E_TransferSyntax ts;

    DcmFileFormat fileformat;
    DcmDataset* mydatasete = fileformat.getDataset();
    I2DBmpSource* bmpSource = new I2DBmpSource();

    unsigned char* currentData = NULL;
    unsigned char* pImageDat = NULL;
    int iDateLen = 0;
    int iCount = 0;

    for (int i = 0; i < iNumberOfFrames; ++i)
    {
        Dcm_InputFile* mdi = CDicomParameter::instance().getBMPDicomFile();
        if (NULL == mdi)
        {
            break;
        }

        OFString filename = mdi->PathName;
        I2DBmpSource* bmpSource = new I2DBmpSource();
        bmpSource->setImageFile(filename);

        char* pixData = NULL;
        bmpSource->readPixelData(rows, cols, samplePerPixel, photoMetrInt, bitsAlloc, bitsStored, highBit, pixelRpr,
                                 planConf, pixAspectH, pixAspectV, pixData, length, ts);
        if (NULL == pImageDat)
        {
            pImageDat = (unsigned char*)malloc(rows * cols * 10 * iNumberOfFrames);
            if (NULL == pImageDat)
            {
                return EXITCODE_SETUID_FAILED;
            }
            memset(pImageDat, 0x0, rows * cols * 10 * iNumberOfFrames);
            currentData = pImageDat;
        }
        memcpy(currentData, pixData, length);
        currentData += length;
        iDateLen += length;

        delete bmpSource;
        delete pixData;
        if (mdi != NULL)
        {
            if (mdi->pRGB != NULL)
            {
                free(mdi->pRGB);
                mdi->pRGB = NULL;
            }
            delete mdi;
            mdi = NULL;
        }
        pixData = NULL;
        bmpSource = NULL;
        ++iCount;
    };

    if (0 == iCount)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    if (pImageDat != NULL)
    {
        char pCount[128] = {0};
        sprintf(pCount, "%d", iCount);
        AddPatientElementsToDicom(mydatasete);
        AddStudyElementsToDicom(mydatasete);
        AddParameters(mydatasete);
        AddSeriesElementsToDicom(mydatasete);
        // adStrValue(DCM_InstanceNumber, pCount);
        adU16Value(DCM_PixelRepresentation, 0);
        adStrValue(DCM_CineRate, CDicomParameter::instance().getFrameRate());
        adStrValue(DCM_NumberOfFrames, pCount);
        adU16Value(DCM_SamplesPerPixel, samplePerPixel);
        adU16Value(DCM_Rows, rows);
        adU16Value(DCM_Columns, cols);
        adU16Value(DCM_BitsAllocated, bitsAlloc);
        adU16Value(DCM_BitsStored, bitsStored);
        adU16Value(DCM_HighBit, highBit);
        adU16Value(DCM_PlanarConfiguration, planConf);

        mydatasete->putAndInsertUint8Array(DCM_PixelData, pImageDat, iDateLen);
        // adStrValue(DCM_UltrasoundColorDataPresent, pCount);
        mydatasete->putAndInsertOFStringArray(DCM_PhotometricInterpretation, photoMetrInt);
        OFCondition status = fileformat.saveFile(pSavePath, ts);
        delete pImageDat;
        pImageDat = NULL;

        if (status.bad())
        {
            DCMNET_DEBUG("Error");
            return EXITCODE_INVALID_INPUT_FILE;
        }

        return sendDicomFile(pSavePath);
    }
    return EXITCODE_INVALID_INPUT_FILE;
}

int createJpegFileMake()
{
    int iNumberOfFrames = CDicomParameter::instance().getFileCounts();
    if (0 == iNumberOfFrames)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pSavePath[128] = {0};
    snprintf(pSavePath, 128, "%s/%lld_jpeg.dcm", CDicomParameter::instance().getStoragePath(), getCurrentTimestamp());
    if (baseIsFileExist(pSavePath))
    {
        return sendDicomFile(pSavePath);
    }
    DcmFileFormat fileformat;
    DcmDataset* mydatasete = fileformat.getDataset();
    DcmPixelSequence* seq = new DcmPixelSequence(DcmTag(DCM_PixelData, EVR_OB));
    Uint16 rows, cols, samplePerPixel, bitsAlloc, bitsStored, highBit, pixelRpr, planConf, pixAspectH, pixAspectV;
    OFString photoMetrInt;
    Uint32 length;
    E_TransferSyntax ts;
    seq->insert(new DcmPixelItem(DcmTag(DCM_Item, EVR_OB)));
    int iCount = 0;

    for (int i = 0; i < iNumberOfFrames; ++i)
    {
        Dcm_InputFile* mdi = CDicomParameter::instance().getJPEGDicomFile();
        if (NULL == mdi)
        {
            break;
        }

        I2DJpegSource* bmpSource = new I2DJpegSource();
        bmpSource->setImageFile(mdi->PathName);

        char* pixData = NULL;
        bmpSource->readPixelData(rows, cols, samplePerPixel, photoMetrInt, bitsAlloc, bitsStored, highBit, pixelRpr,
                                 planConf, pixAspectH, pixAspectV, pixData, length, ts);

        DcmPixelItem* newItem = new DcmPixelItem(DcmTag(DCM_Item, EVR_OB));
        if (newItem != NULL)
        {
            seq->insert(newItem);
            OFCondition result = newItem->putUint8Array((Uint8*)pixData, length);
            ++iCount;
        }
        delete bmpSource;
        delete pixData;
        if (mdi != NULL)
        {
            if (mdi->pRGB != NULL)
            {
                free(mdi->pRGB);
                mdi->pRGB = NULL;
            }
            delete mdi;
            mdi = NULL;
        }

        bmpSource = NULL;
        pixData = NULL;
    };
    if (iCount == 0)
    {
        return EXITCODE_NO_INPUT_FILES;
    }

    char pCount[128] = {0};
    sprintf(pCount, "%d", iCount);
    AddPatientElementsToDicom(mydatasete);
    AddParameters(mydatasete);
    AddStudyElementsToDicom(mydatasete);
    AddSeriesElementsToDicom(mydatasete);

    // adStrValue(DCM_InstanceNumber, pCount);
    adU16Value(DCM_PixelRepresentation, 0);
    adU16Value(DCM_SamplesPerPixel, 3);
    adStrValue(DCM_CineRate, CDicomParameter::instance().getFrameRate());
    adStrValue(DCM_NumberOfFrames, pCount);
    adU16Value(DCM_SamplesPerPixel, samplePerPixel);
    adU16Value(DCM_Rows, rows);
    adU16Value(DCM_Columns, cols);
    adU16Value(DCM_BitsAllocated, bitsAlloc);
    adU16Value(DCM_BitsStored, bitsStored);
    adU16Value(DCM_HighBit, highBit);
    adU16Value(DCM_PlanarConfiguration, planConf);
    mydatasete->putAndInsertOFStringArray(DCM_PhotometricInterpretation, photoMetrInt);
    // adStrValue(DCM_UltrasoundColorDataPresent, pCount);

    mydatasete->insert(seq, OFFalse, OFFalse);

    OFCondition status = fileformat.saveFile(pSavePath, ts);
    if (status.bad())
    {
        DCMNET_DEBUG("Error");
        return EXITCODE_INVALID_INPUT_FILE;
    }
    return sendDicomFile(pSavePath);
}

int sendDicomFile(const char* pPath)
{
    bool bRet = dcmsend(pPath);
    if (baseIsFileExist(pPath))
    {
        baseDeleteFile(pPath);
    }
    return bRet;
}

typedef struct sockaddr SA;

static inline SA* sockaddr_cast(struct sockaddr_in* addr)
{
    return reinterpret_cast<SA*>(addr);
}
static inline SA const* sockaddr_cast(struct sockaddr_in const* addr)
{
    return reinterpret_cast<SA const*>(addr);
}

bool connectBySocket(int iSoecket, const char* pSendtoAddr, unsigned short usSendtoPort)
{
    if ((NULL == pSendtoAddr) || (usSendtoPort < 1))
    {
        return false;
    }

    struct sockaddr_in siAddr;
    bzero(&siAddr, sizeof(siAddr));
    siAddr.sin_family = AF_INET;
    siAddr.sin_port = htons(usSendtoPort);
    siAddr.sin_addr.s_addr = ::inet_addr(pSendtoAddr);
    socklen_t addrlen = sizeof siAddr;

    int iReturn = ::connect(iSoecket, sockaddr_cast(&siAddr), addrlen);
    if (-1 == iReturn)
    {
        return false;
    }
    return true;
}

int connectServer(int sock_fd, const char* ip, int port)
{
    struct sockaddr_in servaddr;
    bzero(&servaddr, sizeof(servaddr));
    servaddr.sin_family = AF_INET;
    servaddr.sin_port = htons(port);
    inet_aton(ip, &servaddr.sin_addr);

    fcntl(sock_fd, F_SETFL, fcntl(sock_fd, F_GETFL, 0) | O_NONBLOCK);
    int connected = connect(sock_fd, (struct sockaddr*)&servaddr, sizeof(struct sockaddr_in));
    int ret = -1;
    if (connected != 0)
    {
        if (errno != EINPROGRESS)
            DebugPrintf("connect error :%s", strerror(errno));
        else
        {
            struct timeval tm = {atoi(CDicomParameter::instance().getOptTimeout()) + 2, 0};
            fd_set wset, rset;
            FD_ZERO(&wset);
            FD_ZERO(&rset);
            FD_SET(sock_fd, &wset);
            FD_SET(sock_fd, &rset);
            long t1 = time(NULL);
            ret = select(sock_fd + 1, &rset, &wset, NULL, &tm);
            long t2 = time(NULL);
            DebugPrintf("interval time: %d", t2 - t1);
            DebugPrintf("ret: %d", ret);
            if (ret < 0)
            {
                DebugPrintf("network error in connect\n");
            }
            else if (ret == 0)
            {
                DebugPrintf("connect time out\n");
            }
            else if (ret > 0)
            {
                //                ret = -1;
                //                if(FD_ISSET(sock_fd,&wset))
                //                {
                //                    fcntl(sock_fd,F_SETFL,fcntl(sock_fd,F_GETFL,0) & ~O_NONBLOCK);
                //                    const char* pValue = "testValue";

                //                    int iTimeOut = atoi(CDicomParameter::instance().getOptTimeout()) + 2;
                //                    ANDLOG("iSoecket iTimeOut = [%d]\n", iTimeOut);
                //                    struct timeval timeout={iTimeOut, 0};
                //                    setsockopt(sock_fd, SOL_SOCKET, SO_SNDTIMEO, (const char*)&timeout,
                //                    sizeof(timeout)); setsockopt(sock_fd, SOL_SOCKET, SO_RCVTIMEO, (const
                //                    char*)&timeout, sizeof(timeout)); int iBit = ::send(sock_fd, pValue,
                //                    strlen(pValue), 0); ANDLOG("iSoecket send = [%d]\n", iBit); if (iBit > 0)
                //                    {
                //                        char pValue2[256] = {0};
                //                        ret = 0;
                //                        iBit = ::recv(sock_fd, pValue2, 256, 0);
                //                        ANDLOG("iSoecket recv = [%d]\n", iBit);
                //                        if (iBit > 0)
                //                        {
                //                            ANDLOG("iSoecket recv Value = [%s]\n", pValue2);
                //                        }
                //                    }
                //                    else
                //                    {
                //                        ANDLOG("other error when iBit:%d\n", iBit);
                //                    }
                //                }
                //                else
                //                {
                //                    ANDLOG("other error when select:%s\n",strerror(errno));
                //                }
            }
        }
    }
    else
    {
        DebugPrintf("connect :%d\n", connected);
    }

    DebugPrintf("return res: %d\n", ret);
    return ret;
}

bool isDicomServerConnect(const char* pAetValue, const char* pAeCValue)
{
    if (strnlen(pAetValue, 256) > 0 && strnlen(pAeCValue, 256) > 0)
    {
        int argc = 10;
        char* argv[] = {"--version",
                        "-aet",
                        (char*)pAetValue,
                        "-aec",
                        (char*)pAeCValue,
                        CDicomParameter::instance().getServerIP(),
                        CDicomParameter::instance().getServerPort(),
                        "-v",
                        "--timeout",
                        CDicomParameter::instance().getOptTimeout()};
        return (EXITCODE_NO_ERROR == isScuConnect(argc, argv));
    }
    else if (strnlen(pAetValue, 256) > 0)
    {
        int argc = 8;
        char* argv[] = {"--version",
                        "-aet",
                        (char*)pAetValue,
                        CDicomParameter::instance().getServerIP(),
                        CDicomParameter::instance().getServerPort(),
                        "-v",
                        "--timeout",
                        CDicomParameter::instance().getOptTimeout()};
        return (EXITCODE_NO_ERROR == isScuConnect(argc, argv));
    }
    else if (strnlen(pAeCValue, 256) > 0)
    {
        int argc = 8;
        char* argv[] = {"--version",
                        "-aec",
                        (char*)pAeCValue,
                        CDicomParameter::instance().getServerIP(),
                        CDicomParameter::instance().getServerPort(),
                        "-v",
                        "--timeout",
                        CDicomParameter::instance().getOptTimeout()};
        return (EXITCODE_NO_ERROR == isScuConnect(argc, argv));
    }
    else
    {
        return false;
    }
}

int dcmsend(const char* pPath)
{
    if (!baseIsFileExist(pPath))
    {
        return EXITCODE_NO_INPUT_FILES;
    }
    OFOStringStream optStream;
    OFOStringStream optStreamCOUT;
    int argc = 8;
    char* argv[] = {"--verbose",
                    CDicomParameter::instance().getServerIP(),
                    CDicomParameter::instance().getServerPort(),
                    const_cast<char*>(pPath),
                    "-aet",
                    CDicomParameter::instance().getOptAeTitle(),
                    "-aec",
                    CDicomParameter::instance().getOptAcTitle()};

    const char* opt_peer = NULL;
    const char* opt_peerTitle = PEERAPPLICATIONTITLE;
    const char* opt_ourTitle = APPLICATIONTITLE;

    E_FileReadMode opt_readMode = ERM_fileOnly;

    OFCmdUnsignedInt opt_port = 0;
    OFCmdUnsignedInt opt_timeout = atoi(CDicomParameter::instance().getOptTimeout());
    OFCmdUnsignedInt opt_dimseTimeout = atoi(CDicomParameter::instance().getOptDimseTimeout());
    OFCmdUnsignedInt opt_acseTimeout = atoi(CDicomParameter::instance().getOptAcseTimeout());
    OFCmdUnsignedInt opt_maxReceivePDULength = ASC_DEFAULTMAXPDU;
    OFCmdUnsignedInt opt_maxSendPDULength = 0;
    T_DIMSE_BlockingMode opt_blockMode = DIMSE_BLOCKING;
#ifdef WITH_ZLIB
    OFCmdUnsignedInt opt_compressionLevel = 0;
#endif

    OFBool opt_showPresentationContexts = OFFalse;
    OFBool opt_haltOnInvalidFile = OFTrue;
    OFBool opt_haltOnUnsuccessfulStore = OFTrue;
    OFBool opt_allowIllegalProposal = OFTrue;
    OFBool opt_checkUIDValues = OFTrue;
    OFBool opt_multipleAssociations = OFTrue;
    DcmStorageSCU::E_DecompressionMode opt_decompressionMode = DcmStorageSCU::DM_losslessOnly;

    OFBool opt_dicomDir = OFFalse;
    OFBool opt_scanDir = OFFalse;
    OFBool opt_recurse = OFFalse;
    const char* opt_scanPattern = "";
    const char* opt_reportFilename = "";

    int paramCount = 0;

    OFConsoleApplication app(OFFIS_CONSOLE_APPLICATION, "Simple DICOM storage SCU (sender)", rcsid);
    OFCommandLine cmd;

    cmd.setParamColumn(LONGCOL + SHORTCOL + 4);
    cmd.addParam("peer", "hostname of DICOM peer");
    cmd.addParam("port", "tcp/ip port number of peer");
    cmd.addParam("dcmfile-in", "DICOM file or directory to be transmitted", OFCmdParam::PM_MultiMandatory);

    cmd.setOptionColumns(LONGCOL, SHORTCOL);
    cmd.addGroup("general options:", LONGCOL, SHORTCOL + 2);
    cmd.addOption("--help", "-h", "print this help text and exit", OFCommandLine::AF_Exclusive);
    cmd.addOption("--version", "print version information and exit", OFCommandLine::AF_Exclusive);
    cmd.addOption("--list-decoders", "list transfer syntaxes of decoders and exit", OFCommandLine::AF_Exclusive);
    OFLog::addOptions(cmd);
    cmd.addOption("--verbose-pc", "+v", "show presentation contexts in verbose mode");

    cmd.addGroup("input options:");
    cmd.addSubGroup("input file format:");
    cmd.addOption("--read-file", "+f", "read file format or data set");
    cmd.addOption("--read-file-only", "+fo", "read file format only (default)");
    cmd.addOption("--read-dataset", "-f", "read data set without file meta information");
    cmd.addSubGroup("input files:");
    cmd.addOption("--read-from-dicomdir", "+rd", "read information on input files from DICOMDIR");
    cmd.addOption("--scan-directories", "+sd", "scan directories for input files (dcmfile-in)");
#ifdef PATTERN_MATCHING_AVAILABLE
    cmd.addOption("--scan-pattern", "+sp", 1, "[p]attern: string (only w/ --scan-directories)",
                  "pattern for filename matching (wildcards)");
#endif
    cmd.addOption("--no-recurse", "-r", "do not recurse within directories (default)");
    cmd.addOption("--recurse", "+r", "recurse within specified directories");

    cmd.addGroup("processing options:");
    cmd.addSubGroup("transfer syntax conversion:");
    cmd.addOption("--decompress-never", "-dn", "never decompress compressed data sets");
    cmd.addOption("--decompress-lossless", "+dls", "only decompress lossless compression (default)");
    cmd.addOption("--decompress-lossy", "+dly", "decompress both lossy and lossless compression");
#ifdef WITH_ZLIB
    cmd.addSubGroup("deflate compression level:");
    cmd.addOption("--compression-level", "+cl", 1, "[l]evel: integer (default: 6)",
                  "0=uncompressed, 1=fastest, 9=best compression");
#endif
    cmd.addSubGroup("other processing options:");
    cmd.addOption("--no-halt", "-nh", "do not halt on first invalid input file\nor if unsuccessful store encountered");
    cmd.addOption("--no-illegal-proposal", "-nip",
                  "do not propose any presentation context that\ndoes not contain the default TS (if needed)");
    cmd.addOption("--no-uid-checks", "-nuc", "do not check UID values of input files");

    cmd.addGroup("network options:");
    cmd.addSubGroup("application entity titles:");
    cmd.addOption("--aetitle", "-aet", 1, "[a]etitle: string",
                  "set my calling AE title (default: " APPLICATIONTITLE ")");
    cmd.addOption("--call", "-aec", 1, "[a]etitle: string",
                  "set called AE title of peer (default: " PEERAPPLICATIONTITLE ")");
    cmd.addSubGroup("association handling:");
    cmd.addOption("--multi-associations", "+ma",
                  "use multiple associations (one after the other)\nif needed to transfer the instances (default)");
    cmd.addOption("--single-association", "-ma", "always use a single association");
    cmd.addSubGroup("other network options:");
    cmd.addOption("--timeout", "-to", 1, "[s]econds: integer (default: unlimited)", "timeout for connection requests");
    CONVERT_TO_STRING("[s]econds: integer (default: " << opt_acseTimeout << ")", optString1);
    cmd.addOption("--acse-timeout", "-ta", 1, optString1.c_str(), "timeout for ACSE messages");
    cmd.addOption("--dimse-timeout", "-td", 1, "[s]econds: integer (default: unlimited)", "timeout for DIMSE messages");
    CONVERT_TO_STRING("[n]umber of bytes: integer (" << ASC_MINIMUMPDUSIZE << ".." << ASC_MAXIMUMPDUSIZE << ")",
                      optString2);
    CONVERT_TO_STRING("set max receive pdu to n bytes (default: " << opt_maxReceivePDULength << ")", optString3);
    cmd.addOption("--max-pdu", "-pdu", 1, optString2.c_str(), optString3.c_str());
    cmd.addOption("--max-send-pdu", 1, optString2.c_str(), "restrict max send pdu to n bytes");
    cmd.addGroup("output options:");
    cmd.addSubGroup("general:");
    cmd.addOption("--create-report-file", "+crf", 1, "[f]ilename: string",
                  "create a detailed report on the transfer\n(if successful) and write it to text file f");

    /* evaluate command line */
    prepareCmdLineArgs(argc, argv, OFFIS_CONSOLE_APPLICATION);
    if (app.parseCommandLine(cmd, argc, argv))
    {
        /* check exclusive options first */
        if (cmd.hasExclusiveOption())
        {
            if (cmd.findOption("--version"))
            {
                app.printHeader(OFTrue /*print host identifier*/);
                optStreamCOUT << OFendl << "External libraries used:" << OFendl;
#ifdef WITH_ZLIB
                optStreamCOUT << "- ZLIB, Version " << zlibVersion() << OFendl;
#endif
                optStreamCOUT << "- " << DiJPEGPlugin::getLibraryVersionString() << OFendl;
                optStreamCOUT << "- " << DJLSDecoderRegistration::getLibraryVersionString() << OFendl;
                return EXITCODE_NO_ERROR;
            }
            if (cmd.findOption("--list-decoders"))
            {
                app.printHeader(OFFalse /*print host identifier*/);
                optStreamCOUT << OFendl << "Transfer syntaxes supported natively:" << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_LittleEndianImplicit).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_LittleEndianExplicit).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_BigEndianExplicit).getXferName() << OFendl;
                optStreamCOUT << OFendl << "Transfer syntaxes supported by decoders:" << OFendl;
#ifdef WITH_ZLIB
                optStreamCOUT << "- " << DcmXfer(EXS_DeflatedLittleEndianExplicit).getXferName() << OFendl;
#endif
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess1).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess2_4).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess6_8).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess10_12).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess14).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGProcess14SV1).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGLSLossless).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_JPEGLSLossy).getXferName() << OFendl;
                optStreamCOUT << "- " << DcmXfer(EXS_RLELossless).getXferName() << OFendl;
                return EXITCODE_NO_ERROR;
            }
        }

        /* general options */
        OFLog::configureFromCommandLine(cmd, app);
        if (cmd.findOption("--verbose-pc"))
        {
            app.checkDependence("--verbose-pc", "verbose mode", dcmsendLogger.isEnabledFor(OFLogger::INFO_LOG_LEVEL));
            opt_showPresentationContexts = OFTrue;
        }

        /* input options */
        cmd.beginOptionBlock();
        if (cmd.findOption("--read-file"))
            opt_readMode = ERM_autoDetect;
        if (cmd.findOption("--read-file-only"))
            opt_readMode = ERM_fileOnly;
        if (cmd.findOption("--read-dataset"))
            opt_readMode = ERM_dataset;
        cmd.endOptionBlock();

        if (cmd.findOption("--read-from-dicomdir"))
            opt_dicomDir = OFTrue;
        if (cmd.findOption("--scan-directories"))
            opt_scanDir = OFTrue;
#ifdef PATTERN_MATCHING_AVAILABLE
        if (cmd.findOption("--scan-pattern"))
        {
            app.checkDependence("--scan-pattern", "--scan-directories", opt_scanDir);
            app.checkValue(cmd.getValue(opt_scanPattern));
        }
#endif
        cmd.beginOptionBlock();
        if (cmd.findOption("--no-recurse"))
            opt_recurse = OFFalse;
        if (cmd.findOption("--recurse"))
        {
            app.checkDependence("--recurse", "--scan-directories", opt_scanDir);
            opt_recurse = OFTrue;
        }
        cmd.endOptionBlock();

        /* processing options */
        cmd.beginOptionBlock();
        if (cmd.findOption("--decompress-never"))
            opt_decompressionMode = DcmStorageSCU::DM_never;
        if (cmd.findOption("--decompress-lossless"))
            opt_decompressionMode = DcmStorageSCU::DM_losslessOnly;
        if (cmd.findOption("--decompress-lossy"))
            opt_decompressionMode = DcmStorageSCU::DM_lossyAndLossless;
        cmd.endOptionBlock();
#ifdef WITH_ZLIB
        if (cmd.findOption("--compression-level"))
        {
            app.checkValue(cmd.getValueAndCheckMinMax(opt_compressionLevel, 0, 9));
            dcmZlibCompressionLevel.set(OFstatic_cast(int, opt_compressionLevel));
        }
#endif
        if (cmd.findOption("--no-halt"))
        {
            opt_haltOnInvalidFile = OFFalse;
            opt_haltOnUnsuccessfulStore = OFFalse;
        }
        if (cmd.findOption("--no-illegal-proposal"))
            opt_allowIllegalProposal = OFFalse;
        if (cmd.findOption("--no-uid-checks"))
            opt_checkUIDValues = OFFalse;

        /* network options */
        if (cmd.findOption("--aetitle"))
            app.checkValue(cmd.getValue(opt_ourTitle));
        if (cmd.findOption("--call"))
            app.checkValue(cmd.getValue(opt_peerTitle));

        cmd.beginOptionBlock();
        if (cmd.findOption("--multi-associations"))
            opt_multipleAssociations = OFTrue;
        if (cmd.findOption("--single-association"))
            opt_multipleAssociations = OFFalse;
        cmd.endOptionBlock();

        if (cmd.findOption("--timeout"))
        {
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            dcmConnectionTimeout.set(OFstatic_cast(Sint32, opt_timeout));
        }
        if (cmd.findOption("--acse-timeout"))
            app.checkValue(cmd.getValueAndCheckMin(opt_acseTimeout, 1));
        if (cmd.findOption("--dimse-timeout"))
        {
            app.checkValue(cmd.getValueAndCheckMin(opt_dimseTimeout, 1));
            opt_blockMode = DIMSE_NONBLOCKING;
        }
        if (cmd.findOption("--max-pdu"))
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxReceivePDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
        if (cmd.findOption("--max-send-pdu"))
        {
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxSendPDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
            dcmMaxOutgoingPDUSize.set(OFstatic_cast(Uint32, opt_maxSendPDULength));
        }

        /* output options */
        if (cmd.findOption("--create-report-file"))
            app.checkValue(cmd.getValue(opt_reportFilename));

        /* command line parameters */
        paramCount = cmd.getParamCount();
        cmd.getParam(1, opt_peer);
        app.checkParam(cmd.getParamAndCheckMinMax(2, opt_port, 1, 65535));
    }

    /* print resource identifier */
    OFLOG_DEBUG(dcmsendLogger, rcsid << OFendl);

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        OFLOG_WARN(dcmsendLogger,
                   "no data dictionary loaded, check environment variable: " << DCM_DICT_ENVIRONMENT_VARIABLE);
    }

    /* start with the real work */
    if (opt_scanDir)
        OFLOG_INFO(dcmsendLogger, "determining input files ...");
    /* iterate over all input filenames/directories */
    OFList<OFString> inputFiles;
    const char* paramString = NULL;
    for (int i = 3; i <= paramCount; i++)
    {
        cmd.getParam(i, paramString);
        /* search directory recursively (if required) */
        if (OFStandard::dirExists(paramString))
        {
            if (opt_scanDir)
                OFStandard::searchDirectoryRecursively(paramString, inputFiles, opt_scanPattern, "" /* dirPrefix */,
                                                       opt_recurse);
            else
                OFLOG_WARN(dcmsendLogger,
                           "ignoring directory because option --scan-directories is not set: " << paramString);
        }
        else
            inputFiles.push_back(paramString);
    }

    /* check whether there are any input files at all */
    if (inputFiles.empty())
    {
        OFLOG_FATAL(dcmsendLogger, "no input files to be processed");
        cleanup();
        return EXITCODE_NO_INPUT_FILES;
    }

    DcmStorageSCU storageSCU;
    OFCondition status;
    unsigned long numInvalidFiles = 0;

    /* set parameters used for processing the input files */
    storageSCU.setReadFromDICOMDIRMode(opt_dicomDir);
    storageSCU.setHaltOnInvalidFileMode(opt_haltOnInvalidFile);

    OFLOG_INFO(dcmsendLogger, "checking input files ...");
    /* iterate over all input filenames */
    const char* currentFilename = NULL;
    OFListIterator(OFString) if_iter = inputFiles.begin();
    OFListIterator(OFString) if_last = inputFiles.end();
    while (if_iter != if_last)
    {
        currentFilename = (*if_iter).c_str();
        /* and add them to the list of instances to be transmitted */
        status = storageSCU.addDicomFile(currentFilename, opt_readMode, opt_checkUIDValues);
        if (status.bad())
        {
            /* check for empty filename */
            if (strlen(currentFilename) == 0)
                currentFilename = "<empty string>";
            /* if something went wrong, we either terminate or ignore the file */
            if (opt_haltOnInvalidFile)
            {
                OFLOG_FATAL(dcmsendLogger, "bad DICOM file: " << currentFilename << ": " << status.text());
                cleanup();
                return EXITCODE_INVALID_INPUT_FILE;
            }
            else
            {
                OFLOG_WARN(dcmsendLogger,
                           "bad DICOM file: " << currentFilename << ": " << status.text() << ", ignoring file");
            }
            ++numInvalidFiles;
        }
        ++if_iter;
    }

    /* check whether there are any valid input files */
    if (storageSCU.getNumberOfSOPInstances() == 0)
    {
        OFLOG_FATAL(dcmsendLogger, "no valid input files to be processed");
        cleanup();
        return EXITCODE_NO_VALID_INPUT_FILES;
    }
    else
    {
        OFLOG_DEBUG(dcmsendLogger, "in total, there are " << storageSCU.getNumberOfSOPInstances()
                                                          << " SOP instances to be sent, " << numInvalidFiles
                                                          << " invalid files are ignored");
    }

    /* set network parameters */
    storageSCU.setPeerHostName(opt_peer);
    storageSCU.setPeerPort(OFstatic_cast(Uint16, opt_port));
    storageSCU.setPeerAETitle(opt_peerTitle);
    storageSCU.setAETitle(opt_ourTitle);
    storageSCU.setMaxReceivePDULength(OFstatic_cast(Uint32, opt_maxReceivePDULength));
    storageSCU.setACSETimeout(OFstatic_cast(Uint32, opt_acseTimeout));
    storageSCU.setDIMSETimeout(OFstatic_cast(Uint32, opt_dimseTimeout));
    storageSCU.setDIMSEBlockingMode(opt_blockMode);
    storageSCU.setVerbosePCMode(opt_showPresentationContexts);
    storageSCU.setDatasetConversionMode(opt_decompressionMode != DcmStorageSCU::DM_never);
    storageSCU.setDecompressionMode(opt_decompressionMode);
    storageSCU.setHaltOnUnsuccessfulStoreMode(opt_haltOnUnsuccessfulStore);
    storageSCU.setAllowIllegalProposalMode(opt_allowIllegalProposal);

    /* output information on the single/multiple associations setting */
    if (opt_multipleAssociations)
    {
        OFLOG_DEBUG(dcmsendLogger, "multiple associations allowed (option --multi-associations used)");
    }
    else
    {
        OFLOG_DEBUG(dcmsendLogger, "only a single associations allowed (option --single-association used)");
    }

    /* add presentation contexts to be negotiated (if there are still any) */
    while ((status = storageSCU.addPresentationContexts()).good())
    {
        if (opt_multipleAssociations)
        {
            /* output information on the start of the new association */
            if (dcmsendLogger.isEnabledFor(OFLogger::DEBUG_LOG_LEVEL))
            {
                OFLOG_DEBUG(dcmsendLogger, OFString(65, '-') << OFendl << "starting association #"
                                                             << (storageSCU.getAssociationCounter() + 1));
            }
            else
            {
                OFLOG_INFO(dcmsendLogger, "starting association #" << (storageSCU.getAssociationCounter() + 1));
            }
        }
        OFLOG_INFO(dcmsendLogger, "initializing network ...");
        /* initialize network */
        status = storageSCU.initNetwork();
        if (status.bad())
        {
            OFLOG_FATAL(dcmsendLogger, "cannot initialize network: " << status.text());
            cleanup();
            return EXITCODE_CANNOT_INITIALIZE_NETWORK;
        }

        OFLOG_INFO(dcmsendLogger, "negotiating network association ...");
        /* negotiate network association with peer */
        status = storageSCU.negotiateAssociation();
        if (status.bad())
        {
            // check whether we can continue with a new association
            if (status == NET_EC_NoAcceptablePresentationContexts)
            {
                OFLOG_ERROR(dcmsendLogger, "cannot negotiate network association: " << status.text());
                // check whether there are any SOP instances to be sent
                const size_t numToBeSent = storageSCU.getNumberOfSOPInstancesToBeSent();
                if (numToBeSent > 0)
                {
                    OFLOG_WARN(dcmsendLogger, "trying to continue with a new association "
                                                  << "in order to send the remaining " << numToBeSent
                                                  << " SOP instances");
                }
            }
            else
            {
                OFLOG_FATAL(dcmsendLogger, "cannot negotiate network association: " << status.text());
                cleanup();
                return EXITCODE_CANNOT_NEGOTIATE_ASSOCIATION;
            }
        }
        if (status.good())
        {
            OFLOG_INFO(dcmsendLogger, "sending SOP instances ...");
            /* send SOP instances to be transferred */
            status = storageSCU.sendSOPInstances();
            if (status.bad())
            {
                OFLOG_FATAL(dcmsendLogger, "cannot send SOP instance: " << status.text());
                // handle certain error conditions (initiated by the communication peer)
                if (status == DUL_PEERREQUESTEDRELEASE)
                {
                    // peer requested release (aborting)
                    storageSCU.closeAssociation(DCMSCU_PEER_REQUESTED_RELEASE);
                }
                else if (status == DUL_PEERABORTEDASSOCIATION)
                {
                    // peer aborted the association
                    storageSCU.closeAssociation(DCMSCU_PEER_ABORTED_ASSOCIATION);
                }
                cleanup();
                return EXITCODE_CANNOT_SEND_REQUEST;
            }
        }
        /* close current network association */
        storageSCU.releaseAssociation();
        /* check whether multiple associations are permitted */
        if (!opt_multipleAssociations)
            break;
    }

    /* if anything went wrong, report it to the logger */
    if (status.bad() && (status != NET_EC_NoPresentationContextsDefined))
    {
        OFLOG_ERROR(dcmsendLogger, "cannot add presentation contexts: " << status.text());
        cleanup();
        return EXITCODE_CANNOT_ADD_PRESENTATION_CONTEXT;
    }

    /* create a detailed report on the transfer of instances ... */
    if ((opt_reportFilename != NULL) && (strlen(opt_reportFilename) > 0))
    {
        /* ... and write it to the specified text file */
        status = storageSCU.createReportFile(opt_reportFilename);
        if (status.bad())
        {
            cleanup();
            return EXITCODE_CANNOT_WRITE_REPORT_FILE; // TODO: do we really want to exit?
        }
    }

    /* output some status information on the overall sending process */
    if (dcmsendLogger.isEnabledFor(OFLogger::INFO_LOG_LEVEL))
    {
        OFString summaryText;
        storageSCU.getStatusSummary(summaryText);
        OFLOG_INFO(dcmsendLogger, OFendl << summaryText);
    }

    /* make sure that everything is cleaned up properly */
    cleanup();
    return EXITCODE_NO_ERROR;
}

const char* findScuWorkList(const char* pInValue)
{
    if (NULL == pInValue)
    {
        DebugPrintf("NULL == pInValue");
        return "";
    }

    Json jsCandidate(pInValue);

    int iArgcCount = 0;
    char** argStr = new char*[128];
    FOR(128)
    {
        argStr[i] = NULL;
    }

    char pModalityValue[64] = {0};
    char pScheduledProcedureStepStartDateValue[64] = {0};
    char pScheduledProcedureStepEndDateValue[64] = {0};

    FOR(128)
    {
        if (jsCandidate[i].c_str() != NULL && jsCandidate[i].isKeyExist("Target"))
        {
            const char* strTaget = jsCandidate[i]["Target"].toStr();
            if (strTaget != NULL)
            {
                argStr[iArgcCount] = const_cast<char*>(strTaget);
                ++iArgcCount;
            }
            const char* strValue = jsCandidate[i]["Value"].toStr();
            if (strValue != NULL && !baseStrcmp(strValue, "None"))
            {
                string str = strValue;
                if (0)
                {
                    // DebugPrintf("str = [%s]", str.c_str());
                    if (str.find("Modality=") != std::string::npos)
                    {
                        //                        baseStrncpy(pModalityValue, str.c_str() + strlen("Modality="), 64);
                    }
                    if (str.find("ScheduledProcedureStepStartDate=") != std::string::npos)
                    {
                        //                        baseStrncpy(pScheduledProcedureStepStartDateValue,
                        //                                    str.c_str() + strlen("ScheduledProcedureStepStartDate="),
                        //                                    64);
                    }
                    if (str.find("ScheduledProcedureStepEndDate=") != std::string::npos)
                    {
                        //                        baseStrncpy(pScheduledProcedureStepEndDateValue,
                        //                                    str.c_str() + strlen("ScheduledProcedureStepEndDate="),
                        //                                    64);
                    }
                    iArgcCount--;
                }
                else
                {
                    argStr[iArgcCount] = const_cast<char*>(strValue);
                    ++iArgcCount;
                }
            }
        }
    }

    OFList<OFString> fileNameList;
    OFBool opt_abortAssociation = OFFalse;
    const char* opt_abstractSyntax = UID_FINDModalityWorklistInformationModel;
    int opt_acse_timeout = 30;
    T_DIMSE_BlockingMode opt_blockMode = DIMSE_BLOCKING;
    OFCmdSignedInt opt_cancelAfterNResponses = -1;
    int opt_dimse_timeout = 0;
    int opt_outputResponsesToLogger = 0;
    DcmFindSCUExtractMode opt_extractResponses = FEM_none;
    OFString opt_extractXMLFilename;
    OFString opt_outputDirectory = ".";
    OFCmdUnsignedInt opt_maxReceivePDULength = ASC_DEFAULTMAXPDU;
    E_TransferSyntax opt_networkTransferSyntax = EXS_Unknown;
    const char* opt_ourTitle = APPLICATIONTITLE;
    const char* opt_peer = CDicomParameter::instance().getServerIP();
    const char* opt_peerTitle = PEERAPPLICATIONTITLE;
    OFCmdUnsignedInt opt_port = atoi(CDicomParameter::instance().getServerPort());
    OFCmdUnsignedInt opt_repeatCount = 1;
    OFList<OFString> overrideKeys;
    DcmTLSOptions tlsOptions(NET_REQUESTOR);

#ifdef WITH_ZLIB
    OFCmdUnsignedInt opt_compressionLevel = 0;
#endif

    OFBool opt_automaticDataCorrection = OFFalse;

    OFStandard::initializeNetwork();
#ifdef WITH_OPENSSL
    DcmTLSTransportLayer::initializeOpenSSL();
#endif

    char tempstr[20];
    OFString temp_str;
    OFConsoleApplication app(OFFIS_CONSOLE_APPLICATION, "DICOM query (C-FIND) SCU", rcsid);
    OFCommandLine cmd;

    OFOStringStream optStreamCOUT;
    cmd.setParamColumn(LONGCOL + SHORTCOL + 4);
    cmd.addParam("peer", "hostname of DICOM peer");
    cmd.addParam("port", "tcp/ip port number of peer");
    cmd.addParam("dcmfile-in", "DICOM query file(s)", OFCmdParam::PM_MultiOptional);

    cmd.setOptionColumns(LONGCOL, SHORTCOL);
    cmd.addGroup("general options:", LONGCOL, SHORTCOL + 2);
    cmd.addOption("--help", "-h", "print this help text and exit", OFCommandLine::AF_Exclusive);
    cmd.addOption("--version", "print version information and exit", OFCommandLine::AF_Exclusive);
    OFLog::addOptions(cmd);

    cmd.addGroup("network options:");
    cmd.addSubGroup("override matching keys:");
    cmd.addOption("--key", "-k", 1, "[k]ey: gggg,eeee=\"str\", path or dict. name=\"str\"", "override matching key");
    cmd.addSubGroup("query information model:");
    cmd.addOption("--worklist", "-W", "use modality worklist information model (def.)");
    cmd.addOption("--patient", "-P", "use patient root information model");
    cmd.addOption("--study", "-S", "use study root information model");
    cmd.addOption("--psonly", "-O", "use patient/study only information model");
    cmd.addSubGroup("application entity titles:");
    OFString opt1 = "set my calling AE title (default: ";
    opt1 += APPLICATIONTITLE;
    opt1 += ")";
    cmd.addOption("--aetitle", "-aet", 1, "[a]etitle: string", opt1.c_str());
    OFString opt2 = "set called AE title of peer (default: ";
    opt2 += PEERAPPLICATIONTITLE;
    opt2 += ")";
    cmd.addOption("--call", "-aec", 1, "[a]etitle: string", opt2.c_str());
    cmd.addSubGroup("post-1993 value representations:");
    cmd.addOption("--enable-new-vr", "+u", "enable support for new VRs (UN/UT) (default)");
    cmd.addOption("--disable-new-vr", "-u", "disable support for new VRs, convert to OB");
    cmd.addSubGroup("proposed transmission transfer syntaxes:");
    cmd.addOption("--propose-uncompr",
                  "-x=", "propose all uncompressed TS, explicit VR\nwith local byte ordering first (default)");
    cmd.addOption("--propose-little", "-xe", "propose all uncompressed TS, explicit VR\nlittle endian first");
    cmd.addOption("--propose-big", "-xb", "propose all uncompressed TS, explicit VR\nbig endian first");
#ifdef WITH_ZLIB
    cmd.addOption("--propose-deflated", "-xd",
                  "propose deflated explicit VR little endian TS\nand all uncompressed transfer syntaxes");
#endif
    cmd.addOption("--propose-implicit", "-xi", "propose implicit VR little endian TS only");
#ifdef WITH_ZLIB
    cmd.addSubGroup("deflate compression level (only with --propose-deflated):");
    cmd.addOption("--compression-level", "+cl", 1, "[l]evel: integer (default: 6)",
                  "0=uncompressed, 1=fastest, 9=best compression");
#endif
    cmd.addSubGroup("other network options:");
    OFString opt3 = "set max receive pdu to n bytes (default: ";
    sprintf(tempstr, "%ld", OFstatic_cast(long, ASC_DEFAULTMAXPDU));
    opt3 += tempstr;
    opt3 += ")";
    OFString opt4 = "[n]umber of bytes: integer (";
    sprintf(tempstr, "%ld", OFstatic_cast(long, ASC_MINIMUMPDUSIZE));
    opt4 += tempstr;
    opt4 += "..";
    sprintf(tempstr, "%ld", OFstatic_cast(long, ASC_MAXIMUMPDUSIZE));
    opt4 += tempstr;
    opt4 += ")";
    cmd.addOption("--timeout", "-to", 1, "[s]econds: integer (default: unlimited)", "timeout for connection requests");
    cmd.addOption("--acse-timeout", "-ta", 1, "[s]econds: integer (default: 30)", "timeout for ACSE messages");
    cmd.addOption("--dimse-timeout", "-td", 1, "[s]econds: integer (default: unlimited)", "timeout for DIMSE messages");
    cmd.addOption("--max-pdu", "-pdu", 1, opt4.c_str(), opt3.c_str());
    cmd.addOption("--repeat", 1, "[n]umber: integer", "repeat n times");
    cmd.addOption("--abort", "abort association instead of releasing it");
    cmd.addOption("--cancel", 1, "[n]umber: integer", "cancel after n responses (default: never)");

    // add TLS specific command line options if (and only if) we are compiling with OpenSSL
    tlsOptions.addTLSCommandlineOptions(cmd);

    cmd.addGroup("output options:");
    cmd.addSubGroup("general:");
    cmd.addOption("--output-directory", "-od", 1, "[d]irectory: string (default: \".\")",
                  "write output files to existing directory d");
    cmd.addSubGroup("automatic data correction:");
    cmd.addOption("--enable-correction", "+dc", "enable automatic data correction");
    cmd.addOption("--disable-correction", "-dc", "disable automatic data correction (default)");
    cmd.addSubGroup("C-FIND responses:");
    cmd.addOption("--show-responses", "+sr", "always output responses to the logger");
    cmd.addOption("--hide-responses", "-sr", "do not output responses to the logger");
    cmd.addOption("--extract", "-X", "extract responses to DICOM file (rsp0001.dcm...)");
    cmd.addOption("--extract-xml", "-Xx", "extract responses to XML file (rsp0001.xml...)");
    cmd.addOption("--extract-xml-single", "-Xs", 1, "[f]ilename: string", "extract all responses to given XML file f");

    prepareCmdLineArgs(iArgcCount, argStr, OFFIS_CONSOLE_APPLICATION);
    if (app.parseCommandLine(cmd, iArgcCount, argStr))
    {
        if (cmd.hasExclusiveOption())
        {
            if (cmd.findOption("--version"))
            {
                app.printHeader(OFTrue);
                optStreamCOUT << OFendl << "External libraries used:";
#if !defined(WITH_ZLIB) && !defined(WITH_OPENSSL) && !defined(DCMTK_ENABLE_CHARSET_CONVERSION)
                optStreamCOUT << " none" << OFendl;
#else
                optStreamCOUT << OFendl;
#endif
#ifdef WITH_ZLIB
                optStreamCOUT << "- ZLIB, Version " << zlibVersion() << OFendl;
#endif
                // print OpenSSL version if (and only if) we are compiling with OpenSSL
                tlsOptions.printLibraryVersion();
                return "";
            }

            // check if the command line contains the --list-ciphers option
            if (tlsOptions.listOfCiphersRequested(cmd))
            {
                // tlsOptions.printSupportedCiphersuites(app, COUT);
                return "";
            }
        }

        // cmd.getParam(1, opt_peer);
        // app.checkParam(cmd.getParamAndCheckMinMax(2, opt_port, 1, 65535));

        OFLog::configureFromCommandLine(cmd, app);

        if (cmd.findOption("--key", 0, OFCommandLine::FOM_FirstFromLeft))
        {
            const char* ovKey = NULL;
            do
            {
                app.checkValue(cmd.getValue(ovKey));
                overrideKeys.push_back(ovKey);
            } while (cmd.findOption("--key", 0, OFCommandLine::FOM_NextFromLeft));
        }

        cmd.beginOptionBlock();
        if (cmd.findOption("--worklist"))
            opt_abstractSyntax = UID_FINDModalityWorklistInformationModel;
        if (cmd.findOption("--patient"))
            opt_abstractSyntax = UID_FINDPatientRootQueryRetrieveInformationModel;
        if (cmd.findOption("--study"))
            opt_abstractSyntax = UID_FINDStudyRootQueryRetrieveInformationModel;
        if (cmd.findOption("--psonly"))
            opt_abstractSyntax = UID_RETIRED_FINDPatientStudyOnlyQueryRetrieveInformationModel;
        cmd.endOptionBlock();

        if (cmd.findOption("--aetitle"))
            app.checkValue(cmd.getValue(opt_ourTitle));
        if (cmd.findOption("--call"))
            app.checkValue(cmd.getValue(opt_peerTitle));

        cmd.beginOptionBlock();
        if (cmd.findOption("--propose-uncompr"))
            opt_networkTransferSyntax = EXS_Unknown;
        if (cmd.findOption("--propose-little"))
            opt_networkTransferSyntax = EXS_LittleEndianExplicit;
        if (cmd.findOption("--propose-big"))
            opt_networkTransferSyntax = EXS_BigEndianExplicit;
        if (cmd.findOption("--propose-implicit"))
            opt_networkTransferSyntax = EXS_LittleEndianImplicit;
#ifdef WITH_ZLIB
        if (cmd.findOption("--propose-deflated"))
            opt_networkTransferSyntax = EXS_DeflatedLittleEndianExplicit;
#endif
        cmd.endOptionBlock();

#ifdef WITH_ZLIB
        if (cmd.findOption("--compression-level"))
        {
            app.checkDependence("--compression-level", "--propose-deflated",
                                (opt_networkTransferSyntax == EXS_DeflatedLittleEndianExplicit));
            app.checkValue(cmd.getValueAndCheckMinMax(opt_compressionLevel, 0, 9));
            dcmZlibCompressionLevel.set(OFstatic_cast(int, opt_compressionLevel));
        }
#endif

        cmd.beginOptionBlock();
        if (cmd.findOption("--enable-new-vr"))
            dcmEnableGenerationOfNewVRs();
        if (cmd.findOption("--disable-new-vr"))
            dcmDisableGenerationOfNewVRs();
        cmd.endOptionBlock();

        if (cmd.findOption("--timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            dcmConnectionTimeout.set(OFstatic_cast(Sint32, opt_timeout));
        }

        if (cmd.findOption("--acse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_acse_timeout = OFstatic_cast(int, opt_timeout);
        }

        if (cmd.findOption("--dimse-timeout"))
        {
            OFCmdSignedInt opt_timeout = 0;
            app.checkValue(cmd.getValueAndCheckMin(opt_timeout, 1));
            opt_dimse_timeout = OFstatic_cast(int, opt_timeout);
            opt_blockMode = DIMSE_NONBLOCKING;
        }

        if (cmd.findOption("--max-pdu"))
            app.checkValue(cmd.getValueAndCheckMinMax(opt_maxReceivePDULength, ASC_MINIMUMPDUSIZE, ASC_MAXIMUMPDUSIZE));
        if (cmd.findOption("--repeat"))
            app.checkValue(cmd.getValueAndCheckMin(opt_repeatCount, 1));
        if (cmd.findOption("--abort"))
            opt_abortAssociation = OFTrue;
        if (cmd.findOption("--cancel"))
            app.checkValue(cmd.getValueAndCheckMin(opt_cancelAfterNResponses, 0));

        if (cmd.findOption("--output-directory"))
            app.checkValue(cmd.getValue(opt_outputDirectory));

        cmd.beginOptionBlock();
        if (cmd.findOption("--enable-correction"))
            opt_automaticDataCorrection = OFTrue;
        if (cmd.findOption("--disable-correction"))
            opt_automaticDataCorrection = OFFalse;
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--show-responses"))
            opt_outputResponsesToLogger = 1;
        if (cmd.findOption("--hide-responses"))
            opt_outputResponsesToLogger = 2;
        cmd.endOptionBlock();

        cmd.beginOptionBlock();
        if (cmd.findOption("--extract"))
            opt_extractResponses = FEM_dicomFile;
        if (cmd.findOption("--extract-xml"))
            opt_extractResponses = FEM_xmlFile;
        if (cmd.findOption("--extract-xml-single"))
        {
            opt_extractResponses = FEM_singleXMLFile;
            app.checkValue(cmd.getValue(opt_extractXMLFilename));
        }
        cmd.endOptionBlock();

        int paramCount = cmd.getParamCount();
        const char* currentFilename = NULL;
        OFString errormsg;

        for (int i = 3; i <= paramCount; i++)
        {
            cmd.getParam(i, currentFilename);
            if (access(currentFilename, R_OK) < 0)
            {
                errormsg = "cannot access file: ";
                errormsg += currentFilename;
                app.printError(errormsg.c_str());
            }
            fileNameList.push_back(currentFilename);
        }

        if (fileNameList.empty() && overrideKeys.empty())
        {
            app.printError("either query file or override keys (or both) must be specified");
        }

        // evaluate (most of) the TLS command line options (if we are compiling with OpenSSL)
        tlsOptions.parseArguments(app, cmd);
    }

    if (argStr != NULL)
    {
        delete[] argStr;
    }

    if (opt_outputResponsesToLogger == 0)
    {
        if (!cmd.findOption("--log-config"))
        {
            if (cmd.findOption("--extract") || cmd.findOption("--extract-xml") ||
                cmd.findOption("--extract-xml-single"))
            {
                OFLog::getLogger(DCMNET_LOGGER_NAME ".responses").setLogLevel(OFLogger::OFF_LOG_LEVEL);
            }
            else if (!cmd.findOption("--quiet") && !cmd.findOption("--verbose") && !cmd.findOption("--debug") &&
                     !cmd.findOption("--log-level"))
            {
                OFLog::getLogger(DCMNET_LOGGER_NAME ".responses").setLogLevel(OFLogger::INFO_LOG_LEVEL);
            }
        }
    }
    else if (opt_outputResponsesToLogger == 1)
    {
        // always show C-FIND responses
        OFLog::getLogger(DCMNET_LOGGER_NAME ".responses").setLogLevel(OFLogger::INFO_LOG_LEVEL);
    }
    else if (opt_outputResponsesToLogger == 2)
    {
        // never show C-FIND responses
        OFLog::getLogger(DCMNET_LOGGER_NAME ".responses").setLogLevel(OFLogger::OFF_LOG_LEVEL);
    }

    /* print resource identifier */
    OFLOG_DEBUG(findscuLogger, rcsid << OFendl);

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        OFLOG_WARN(findscuLogger,
                   "no data dictionary loaded, check environment variable: " << DCM_DICT_ENVIRONMENT_VARIABLE);
    }

    opt_outputDirectory = CDicomParameter::instance().getStoragePath();
    opt_outputDirectory.append("/worklist/");
    // DebugPrintf("opt_outputDirectory = [%s]", opt_outputDirectory.c_str());

    /* make sure that output directory can be used (if needed) */
    if (opt_extractResponses != FEM_none)
    {
        if (!OFStandard::dirExists(opt_outputDirectory))
        {
            baseOpenDir(opt_outputDirectory.c_str());
        }
        else
        {
            baseDeleteFileMask(opt_outputDirectory.c_str(), ".dcm");
        }
    }

    if (!OFStandard::dirExists(opt_outputDirectory))
    {
        return "";
    }

    // enabled or disable removal of trailing padding
    dcmEnableAutomaticInputDataCorrection.set(opt_automaticDataCorrection);

    // declare findSCU handler and initialize network
    DcmFindSCU findscu;
    OFCondition cond = findscu.initializeNetwork(opt_acse_timeout);
    if (cond.bad())
    {
        OFLOG_ERROR(findscuLogger, DimseCondition::dump(temp_str, cond));
        return "";
    }

#ifdef WITH_OPENSSL
    if (tlsOptions.secureConnectionRequested())
    {
        /* create a secure transport layer */
        cond = tlsOptions.createTransportLayer(NULL, NULL, app, cmd);
        if (cond.bad())
        {
            OFLOG_FATAL(findscuLogger, DimseCondition::dump(temp_str, cond));
            return "";
        }

        /* activate secure transport layer */
        cond = findscu.setTransportLayer(tlsOptions.getTransportLayer());
        if (cond.bad())
        {
            OFLOG_ERROR(findscuLogger, DimseCondition::dump(temp_str, cond));
            return "";
        }
    }
#endif

    // do the main work: negotiate network association, perform C-FIND transaction,
    // process results, and finally tear down the association.

    cond = findscu.performQuery(
        opt_peer, opt_port, opt_ourTitle, opt_peerTitle, opt_abstractSyntax, opt_networkTransferSyntax, opt_blockMode,
        opt_dimse_timeout, opt_maxReceivePDULength, tlsOptions.secureConnectionRequested(), opt_abortAssociation,
        opt_repeatCount, opt_extractResponses, opt_cancelAfterNResponses, &overrideKeys,
        NULL, /* we want to use the default callback */
        &fileNameList, opt_outputDirectory.c_str(), opt_extractXMLFilename.c_str(), pModalityValue,
        pScheduledProcedureStepStartDateValue, pScheduledProcedureStepEndDateValue);

    // make sure that an appropriate exit code is returned
    int exitCode = cond.good() ? 0 : 2;

    // destroy network structure
    cond = findscu.dropNetwork();
    if (cond.bad())
    {
        OFLOG_ERROR(findscuLogger, DimseCondition::dump(temp_str, cond));
        if (!exitCode)
            exitCode = 3;
    }

    OFStandard::shutdownNetwork();

    cond = tlsOptions.writeRandomSeed();
    if (cond.bad())
    {
        // failure to write back the random seed is a warning, not an error
        OFLOG_WARN(findscuLogger, DimseCondition::dump(temp_str, cond));
    }
    return "";
}

static OFBool printFilename = OFFalse;
static OFBool printFileSearch = OFFalse;
static OFBool printAllInstances = OFTrue;
static OFBool prependSequenceHierarchy = OFFalse;
static int m_PrintTagCount = 0;
static size_t fileCounter = 0;

static const int MAX_PRINT_TAG_NAMES = 1024;
static const char* printTagNames[MAX_PRINT_TAG_NAMES];
static const DcmTagKey* printTagKeys[MAX_PRINT_TAG_NAMES];
static OFCmdUnsignedInt maxReadLength = 4096; // default is 4 KB

static DcmTagKey parseTagKey(const char* tagName)
{
    unsigned int group = 0xffff;
    unsigned int elem = 0xffff;
    if (sscanf(tagName, "%x,%x", &group, &elem) != 2)
    {
        DcmTagKey tagKey;
        /* it is a name */
        const DcmDataDictionary& globalDataDict = dcmDataDict.rdlock();
        const DcmDictEntry* dicent = globalDataDict.findEntry(tagName);
        if (dicent == NULL)
        {
            OFLOG_WARN(dcmdumpLogger, "unrecognized tag name: '" << tagName << "'");
            tagKey = DCM_UndefinedTagKey;
        }
        else
        {
            tagKey = dicent->getKey();
        }
        dcmDataDict.rdunlock();
        return tagKey;
    }
    else /* tag name has format "gggg,eeee" */
    {
        return DcmTagKey(OFstatic_cast(Uint16, group), OFstatic_cast(Uint16, elem));
    }
}

static OFBool addPrintTagName(const char* tagName)
{
    if (m_PrintTagCount >= MAX_PRINT_TAG_NAMES)
    {
        OFLOG_WARN(dcmdumpLogger, "too many print tag options (max: " << MAX_PRINT_TAG_NAMES << ")");
        return OFFalse;
    }

    unsigned int group = 0xffff;
    unsigned int elem = 0xffff;
    if (sscanf(tagName, "%x,%x", &group, &elem) != 2)
    {
        /* it is a name */
        const DcmDataDictionary& globalDataDict = dcmDataDict.rdlock();
        const DcmDictEntry* dicent = globalDataDict.findEntry(tagName);
        if (dicent == NULL)
        {
            OFLOG_WARN(dcmdumpLogger, "unrecognized tag name: '" << tagName << "'");
            dcmDataDict.rdunlock();
            return OFFalse;
        }
        else
        {
            /* note for later */
            printTagKeys[m_PrintTagCount] = new DcmTagKey(dicent->getKey());
        }
        dcmDataDict.rdunlock();
    }
    else
    {
        /* tag name has format xxxx,xxxx */
        /* do not lookup in dictionary, tag could be private */
        printTagKeys[m_PrintTagCount] = NULL;
    }

    size_t buflen = strlen(tagName) + 1;
    char* buf = OFstatic_cast(char*, malloc(buflen));
    OFStandard::strlcpy(buf, tagName, buflen);
    printTagNames[m_PrintTagCount] = buf;
    m_PrintTagCount++;
    return OFTrue;
}

/* main program */

#define SHORTCOL 3
#define LONGCOL 21

static void printResultW(DcmStack& stack, size_t printFlags, const char* pixelFileName = NULL,
                         size_t* pixelCounter = NULL)
{
    unsigned long n = stack.card();
    if (n == 0)
    {
        return;
    }

    if (prependSequenceHierarchy)
    {
        if (printFlags & DCMTypes::PF_useANSIEscapeCodes)
            DebugPrintf("DCMDATA_ANSI_ESCAPE_CODE_TAG");
        /* print the path leading up to the top stack elem */
        for (unsigned long i = n - 1; i >= 1; i--)
        {
            DcmObject* dobj = stack.elem(i);
            /* do not print if a DCM_Item as this is not
             * very helpful to distinguish instances.
             */
            if (dobj != NULL && dobj->getTag().getXTag() != DCM_Item)
            {
                DebugPrintf("dobj->getTag() = [%s][%s]", dobj->getTag().getVR().getVRName());
            }
        }
    }

    DcmElement* subElem = (DcmElement*)stack.top();
    const char* pTRG = subElem->getTag().toString().c_str();
    const char* pVal = DcmVR(subElem->getVR()).getVRName();
    string strValue = "";
    strValue.append("Target=");
    strValue.append(pTRG);
    strValue.append("#,,#,,,#,,#VR=");
    strValue.append(pVal);
    strValue.append("#,,#,,,#,,#Value=");

    if (baseStrcmp(pVal, "UL") || baseStrcmp(pVal, "OL") || baseStrcmp(pVal, "up"))
    {
        Uint32 ulValue = 0;
        if (subElem->getUint32(OFconst_cast(Uint32&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%u", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "OV") || baseStrcmp(pVal, "UV"))
    {
        Uint64 ulValue = 0;
        if (subElem->getUint64(OFconst_cast(Uint64&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%lu", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "SL"))
    {
        Sint32 ulValue = 0;
        if (subElem->getSint32(OFconst_cast(Sint32&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%d", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "SS"))
    {
        Sint16 ulValue = 0;
        if (subElem->getSint16(OFconst_cast(Sint16&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%d", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "SV"))
    {
        Sint64 ulValue = 0;
        if (subElem->getSint64(OFconst_cast(Sint64&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%ld", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "US") || baseStrcmp(pVal, "AT") || baseStrcmp(pVal, "OW") || baseStrcmp(pVal, "xs") ||
             baseStrcmp(pVal, "lt"))
    {
        Uint16 ulValue = 0;
        if (subElem->getUint16(OFconst_cast(Uint16&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%d", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "FD") || baseStrcmp(pVal, "OD"))
    {
        Float64 ulValue = 0;
        if (subElem->getFloat64(OFconst_cast(Float64&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%f", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "FL") || baseStrcmp(pVal, "OF"))
    {
        Float32 ulValue = 0;
        if (subElem->getFloat32(OFconst_cast(Float32&, ulValue)).good())
        {
            char pVr[56] = {0};
            sprintf(pVr, "%f", ulValue);
            strValue.append(pVr);
        }
    }
    else if (baseStrcmp(pVal, "OB") || baseStrcmp(pVal, "ox"))
    {
        Uint8* byteVals = new Uint8[10];
        for (int i = 0; i < 10; ++i)
        {
            byteVals[i] = 122;
        }
        if (subElem->getUint8Array(OFconst_cast(Uint8*&, byteVals)).good())
        {
            if (byteVals != NULL)
            {
                for (int i = 0; i < 2; ++i)
                {
                    if (122 == byteVals[i])
                    {
                        break;
                    }
                    if (i > 0)
                    {
                        strValue.append("\\");
                    }
                    char pVr[56] = {0};
                    sprintf(pVr, "%d", byteVals[i]);
                    strValue.append(pVr);
                }
            }
        }
    }
    else if (baseStrcmp(pVal, "SQ") || baseStrcmp(pVal, "na"))
    {
    }
    else
    {
        char* pPbUF = "";
        subElem->getString(pPbUF);
        if (NULL == pPbUF)
        {
            pPbUF = "";
        }

        strValue.append(pPbUF);
    }

    strValue.append("#,,#,,,#,,#Parent=");
    if (!CDicomParameter::instance().getDicomStr().empty())
    {
        CDicomParameter::instance().setDicomStr(",#,###,#,");
    }
    CDicomParameter::instance().setDicomStr(strValue);
}

static int dumpFileW(const OFFilename& ifname, const E_FileReadMode readMode, const E_TransferSyntax xfer,
                     const size_t printFlags, const OFBool loadIntoMemory, const OFBool stopOnErrors,
                     const OFBool convertToUTF8, const DcmTagKey& stopParsingAtElement, const char* pixelDirectory)
{
    int result = 0;

    printFilename = OFFalse;
    printFileSearch = OFFalse;
    printAllInstances = OFTrue;
    prependSequenceHierarchy = OFFalse;
    m_PrintTagCount = 0;
    fileCounter = 0;

    if (ifname.isEmpty())
    {
        OFLOG_ERROR(dcmdumpLogger, OFFIS_CONSOLE_APPLICATION << ": invalid filename: <empty string>");
        return 1;
    }

    CDicomParameter::instance().clearDicomStr();
    DcmFileFormat dfile;
    DcmObject* dset = &dfile;
    if (readMode == ERM_dataset)
        dset = dfile.getDataset();

    OFCondition cond;
    //    DebugPrintf("stopParsingAtElement = [%s]", stopParsingAtElement == DCM_UndefinedTagKey?
    //                    "stopParsingAtElement == DCM_UndefinedTagKey":"stopParsingAtElement != DCM_UndefinedTagKey");
    //    DebugPrintf("stller ifname = [%s]", ifname.getCharPointer());

    if (stopParsingAtElement == DCM_UndefinedTagKey)
    {
        cond = dfile.loadFile(ifname, xfer, EGL_noChange, OFstatic_cast(Uint32, maxReadLength), readMode);
    }
    else
    {
        // instead of using loadFile(), we call loadFileUntilTag().
        cond = dfile.loadFileUntilTag(ifname, xfer, EGL_noChange, OFstatic_cast(Uint32, maxReadLength), readMode,
                                      stopParsingAtElement);
    }

    // DebugPrintf("stller loadFile end");
    if (cond.bad())
    {
        OFLOG_ERROR(dcmdumpLogger, OFFIS_CONSOLE_APPLICATION << ": " << cond.text() << ": reading file: " << ifname);
        result = 1;
        if (stopOnErrors)
            return result;
    }

    if (loadIntoMemory)
        dfile.loadAllDataIntoMemory();
    // DebugPrintf("stller loadAllDataIntoMemory end");

    const char* pCharSet = CDicomParameter::instance().getOptCharSet();
    if (pCharSet != NULL && strlen(pCharSet) > 0)
    {
        OFString toCharset(CDicomParameter::instance().getOptCharSet());
        cond = dfile.convertCharacterSet(toCharset, 0);
        if (cond.bad())
        {
            OFLOG_FATAL(dcmdumpLogger, cond.text() << ": converting file to UTF-8: " << ifname);
            return 1;
        }
    }
    //    DebugPrintf("stller getOptCharSet end");

    //#ifdef DCMTK_ENABLE_CHARSET_CONVERSION
    //    if (convertToUTF8)
    //    {
    //        OFLOG_INFO(dcmdumpLogger, "converting all element values that are affected by Specific Character Set
    //        (0008,0005) to UTF-8"); cond = dfile.convertToUTF8(); if (cond.bad())
    //        {
    //            OFLOG_FATAL(dcmdumpLogger, cond.text() << ": converting file to UTF-8: " << ifname);
    //            result = 1;
    //            if (stopOnErrors) return result;
    //        }
    //    }
    //#else
    //    // avoid compiler warning on unused variable
    //    (void)convertToUTF8;
    //#endif
    //    cond = dfile.convertToUTF8();
    //    if (cond.bad())
    //    {
    //        OFLOG_FATAL(dcmdumpLogger, cond.text() << ": converting file to GB2312: " << ifname);
    //        result = 1;
    //        if (stopOnErrors) return result;
    //    }

    size_t pixelCounter = 0;
    const char* pixelFileName = NULL;
    OFFilename pixelFilenameStr;
    if (pixelDirectory != NULL)
    {
        /* create filename for pixel data */
        OFFilename fileName;
        OFStandard::getFilenameFromPath(fileName, ifname);
        OFStandard::combineDirAndFilename(pixelFilenameStr, pixelDirectory, fileName);
        pixelFileName = pixelFilenameStr.getCharPointer();
    }
    addPrintTagName("PatientName");
    //    addPrintTagName("PatientID");
    //    addPrintTagName("RequestedProcedureID");
    //    addPrintTagName("PatientAge");
    //    addPrintTagName("PatientBirthDate");
    //    addPrintTagName("PatientSex");
    //    addPrintTagName("PatientSize");
    //    addPrintTagName("PatientWeight");

    // DebugPrintf("strValue = combineDirAndFilename end m_PrintTagCount = [%d]", m_PrintTagCount);

    /* dump complete file content */
    if (m_PrintTagCount > 0)
    {
        OFBool firstTag = OFTrue;
        /* only print specified tags */
        for (int i = 0; i < m_PrintTagCount; ++i)
        {
            unsigned int group = 0xffff;
            unsigned int elem = 0xffff;
            DcmTagKey searchKey;
            const char* tagName = printTagNames[i];
            if (printTagKeys[i])
                searchKey = *printTagKeys[i];
            else if (sscanf(tagName, "%x,%x", &group, &elem) == 2)
                searchKey.set(OFstatic_cast(Uint16, group), OFstatic_cast(Uint16, elem));
            else
            {
                OFLOG_FATAL(dcmdumpLogger, "Internal ERROR in File " << __FILE__ << ", Line " << __LINE__ << OFendl
                                                                     << " -- Named tag inconsistency");
                return result;
            }

            DcmStack stack;
            if (dset->search(searchKey, stack, ESM_fromHere, OFTrue) == EC_Normal)
            {
                if (firstTag)
                {
                    if (!printFilename)
                    {
                        /* a newline separates two consecutive "dumps" */
                        //                        if (++fileCounter > 1)
                        //                            COUT << OFendl;
                    }
                    /* print header with filename */
                    if (printFileSearch)
                        DebugPrintf("# [%d] [%d] [%s]", OFFIS_CONSOLE_APPLICATION, fileCounter,
                                    ifname.getCharPointer());
                    firstTag = OFFalse;
                }

                printResultW(stack, printFlags, pixelFileName, &pixelCounter);
                //                if (printAllInstances)
                //                {
                //                    while (dset->search(searchKey, stack, ESM_afterStackTop, OFTrue) == EC_Normal);
                //                      printResultW(stack, printFlags, pixelFileName, &pixelCounter);
                //                }
            }
        }
    }
    return result;
}

const char* getDicomFileParameters(const char* pPath)
{
    DcmFileFormat fileformat;
    OFCondition oc = fileformat.loadFile(pPath);
    DcmDataset* pDataset = fileformat.getDataset();
    string pValue = "[";
    const char* tString;
    pDataset->findAndGetString(DCM_InstanceNumber, tString);
    pValue.append("{\"Target\"=\"");
    pValue.append(tString);
    pValue.append("\"}");
    pDataset->findAndGetString(DCM_PatientName, tString);
    pValue.append("{\"Target\"=\"");
    pValue.append(tString);
    pValue.append("\"}");
    pDataset->findAndGetString(DCM_PatientID, tString);
    pValue.append("{\"Target\"=\"");
    pValue.append(tString);
    pValue.append("\"}");
    pDataset->findAndGetString(DCM_PatientBirthDate, tString);
    pValue.append("{\"Target\"=\"");
    pValue.append(tString);
    pValue.append("\"}");
    pDataset->findAndGetString(DCM_PatientSex, tString);
    pValue.append("{\"Target\"=\"");
    pValue.append(tString);
    pValue.append("\"}");
    pValue.append("]");
    return pValue.c_str();
}

int getDicomFileParameters(int argc, char* argv[])
{
    OFBool loadIntoMemory = OFTrue;
    size_t printFlags = DCMTypes::PF_shortenLongTagValues;
    E_FileReadMode readMode = ERM_autoDetect;
    E_TransferSyntax xfer = EXS_Unknown;
    OFBool stopOnErrors = OFTrue;
    const char* pixelDirectory = NULL;
    OFBool convertToUTF8 = true;
    DcmTagKey stopParsingBeforeElement = DCM_UndefinedTagKey;

    /* make sure data dictionary is loaded */
    if (!dcmDataDict.isDictionaryLoaded())
    {
        OFLOG_WARN(dcmdumpLogger,
                   "no data dictionary loaded, check environment variable: " << DCM_DICT_ENVIRONMENT_VARIABLE);
    }

    OFList<OFFilename> inputFiles;
    OFFilename fn(argv[0]);
    if (inputFiles.empty())
    {
        inputFiles.push_back(fn);
    }

    const size_t count = inputFiles.size();
    OFFilename current;
    OFListIterator(OFFilename) if_iter = inputFiles.begin();
    OFListIterator(OFFilename) if_last = inputFiles.end();
    /* iterate over all input filenames */
    while (if_iter != if_last)
    {
        current = (*if_iter++);
        dumpFileW(current, readMode, xfer, printFlags, loadIntoMemory, stopOnErrors, convertToUTF8,
                  stopParsingBeforeElement, pixelDirectory);
    }

    return 0;
}

int cTRle(const char* opt_ifname, const char* opt_ofname)
{
    return 0;
}
