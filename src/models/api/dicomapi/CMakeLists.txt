project(dicomapi)
add_definitions(-DDICOMAPI_LIBRARY)
INCLUDE_DIRECTORIES("${CMAKE_CURRENT_SOURCE_DIR}/include" 
                    ${DCMTK_INCLUDE_DIRS}
                    "${CMAKE_CURRENT_SOURCE_DIR}/decodeapi/include" 
                    "${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/"
                    "${CMAKE_CURRENT_SOURCE_DIR}/"
                    "${CMAKE_SOURCE_DIR}/src/models/api/uscontextapi/platform_support/"
                    )
include_depends()
add_library(dicomapi STATIC
    dcmapi.cpp
    echoscu.cpp
    dicomapi.cpp
    "${CMAKE_CURRENT_SOURCE_DIR}/decodeapi/jpegcode.cpp" 
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/icalibrationregion.cpp
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/bcalibrationregion.cpp
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/calibrationregiondata.cpp
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/calibrationregionmodel.cpp
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/dcalibrationregion.cpp
    ${CMAKE_SOURCE_DIR}/src/corelib/dicommodel/mcalibrationregion.cpp
)
target_link_libraries(${PROJECT_NAME} patientapi ${DCMTK_LIBRARIES})

install(DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    DESTINATION bin
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    DESTINATION lib
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION include
    FILES_MATCHING PATTERN "*.h")


