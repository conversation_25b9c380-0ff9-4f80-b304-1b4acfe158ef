/**
 * @file BaseVector.h
 * @brief all data
 * <AUTHOR>
 * @version Ver 0.1 2018
 *
 */
#ifndef BASENODE_H
#define BASENODE_H
#include "basedefine.h"
#include "baselock.h"

extern "C"
{

    typedef struct
    {
        unsigned long nModId;
        unsigned long nIfId;
        unsigned long nRef;
    } ModletMan;

    typedef struct _tagBitSide
    {
        void* pData;
        unsigned long bitSide;
        unsigned long uLen;
        struct _tagBitSide* next;
    } eBitSide, *pBitSide;

    typedef struct _tagBitSideNode
    {
        pBitSide eBitSideHead;
        unsigned long nCnt;

        WmLock mutexLock;
    } eBitSideNode, *pBitSideNode;

    pBitSideNode bitSideNodeNew();
    void bitSideNodeDelete(pBitSideNode pVector);
    void bitSideNodeValueDelete(pBitSideNode pVector);
    unsigned long bitSideNodeGetCount(pBitSideNode pVector);
    void addBitSideHead(pBitSideNode eBSL, unsigned long llbitSide, unsigned long uLenS, const void* pvData);
    pBitSide bitSideNodeRemove(pBitSideNode pVector);
    void bitSideDelete(pBitSide pBSide);
}
#endif // BASENODE_H
