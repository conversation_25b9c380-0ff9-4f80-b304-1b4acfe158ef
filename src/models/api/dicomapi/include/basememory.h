/**
 * @file basememory.h
 * @brief
 * <AUTHOR>
 * @version Ver 0.1 2018
 *
 */

#ifndef _BASE_MEMORY_H_
#define _BASE_MEMORY_H_
#include "basedefine.h"

#define baseMalloc(size) baseMallocDebug(size, NULL, 0, NULL)
#define baseFree(ptr) baseFreeDebug(ptr, NULL, 0, NULL)

void* baseMallocDebug(unsigned long size, const char* pFileName, int iLine, const char* pFunction);
void* baseRealloc(void* ptr, unsigned int size);
void baseFreeDebug(void* ptr, const char* pFileName, int iLine, const char* pFunction);
void baseMemset(void* ptr, int value, unsigned long size);
void baseMemcpy(void* desptr, void const* originptr, unsigned long offset, unsigned long length);
void baseAllocopy(char*& to, const char* from);

#endif
