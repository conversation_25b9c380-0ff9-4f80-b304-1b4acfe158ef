#ifndef DICOMPARAMETERS_H
#define DICOMPARAMETERS_H
#include <vector>
#include <string.h>
#include <stdlib.h>
#include <list>
#include <iostream>
#include "basetype.h"

using namespace ::std;

typedef std::list<Dcm_InputFile*> FILENAMELIST;
typedef std::list<Dcm_InputFileParameters*> FILEPARAMETERSLIST;
typedef std::list<unsigned char*> INSTERDATALIST;

#define DefFun(retType, Name, getFunctionName, setFunctionName)                                                        \
private:                                                                                                               \
    retType Name;                                                                                                      \
                                                                                                                       \
public:                                                                                                                \
    retType getFunctionName()                                                                                          \
    {                                                                                                                  \
        if (NULL == Name)                                                                                              \
        {                                                                                                              \
            setFunctionName("");                                                                                       \
        }                                                                                                              \
        return Name;                                                                                                   \
    }                                                                                                                  \
    void setFunctionName(const retType value)                                                                          \
    {                                                                                                                  \
        if (NULL == Name)                                                                                              \
        {                                                                                                              \
            Name = (retType)malloc(128);                                                                               \
        }                                                                                                              \
        if (Name != NULL)                                                                                              \
        {                                                                                                              \
            memset(Name, 0x0, 128);                                                                                    \
            strncpy(Name, value, 128);                                                                                 \
        }                                                                                                              \
    }

#define DefAddList()                                                                                                   \
public:                                                                                                                \
    void addDicomFileList(Dcm_InputFile* mbi)                                                                          \
    {                                                                                                                  \
        if (NULL != mbi)                                                                                               \
        {                                                                                                              \
            m_InputFileList.push_back(mbi);                                                                            \
        }                                                                                                              \
    }

#define DefDeleteList()                                                                                                \
public:                                                                                                                \
    void delAllDicomFile()                                                                                             \
    {                                                                                                                  \
        for (FILENAMELIST::iterator it = m_InputFileList.begin(); it != m_InputFileList.end(); ++it)                   \
        {                                                                                                              \
            Dcm_InputFile* mbi = *it;                                                                                  \
            if (NULL != mbi)                                                                                           \
            {                                                                                                          \
                delete mbi;                                                                                            \
                mbi = NULL;                                                                                            \
            }                                                                                                          \
        }                                                                                                              \
        m_InputFileList.clear();                                                                                       \
    }

#define DefGetListSize()                                                                                               \
public:                                                                                                                \
    int getFileCounts()                                                                                                \
    {                                                                                                                  \
        return m_InputFileList.size();                                                                                 \
    }

#define DefDicomFileFun(getFunctionName, retType)                                                                      \
public:                                                                                                                \
    Dcm_InputFile* getFunctionName()                                                                                   \
    {                                                                                                                  \
        for (FILENAMELIST::iterator it = m_InputFileList.begin(); it != m_InputFileList.end(); ++it)                   \
        {                                                                                                              \
            Dcm_InputFile* mbi = *it;                                                                                  \
            if (NULL != mbi && retType == mbi->FileType)                                                               \
            {                                                                                                          \
                m_InputFileList.erase(it);                                                                             \
                return mbi;                                                                                            \
            }                                                                                                          \
        }                                                                                                              \
        return NULL;                                                                                                   \
    }

#define DefAddParametersList()                                                                                         \
public:                                                                                                                \
    void addDicomFileParametersList(Dcm_InputFileParameters* newMbi)                                                   \
    {                                                                                                                  \
        if (NULL != newMbi)                                                                                            \
        {                                                                                                              \
            for (FILEPARAMETERSLIST::iterator it = m_InputFileListParameters.begin();                                  \
                 it != m_InputFileListParameters.end(); ++it)                                                          \
            {                                                                                                          \
                Dcm_InputFileParameters* mbi = *it;                                                                    \
                if (NULL != mbi && newMbi->StartTypeParameters == mbi->StartTypeParameters &&                          \
                    newMbi->EndTypeParameters == mbi->EndTypeParameters)                                               \
                {                                                                                                      \
                    m_InputFileListParameters.erase(it);                                                               \
                    delete mbi;                                                                                        \
                    mbi = NULL;                                                                                        \
                    break;                                                                                             \
                }                                                                                                      \
            }                                                                                                          \
            m_InputFileListParameters.push_back(newMbi);                                                               \
        }                                                                                                              \
    }

#define DefDeleteParametersList()                                                                                      \
public:                                                                                                                \
    void delAllDicomFileParameters()                                                                                   \
    {                                                                                                                  \
        for (FILEPARAMETERSLIST::iterator it = m_InputFileListParameters.begin();                                      \
             it != m_InputFileListParameters.end(); ++it)                                                              \
        {                                                                                                              \
            Dcm_InputFileParameters* mbi = *it;                                                                        \
            if (NULL != mbi)                                                                                           \
            {                                                                                                          \
                delete mbi;                                                                                            \
                mbi = NULL;                                                                                            \
            }                                                                                                          \
        }                                                                                                              \
        m_InputFileListParameters.clear();                                                                             \
    }

#define DefGetParametersListSize()                                                                                     \
public:                                                                                                                \
    int getParametersCounts()                                                                                          \
    {                                                                                                                  \
        return m_InputFileListParameters.size();                                                                       \
    }

#define DefDicomFileParametersFun()                                                                                    \
public:                                                                                                                \
    FILEPARAMETERSLIST getFunctionParameters()                                                                         \
    {                                                                                                                  \
        return m_InputFileListParameters;                                                                              \
    }

#define DefGetDataFun(a, b, c, d)                                                                                      \
public:                                                                                                                \
    d* a()                                                                                                             \
    {                                                                                                                  \
        for (b::iterator it = c.begin(); it != c.end(); ++it)                                                          \
        {                                                                                                              \
            d* mbi = *it;                                                                                              \
            if (NULL != mbi)                                                                                           \
            {                                                                                                          \
                c.erase(it);                                                                                           \
                return mbi;                                                                                            \
            }                                                                                                          \
        }                                                                                                              \
        return NULL;                                                                                                   \
    }

#define DefCalibrationRegionValue()                                                                                    \
    ;                                                                                                                  \
                                                                                                                       \
private:                                                                                                               \
    double m_CalibrationRegionValue[10][30];                                                                           \
                                                                                                                       \
public:                                                                                                                \
    void setCalibrationRegionValue(int iS, int iBit, double iValue)                                                    \
    {                                                                                                                  \
        m_CalibrationRegionValue[iS][iBit] = iValue;                                                                   \
    }                                                                                                                  \
    double getCalibrationRegionValue(int iS, int iBit)                                                                 \
    {                                                                                                                  \
        return m_CalibrationRegionValue[iS][iBit];                                                                     \
    }                                                                                                                  \
    void resetCalibrationRegionValue()                                                                                 \
    {                                                                                                                  \
        for (int x = 0; x < 10; ++x)                                                                                   \
        {                                                                                                              \
            for (int y = 0; y < 30; ++y)                                                                               \
            {                                                                                                          \
                m_CalibrationRegionValue[x][y] = -0xFFFF;                                                              \
            }                                                                                                          \
        }                                                                                                              \
    }

#define DefileDicomCallBack()                                                                                          \
    ;                                                                                                                  \
                                                                                                                       \
private:                                                                                                               \
    CALLBACK_SYSTEM_INFORMATION m_DICOMCALLBACK;                                                                       \
    string m_DicomStr;                                                                                                 \
                                                                                                                       \
public:                                                                                                                \
    void setDICOMCALLBACK(CALLBACK_SYSTEM_INFORMATION cb)                                                              \
    {                                                                                                                  \
        m_DICOMCALLBACK = cb;                                                                                          \
    }                                                                                                                  \
    CALLBACK_SYSTEM_INFORMATION getDicomCallBack()                                                                     \
    {                                                                                                                  \
        return m_DICOMCALLBACK;                                                                                        \
    }                                                                                                                  \
    void setDicomStr(string cb)                                                                                        \
    {                                                                                                                  \
        m_DicomStr.append(cb);                                                                                         \
    }                                                                                                                  \
    void clearDicomStr()                                                                                               \
    {                                                                                                                  \
        m_DicomStr = "";                                                                                               \
    }                                                                                                                  \
    string getDicomStr()                                                                                               \
    {                                                                                                                  \
        return m_DicomStr;                                                                                             \
    }                                                                                                                  \
    void callDicomCallBack(int iType, const char* Target, const char* pValue, const char* pBuffer, int iBuffLen)       \
    {                                                                                                                  \
        m_DICOMCALLBACK(iType, Target, pValue, pBuffer, iBuffLen);                                                     \
    }

class CDicomParameter
{
public:
    static CDicomParameter& instance()
    {
        static CDicomParameter instance;
        return instance;
    }

private:
    CDicomParameter()
        : m_DicomDath(NULL)
        , m_StoragePath(NULL)
        , m_CreateDicomFilePath(NULL)
        , m_StudyDate(NULL)
        , m_StudyTime(NULL)
        , m_SeriesInstanceUID(NULL)
        , m_StudyID(NULL)
        , m_PatientsName(NULL)
        , m_PatientID(NULL)
        , m_PatientsBirthDate(NULL)
        , m_AccessionNumber(NULL)
        , m_PatientsAge(NULL)
        , m_PatientsSex(NULL)
        , m_ReferringPhysiciansName(NULL)
        , m_Modality(NULL)
        , m_FrameTime(NULL)
        , m_SourceApplicationEntityTitle(NULL)
        , m_StudyInstanceUID(NULL)
        , m_ReferringPhysicianName(NULL)
        , m_SeriesDate(NULL)
        , m_ServerIP(NULL)
        , m_ServerPort(NULL)
        , m_JpegLevel(NULL)
        , m_FileType(NULL)
        , m_FrameRate(NULL)
        , m_SeriesTime(NULL)
        , m_PixelSpacing(NULL)
        , m_DoctorsName(NULL)
        , m_CaseHistory(NULL)
        , m_StudyDescription(NULL)
        , m_InstitutionName(NULL)
        , m_Manufacturer(NULL)
        , m_SopInstanceID(NULL)
        , m_ImageNumber(NULL)
        , m_SendDicomDataType(NULL)
        , m_SOPInstanceUID(NULL)
        , m_OptTimeout(NULL)
        , m_OptDimseTimeout(NULL)
        , m_OptAcseTimeout(NULL)
        , m_DICOMCALLBACK(NULL)
        , m_DicomStr("")
        , m_OptAeTitle(NULL)
        , m_OptAcTitle(NULL)
        , m_OptCharSet(NULL)
    {
        resetCalibrationRegionValue();
    }

    ~CDicomParameter()
    {
    }

    DefFun(char*, m_DicomDath, getDicomDath, setDicomDath);
    DefFun(char*, m_StoragePath, getStoragePath, setStoragePath);
    DefFun(char*, m_CreateDicomFilePath, getCreateDicomFilePath, setCreateDicomFilePath);
    DefFun(char*, m_ServerIP, getServerIP, setServerIP);
    DefFun(char*, m_ServerPort, getServerPort, setServerPort);

    DefFun(char*, m_StudyDate, getStudyDate, setStudyDate);
    DefFun(char*, m_StudyTime, getStudyTime, setStudyTime);
    DefFun(char*, m_StudyID, getStudyID, setStudyID);
    DefFun(char*, m_SeriesDate, getSeriesDate, setSeriesDate);
    DefFun(char*, m_SeriesTime, getSeriesTime, setSeriesTime);
    DefFun(char*, m_PatientsName, getPatientsName, setPatientsName);
    DefFun(char*, m_PatientID, getPatientID, setPatientID);
    DefFun(char*, m_PatientsBirthDate, getPatientsBirthDate, setPatientsBirthDate);
    DefFun(char*, m_AccessionNumber, getAccessionNumber, setAccessionNumber);
    DefFun(char*, m_PatientsAge, getPatientsAge, setPatientsAge);
    DefFun(char*, m_PatientsSex, getPatientsSex, setPatientsSex);
    DefFun(char*, m_ReferringPhysiciansName, getReferringPhysiciansName, setReferringPhysiciansName);
    DefFun(char*, m_Modality, getModality, setModality);
    DefFun(char*, m_FrameTime, getFrameTime, setFrameTime);
    DefFun(char*, m_SourceApplicationEntityTitle, getSourceApplicationEntityTitle, setSourceApplicationEntityTitle);
    DefFun(char*, m_StudyInstanceUID, getStudyInstanceUID, setStudyInstanceUID);
    DefFun(char*, m_SeriesInstanceUID, getSeriesInstanceUID, setSeriesInstanceUID);

    DefFun(char*, m_JpegLevel, getJpegLevel, setJpegLevel);
    DefFun(char*, m_FileType, getFileType, setFileType);
    DefFun(char*, m_FrameRate, getFrameRate, setFrameRate);
    DefFun(char*, m_PixelSpacing, getPixelSpacing, setPixelSpacing);
    DefFun(char*, m_DoctorsName, getDoctorsName, setDoctorsName);
    DefFun(char*, m_CaseHistory, getCaseHistory, setCaseHistory);
    DefFun(char*, m_StudyDescription, getStudyDescription, setStudyDescription);
    DefFun(char*, m_InstitutionName, getInstitutionName, setInstitutionName);
    DefFun(char*, m_Manufacturer, getManufacturer, setManufacturer);
    DefFun(char*, m_SopInstanceID, getSopInstanceID, setSopInstanceID);
    DefFun(char*, m_ImageNumber, getImageNumber, setImageNumber);
    DefFun(char*, m_ReferringPhysicianName, getReferringPhysicianName, setReferringPhysicianName);
    DefFun(char*, m_SendDicomDataType, getSendDicomDataType, setSendDicomDataType);
    DefFun(char*, m_SOPInstanceUID, getSOPInstanceUID, setSOPInstanceUID);
    DefFun(char*, m_OptTimeout, getOptTimeout, setOptTimeout);
    DefFun(char*, m_OptDimseTimeout, getOptDimseTimeout, setOptDimseTimeout);
    DefFun(char*, m_OptAcseTimeout, getOptAcseTimeout, setOptAcseTimeout);
    DefFun(char*, m_OptAeTitle, getOptAeTitle, setOptAeTitle);
    DefFun(char*, m_OptAcTitle, getOptAcTitle, setOptAcTitle);
    DefFun(char*, m_OptCharSet, getOptCharSet, setOptCharSet);

    DefAddList();
    DefDeleteList();
    DefGetListSize();
    DefDicomFileFun(getRGBDicomFile, EDICOM_INPUT_TYPE_RGB);
    DefDicomFileFun(getBMPDicomFile, EDICOM_INPUT_TYPE_BMP);
    DefDicomFileFun(getJPEGDicomFile, EDICOM_INPUT_TYPE_JPEG);

    DefAddParametersList();
    DefDeleteParametersList();
    DefGetParametersListSize();
    DefDicomFileParametersFun();
    DefCalibrationRegionValue();

    DefileDicomCallBack();

private:
    FILENAMELIST m_InputFileList;
    FILEPARAMETERSLIST m_InputFileListParameters;
    INSTERDATALIST m_InputFileRgbList;
};

#endif // DICOMPARAMETERS_H
