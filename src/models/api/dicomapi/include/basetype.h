/**
 * @file basetype.h
 * @brief
 * <AUTHOR>
 * @version Ver 0.1 2018
 *
 */

#ifndef _BASE_TYPE_H_
#define _BASE_TYPE_H_

#include "basedefine.h"
#include "basesize.h"
#include "basememory.h"
#include "basedebug.h"
#include <endian.h>
#include <sys/types.h>

#define FOR(cnt) for (int i = 0; i < (cnt); ++i)

typedef enum
{
    SOCKET_DATA_BIT_START = 0,
    SOCKET_DATA_DATA_HEAD_LEN = 4,
    SOCKET_DATA_DATA_LEN = 8,
    SOCKET_DATA_DATA_DEST = 12,
} SOCKET_DATA_DATA_LEN_BIT;

typedef enum
{
    EN_SYSTEM_INFORMATION_TYPE_NONE = 0,
    EN_SYSTEM_INFORMATION_TYPE_START_CODE = 1,
    EN_SYSTEM_INFORMATION_TYPE_END_CODE = 2,
    EN_SYSTEM_INFORMATION_TYPE_KEY_ENTER_EVENT = 3,
    EN_SYSTEM_INFORMATION_TYPE_KEY_MOVE_EVENT = 4,
    EN_SYSTEM_INFORMATION_TYPE_VOLUMES_SPACING = 5,
    EN_SYSTEM_INFORMATION_TYPE_WINDOWS_SIZE = 6,
    EN_SYSTEM_INFORMATION_TYPE_ANGLE_ROTATION = 7,
    EN_SYSTEM_INFORMATION_TYPE_ANGLE_RESTORE = 8,
    EN_SYSTEM_INFORMATION_TYPE_MODE_SIZE = 9,
    EN_SYSTEM_INFORMATION_TYPE_MODE_RESTORE = 10,
    EN_SYSTEM_INFORMATION_TYPE_VIDEO_FRAME = 127,
    EN_SYSTEM_INFORMATION_TYPE_HID_PROGRESSBAR = 404,
    EN_SYSTEM_INFORMATION_TYPE_MAX,
} EN_SYSTEM_INFORMATION_TYPE;

typedef enum
{
    EN_PRINT_SERVICE_TYPE_REPORT,
    EN_PRINT_SERVICE_TYPE_IMAGE,
    EN_PRINT_SERVICE_TYPE_VIDEO,
    EN_PRINT_SERVICE_TYPE_MAX
} EN_PRINT_SERVICE_TYPE;

typedef enum
{
    EN_PRINT_SERVICE_PAGESIZE_TYPE_A4,
    EN_PRINT_SERVICE_PAGESIZE_TYPE_LETTER,
    EN_PRINT_SERVICE_PAGESIZE_TYPE_LEGAL,
    EN_PRINT_SERVICE_PAGESIZE_TYPE_B5,
    EN_PRINT_SERVICE_PAGESIZE_TYPE_MAX
} EN_PRINT_SERVICE_PAGESIZE_TYPE;

typedef enum
{
    EN_PRINT_SERVICE_QUALITY_TYPE_NONE,
    EN_PRINT_SERVICE_QUALITY_TYPE_Economy,
    EN_PRINT_SERVICE_QUALITY_TYPE_STANDARD,
    EN_PRINT_SERVICE_QUALITY_TYPE_HIGH,
    EN_PRINT_SERVICE_QUALITY_TYPE_PHOTO,
    EN_PRINT_SERVICE_QUALITY_TYPE_HIGHTPHOTO,
    EN_PRINT_SERVICE_QUALITY_TYPE_BEST,
    EN_PRINT_SERVICE_QUALITY_TYPE_MAX
} EN_PRINT_SERVICE_QUALITY_TYPE;

typedef enum
{
    EN_PRINT_SERVICE_ORIENTATION_TYPE_0 = 0,
    EN_PRINT_SERVICE_ORIENTATION_TYPE_90 = 90,
    EN_PRINT_SERVICE_ORIENTATION_TYPE_MAX
} EN_PRINT_SERVICE_ORIENTATION_TYPE;

typedef struct SPrinterServiceItem
{
    char* pPrinterName;
    int iPageSize;
    int iQuality;
    int iOrientation;

    void Init()
    {
        pPrinterName = (char*)baseMalloc(BASE_STRING_LEN_MIN + 1);
        if (pPrinterName)
        {
            baseMemset(pPrinterName, 0x0, sizeof(pPrinterName));
        }

        iPageSize = 0;
        iQuality = 0;
        iOrientation = 0;
    }

    SPrinterServiceItem()
    {
        Init();
    }

    ~SPrinterServiceItem()
    {
        if (pPrinterName)
        {
            baseFree(pPrinterName);
        }
    }
} SPrinterServiceItem;

typedef enum
{
    EN_SET_PRINTER_FW_1020 = 0,
    EN_SET_PRINTER_FW_OTHER,
    EN_SET_PRINTER_FW_MAX
} EN_SET_PRINTER_FW;

typedef enum
{
    EN_SET_PRINTER_VERSION_1 = 0,
    EN_SET_PRINTER_VERSION_2,
    EN_SET_PRINTER_VERSION_3,
    EN_SET_PRINTER_VERSION_MAX
} EN_SET_PRINTER_VERSION;

typedef enum
{
    EN_INPUT_PPD_FILE_OK = 0,
    EN_INPUT_PPD_FILE_PATH_ERROR = -1,
    EN_INPUT_PPD_FILE_NAME_ERROR = -2,
    EN_INPUT_PPD_FILE_HAVE_NAME_ERROR = -3,
    EN_INPUT_PPD_FILE_NO_NICKNAME_ERROR = -4,
    EN_INPUT_PPD_FILE_HAVE_THE_SAME_MODEL_NAME_ERROR = -5,
    EN_INPUT_PPD_FILE_NO_BLOCKS_ERROR = -6,
    EN_INPUT_PPD_FILE_MAX = -10
} EN_INPUT_PPD_FILE;

typedef enum
{
    EDICOM_PARAMETERS_TYPE_StudyDate = 0,
    EDICOM_PARAMETERS_TYPE_StudyTime = 1,
    EDICOM_PARAMETERS_TYPE_StudyID = 2,
    EDICOM_PARAMETERS_TYPE_PatientsName = 3,
    EDICOM_PARAMETERS_TYPE_PatientID = 4,
    EDICOM_PARAMETERS_TYPE_PatientsBirthDate = 5,
    EDICOM_PARAMETERS_TYPE_AccessionNumber = 6,
    EDICOM_PARAMETERS_TYPE_PatientsAge = 7,
    EDICOM_PARAMETERS_TYPE_PatientsSex = 8,
    EDICOM_PARAMETERS_TYPE_ReferringPhysiciansName = 9,
    EDICOM_PARAMETERS_TYPE_Modality = 10,
    EDICOM_PARAMETERS_TYPE_FrameTime = 11,
    EDICOM_PARAMETERS_TYPE_SourceApplicationEntityTitle = 12,
    EDICOM_PARAMETERS_TYPE_StudyInstanceID = 13,
    EDICOM_PARAMETERS_TYPE_SeriesInstanceID = 14,
    EDICOM_PARAMETERS_TYPE_ServerIP = 15,
    EDICOM_PARAMETERS_TYPE_ServerPort = 16,
    EDICOM_PARAMETERS_TYPE_JPEGLevel = 17,
    EDICOM_PARAMETERS_TYPE_FileType = 18,
    EDICOM_PARAMETERS_TYPE_FrameRate = 19,
    EDICOM_PARAMETERS_TYPE_PixelSpacing = 20,
    EDICOM_PARAMETERS_TYPE_DoctorsName = 21,
    EDICOM_PARAMETERS_TYPE_CaseHistory = 22,
    EDICOM_PARAMETERS_TYPE_StudyDescription = 23,
    EDICOM_PARAMETERS_TYPE_InstitutionName = 24,
    EDICOM_PARAMETERS_TYPE_Manufacturer = 25,
    EDICOM_PARAMETERS_TYPE_SopInstanceID = 26,
    EDICOM_PARAMETERS_TYPE_ImageNumber = 27,
    EDICOM_PARAMETERS_TYPE_ReferringPhysicianName = 28,
    EDICOM_PARAMETERS_TYPE_SeriesDate = 29,
    EDICOM_PARAMETERS_TYPE_SeriesTime = 30,
    EDICOM_PARAMETERS_TYPE_SendDicomDataType = 31,
    EDICOM_PARAMETERS_TYPE_SOPInstanceID = 32,
    EDICOM_PARAMETERS_TYPE_OptTimeout = 33,
    EDICOM_PARAMETERS_TYPE_OptDimseTimeout = 34,
    EDICOM_PARAMETERS_TYPE_OptAcseTimeout = 35,
    EDICOM_PARAMETERS_TYPE_OptAeTitle = 36,
    EDICOM_PARAMETERS_TYPE_OptAcTitle = 37,
    EDICOM_PARAMETERS_TYPE_OptCharSet = 38,
    EDICOM_PARAMETERS_TYPE_MAX
} EDICOM_PARAMETERS_TYPE;

typedef struct FileHead
{
    int FileType;
    int DataType;
    int Frame;
    unsigned short Width;
    unsigned short Height;
    void Init()
    {
        FileType = 0;
        DataType = 0;
        Frame = 0;
        Width = 640;
        Height = 512;
    }
    FileHead()
    {
        Init();
    }
} FileStruct_head, *pFileStruct_head;

#define PUT_2B(array, offset, value)                                                                                   \
    (array[offset] = (char)((value)&0xFF), array[offset + 1] = (char)(((value) >> 8) & 0xFF))
#define PUT_4B(array, offset, value)                                                                                   \
    (array[offset] = (char)((value)&0xFF), array[offset + 1] = (char)(((value) >> 8) & 0xFF),                          \
     array[offset + 2] = (char)(((value) >> 16) & 0xFF), array[offset + 3] = (char)(((value) >> 24) & 0xFF))

#define WIDTHBYTES(i) (((i) + 31) / 32 * 4)

typedef struct _DcmDataset_Tag
{
    char StudyDate[20];
    char StudyTime[20];
    char StudyID[20];
    char PatientsName[30];
    char PatientID[30];
    char PatientsBirthDate[20];
    char AccessionNumber[30]; // added 2010-07-13
    char PatientsAge[10];
    char PatientsSex[4];
    char ReferringPhysiciansName[30];
    char Modality[10];
    char FrameTime[10];
    char SourceApplicationEntityTitle[64]; // added 2011-04-06
    char StudyInstanceID[128];
    char SeriesInstanceID[128];
    void Init()
    {
        memset(&StudyDate, 0x0, sizeof(StudyDate));
        memset(&StudyTime, 0x0, sizeof(StudyTime));
        memset(&StudyID, 0x0, sizeof(StudyID));
        memset(&PatientsName, 0x0, sizeof(PatientsName));
        memset(&PatientID, 0x0, sizeof(PatientID));
        memset(&PatientsBirthDate, 0x0, sizeof(PatientsBirthDate));
        memset(&AccessionNumber, 0x0, sizeof(AccessionNumber));
        memset(&PatientsAge, 0x0, sizeof(PatientsAge));
        memset(&PatientsSex, 0x0, sizeof(PatientsSex));
        memset(&ReferringPhysiciansName, 0x0, sizeof(ReferringPhysiciansName));
        memset(&Modality, 0x0, sizeof(Modality));
        memset(&FrameTime, 0x0, sizeof(FrameTime));
        memset(&SourceApplicationEntityTitle, 0x0, sizeof(SourceApplicationEntityTitle));
        memset(&StudyInstanceID, 0x0, sizeof(StudyInstanceID));
        memset(&SeriesInstanceID, 0x0, sizeof(SeriesInstanceID));
    }

    _DcmDataset_Tag()
    {
        Init();
    }
} DcmDataset_Tag, *PDcmDataset_Tag;

typedef enum
{
    EDICOM_INPUT_TYPE_RGB = 0,
    EDICOM_INPUT_TYPE_BMP = 1,
    EDICOM_INPUT_TYPE_JPEG = 2,
    EDICOM_INPUT_TYPE_MP4 = 3,
    EDICOM_INPUT_TYPE_RGB_VIDEO = 4,
    EDICOM_INPUT_TYPE_MAX
} EDICOM_INPUT_TYPE;

typedef struct _Dcm_InputFile
{
    char PathName[128];
    int FileType;
    bool isFloder;
    unsigned char* pRGB;
    int iLen;
    int iW;
    int iH;
    void Init()
    {
        memset(&PathName, 0x0, sizeof(PathName));
        FileType = EDICOM_INPUT_TYPE_RGB;
        isFloder = false;
        pRGB = NULL;
        iLen = 0;
        iW = 0;
        iH = 0;
    }

    _Dcm_InputFile()
    {
        Init();
    }

    ~_Dcm_InputFile()
    {
        if (pRGB != NULL)
        {
            delete pRGB;
            pRGB = NULL;
        }
    }
} Dcm_InputFile, *PDcm_InputFile;

typedef struct _Dcm_InputFileParameters
{
    int StartTypeParameters;
    int EndTypeParameters;
    char pParameters[256];
    void Init()
    {
        memset(&pParameters, 0x0, sizeof(pParameters));
        StartTypeParameters = 0;
        EndTypeParameters = 0;
    }

    _Dcm_InputFileParameters()
    {
        Init();
    }

    ~_Dcm_InputFileParameters()
    {
    }
} Dcm_InputFileParameters, *PDcm_InputFileParameters;

typedef enum
{
    EDICOM_PARAMETERS_Sequence_RegionSpatialFormat = 0,
    EDICOM_PARAMETERS_Sequence_RegionDataType = 1,
    EDICOM_PARAMETERS_Sequence_RegionFlags = 2,
    EDICOM_PARAMETERS_Sequence_RegionLocationMinX0 = 3,
    EDICOM_PARAMETERS_Sequence_RegionLocationMinY0 = 4,
    EDICOM_PARAMETERS_Sequence_RegionLocationMaxX1 = 5,
    EDICOM_PARAMETERS_Sequence_RegionLocationMaxY1 = 6,
    EDICOM_PARAMETERS_Sequence_ReferencePixelX0 = 7,
    EDICOM_PARAMETERS_Sequence_ReferencePixelY0 = 8,
    EDICOM_PARAMETERS_Sequence_PhysicalUnitsXDirection = 9,
    EDICOM_PARAMETERS_Sequence_PhysicalUnitsYDirection = 10,
    EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueX = 11,
    EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueY = 12,
    EDICOM_PARAMETERS_Sequence_PhysicalDeltaX = 13,
    EDICOM_PARAMETERS_Sequence_PhysicalDeltaY = 14,
    EDICOM_PARAMETERS_Sequence_TransducerFrequency = 15,
    EDICOM_PARAMETERS_Sequence_PulseRepetitionFrequency = 16,
    EDICOM_PARAMETERS_Sequence_PixelSpacing = 17,
    EDICOM_PARAMETERS_Sequence_Max,
} EDICOM_PARAMETERS_Sequence;
#endif
