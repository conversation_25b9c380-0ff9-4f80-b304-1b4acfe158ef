/**
 * @file basetime.h
 * @brief
 * <AUTHOR>
 * @version Ver 0.1 2018
 *
 */

#ifndef _BASE_TIME_H_
#define _BASE_TIME_H_

#include "basedefine.h"

long long getCurrentMonotonic();
long long getCurrentTimestamp()
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ((long long)ts.tv_sec) * 1000 + ts.tv_nsec / 1000000;
}
void getCurrentAllTimeValue(char* pTime);
void getCurrentDateTimeValue(char* pTime);
void getCurrentTimeValue(char* pTime);
void setTimeSleep(unsigned int milliseconds);

#endif
