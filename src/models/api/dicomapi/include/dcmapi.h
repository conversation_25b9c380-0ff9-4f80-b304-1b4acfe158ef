#ifndef DCMAPI_H
#define DCMAPI_H

void resetElementsID();

int createRGBFileMake(const char* pSaveStoragePath);
int createBMPFileMake(const char* pSaveStoragePath);
int createRLEFileMake(const char* pSaveStoragePath);

int createDicomFile(const char* pSaveStoragePath);
int createFileAndSendToDicom();

bool isDicomServerConnect(const char* pAetValue, const char* pAeCValue);

int createBMPMake();
int createJpegFileMake();
int dcmsend(const char* pPath);
int sendDicomFile(const char* pPath);
const char* findScuWorkList(const char* pInValue);
int getDicomFileParameters(int argc, char* argv[]);
const char* getDicomFileParameters(const char* pPath);
int cTRle(const char* opt_ifname, const char* opt_ofname);

#endif // DCMAPI_H
