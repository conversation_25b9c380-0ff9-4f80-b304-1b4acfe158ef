/**
 * @file basesize.h
 * @brief
 * <AUTHOR>
 * @version Ver 0.1 2018
 *
 */

#ifndef _BASE_SIZE_H_
#define _BASE_SIZE_H_

#define FILE_STRING_LEN 256
#define U16HZ_STRING_LEN 64
#define HZ_STRING_LEN 127
#define BASE_SPS_LEN_MIN 32
#define BASE_HZ_LEN_MIN 256
#define BASE_STRING_LEN_MIN 256
#define BASE_STRING_BUFF_LEN_MAX 4096

#define INPUT_MENU_ITEM 10
#define INPUT_MENU_PY_SIZE 30
#define INPUT_MENU_HZ_SIZE 16
#define INPUT_MENU_HZ_SIZE_START 32
#define INPUT_MENU_HZ_SIZE_LMIN 5
#define INPUT_MENU_HZ_SIZE_LMIN_OUT 3
#define INPUT_MENU_HZ_SIZE_LMAX 9
#define INPUT_MENU_KEY_SIZE 9

#define INPUT_MENU_HZ_DISPLAY_SIZE 18
#define INPUT_MENU_INDEX_MAX_SIZE 900
#define INPUT_MENU_EMPTY_MAX_SIZE 124

#define MAX_FILE_SIZE 0x40000000
#define IDATA_STRING_LEN 64
#define BASE_STRING_LEN_MAX 0xffff

#define CIKUHZ_STRING_LEN 12080
#define CIKUHZ_CHAR_LEN 3

#define SOCKET_OPT_BASE_SIZE 1024

#define SOCKET_STRING_DATA_TAGET 28
#define SOCKET_DATA_CONNECT_LEN 8
#define SOCKET_DATA_CONNECT_FLAG 16
#define SOCKET_DATA_CMDPACKET_MINLEN 12
#define SOCKET_DATA_CMDPACKET_MIN_WAIT_TIME 200 // ms
#define SOCKET_WAIT_TIME 500                    // ms
#define SOCKET_WAIT_TIME_FOR_SELECT 3           // s
#define SOCKET_WAIT_TIME_FOR_SENDTO 16          // ms
#define SOCKET_APPLY_COUNT 5
#define TIME_STRING_LEN 128
#define IDATA_STRING_LEN 64
#define IDATA_STRING_PULS_LEN 192
#define SOCKET_PORT_MIN 1
#define SOCKET_PORT_MAX 0xffff
#define BASE_STRING_LEN_MAX 0xffff
#define SOCKET_SEND_BIT_MAX 1460
#define SOCKET_RESEND_MAX 0x20
#define BASE_DATA_IMAGE_LEN_MAX (0xf1dc0)
#define BASE_DATA_IMAGE_LEN_MAX_D (0x140a000)
#define FREEHAND3D_SHOWPICTURE_SLEEPTIME_MAX 200
#define FREEHAND3D_SHOWWINDOWS_SLEEPTIME_MAX 500
#define FREEHAND3D_MOVE_EVENT_SLEEPTIME 600
#define FREEHAND3D_MOVE_EVENT_SLEEPTIME_BIT_MAX 70
#define FREEHAND3D_AVTICE10TIMES_SLEEPTIME_MAX 300

#define QBIT_WINDOWS_SIZE_WIDTH 0x280
#define QBIT_WINDOWS_SIZE_HIGHT 0x200
#define QBIT_WINDOWS_MOVE_START_X 773
#define QBIT_WINDOWS_MOVE_END_X -250
#define QBIT_WINDOWS_MOVE_START_Y 712
#define QBIT_WINDOWS_MOVE_END_Y -55

#define QBIT_WINDOWS_SIZE_ZOOM_IN_WIDTH 800
#define QBIT_WINDOWS_SIZE_ZOOM_IN_HIGHT 640
#define QBIT_WINDOWS_ZOOM_IN_MOVE_START_X 911
#define QBIT_WINDOWS_ZOOM_IN_MOVE_END_X -112

#define QBIT_WINDOWS_SIZE_ZOOM_IN_CC (0.9)

#define FREEHAND3D_PICURECATCHLEN_WS_MAX 35
#define FREEHAND3D_PICURECATCHLEN_WD_MAX 580
#define FREEHAND3D_PICURECATCHLEN_HS_MAX 105
#define FREEHAND3D_PICURECATCHLEN_HD_MAX 380
#define FREEHAND3D_PICURECATCHLEN_MIX_HW 100
#define FREEHAND3D_PICURECATCHLEN_BASE_STEP_MAX 2
#define FREEHAND3D_PICURECATCHLEN_BASE_STEP2_MAX 4
#define FREEHAND3D_PICURECATCH_NITS_MAX 30
#define DefineMaxPictureSize (983040)
#define MAX_LEN 1024

#endif
