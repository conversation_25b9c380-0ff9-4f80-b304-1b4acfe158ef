#include "dicomapi.h"
#include "dcmapi.h"
#include <stdio.h>
#include "basedefine.h"
#include "dicomparameters.h"
#include "baselock.h"
#include "jpegcode.h"
#include "study.h"
#include "dicomgenerateuniqueidentifier.h"
#include "setting.h"
#include <QDir>
#include "calibrationregiondata.h"
#include "variantutil.h"
#include "bfpnames.h"
#include "calibrationregionmodel.h"
#include "resource.h"
#include "basetime.h"
#include "basefile.h"
#include "dataaccesslayerhelper.h"
#include <QTextCodec>
#include <QDomDocument>
#define LOGD(...) ((void)qDebug(__VA_ARGS__))
#define QSTR2CSTR(x) (x.toStdString().c_str())
#define SETP(x, y) DCMSetDicomFileParameter(x, QSTR2CSTR(y))
static WmLock m_mutexLock = NULL;
static QList<SonoParameters*> m_SonoParametersList;
static QList<CalibrationRegionData*> m_RegionDataList;
static DcmInfoStruct m_dcminfo;
bool baseOpenDir(const char* pathname)
{
    QDir dir;
    if (!dir.exists(pathname))
    {
        dir.mkpath(pathname);
    }
}
void dicomCallBack(int iType, const char* Target, const char* pValue, const char* pBuffer, int iBuffLen)
{
}
void DCMInit()
{
    QString dcmWorkPath = Resource::getWritableDir("") + Resource::pathSeparator + "dicom";
    QDir dir;
    if (!dir.exists(dcmWorkPath))
    {
        dir.mkpath(dcmWorkPath);
    }
    std::string pathstring = dcmWorkPath.toStdString();
    const char* pStoragePath = pathstring.c_str();
    QString dicomDicPath = Resource::resPath + "dicom";
    std::string dicpath = dicomDicPath.toStdString();
    if (NULL == m_mutexLock)
    {
        m_mutexLock = baseCreateLock("DICOM_LOCK");
    }
    char pDicomDath[128] = {0};
    char pCreateDicomFile[128] = {0};
    snprintf(pDicomDath, 128, "%s/dicom_ios.dic", dicpath.c_str());
    snprintf(pCreateDicomFile, 128, "%s/createdicom.dcm", pStoragePath);
    CIParameter().setDicomDath(pDicomDath);
    CIParameter().setStoragePath(pStoragePath);
    CIParameter().setCreateDicomFilePath(pCreateDicomFile);
    CIParameter().setSendDicomDataType("0");
    CIParameter().setJpegLevel("99");
    CIParameter().setOptTimeout("10");
    CIParameter().setOptDimseTimeout("0");
    CIParameter().setOptAcseTimeout("30");
    baseDeleteFileMask(pStoragePath, ".dcm");
    CIParameter().setDICOMCALLBACK(dicomCallBack);
    CIParameter().setOptAcTitle("DCMSEND");
    CIParameter().setOptAeTitle("ANY-SCP");
    CIParameter().setFrameTime("100");
    // printf("path %s\n",CDicomParameter::instance().getStoragePath());
}

void DCMAddPictureFileForMakeDicomFile(const char* pStoragePath, bool iIsFloder)
{
    Dcm_InputFile* dcmFile = new Dcm_InputFile();
    strncpy(dcmFile->PathName, pStoragePath, 128);
    dcmFile->FileType = atoi(CIParameter().getFileType());
    dcmFile->isFloder = (1 == iIsFloder);
    baseMutexLock(m_mutexLock);
    CIParameter().addDicomFileList(dcmFile);
    baseUnLock(m_mutexLock);
}

void DCMMakeBMPToDicomFile()
{
    baseMutexLock(m_mutexLock);
    createBMPFileMake(NULL);
    CIParameter().delAllDicomFile();
    baseUnLock(m_mutexLock);
}

void DCMMakeJpegToDicomFile()
{
    baseMutexLock(m_mutexLock);
    createJpegFileMake();
    CIParameter().delAllDicomFile();
    baseUnLock(m_mutexLock);
}

int DCMAddRGBDataForMakeDicomFile(const unsigned char* buf, int iLen, int iW, int iH)
{
    Dcm_InputFile* dcmFile = new Dcm_InputFile();
    dcmFile->isFloder = false;
    dcmFile->FileType = EDICOM_INPUT_TYPE_RGB;

    int iSendFileType = atoi(CIParameter().getSendDicomDataType());

    LOGD("AddRGBDataForMakeDicomFile  %d  %d  %d\n", iLen, iW, iH);
    if (0 == iSendFileType)
    {
        //        JPEGCodec jpegc; \
//        jpegc.setQuality(99); \
//        JPEGCodec::Buffer buffer; \
//        jpegc.encode((unsigned char const *)buf, iLen, iW, iH, iW*4, buffer);

        DefJpegDecode(buf, iLen, iW, iH);
        dcmFile->pRGB = (uint8_t*)malloc(buffer.size());
        if (NULL == dcmFile->pRGB)
        {
            return false;
        }
        memcpy(dcmFile->pRGB, buffer.data(), buffer.size());
        dcmFile->iLen = buffer.size();
    }
    else if (2 == iSendFileType)
    {
        dcmFile->pRGB = (uint8_t*)malloc(iLen);
        if (NULL == dcmFile->pRGB)
        {
            return false;
        }
        memcpy(dcmFile->pRGB, buf, iLen);
        dcmFile->iLen = iLen;
    }
    else
    {
        dcmFile->pRGB = (uint8_t*)malloc(iLen);
        if (NULL == dcmFile->pRGB)
        {
            return false;
        }
        memcpy(dcmFile->pRGB, buf, iLen);
        dcmFile->iLen = iLen;
    }

    dcmFile->iW = iW;
    dcmFile->iH = iH;
    baseMutexLock(m_mutexLock);
    CIParameter().addDicomFileList(dcmFile);
    baseUnLock(m_mutexLock);
}

int DCMMakeRGBToDicomFile(const char* pStoragePath)
{
    baseMutexLock(m_mutexLock);
    int iRet = createDicomFile(pStoragePath);
    CIParameter().delAllDicomFile();
    baseUnLock(m_mutexLock);
    return (0 == iRet);
}

void DCMResetElementsID()
{
    baseMutexLock(m_mutexLock);
    resetElementsID();
    baseUnLock(m_mutexLock);
}

bool DCMIsConnect(const char* pAetValue, const char* pAeCallValue)
{
    bool bRet = false;
    if (NULL == pAeCallValue)
    {
        return bRet;
    }
    baseMutexLock(m_mutexLock);
    bRet = isDicomServerConnect(pAetValue, pAeCallValue);
    baseUnLock(m_mutexLock);
    return bRet;
}

void DCMSetDicomFileParameter(int target, const char* pValue)
{
    baseMutexLock(m_mutexLock);
    switch (target)
    {
        DefSetValue(EDICOM_PARAMETERS_TYPE_StudyDate, setStudyDate);
        DefSetValue(EDICOM_PARAMETERS_TYPE_StudyTime, setStudyTime);
        DefSetValue(EDICOM_PARAMETERS_TYPE_StudyID, setStudyID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PatientsName, setPatientsName);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PatientID, setPatientID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PatientsBirthDate, setPatientsBirthDate);
        DefSetValue(EDICOM_PARAMETERS_TYPE_AccessionNumber, setAccessionNumber);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PatientsAge, setPatientsAge);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PatientsSex, setPatientsSex);
        DefSetValue(EDICOM_PARAMETERS_TYPE_ReferringPhysiciansName, setReferringPhysiciansName);
        DefSetValue(EDICOM_PARAMETERS_TYPE_Modality, setModality);
        DefSetValue(EDICOM_PARAMETERS_TYPE_FrameTime, setFrameTime);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SourceApplicationEntityTitle, setSourceApplicationEntityTitle);
        DefSetValue(EDICOM_PARAMETERS_TYPE_StudyInstanceID, setStudyInstanceUID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SeriesInstanceID, setSeriesInstanceUID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_ServerIP, setServerIP);
        DefSetValue(EDICOM_PARAMETERS_TYPE_ServerPort, setServerPort);
        DefSetValue(EDICOM_PARAMETERS_TYPE_JPEGLevel, setJpegLevel);
        DefSetValue(EDICOM_PARAMETERS_TYPE_FileType, setFileType);
        DefSetValue(EDICOM_PARAMETERS_TYPE_FrameRate, setFrameRate);
        DefSetValue(EDICOM_PARAMETERS_TYPE_PixelSpacing, setPixelSpacing);
        DefSetValue(EDICOM_PARAMETERS_TYPE_DoctorsName, setDoctorsName);
        DefSetValue(EDICOM_PARAMETERS_TYPE_CaseHistory, setCaseHistory);
        DefSetValue(EDICOM_PARAMETERS_TYPE_StudyDescription, setStudyDescription);
        DefSetValue(EDICOM_PARAMETERS_TYPE_InstitutionName, setInstitutionName);
        DefSetValue(EDICOM_PARAMETERS_TYPE_Manufacturer, setManufacturer);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SopInstanceID, setSopInstanceID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_ImageNumber, setImageNumber);
        DefSetValue(EDICOM_PARAMETERS_TYPE_ReferringPhysicianName, setReferringPhysicianName);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SeriesDate, setSeriesDate);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SeriesTime, setSeriesTime);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SendDicomDataType, setSendDicomDataType);
        DefSetValue(EDICOM_PARAMETERS_TYPE_SOPInstanceID, setSOPInstanceUID);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptTimeout, setOptTimeout);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptDimseTimeout, setOptDimseTimeout);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptAcseTimeout, setOptAcseTimeout);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptAeTitle, setOptAeTitle);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptAcTitle, setOptAcTitle);
        DefSetValue(EDICOM_PARAMETERS_TYPE_OptCharSet, setOptCharSet);
    default:
    {
        break;
    }
    }
    baseUnLock(m_mutexLock);
}

const char* DCMGetDicomFileParameter(int target, char* pValue)
{
    switch (target)
    {
        DefGetValue(EDICOM_PARAMETERS_TYPE_StudyDate, getStudyDate);
        DefGetValue(EDICOM_PARAMETERS_TYPE_StudyTime, getStudyTime);
        DefGetValue(EDICOM_PARAMETERS_TYPE_StudyID, getStudyID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PatientsName, getPatientsName);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PatientID, getPatientID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PatientsBirthDate, getPatientsBirthDate);
        DefGetValue(EDICOM_PARAMETERS_TYPE_AccessionNumber, getAccessionNumber);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PatientsAge, getPatientsAge);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PatientsSex, getPatientsSex);
        DefGetValue(EDICOM_PARAMETERS_TYPE_ReferringPhysiciansName, getReferringPhysiciansName);
        DefGetValue(EDICOM_PARAMETERS_TYPE_Modality, getModality);
        DefGetValue(EDICOM_PARAMETERS_TYPE_FrameTime, getFrameTime);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SourceApplicationEntityTitle, getSourceApplicationEntityTitle);
        DefGetValue(EDICOM_PARAMETERS_TYPE_StudyInstanceID, getStudyInstanceUID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SeriesInstanceID, getSeriesInstanceUID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_ServerIP, getServerIP);
        DefGetValue(EDICOM_PARAMETERS_TYPE_ServerPort, getServerPort);
        DefGetValue(EDICOM_PARAMETERS_TYPE_JPEGLevel, getJpegLevel);
        DefGetValue(EDICOM_PARAMETERS_TYPE_FileType, getFileType);
        DefGetValue(EDICOM_PARAMETERS_TYPE_FrameRate, getFrameRate);
        DefGetValue(EDICOM_PARAMETERS_TYPE_PixelSpacing, getPixelSpacing);
        DefGetValue(EDICOM_PARAMETERS_TYPE_DoctorsName, getDoctorsName);
        DefGetValue(EDICOM_PARAMETERS_TYPE_CaseHistory, getCaseHistory);
        DefGetValue(EDICOM_PARAMETERS_TYPE_StudyDescription, getStudyDescription);
        DefGetValue(EDICOM_PARAMETERS_TYPE_InstitutionName, getInstitutionName);
        DefGetValue(EDICOM_PARAMETERS_TYPE_Manufacturer, getManufacturer);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SopInstanceID, getSopInstanceID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_ImageNumber, getImageNumber);
        DefGetValue(EDICOM_PARAMETERS_TYPE_ReferringPhysicianName, getReferringPhysicianName);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SeriesDate, getSeriesDate);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SeriesTime, getSeriesTime);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SendDicomDataType, getSendDicomDataType);
        DefGetValue(EDICOM_PARAMETERS_TYPE_SOPInstanceID, getSOPInstanceUID);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptTimeout, getOptTimeout);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptDimseTimeout, getOptDimseTimeout);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptAcseTimeout, getOptAcseTimeout);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptAeTitle, getOptAeTitle);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptAcTitle, getOptAcTitle);
        DefGetValue(EDICOM_PARAMETERS_TYPE_OptCharSet, getOptCharSet);
    default:
    {
        break;
    }
    }
    return NULL;
}

void DCMInsertDicomFileSequenceItem(int iSequence, int iTarget, double doubleValue)
{
    baseMutexLock(m_mutexLock);
    CIParameter().setCalibrationRegionValue(iSequence, iTarget, doubleValue);
    baseUnLock(m_mutexLock);
}

void DCMSetDicomFileParameters(int iStartTarget, int iEndTarget, const char* strValue)
{
    Dcm_InputFileParameters* Di = new Dcm_InputFileParameters();
    Di->StartTypeParameters = iStartTarget;
    Di->EndTypeParameters = iEndTarget;
    memset(Di->pParameters, 0, sizeof(Di->pParameters));
    strncpy(Di->pParameters, strValue, strlen(strValue));
    baseMutexLock(m_mutexLock);
    CIParameter().addDicomFileParametersList(Di);
    baseUnLock(m_mutexLock);
}

void DCMDelDicomFileParameters()
{
    baseMutexLock(m_mutexLock);
    CIParameter().delAllDicomFileParameters();
    baseUnLock(m_mutexLock);
}

int DCMSendDICOM()
{
    int iRet = 0;
    baseMutexLock(m_mutexLock);
    iRet = createFileAndSendToDicom();
    CIParameter().delAllDicomFile();
    baseUnLock(m_mutexLock);
    return iRet;
}
inline int UTF82UnicodeOne(const char* utf8, wchar_t& wch)
{
    unsigned char firstCh = utf8[0];
    if (firstCh >= 0xC0)
    {
        int afters, code;
        if ((firstCh & 0xE0) == 0xC0)
        {
            afters = 2;
            code = firstCh & 0x1F;
        }
        else if ((firstCh & 0xF0) == 0xE0)
        {
            afters = 3;
            code = firstCh & 0xF;
        }
        else if ((firstCh & 0xF8) == 0xF0)
        {
            afters = 4;
            code = firstCh & 0x7;
        }
        else if ((firstCh & 0xFC) == 0xF8)
        {
            afters = 5;
            code = firstCh & 0x3;
        }
        else if ((firstCh & 0xFE) == 0xFC)
        {
            afters = 6;
            code = firstCh & 0x1;
        }
        else
        {
            wch = firstCh;
            return 1;
        }
        for (int k = 1; k < afters; ++k)
        {
            if ((utf8[k] & 0xC0) != 0x80)
            {
                wch = firstCh;
                return 1;
            }

            code <<= 6;
            code |= (unsigned char)utf8[k] & 0x3F;
        }

        wch = code;
        return afters;
    }
    else
    {
        wch = firstCh;
    }

    return 1;
}

int UTF82Unicode(const char* utf8Buf, wchar_t* pUniBuf, int utf8Leng)
{
    int i = 0, count = 0;
    while (i < utf8Leng)
    {
        i += UTF82UnicodeOne(utf8Buf + i, pUniBuf[count]);
        count++;
    }

    return count;
}
void getCurrentDWorkList(const char* worklistfile)
{
    const char* pValue = "";
    int argcd = 1;
    char* argvd[] = {(char*)worklistfile};
    baseMutexLock(m_mutexLock);
    CIParameter().clearDicomStr();
    getDicomFileParameters(argcd, argvd);
    pValue = CIParameter().getDicomStr().c_str();
    baseUnLock(m_mutexLock);

    int len = strlen(pValue);
    wchar_t* wcs = new wchar_t[len * 2];
    int nRet = UTF82Unicode(pValue, wcs, len);
    char* jcs = new char[nRet];
    for (int i = 0; i < nRet; i++)
    {
        jcs[i] = (char)wcs[i];
    }

    // jstring retString = ev->NewString(jcs, nRet);
    delete[] wcs;
    delete[] jcs;
}

/**
 * @brief codeforDicomCharset 根据dicom编码获取时间字符编码
 * @param Charset
 * @return
 */
QString codeforDicomCharset(QString Charset)
{
    QString encodeing = "ISO 8859-1";
    if (Charset.compare("ISO_IR 100") == 0)
    {
    }
    else if (Charset.compare("ISO_IR 144") == 0)
    {
        encodeing = "ISO 8859-5";
    }
    else if (Charset.compare("ISO_IR 192") == 0)
    {
        encodeing = "UTF-8";
    }
    else if (Charset.compare("GB18030") == 0)
    {
        encodeing = "GB18030";
    }
    return encodeing;
}
/**
 * @brief encodexml2Utf8 根据编码将xml转换成统一的utf-8编码的xml文件
 * @param filename
 * @param Charset
 */
void encodexml2Utf8(QString filename, QString Charset)
{
    QString encodeing = codeforDicomCharset(Charset);
    QFile file(filename);
    if (!file.open(QFile::ReadWrite))
        return;
    QTextStream stream(&file);
    QTextCodec* codec = QTextCodec::codecForName(encodeing.toStdString().c_str());
    stream.setCodec(codec);
    QString content = stream.readAll();
    QDomDocument doc;
    if (!doc.setContent(content))
    {
        file.close();
        return;
    }
    file.close();
    QFile wfile(filename);
    if (wfile.open(QFile::WriteOnly | QFile::Text | QFile::Truncate))
    {
        QTextStream out(&wfile);
        stream.setCodec("UTF-8");
        doc.save(out, QDomNode::EncodingFromTextStream);
        wfile.close();
    }
}
int DCMFindWorkList(const char* serverip, int port, int opTimeOut, const char* Charset, const char* patientName,
                    const char* patientId, const char* reequestedProcedureID,
                    const char* scheduledProcedureStepStartDate, const char* aetitle, const char* aecremote,
                    char* outputPath)
{
    //对于patientname 可能涉及到编码转换方面，所以需要根据worklist配置的编码类型做转换
    QString ptnamestr = QString(patientName);
    QTextCodec* utf8 = QTextCodec::codecForName("UTF-8");
    QString encodeing = codeforDicomCharset(Charset);
    QTextCodec* codec = QTextCodec::codecForName(encodeing.toStdString().c_str());

    QString strUnicode = utf8->toUnicode(ptnamestr.toLocal8Bit().data());
    QByteArray gb_bytes = codec->fromUnicode(strUnicode);
    const char* ptname = gb_bytes.toStdString().c_str();
    //-Xx 生成xml文件
    const char* queryStr = "[{\"Target\":\"-P\",\"Value\":\"None\"},\
{\"Target\":\"%s\",\"Value\":\"None\"},\
{\"Target\":\"%d\",\"Value\":\"None\"},\
{\"Target\":\"-Xx\",\"Value\":\"None\"},\
{\"Target\":\"--timeout\",\"Value\":%d},\
{\"Target\":\"-W\",\"Value\":\"None\"},\
{\"Target\":\"-k\",\"Value\":\"SpecificCharacterSet=%s\"},\
{\"Target\":\"-k\",\"Value\":\"PatientName=%s\"},\
{\"Target\":\"-k\",\"Value\":\"RequestedProcedureID=%s\"},\
{\"Target\":\"-k\",\"Value\":\"ScheduledProcedureStepSequence[0].ScheduledProcedureStepStartDate=%s\"},\
{\"Target\":\"-k\",\"Value\":\"ScheduledProcedureStepEndDate\"},\
{\"Target\":\"-k\",\"Value\":\"ScheduledProcedureStepSequence[0].Modality=US\"},\
{\"Target\":\"-k\",\"Value\":\"PatientID=%s\"},\
{\"Target\":\"-k\",\"Value\":\"PatientAge\"},\
{\"Target\":\"-k\",\"Value\":\"PatientBirthDate\"},\
{\"Target\":\"-k\",\"Value\":\"PatientSex\"},\
{\"Target\":\"-k\",\"Value\":\"PatientSize\"},\
{\"Target\":\"-k\",\"Value\":\"PatientWeight\"},\
{\"Target\":\"-k\",\"Value\":\"PatientAddress\"},\
{\"Target\":\"-k\",\"Value\":\"StudyInstanceUID\"},\
{\"Target\":\"-k\",\"Value\":\"AccessionNumber\"},\
{\"Target\":\"-aet\",\"Value\":\"%s\"},\
{\"Target\":\"-aec\",\"Value\":\"%s\"}]";
    const QString str_WorklistItem = ",#,###,#,";
    const QString str_WorklistItemValue = "#,,#,,,#,,#";
    char* queryReal = new char[strlen(queryStr) + 512];
    sprintf(queryReal, queryStr, serverip, port, opTimeOut, Charset, ptname, reequestedProcedureID,
            scheduledProcedureStepStartDate, patientId, aetitle, aecremote);
    qDebug() << __FUNCTION__ << QString(queryReal);

    QString rsDir = CDicomParameter::instance().getStoragePath();
    rsDir = rsDir + Resource::pathSeparator + "worklist";
    std::string pathstring = rsDir.toStdString();
    const char* pStoragePath = pathstring.c_str();
    baseDeleteFileMask(pStoragePath, ".xml");
    SETP(EDICOM_PARAMETERS_TYPE_ServerIP, QString(serverip));
    SETP(EDICOM_PARAMETERS_TYPE_ServerPort, QString::number(port));
    SETP(EDICOM_PARAMETERS_TYPE_OptTimeout, (QString::number(opTimeOut)));
    SETP(EDICOM_PARAMETERS_TYPE_OptCharSet, QString(Charset));
    baseMutexLock(m_mutexLock);
    const char* pOutValue = findScuWorkList(queryReal);
    baseUnLock(m_mutexLock);

    QDir myDir(rsDir);
    int count = 0;
    foreach (const QFileInfo& mfi, myDir.entryInfoList())
    {
        QString filename = mfi.fileName();
        if (filename == "." || filename == "..")
            continue;
        LOGD("workF ==>%s \n", qPrintable(filename));
        encodexml2Utf8(rsDir + Resource::pathSeparator + filename, Charset);
        count++;
    }
    memcpy(outputPath, pStoragePath, strlen(pStoragePath));
    delete[] queryReal;
    return count;
}
void setGDPR()
{
    QDateTime current_date_time = QDateTime::currentDateTime();
    QString current_date = current_date_time.toString("yyyyMMdd");
    QString current_time = current_date_time.toString("hhmmss.z");
    QString current_datetime = current_date_time.toString("yyyyMMdd.hhmm");
    QString current_date_time_ = current_date_time.toString("yyyyMMdd.hhmmss");
    SETP(EDICOM_PARAMETERS_TYPE_PatientsBirthDate, QString("")); // birthdate
    SETP(EDICOM_PARAMETERS_TYPE_PatientsAge, QString(""));
    DCMSetDicomFileParameters(0x0010, 0x1030, ""); // weight
    DCMSetDicomFileParameters(0x0010, 0x1020, ""); // patientSize
    SETP(EDICOM_PARAMETERS_TYPE_PatientsName, ("DeName-" + current_date_time_));
    SETP(EDICOM_PARAMETERS_TYPE_PatientID, ("DeId-" + current_date_time_));
    SETP(EDICOM_PARAMETERS_TYPE_PatientsSex, (QString("O")));
    SETP(EDICOM_PARAMETERS_TYPE_StudyDate, (current_date));
    SETP(EDICOM_PARAMETERS_TYPE_StudyTime, (current_time));
    SETP(EDICOM_PARAMETERS_TYPE_AccessionNumber, (QString("O")));
    SETP(EDICOM_PARAMETERS_TYPE_ReferringPhysiciansName, QString("0"));
    SETP(EDICOM_PARAMETERS_TYPE_StudyID, (current_datetime));
    char pValue[256];
    bzero(pValue, sizeof(pValue));
    DCMGetDicomFileParameter(EDICOM_PARAMETERS_TYPE_StudyInstanceID, pValue);
    QString UID = QString(pValue);
    UID = UID.section(".", 0, 7) + "." + UID.section(".", 8, 8).fill('0') + "." + UID.section(".", -3, -1);
    SETP(EDICOM_PARAMETERS_TYPE_StudyInstanceID, (UID));
    SETP(EDICOM_PARAMETERS_TYPE_SeriesDate, (current_date));
    SETP(EDICOM_PARAMETERS_TYPE_SeriesTime, (current_time));
    DCMSetDicomFileParameters(0x0018, 0x1000, "**********");
    bzero(pValue, sizeof(pValue));
    DCMGetDicomFileParameter(EDICOM_PARAMETERS_TYPE_SeriesInstanceID, pValue);
    UID = QString(pValue);
    UID = UID.section(".", 0, 7) + "." + UID.section(".", 8, 8).fill('0') + "." + UID.section(".", -3, -1);
    SETP(EDICOM_PARAMETERS_TYPE_SeriesInstanceID, (UID));
    DCMSetDicomFileParameters(0x0008, 0x0022, QSTR2CSTR(current_date));
    DCMSetDicomFileParameters(0x0008, 0x0023, QSTR2CSTR(current_date));
}
int DCMPrepareDicomInfo()
{
    Patient* patientPtr = m_dcminfo.patientPtr;
    DCMDelDicomFileParameters();
    resetElementsID();
    // other tag
    DCMSetDicomFileParameters(0x0010, 0x1020,
                              QSTR2CSTR(QString("%1").arg(patientPtr->Studies().first()->Height() / 100)));
    DCMSetDicomFileParameters(0x0008, 0x0005, m_dcminfo.scp.CharSet); //"ISO_IR 100"
    DCMSetDicomFileParameters(0x0008, 0x1090, "SonoEye");
    DCMSetDicomFileParameters(0x0010, 0x1030, QSTR2CSTR(QString("%1").arg(patientPtr->Studies().first()->Weight())));
    DCMSetDicomFileParameters(0x0018, 0x1020, "V1.0.0.0");
    DCMSetDicomFileParameters(0x0020, 0x0013,
                              QSTR2CSTR(QString::number(m_dcminfo.instanceNum))); //?image num 文件夹中改文件的序号
    DCMSetDicomFileParameters(0x0020, 0x0020, "");
    DCMSetDicomFileParameters(0x0028, 0x0006, "0");

    SETP(EDICOM_PARAMETERS_TYPE_StudyDate, (patientPtr->Studies().first()->StudyDateRaw()));
    SETP(EDICOM_PARAMETERS_TYPE_StudyTime, (patientPtr->Studies().first()->StudyTimeRaw()));
    QString studyId = patientPtr->Studies().first()->StudyDateRaw() + patientPtr->Studies().first()->StudyTimeRaw();
    SETP(EDICOM_PARAMETERS_TYPE_StudyID, (studyId));
    SETP(EDICOM_PARAMETERS_TYPE_PatientsName, (patientPtr->PatientsName()));
    SETP(EDICOM_PARAMETERS_TYPE_PatientID, (patientPtr->PatientId()));
    SETP(EDICOM_PARAMETERS_TYPE_PatientsBirthDate, (patientPtr->PatientsBirthDateRaw()));
    SETP(EDICOM_PARAMETERS_TYPE_AccessionNumber, (patientPtr->Studies().first()->AccessionNumber()));
    SETP(EDICOM_PARAMETERS_TYPE_PatientsAge, (QString::number(patientPtr->PatientsAge())));
    SETP(EDICOM_PARAMETERS_TYPE_PatientsSex, (patientPtr->PatientsDcmSex()));
    SETP(EDICOM_PARAMETERS_TYPE_ReferringPhysiciansName, QString(""));
    SETP(EDICOM_PARAMETERS_TYPE_Modality, (QString("US")));

    //这里单帧图片都设置0
    SETP(EDICOM_PARAMETERS_TYPE_FrameTime, (QString::number(m_dcminfo.frametime, 'f', 1))); //幀時間 電影
    SETP(EDICOM_PARAMETERS_TYPE_FrameRate, (QString::number(m_dcminfo.framerate)));
    ; //幀率
    // DCMSetDicomFileParameter(EDICOM_PARAMETERS_TYPE_SourceApplicationEntityTitle,);
    QString StudyInstanceID, SeriesInstanceID;
    DicomGenerateUniqueIdentifier::getStudyAndSeriesInstanceID(patientPtr->Studies().first()->StudyInstanceUid(),
                                                               StudyInstanceID, SeriesInstanceID);
    SETP(EDICOM_PARAMETERS_TYPE_StudyInstanceID, (StudyInstanceID));
    SETP(EDICOM_PARAMETERS_TYPE_SeriesInstanceID, (SeriesInstanceID));

    SETP(EDICOM_PARAMETERS_TYPE_JPEGLevel, (QString("100")));
    SETP(EDICOM_PARAMETERS_TYPE_FileType, (QString("0"))); //圖片

    SETP(EDICOM_PARAMETERS_TYPE_PixelSpacing,
         (QString("%1\\%2").arg(m_dcminfo.pixelSizeMMWithZoom).arg(m_dcminfo.pixelSizeMMWithZoom))); //(0028,0030
    SETP(EDICOM_PARAMETERS_TYPE_DoctorsName, (patientPtr->DoctorsName()));
    SETP(EDICOM_PARAMETERS_TYPE_CaseHistory, (patientPtr->CaseHistory()));
    SETP(EDICOM_PARAMETERS_TYPE_StudyDescription, (patientPtr->Studies().first()->StudyDescription()));
    SETP(EDICOM_PARAMETERS_TYPE_InstitutionName, Setting::instance().defaults().hospName());
    SETP(EDICOM_PARAMETERS_TYPE_Manufacturer, Setting::instance().defaults().manufacturer());
    // SETP(EDICOM_PARAMETERS_TYPE_SopInstanceID, QString(""));
    // SETP(EDICOM_PARAMETERS_TYPE_ImageNumber, QString(""));
    // SETP(EDICOM_PARAMETERS_TYPE_ReferringPhysicianName, QString(""));
    SETP(EDICOM_PARAMETERS_TYPE_SeriesDate, (patientPtr->Studies().first()->StudyDateRaw()));
    SETP(EDICOM_PARAMETERS_TYPE_SeriesTime, (patientPtr->Studies().first()->StudyTimeRaw()));
    SETP(EDICOM_PARAMETERS_TYPE_SendDicomDataType, (QString::number(m_dcminfo.imgCompressed)));
    // SETP(EDICOM_PARAMETERS_TYPE_SOPInstanceID, (QString("0")));
    // SETP(EDICOM_PARAMETERS_TYPE_OptCharSet, );

    QScopedPointer<CalibrationRegionModel> regionModel(new CalibrationRegionModel());
    regionModel->setSonoParametersList(m_dcminfo.sonoParaList);
    QList<CalibrationRegionData*> regionList = regionModel->regionDataList();

    for (int iS = 0; iS < regionList.size(); iS++)
    {
        CalibrationRegionData* cf = regionList[iS];
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionSpatialFormat,
                                       cf->getRegionSpatialFormat());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionDataType, cf->getRegionDataType());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionFlags, cf->getRegionFlags());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionLocationMinX0,
                                       cf->getRegionLocationMinX0());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionLocationMinY0,
                                       cf->getRegionLocationMinY0() + m_dcminfo.posTop);
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionLocationMaxX1,
                                       cf->getRegionLocationMaxX1());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_RegionLocationMaxY1,
                                       cf->getRegionLocationMaxY1() + m_dcminfo.posTop);
        if (cf->hasReferencePixel())
        {
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelX0, cf->getReferencePixelX0());
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelY0, cf->getReferencePixelY0());
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueX,
                                           cf->getReferencePixelPhysicalValueX());
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueY,
                                           cf->getReferencePixelPhysicalValueY());
        }
        else
        {
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelX0, 0);
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelY0, 0);
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueX, 0);
            DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_ReferencePixelPhysicalValueY, 0);
        }
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_PhysicalUnitsXDirection,
                                       cf->getPhysicalUnitsXDirection());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_PhysicalUnitsYDirection,
                                       cf->getPhysicalUnitsYDirection());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_PhysicalDeltaX, cf->getPhysicalDeltaX());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_PhysicalDeltaY, cf->getPhysicalDeltaY());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_TransducerFrequency,
                                       cf->getTransducerFrequency());
        DCMInsertDicomFileSequenceItem(iS, EDICOM_PARAMETERS_Sequence_PulseRepetitionFrequency,
                                       cf->getPulseRepetitionFrequency());
    }
    if (m_dcminfo.isGDPR)
        setGDPR();
    delete m_dcminfo.patientPtr;
    m_dcminfo.patientPtr = nullptr;
}
//---------------------------------------------------------------------------
#include "videorecorder.h"
class DicomRecorder : public platform_videorecord::VideoRecorder
{
public:
    DicomRecorder(int sizew, int sizeh)
        : platform_videorecord::VideoRecorder(sizew, sizeh)
    {
    }
    virtual void start()
    {
    }
    virtual void setuserdata(void* otherPtr)
    {
        DcmInfoStruct* dcminfoPtr = (DcmInfoStruct*)otherPtr;
        m_dcminfo.frameindex = dcminfoPtr->frameindex;
        m_dcminfo.frametime = dcminfoPtr->frametime;
        m_dcminfo.framerate = dcminfoPtr->framerate;
        m_dcminfo.pixelSizeMMWithZoom = dcminfoPtr->pixelSizeMMWithZoom;
        m_dcminfo.sonoParaList = dcminfoPtr->sonoParaList;
        m_dcminfo.posTop = dcminfoPtr->posTop;
        DCMPrepareDicomInfo();
    }
    virtual void stop(bool save2Local = true)
    {
    }
    virtual int finish(char* exportfile)
    {
        snprintf(exportfile, 128, "%s/%lld_rgb.dcm", CDicomParameter::instance().getStoragePath(),
                 getCurrentTimestamp());
        int ret = DCMMakeRGBToDicomFile(exportfile);
        if (ret == 0)
            return fileSize(exportfile);
        else
            return -1;
    }
    virtual void add_img_frame(uint8_t* data, int size)
    {
        uint8_t* ptrr_rgb = new uint8_t[video_width * video_height * 3];
        VideoRecorder::rgba2rgb(ptrr_rgb, data, video_width, video_height);
        DCMAddRGBDataForMakeDicomFile(ptrr_rgb, video_width * video_height * 3, video_width, video_height);
        delete[] ptrr_rgb;
    }
    virtual void add_video_frame(uint8_t* data, int size, double timestamp)
    {
        uint8_t* ptrr_rgb = new uint8_t[video_width * video_height * 3];
        VideoRecorder::bgra2rgb(ptrr_rgb, data, video_width, video_height);
        DCMAddRGBDataForMakeDicomFile(ptrr_rgb, video_width * video_height * 3, video_width, video_height);
        delete[] ptrr_rgb;
    }
};
void* extern_dicomrecordptr(int video_width, int video_height)
{
    return (void*)new DicomRecorder(video_width, video_height);
}
static bool staticinitblock = []() {
    static bool _init = false;
    if (!_init)
    {
        DCMInit();
        m_dcminfo.patientPtr = nullptr;
        _init = true;
    }
    return true;
}();
void DCMSCPConfig(const char* serverip, int port, int opTimeOut, char* OptAeTitle, char* OptAcTitle, char* Charset)
{
    strncpy(m_dcminfo.scp.serverIp, serverip, strlen(serverip) + 1);
    m_dcminfo.scp.serverPort = port;
    m_dcminfo.scp.opTimeOut = opTimeOut;
    strncpy(m_dcminfo.scp.OptAeTitle, OptAeTitle, strlen(OptAeTitle) + 1);
    strncpy(m_dcminfo.scp.OptAcTitle, OptAcTitle, strlen(OptAcTitle) + 1);
    strncpy(m_dcminfo.scp.CharSet, Charset, strlen(Charset) + 1);

    SETP(EDICOM_PARAMETERS_TYPE_ServerIP, QString(m_dcminfo.scp.serverIp));
    SETP(EDICOM_PARAMETERS_TYPE_ServerPort, QString::number(m_dcminfo.scp.serverPort));
    SETP(EDICOM_PARAMETERS_TYPE_OptTimeout, (QString::number(m_dcminfo.scp.opTimeOut)));
    // SETP(EDICOM_PARAMETERS_TYPE_OptDimseTimeout, (QString(opTimeOut)));
    // SETP(EDICOM_PARAMETERS_TYPE_OptAcseTimeout, (QString(opTimeOut)));
    SETP(EDICOM_PARAMETERS_TYPE_OptAeTitle, (QString(m_dcminfo.scp.OptAeTitle)));
    SETP(EDICOM_PARAMETERS_TYPE_OptAcTitle, (QString(m_dcminfo.scp.OptAcTitle)));
}

int DCMSendDicomByPath(const char* dicompath)
{
    if (!DCMIsConnect(m_dcminfo.scp.OptAeTitle, m_dcminfo.scp.OptAcTitle))
    {
        return -1;
    }
    int ret = sendDicomFile(dicompath);
    return ret;
}

void DCMSetDicomTagConfig(const char* patientId, int instanceNum, int compressed, int isGDPR)
{
    if (m_dcminfo.patientPtr != nullptr)
        delete m_dcminfo.patientPtr;
    Patient* pt = DataAccessLayerHelper::getPatient(patientId);
    qDebug() << __FUNCTION__ << QString(patientId) << instanceNum << compressed << (quintptr)pt;
    if (pt != NULL)
        m_dcminfo.patientPtr = pt;
    m_dcminfo.instanceNum = instanceNum;
    m_dcminfo.imgCompressed = 1 - compressed;
    m_dcminfo.isGDPR = isGDPR;
    SETP(EDICOM_PARAMETERS_TYPE_SendDicomDataType, (QString::number(m_dcminfo.imgCompressed)));
}
