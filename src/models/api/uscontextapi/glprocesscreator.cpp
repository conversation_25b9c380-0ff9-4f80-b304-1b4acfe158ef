#include "glprocesscreator.h"
#include "glprocesscontext.h"
#include "timerthread.h"
#include "sonoparameters/sonoparameters.h"
#include "bfpnames.h"
#include <QGLWidget>
GLProcessCreator::GLProcessCreator()
{
}

GLProcessCreator::~GLProcessCreator()
{
    foreach(TimerThread *thread, m_Threads)
    {
        // GLProcessCreator must be created on GUI Thread, when detect context used different thread
        // here have to detele the thread manually
        if(thread != NULL)
        {
            thread->quit();
            thread->wait();
            delete thread;
            thread = NULL;
        }
    }

    qDeleteAll(m_GLWidgets);
    qDeleteAll(m_GLProcessContexts);
}
GLProcessContext *GLProcessCreator::createContext(SonoParameters *sonoparameters,void *sharectx)
{
    if(NULL == sonoparameters)
    {
        return NULL;
    }
    TimerThread *thread = new TimerThread();
    m_Threads.append(thread);
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    GLProcessContext *glProcessContext = new GLProcessContext(sonoparameters->pV(BFPNames::RenderImageSizeStr).toSize(),sharectx,NULL,thread);

    glProcessContext->setSonoParameters(sonoparameters);
#else
    QGLWidget *glWidget = new QGLWidget();
    m_GLWidgets.append(glWidget);
    GLProcessContext *glProcessContext = new GLProcessContext(glWidget,
                                                              sonoparameters->pV(BFPNames::ImageSizeStr).toSize(),
                                                              NULL, thread);
    glProcessContext->setSonoParameters(sonoparameters);
#endif
    m_GLProcessContexts.append(glProcessContext);
    return glProcessContext;
}

const GLProcessContext *GLProcessCreator::context(int index)
{
    return m_GLProcessContexts[index];
}
