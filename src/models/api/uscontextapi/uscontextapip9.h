#ifndef USCONTEXTAPIP9_H
#define USCONTEXTAPIP9_H

#include "linedatahead.h"
#include "modelconfig.h"
#include "usapiparameterfilter.h"
#include "uscontextapi.h"

class UsContextApiP9 : public UsContextApi
{
    Q_OBJECT
  public:
    explicit UsContextApiP9();
    virtual ~UsContextApiP9();

    UsRetCode setScanMode(UsScanMode scanMode);
    virtual UsRetCode start();
    virtual UsRetCode setLayout(UsLayout layout);

    virtual UsRetCode setActiveLayoutIndex(int activeIndex);

    virtual UsRetCode setParameter(const char *const name, const QVariant &value);

    virtual UsRetCode getParameter(const char *const name, QVariant &value);

    virtual UsRetCode getParameterIntMin(const char *const name, int *value);

    virtual UsRetCode getParameterIntStep(const char *const name, int *value);

    virtual UsRetCode getParameterIntMax(const char *const name, int *value);

    virtual UsRetCode getParameterShowText(const char *const name, char *value, const int count);
    /**
     * fpga update
     */
    virtual UsRetCode FinishUpgrade();

    virtual bool selectPreset(const char *presetName);

    virtual UsRetCode StartUpgrade(char *fpga_binpath);

    virtual UsRetCode asyncStartUpgrade(char *fpga_binpath);

    virtual UsRetCode setImageCheckTime(int timeSec);
    virtual UsRetCode getImageCheckTime(int *timeSec);

    virtual UsRetCode setImageCheckState(bool open);
    virtual UsRetCode getImageCheckState(bool *open);
    virtual UsRetCode setImageCheckSensitiveThreshold(float threshold);
    virtual UsRetCode getImageCheckSensitiveThreshold(float *threshold);

    virtual UsRetCode openFunction(UsFunctionType funcType);

    virtual UsRetCode geometryMove(const int offsetX, const int offsetY);

    virtual UsRetCode geometryChangeSize(const int sizeX, const int sizeY);

    virtual UsRetCode getCROIPoints(_UsLogicalPoint_INT *pointArray, const int count, int *validCount);

    virtual UsRetCode imageTransform(double scale, double translateX, double translateY);

    virtual UsRetCode imageTransformReset();

  protected:
    void initializeBeamformer();

    /** 2024-06-18 Write by AlexWang
     * @brief initializeProbePresetModel
     */
    void initializeProbePresetModel();

    void initializeSpecialParameters();

    void initializeGraphicsContext();

    void sonoParametersChanged(SonoParameters *parameters);

    void connectParametersChanged(SonoParameters *sonoParameters);

    void disConnectParametersChanged(SonoParameters *sonoParameters);

    void convertPhysicsFToLogic(const double line, const double depth, QPointF &pt);
    void convertLogicFToPhysics(const QPointF &pt, double *line, double *depth);
  protected slots:
    void onParametersChanged();
    void fpgaUpgradeProcess(int process);
    void upgradeFinalState(int code);
    void onProbeChanged(QVector<int> probelist, QVector<bool> changes);
    void onImageNoChanged();
    void onProbeKeyStateChanged(QVector<quint8> statelist);
    void onDeviceOpenError(int error);
    void onNewImage(ImageEventArgs *imageEventArgs);
    void onImageTransformChanged(bool preset = false);
    void onFanSpeedUpdated(QVector<LineData::FanInfo> fans);

    /** 2024-06-18 Write by AlexWang
     * @brief onAutoFreeze
     */
    void onAutoFreeze();

  private:
    int getROIPointsInColor(_UsLogicalPoint_INT *roi, int size);
    QPointF convertPhysicsToLogicF(int line, double depth, qreal angle, const QTransform &transform);

  private:
    UsApiParameterFilter *m_UsApiParameterFilter;
    _UsLogicalPoint_INT m_CROI[4];
    unsigned int *m_ProbeKeyState;
    int m_ProbeKeyCount;
};

#endif // USCONTEXTAPIP9_H
