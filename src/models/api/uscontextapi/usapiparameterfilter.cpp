#include "usapiparameterfilter.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "iprobedataset.h"
#include "parameter.h"
#include "sonoparameters.h"
#include <QList>

#define MODE_PARAM_KEY(key) #key

UsApiParameterFilter::UsApiParameterFilter(QObject *parent)
    : QObject(parent)
    , m_SonoParameters(NULL)
    , m_ProbeDataSet(NULL)
{
    QList<QString> normalParameters;

    normalParameters << BFPNames::BGainStr
                     << BFPNames::TGCStr
                     // Left Menu
                     << BFPNames::ScanWidthStr << BFPNames::BGammaShowStr << BFPNames::SmoothStr
                     << BFPNames::EdgeShowStr << BFPNames::AcousticPowerBStr << BFPNames::RotationStr
                     << BFPNames::LRInvertStr
                     // advance
                     << BFPNames::ImageZoomCoefStr << BFPNames::BSteeringScanStr
                     << BFPNames::TrapezoidalModeStr
                     // Post Process Menu
                     << BFPNames::BColorMapIndexShowStr << BFPNames::BGrayCurveIndexShowStr
                     << BFPNames::BRejectionShowStr
                     // KeyBoard
                     << BFPNames::UpStr << BFPNames::LeftStr << BFPNames::THIStr << BFPNames::CQYZStr
                     << BFPNames::FocusPosBStr
                     << BFPNames::XContrastValueStr
                     // Bottom Menu
                     << BFPNames::FreqIndexBStr << BFPNames::DynamicRangeStr << BFPNames::FocusNumShowStr
                     << BFPNames::ScpdOnStr << BFPNames::iImageShowStr << BFPNames::FcpdOnStr << BFPNames::FrameAvgStr
                     << BFPNames::HighDensityStr

                     << BFPNames::StartLineStr << BFPNames::StopLineStr << BFPNames::DepthMMStr
                     << BFPNames::DepthShowMMStr << BFPNames::TriplexModeStr
                     << BFPNames::PixelSizeMMStr

                     // c 参数
                     << BFPNames::RoiMidLineStr << BFPNames::RoiHalfLinesStr << BFPNames::RoiMidDepthMMStr
                     << BFPNames::RoiHalfDepthMMStr << BFPNames::SteeringAngleStr << BFPNames::ColorLineDensityStr
                     << BFPNames::GainColorStr << BFPNames::FrameAvgColorStr << BFPNames::BaseLineColorStr
                     << BFPNames::CfColorMapIndexShowStr << BFPNames::CFModeStr << BFPNames::WallFilterColorStr
                     << BFPNames::ColorInvertStateStr
                     << BFPNames::SampleRateDopShowStr
                     //
                     << BFPNames::SystemScanModeStr
                     << BFPNames::CQYZLevelStr
                     //声输出测试
                     << BFPNames::PTBFrmSelfEnStr << BFPNames::PTBFrmSelfStr << BFPNames::AcousticPowerTestCodeStr;
    foreach (const QString &p, normalParameters)
    {
        m_Parameters.insert(p, true);
    }

    initModeNoSettableParameters();
}

void UsApiParameterFilter::setSonoParameters(SonoParameters *sonoParameters)
{
    if (m_SonoParameters == sonoParameters)
    {
        return;
    }
    if (m_SonoParameters != NULL)
    {
        disConnectSonoParameters();
    }
    m_SonoParameters = sonoParameters;
    if (m_SonoParameters != NULL)
    {
        connectSonoParameters();
        initSonoParameters();
    }
}

void UsApiParameterFilter::setProbeDataSet(IProbeDataSet *probeDataSet)
{
    m_ProbeDataSet = probeDataSet;
}

bool UsApiParameterFilter::contains(const QString &parameterName) const
{
    return m_Parameters.contains(parameterName);
}

bool UsApiParameterFilter::activeable(const QString &parameterName) const
{
    return m_Parameters.value(parameterName, false) && parametersSettable(parameterName);
}

QList<QString> UsApiParameterFilter::parameters() const
{
    return m_Parameters.keys();
}

bool UsApiParameterFilter::parametersSettable(const QString &parameterName) const
{
    if (m_SonoParameters->pBV(BFPNames::FreezeStr))
    {
        return false;
    }
    if (m_ModeNoSettableParameters.contains(MODE_PARAM_KEY(SystemScanModeNone), parameterName))
    {
        return false;
    }
    if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeAV)
    {
        return !m_ModeNoSettableParameters.contains(MODE_PARAM_KEY(SystemScanModeAV), parameterName);
    }
    else if (m_SonoParameters->pIV(BFPNames::SystemScanModeStr) == SystemScanModeColorDoppler)
    {
        return !m_ModeNoSettableParameters.contains(MODE_PARAM_KEY(SystemScanModeColorDoppler), parameterName);
    }
    return true;
}

void UsApiParameterFilter::initSonoParameters()
{
    onBSteeringScanValueChanged(m_SonoParameters->pV(BFPNames::BSteeringScanStr));
    onTrapezoidalModeValueChanged(m_SonoParameters->pV(BFPNames::TrapezoidalModeStr));
    onScpdOnValueChanged(m_SonoParameters->pV(BFPNames::ScpdOnStr));
    onProbeIdValueChanged(m_SonoParameters->pV(BFPNames::ProbeIdStr));
}

void UsApiParameterFilter::connectSonoParameters()
{
    connect(m_SonoParameters->parameter(BFPNames::BSteeringScanStr), SIGNAL(valueChanged(const QVariant &, bool)), this,
            SLOT(onBSteeringScanValueChanged(const QVariant &)));
    connect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(const QVariant &, bool)),
            this, SLOT(onTrapezoidalModeValueChanged(const QVariant &)));
    connect(m_SonoParameters->parameter(BFPNames::ScpdOnStr), SIGNAL(valueChanged(const QVariant &, bool)), this,
            SLOT(onScpdOnValueChanged(const QVariant &)));
    connect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(const QVariant &, bool)), this,
            SLOT(onProbeIdValueChanged(const QVariant &)));
}

void UsApiParameterFilter::disConnectSonoParameters()
{
    disconnect(m_SonoParameters->parameter(BFPNames::BSteeringScanStr), SIGNAL(valueChanged(const QVariant &, bool)),
               this, SLOT(onBSteeringScanValueChanged(const QVariant &)));
    disconnect(m_SonoParameters->parameter(BFPNames::TrapezoidalModeStr), SIGNAL(valueChanged(const QVariant &, bool)),
               this, SLOT(onTrapezoidalModeValueChanged(const QVariant &)));
    disconnect(m_SonoParameters->parameter(BFPNames::ScpdOnStr), SIGNAL(valueChanged(const QVariant &, bool)), this,
               SLOT(onScpdOnValueChanged(const QVariant &)));
    disconnect(m_SonoParameters->parameter(BFPNames::ProbeIdStr), SIGNAL(valueChanged(const QVariant &, bool)), this,
               SLOT(onProbeIdValueChanged(const QVariant &)));
}

void UsApiParameterFilter::initModeNoSettableParameters()
{
    // 添加VA模式下 不可设置的参数
    QList<QString> normalParameters;

    normalParameters << BFPNames::FocusPosBStr << BFPNames::FocusNumShowStr << BFPNames::RoiMidLineStr
                     << BFPNames::RoiHalfLinesStr << BFPNames::RoiMidDepthMMStr << BFPNames::RoiHalfDepthMMStr
                     << BFPNames::FcpdOnStr << BFPNames::TrapezoidalModeStr << BFPNames::ScpdOnStr << BFPNames::LeftStr
                     << BFPNames::UpStr << BFPNames::RotationStr << BFPNames::ImageZoomCoefStr
                     << BFPNames::ScanWidthStr
                     // C参数
                     << BFPNames::SteeringAngleStr << BFPNames::ColorLineDensityStr << BFPNames::GainColorStr
                     << BFPNames::FrameAvgColorStr << BFPNames::BaseLineColorStr << BFPNames::CfColorMapIndexShowStr
                     << BFPNames::CFModeStr << BFPNames::WallFilterColorStr << BFPNames::ColorInvertStateStr
                     << BFPNames::SampleRateDopShowStr;
    foreach (const QString &p, normalParameters)
    {
        m_ModeNoSettableParameters.insert(MODE_PARAM_KEY(SystemScanModeAV), p);
    }

    normalParameters.clear();
    normalParameters << BFPNames::DepthMMStr << BFPNames::PixelSizeMMStr << BFPNames::DepthShowMMStr
                     << BFPNames::StartLineStr << BFPNames::StopLineStr;
    foreach (const QString &p, normalParameters)
    {
        m_ModeNoSettableParameters.insert(MODE_PARAM_KEY(SystemScanModeNone), p);
    }

    normalParameters.clear();
    normalParameters << BFPNames::FocusPosBStr << BFPNames::FocusNumShowStr;
    foreach (const QString &p, normalParameters)
    {
        m_ModeNoSettableParameters.insert(MODE_PARAM_KEY(SystemScanModeColorDoppler), p);
    }
}

void UsApiParameterFilter::onFreezeValueChanged(const QVariant &value)
{
    if (!value.toBool())
    {
        foreach (const QString &p, m_Parameters.keys())
        {
            m_Parameters[p] = false;
        }
    }
    else
    {
        foreach (const QString &p, m_Parameters.keys())
        {
            m_Parameters[p] = true;
        }
        if (m_SonoParameters == NULL)
        {
            return;
        }
        initSonoParameters();
    }
}

void UsApiParameterFilter::onBSteeringScanValueChanged(const QVariant &value)
{
    const int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo &info = m_ProbeDataSet->getProbe(probeId);
    if (info.IsLinear)
    {
        m_Parameters.insert(BFPNames::TrapezoidalModeStr, value.toInt() == 20 ? true : false);
    }
    else
    {
        m_Parameters.insert(BFPNames::BSteeringScanStr, false);
        m_Parameters.insert(BFPNames::TrapezoidalModeStr, false);
    }
}

void UsApiParameterFilter::onTrapezoidalModeValueChanged(const QVariant &value)
{
    const int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo &info = m_ProbeDataSet->getProbe(probeId);
    if (info.IsLinear)
    {
        m_Parameters.insert(BFPNames::BSteeringScanStr, !value.toBool());
    }
    else
    {
        m_Parameters.insert(BFPNames::BSteeringScanStr, false);
        m_Parameters.insert(BFPNames::TrapezoidalModeStr, false);
    }

    if (value.toBool())
    {
        m_Parameters.insert(BFPNames::FcpdOnStr, false);
        m_Parameters.insert(BFPNames::ScpdOnStr, false);
    }
    else
    {
        m_Parameters.insert(BFPNames::ScpdOnStr, true);
        if (m_SonoParameters->pBV(BFPNames::ScpdOnStr))
        {
            m_Parameters.insert(BFPNames::FcpdOnStr, false);
        }
        else
        {
            m_Parameters.insert(BFPNames::FcpdOnStr, true);
        }
    }
}

void UsApiParameterFilter::onScpdOnValueChanged(const QVariant &value)
{
    const int probeId = m_SonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo &info = m_ProbeDataSet->getProbe(probeId);
    if (value.toBool() || (info.IsLinear && m_SonoParameters->pBV(BFPNames::TrapezoidalModeStr)))
    {
        m_Parameters.insert(BFPNames::FcpdOnStr, false);
    }
    else
    {
        m_Parameters.insert(BFPNames::FcpdOnStr, true);
    }
}

void UsApiParameterFilter::onProbeIdValueChanged(const QVariant &value)
{
    const int probeId = value.toInt();
    const ProbeDataInfo &info = m_ProbeDataSet->getProbe(probeId);
    if (info.IsLinear)
    {
        if ((!m_Parameters.value(BFPNames::BSteeringScanStr, false)) &&
            (!m_Parameters.value(BFPNames::TrapezoidalModeStr, false)))
        {
            m_Parameters.insert(BFPNames::BSteeringScanStr, true);
            m_Parameters.insert(BFPNames::TrapezoidalModeStr, true);
        }
    }
    else
    {
        m_Parameters.insert(BFPNames::BSteeringScanStr, false);
        m_Parameters.insert(BFPNames::TrapezoidalModeStr, false);
    }
}
