#include "opengloffscreensurface.h"

#include <QCoreApplication>
#include <QPainter>
#define __DEBUG 
#ifdef __DEBUG
#define qdbg qDebug()<<"OFFSCREENQT:["<<__FILE__":"<<__LINE__<<"]"
#define qwrn qDebug()<<"!!!OFFSCREENQT:["<<__FILE__":"<<__LINE__<<"]"
#endif
//#import <UIKit/UIKit.h>
#include <QApplication>
#include <QtPlugin>
#include <iostream>
#include <qgraphicssceneevent.h>
Q_IMPORT_PLUGIN(QIOSIntegrationPlugin)
Q_IMPORT_PLUGIN(CoreAudioPlugin)
//Q_IMPORT_PLUGIN(QSQLiteDriverPlugin)
//Q_IMPORT_PLUGIN(qsqlite)
#define MAXBUFFSIZE 1024*1024*4
static OpenGlOffscreenSurface *paintSurface=nullptr;
static uint8_t *bufferreturn=new uint8_t[MAXBUFFSIZE];
OpenGlOffscreenSurface::OpenGlOffscreenSurface(
        QScreen*     targetScreen,
        const QSize& size)
    : QOffscreenSurface(targetScreen)
    , m_size(size)
{
    setFormat(QSurfaceFormat::defaultFormat());
    m_initialized = false;
    m_updatePending = false;
    create();  // Some platforms require this function to be called on the main (GUI) thread
    initializeInternal();
}
OpenGlOffscreenSurface::~OpenGlOffscreenSurface()
{
    // to delete the FBOs we first need to make the context current
    m_context->makeCurrent(this);
    // destroy framebuffer objects
    if (m_fbo) {
        m_fbo->release();
        delete m_fbo;
        m_fbo = nullptr;
    }
    if (m_resolvedFbo) {
        m_resolvedFbo->release();
        delete m_resolvedFbo;
        m_resolvedFbo = nullptr;
    }
    // destroy shader
    delete m_blitShader;
    m_blitShader = nullptr;
    // free context
    m_context->doneCurrent();
    delete m_context;
    m_context = nullptr;
    // free paint device
    delete m_paintDevice;
    m_paintDevice = nullptr;
    m_initialized = false;
    m_updatePending = false;
    destroy();
}


QOpenGLContext* OpenGlOffscreenSurface::context() const
{
    return (m_context);
}


QOpenGLFunctions* OpenGlOffscreenSurface::functions() const
{
    return (m_functions);
}


GLuint OpenGlOffscreenSurface::framebufferObjectHandle() const
{
    return (m_fbo ? m_fbo->handle() : 0);
}


const QOpenGLFramebufferObject* OpenGlOffscreenSurface::getFramebufferObject() const
{
    return (m_fbo);
}


QPaintDevice* OpenGlOffscreenSurface::getPaintDevice() const
{
    return (m_paintDevice);
}


void OpenGlOffscreenSurface::bindFramebufferObject()
{
    if (m_fbo) {
        m_fbo->bind();
    } else {
        QOpenGLFramebufferObject::bindDefault();
    }
}


bool OpenGlOffscreenSurface::isValid() const
{
    return (m_initialized && m_context && m_fbo);
}


void OpenGlOffscreenSurface::makeCurrent()
{
    makeCurrentInternal();
}


void OpenGlOffscreenSurface::makeCurrentInternal()
{
    if (isValid()) {
        m_context->makeCurrent(this);
    } else {
        qwrn <<"OpenGlOffscreenSurface::makeCurrent() - Window not yet properly initialized!";
    }
}


void OpenGlOffscreenSurface::doneCurrent()
{
    if (m_context) {
        m_context->doneCurrent();
    }
}


QImage OpenGlOffscreenSurface::grabFramebuffer()
{
    std::lock_guard <std::mutex> locker(m_mutex);
    makeCurrentInternal();
    // blit framebuffer to resolve framebuffer first if needed
    if (m_fbo->format().samples() > 0) {
        // check if we have glFrameBufferBlit support. this is true for desktop OpenGL 3.0+, but not
        // OpenGL ES 2.0
        if (m_functions_3_0) {
            // only blit the color buffer attachment
            m_functions_3_0->glBindFramebuffer(GL_READ_FRAMEBUFFER, m_fbo->handle());
            m_functions_3_0->glBindFramebuffer(GL_DRAW_FRAMEBUFFER, m_resolvedFbo->handle());
            m_functions_3_0->glBlitFramebuffer(0, 0, bufferSize().width(),
                    bufferSize().height(), 0, 0, bufferSize().width(),
                    bufferSize().height(), GL_COLOR_BUFFER_BIT, GL_NEAREST);
            m_functions_3_0->glBindFramebuffer(GL_FRAMEBUFFER, 0);
        } else {
            // we must unbind the FBO here, so we can use its texture and bind the default
            // back-buffer
            m_functions->glBindFramebuffer(GL_FRAMEBUFFER, m_resolvedFbo->handle());
            // now use its texture for drawing in the shader
            // --> bind shader and draw textured quad here
            // bind regular FBO again
            m_functions->glBindFramebuffer(GL_FRAMEBUFFER, m_fbo->handle());
        }
        // check if OpenGL errors happened
        if (GLenum error = m_functions->glGetError() != GL_NO_ERROR) {
            qwrn <<"OpenGlOffscreenSurface::grabFramebuffer() - OpenGL error";
        }

        // now grab from resolve FBO
        return (grabFramebufferInternal(m_resolvedFbo));
    } else {
        // no multi-sampling. grab directly from FBO
        return (grabFramebufferInternal(m_fbo));
    }
}  // OpenGlOffscreenSurface::grabFramebuffer


QImage OpenGlOffscreenSurface::grabFramebufferInternal(QOpenGLFramebufferObject* fbo)
{
    QImage image;
    GLuint rendertid= m_fbo->texture();
    *(int*)bufferreturn=rendertid;
    m_functions->glFlush();
    return image;
    // bind framebuffer first
    m_functions->glBindFramebuffer(GL_READ_FRAMEBUFFER, fbo->handle());
    if (m_functions_3_0) {
        m_functions_3_0->glReadBuffer(GL_COLOR_ATTACHMENT0);
    }
    GLenum internalFormat = fbo->format().internalTextureFormat();
    bool hasAlpha = internalFormat == GL_RGBA || internalFormat == GL_BGRA
                    || internalFormat == GL_RGBA8;
    if (internalFormat == GL_BGRA) {
       // qDebug()<<"GL_BGRA";
        image = QImage(fbo->size(), hasAlpha ? QImage::Format_ARGB32 : QImage::Format_RGB32);
        m_functions->glReadPixels(0, 0, fbo->size().width(),
                fbo->size().height(), GL_BGRA, GL_UNSIGNED_BYTE, image.bits());
    } else if ((internalFormat == GL_RGBA) || (internalFormat == GL_RGBA8)) {
        // qDebug()<<"GL_RGBA";
        image=QImage(bufferreturn,fbo->size().width(),fbo->size().height(), QImage::Format_RGBA8888);
        //image = QImage(fbo->size(), hasAlpha ? QImage::Format_RGBA8888 : QImage::Format_RGBX8888);
        m_functions->glReadPixels(0, 0, fbo->size().width(),
                fbo->size().height(), GL_RGBA, GL_UNSIGNED_BYTE, image.bits());
    } else {
        qwrn << "OpenGlOffscreenSurface::grabFramebuffer() - Unsupported framebuffer format"
                 << internalFormat << "!";
    }
    m_functions->glBindFramebuffer(GL_FRAMEBUFFER, m_fbo->handle());

    m_functions->glFlush();

    return image;
}  // OpenGlOffscreenSurface::grabFramebufferInternal


void OpenGlOffscreenSurface::swapBuffers()
{
    swapBuffersInternal();
    emit frameSwapped();
}


void OpenGlOffscreenSurface::swapBuffersInternal()
{
    // blit framebuffer to back buffer
    m_context->makeCurrent(this);
    // make sure all paint operation have been processed
    m_functions->glFlush();
    // check if we have glFrameBufferBlit support. this is true for desktop OpenGL 3.0+, but not
    // OpenGL ES 2.0
    if (m_functions_3_0) {
        // if our framebuffer has multi-sampling, the resolve should be done automagically
        m_functions_3_0->glBindFramebuffer(GL_READ_FRAMEBUFFER, m_fbo->handle());
        m_functions_3_0->glBindFramebuffer(GL_DRAW_FRAMEBUFFER, 0);
        // blit all buffers including depth buffer for further rendering
        m_functions_3_0->glBlitFramebuffer(0, 0, bufferSize().width(),
                bufferSize().height(), 0, 0, bufferSize().width(),
                bufferSize().height(), GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT | GL_STENCIL_BUFFER_BIT,
                GL_NEAREST);
        m_functions_3_0->glBindFramebuffer(GL_FRAMEBUFFER, m_fbo->handle());
    } else {
        // we must unbind the FBO here, so we can use its texture and bind the default back-buffer
        m_functions->glBindFramebuffer(GL_FRAMEBUFFER, 0);
        // now use its texture for drawing in the shader
        // --> bind shader and draw textured quad here
        // bind regular FBO again
        m_functions->glBindFramebuffer(GL_FRAMEBUFFER, m_fbo->handle());
    }
    // check if OpenGL errors happened
    if (GLenum error = m_functions->glGetError() != GL_NO_ERROR) {
       qwrn << "OpenGlOffscreenSurface::swapBuffersInternal() - OpenGL error" << error;
    }
    // now swap back buffer to front buffer
    m_context->swapBuffers(this);
}  // OpenGlOffscreenSurface::swapBuffersInternal


void OpenGlOffscreenSurface::recreateFBOAndPaintDevice()
{
    
    if (m_context && ((m_fbo == nullptr) || (m_fbo->size() != bufferSize()))) {
        qdbg<<"recreateFBOAndPaintDevice";
        m_context->makeCurrent(this);
        // free old FBOs
        if (m_fbo) {
            m_fbo->release();
            delete m_fbo;
            m_fbo = nullptr;
        }
        if (m_resolvedFbo) {
            m_resolvedFbo->release();
            delete m_resolvedFbo;
            m_resolvedFbo = nullptr;
        }
        // create new frame buffer
//        QOpenGLFramebufferObjectFormat format = QGLInfo::DefaultFramebufferFormat();
//        format.setSamples(QGLInfo::HasMultisamplingSupport(m_context) ? 4 : 0);
        //qDebug()<<"QGLInfo::HasMultisamplingSupport(m_context):"<<QGLInfo::HasMultisamplingSupport(m_context);
        QOpenGLFramebufferObjectFormat format;
        format.setSamples(4);//4 开启多重采样
        m_fbo = new QOpenGLFramebufferObject(bufferSize(), format);
        if (m_fbo->isValid()) {
            if (m_fbo->format().samples() > 0) {
                qDebug()<<"we got a framebuffer backed by a multisample renderbuffer"<<m_fbo->format().samples();
            } else {
                qDebug()<<"we got a non-multisample framebuffer, backed by a texture";
            }
        }

        
        if (!m_fbo->isValid()) {
            qwrn<<"OpenGlOffscreenSurface::recreateFbo() - Failed to create background FBO!";
        }
        // clear framebuffer
        m_fbo->bind();
        m_functions->glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT | GL_STENCIL_BUFFER_BIT);
        m_fbo->release();
        // if multi sampling is requested and supported we need a resolve FBO
        if (format.samples() > 0) {
            // create resolve framebuffer with only a color attachment
            format.setAttachment(QOpenGLFramebufferObject::NoAttachment);
            format.setSamples(0);
            m_resolvedFbo = new QOpenGLFramebufferObject(bufferSize(), format);
            if (!m_resolvedFbo->isValid()) {
                qwrn <<"OpenGlOffscreenSurface::recreateFbo() - Failed to create resolve FBO!";
            }
            // clear resolve framebuffer
            m_resolvedFbo->bind();
            m_functions->glClear(GL_COLOR_BUFFER_BIT);
            m_resolvedFbo->release();
        }
    }
    // create paint device for painting with QPainter if needed
    if (!m_paintDevice) {
        m_paintDevice = new QOpenGLPaintDevice;
    }
    // update paint device size if needed
    if (m_paintDevice->size() != bufferSize()) {
        m_paintDevice->setSize(bufferSize());
    }
}  // OpenGlOffscreenSurface::recreateFBOAndPaintDevice


void OpenGlOffscreenSurface::initializeInternal()
{
    if (!m_initialized.exchange(true)) {
        // create OpenGL context. we set the format requested by the user (default:
        // QWindow::requestedFormat())
        m_context = new QOpenGLContext(this);
        m_context->setFormat(format());
        if (m_context->create()) {
            
            m_context->makeCurrent(this);
            // initialize the OpenGL 2.1 / ES 2.0 functions for this object
            m_functions = m_context->functions();
            m_functions->initializeOpenGLFunctions();
            // try initializing the OpenGL 3.0 functions for this object
           // m_functions_3_0 = m_context->versionFunctions <QOpenGLFunctions_3_0>();
            m_functions_3_0= static_cast<QOpenGLExtraFunctions*>(QOpenGLContext::currentContext()->extraFunctions());
            if (m_functions_3_0) {
                m_functions_3_0->initializeOpenGLFunctions();
            } else {
                // if we do not have OpenGL 3.0 functions, glBlitFrameBuffer is not available, so we
                // must do the blit
                // using a shader and the framebuffer texture, so we need to create the shader
                // here...
                // --> allocate m_blitShader, a simple shader for drawing a textured quad
                // --> build quad geometry, VBO, whatever
            }
            // now we have a context, create the FBO
            recreateFBOAndPaintDevice();
        } else {
            m_initialized = false;
            delete m_context;
            m_context = nullptr;
            qwrn <<"Failed to create OpenGL context!";
        }
    }
}  // OpenGlOffscreenSurface::initializeInternal


void OpenGlOffscreenSurface::update()
{
    // only queue an update if there's not already an update pending
    if (!m_updatePending.exchange(true)) {
        QCoreApplication::postEvent(this, new QEvent(QEvent::UpdateRequest));
    }
}

void OpenGlOffscreenSurface::initializeGL() {

}
void OpenGlOffscreenSurface::resizeGL(int width, int height) {

}
void OpenGlOffscreenSurface::render()
{
    std::lock_guard <std::mutex> locker(m_mutex);
    // check if we need to initialize stuff
    initializeInternal();
    // check if we need to call the user initialization
//    makeCurrent(); // TODO: may be makeCurrent() must be here, as noted for QOpenGLWidget.initializeGL()
    if (!m_initializedGL) {
        m_initializedGL = true;
        initializeGL();
    }
    // make context current and bind framebuffer
    makeCurrent();
    bindFramebufferObject();
    // call user paint function
    paintGL();
    doneCurrent();
    // mark that we're done with updating
    m_updatePending = false;
}  // OpenGlOffscreenSurface::render


void OpenGlOffscreenSurface::exposeEvent(QExposeEvent* e)
{
    // render window content if window is exposed
    render();
}  // OpenGlOffscreenSurface::exposeEvent


void OpenGlOffscreenSurface::resizeEvent(QResizeEvent* e)
{
    // call base implementation
    resize(e->size());
    emit resized();
}


void OpenGlOffscreenSurface::resize(const QSize& newSize)
{
    m_mutex.lock();
    // make context current first
    makeCurrent();

    m_size = QSize(newSize);

    // update FBO and paint device
    recreateFBOAndPaintDevice();
    m_mutex.unlock();
    // call user-defined resize method
    resizeGL(bufferSize().width(), bufferSize().height());
}  // OpenGlOffscreenSurface::resize


void OpenGlOffscreenSurface::resize(
        int w,
        int h)
{
    resize(QSize(w, h));
}

void OpenGlOffscreenSurface::setcurrentscene(QGraphicsScene *sc){
    currentscene=sc;
}
void OpenGlOffscreenSurface::setcurrentwidget(QWidget *wg){
    currentwidget=wg;
}
bool OpenGlOffscreenSurface::event(QEvent* event)
{
    switch (event->type()) {
        case QEvent::UpdateLater:
            update();
            return (true);

        case QEvent::UpdateRequest:
            render();
            return (true);

        default:
            return (false);
    }  // switch
}  // OpenGlOffscreenSurface::event


QSize OpenGlOffscreenSurface::bufferSize() const
{
    return (m_size);
}
void OpenGlOffscreenSurface::paintGL()
{
    // QPainter painter(getPaintDevice());
    // painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    // qDebug() << "Before scene render...";
    // //currentscene->render(&painter);
    // currentwidget->render(&painter);
    // qDebug() << "After scene render...";
    // painter.end();

    QPainter painter(getPaintDevice());
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    //static qreal m11 = 1, m12 = 0, m21 = 0, m22 = 1, m31 = 0, m32 = 0;
    //int wOutput = m_fbo->size().width(), hOutput = m_fbo->size().height();
    // int orientation=0;
    // switch(orientation)
    // {
    //     case 0: m22 = -1; m32 = hOutput; break;
    //     case 180: m22 = 1; m32 = 0; break;
    //     case 90: m21 = 1; m31 = 0; break;
    //     case -90: m21 = -1; m31 = hOutput; break;
    // }
    QTransform painterTransform;
    painterTransform.setMatrix(1, 0, 0, 0, -1, 0, 0, m_fbo->size().height(), 1);
    painter.setTransform(painterTransform);
    currentscene->render(&painter);
    //currentwidget->render(&painter);
    //qdbg << "OpenGlOffscreenSurface::paintGL ok...";
    painter.end();

}

// 自定义消息处理程序
void myMessageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QByteArray localMsg = msg.toLocal8Bit();
    switch (type) {
    case QtDebugMsg:
        fprintf(stderr, "$Debug: %s (%s:%u, %s)\n", localMsg.constData(), context.file, context.line, context.function);
        break;
    case QtInfoMsg:
        fprintf(stderr, "$Info: %s (%s:%u, %s)\n", localMsg.constData(), context.file, context.line, context.function);
        break;
    case QtWarningMsg:
        fprintf(stderr, "$Warning: %s (%s:%u, %s)\n", localMsg.constData(), context.file, context.line, context.function);
        break;
    case QtCriticalMsg:
        fprintf(stderr, "$Critical: %s (%s:%u, %s)\n", localMsg.constData(), context.file, context.line, context.function);
        break;
    case QtFatalMsg:
        fprintf(stderr, "$Fatal: %s (%s:%u, %s)\n", localMsg.constData(), context.file, context.line, context.function);
        abort();
    }
}

QOpenGLContext * offscreen_init(QGraphicsScene * scene){
    if(nullptr==paintSurface)
        paintSurface=new OpenGlOffscreenSurface();
    return paintSurface->m_context;
}
uint8_t * offscreen_renderQt(QGraphicsScene * scene){
    paintSurface->setcurrentscene(scene);
    paintSurface->resize(scene->width(), scene->height());
    paintSurface->render();
    paintSurface->grabFramebuffer();
    return bufferreturn;
}
void offscreen_qtevent_generateandSend(QEvent::Type eventtype,int posx,int posy, QGraphicsScene*m_scene){
    switch (eventtype) {
    case QEvent::GraphicsSceneMousePress:{
        qreal sceneX = posx;
        qreal sceneY = posy;
        QGraphicsSceneMouseEvent event(QEvent::GraphicsSceneMousePress);
        event.setScenePos(QPoint(sceneX, sceneY));
        event.setPos(QPoint(sceneX, sceneY));
        //event.setScreenPos(ptGlobal);
        event.setButton(Qt::LeftButton);
        event.setButtons(Qt::LeftButton);
        event.setModifiers(QApplication::keyboardModifiers());
        QApplication::sendEvent(m_scene, &event);

        }
        break;
    case QEvent::GraphicsSceneMouseRelease:{
        qreal sceneX = posx;
        qreal sceneY = posy;
        QGraphicsSceneMouseEvent event(QEvent::GraphicsSceneMouseRelease);
        event.setScenePos(QPoint(sceneX, sceneY));
        event.setPos(QPoint(sceneX, sceneY));
        //event.setScreenPos(ptGlobal);
        event.setButton(Qt::LeftButton);
        event.setButtons(Qt::NoButton);
        event.setModifiers(QApplication::keyboardModifiers());
        QApplication::sendEvent(m_scene, &event);

        }
        break;
    case QEvent::GraphicsSceneMouseMove:{
        qreal sceneX = posx;
        qreal sceneY = posy;
        QGraphicsSceneMouseEvent event(QEvent::GraphicsSceneMouseMove);
        event.setScenePos(QPoint(sceneX, sceneY));
        event.setPos(QPoint(sceneX, sceneY));
        //event.setScreenPos(ptGlobal);
        event.setButton(Qt::LeftButton);
        event.setButtons(Qt::LeftButton);
        event.setModifiers(QApplication::keyboardModifiers());
        QApplication::sendEvent(m_scene, &event);
        }
    case QEvent::UngrabMouse:{
        QEvent event(QEvent::UngrabMouse);
        QApplication::sendEvent(m_scene, &event);
        }
        break;
        break;
    default:
        break;
    }

}

void  noneused();
