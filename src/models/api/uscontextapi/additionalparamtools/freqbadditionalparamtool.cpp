#include "freqbadditionalparamtool.h"
#include "bfpnames.h"
#include "infostruct.h"
#include "iprobedataset.h"
#include "presetconfig.h"
#include "presetconfigfactory.h"
#include "sonoparameters.h"
#include "usapiinfostruct.h"
#include "uscontextapi.h"
#include <QHash>

FreqBAdditionalParamTool::FreqBAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{
}

FreqBAdditionalParamTool::~FreqBAdditionalParamTool()
{
}

UsRetCode FreqBAdditionalParamTool::execute(int *value)
{
    QStringList freqBShowText = m_UsContextApi->freqBShowText();
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    if (m_UsContextApi->isLoad())
    {
        int probeID = sp->pIV(BFPNames::ProbeIdStr);
        const ProbeDataInfo &dataInfo = m_ProbeDataSet->getProbe(probeID);
        QString probename = dataInfo.Name;
        QString presetShowname = sp->pV(BFPNames::ExamModeCaptionStr).toString();
        QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
            m_UsContextApi->presetAliasNames(), m_ProbeDataSet, m_UsContextApi->currentSonoParameters(),
            m_UsContextApi->globalSonoParameters());
        QString realpresetname = presetConfig->getPrestName(dataInfo.Name, presetShowname);
        freqBShowText = getfreqIndexTextList(probename, realpresetname);
    }

    char *ptr = (char *)value;
    if (freqBShowText.size() == 0)
    {
        return UsFailed;
    }
    else
    {
        int l = 0;
        for (int i = 0; i < freqBShowText.size(); i++)
        {
            QString f = freqBShowText[i] + ',';
            memcpy(ptr + l, f.toStdString().c_str(), f.length());
            l += f.length();
        }
        return UsOK;
    }
}

QStringList FreqBAdditionalParamTool::getfreqIndexTextList(QString probename, QString presetname)
{
    QStringList listpresetName = QString(presetname).split("@");
    if (listpresetName.size() > 0)
    {
        presetname = listpresetName[0];
    }
    QHash<QString, QList<PresetAliasName>> presetalias = m_UsContextApi->presetAliasNames();
    if (presetalias.contains(probename))
    {
        QStringList freqtxt;
        QList<int> freqidx;
        for (int i = 0; i < presetalias[probename].size(); i++)
        {
            if (presetalias[probename][i].PresetName == presetname)
            {
                freqtxt = presetalias[probename][i].FreqShowNames;
                freqidx = presetalias[probename][i].FreqBIndexs;
            }
        }
        if (freqtxt.size() > 0)
        {
            return freqtxt;
        }
    }
    return m_UsContextApi->freqBShowText();
}
