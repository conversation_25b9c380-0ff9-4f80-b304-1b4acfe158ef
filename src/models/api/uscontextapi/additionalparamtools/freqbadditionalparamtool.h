#ifndef FREQBADDITIONALPARAMTOOL_H
#define FREQBADDITIONALPARAMTOOL_H

#include "additionalparamtool.h"

class FreqBAdditionalParamTool : public AdditionalParamTool
{
public:
    FreqBAdditionalParamTool(UsContextApi *usContextApi);
    virtual ~FreqBAdditionalParamTool();

    virtual UsRetCode execute(int *value);

private:
    QStringList getfreqIndexTextList(QString probename, QString presetname);
};

#endif // FREQBADDITIONALPARAMTOOL_H
