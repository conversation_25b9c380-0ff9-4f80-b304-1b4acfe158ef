#include "isloadadditionalparamtool.h"
#include "uscontextapi.h"

IsLoadAdditionalParamTool::IsLoadAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{

}

IsLoadAdditionalParamTool::~IsLoadAdditionalParamTool()
{

}

UsRetCode IsLoadAdditionalParamTool::execute(int *value)
{
    if(m_UsContextApi->isLoad())
    {
        value[0] = 1;
    }
    else
    {
        value[0] = 0;
    }
    return UsOK;
}
