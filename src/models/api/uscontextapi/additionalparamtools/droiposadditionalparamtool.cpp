#include "droiposadditionalparamtool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include <QGraphicsScene>
#include "uscontextapi.h"
#include "parameter.h"

DRoiPosAdditionalParamTool::DRoiPosAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{

}

DRoiPosAdditionalParamTool::~DRoiPosAdditionalParamTool()
{

}

UsRetCode DRoiPosAdditionalParamTool::execute(int *value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    QGraphicsScene *graphicsScene = reinterpret_cast<QGraphicsScene*>(m_UsContextApi->graphicsScene());
    if(graphicsScene == NULL)
    {
        return UsFailed;
    }
    int scanlinepos = sp->pIV(BFPNames::DScanLineStr);
    int DSCHEIGHT = graphicsScene->height();
    int DSCWIDTH = graphicsScene->width();
    int coef = sp->pDV(BFPNames::ImageZoomCoefStr);
    float coef1 = (100-(100-coef)/2)/100.0;
    UsLogicalPoint firstpoint;
    m_UsContextApi->convertPhysicsToLogic(scanlinepos,0,&firstpoint);
    int realheight = DSCHEIGHT*(coef/100.0);
    Parameter *p = sp->parameter(BFPNames::SteeringAngleStr);
    int currentsteer = p->text().toInt();
    int offset = tan(currentsteer/180.0*3.1415926)*realheight;
    int offsetx = firstpoint.UsLogicalPoint_INT.X-offset;
    value[0] = (firstpoint.UsLogicalPoint_INT.X+(DSCWIDTH/2));
    value[1] = DSCHEIGHT*(1-coef1);
    value[2] = offsetx+(DSCWIDTH/2);
    value[3] = DSCHEIGHT*coef1;
    return UsOK;
}
