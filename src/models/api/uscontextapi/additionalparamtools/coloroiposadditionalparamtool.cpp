#include "coloroiposadditionalparamtool.h"
#include "bfpnames.h"
#include "formula.h"
#include "iprobedataset.h"
#include "parameter.h"
#include "probeparameters.h"
#include "sonoparameters.h"
#include "uscontextapi.h"
#include <QGraphicsScene>

ColoRoiPosAdditionalParamTool::ColoRoiPosAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{
}

ColoRoiPosAdditionalParamTool::~ColoRoiPosAdditionalParamTool()
{
}

UsRetCode ColoRoiPosAdditionalParamTool::execute(int *value)
{
    QGraphicsScene *graphicsScene = reinterpret_cast<QGraphicsScene *>(m_UsContextApi->graphicsScene());
    if (graphicsScene == NULL)
    {
        return UsFailed;
    }
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    const ProbeDataInfo &probe = m_ProbeDataSet->getProbe(sp->pIV(BFPNames::ProbeIdStr));
    ProbeParameters probeParam = ProbeParameters(probe);
    bool flip = sp->pBV(BFPNames::LeftStr); //相控阵需要翻转
    bool up = sp->pBV(BFPNames::UpStr);
    QTransform flipTrans = Formula::flipTransform(up, flip);
    int DSCHEIGHT = graphicsScene->height();
    int DSCWIDTH = graphicsScene->width();
    int midline = sp->pIV(BFPNames::RoiMidLineStr);
    int halflines = sp->pIV(BFPNames::RoiHalfLinesStr);
    double middepth = sp->pDV(BFPNames::RoiMidDepthMMStr);
    double halfDepth = sp->pDV(BFPNames::RoiHalfDepthMMStr);
    int coef = sp->pDV(BFPNames::ImageZoomCoefStr);
    if (!flip)
    {
        midline = probeParam.lines() - midline;
    }
    int leftBottomX = midline - halflines;
    double leftBottomY = middepth + halfDepth;
    Parameter *p = sp->parameter(BFPNames::SteeringAngleStr);
    int angle = p->text().toInt();
    if (!probe.IsLinear)
    {
        angle = 0;
    }
    //血流roi 下边缘 左右两个点的坐标
    for (int i = 0; i < 2; i++)
    {
        if (i == 1)
        {
            leftBottomX = midline + halflines;
        }
        UsLogicalPoint point;
        m_UsContextApi->convertPhysicsToLogic(leftBottomX, leftBottomY, &point);
        int logicx = point.UsLogicalPoint_INT.X + (DSCWIDTH / 2);
        int logicy = point.UsLogicalPoint_INT.Y;

        int offsety1 = ((100 - coef) / 2.0) / 100.0 * DSCHEIGHT;
        int offsetx1 = -(logicy)*sin(angle / 180.0 * 3.1415926);

        //矩阵转换翻转后的偏移坐标
        QPoint transPos(QPoint(offsetx1, offsety1) * flipTrans);

        value[i * 2] = logicx + transPos.x();
        value[i * 2 + 1] = logicy * cos(abs(angle) / 180.0 * 3.1415926) + transPos.y();
    }
    //加入中间的点
    int centerX = midline;
    int centerY = middepth;
    UsLogicalPoint point;
    m_UsContextApi->convertPhysicsToLogic(centerX, centerY, &point);
    int logicx = point.UsLogicalPoint_INT.X + (DSCWIDTH / 2);
    int logicy = point.UsLogicalPoint_INT.Y;

    int offsety1 = ((100 - coef) / 2.0) / 100.0 * DSCHEIGHT;
    int offsetx1 = -(logicy)*sin(angle / 180.0 * 3.1415926);

    //矩阵转换翻转后的偏移坐标
    QPoint transPos(QPoint(offsetx1, offsety1) * flipTrans);

    value[4] = logicx + transPos.x();
    value[5] = logicy * cos(abs(angle) / 180.0 * 3.1415926) + transPos.y();

    return UsOK;
}
