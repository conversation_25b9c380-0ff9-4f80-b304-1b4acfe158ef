#include "additionalparamtool.h"
#include "uscontextapi.h"

AdditionalParamTool::AdditionalParamTool(UsContextApi *usContextApi)
    : m_UsContextApi(usContextApi)
    , m_ProbeDataSet(usContextApi->probeDataSet())
{
}

AdditionalParamTool::~AdditionalParamTool()
{
}

UsRetCode AdditionalParamTool::execute(QVariant &value)
{
    Q_UNUSED(value)
    return UsFailed;
}

UsRetCode AdditionalParamTool::execute(int *value)
{
    Q_UNUSED(value)
    return UsFailed;
}
