#include "playingadditionalparamtool.h"
#include "uscontextapi.h"

PlayingAdditionalParamTool::PlayingAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{

}

PlayingAdditionalParamTool::~PlayingAdditionalParamTool()
{

}

UsRetCode PlayingAdditionalParamTool::execute(int *value)
{
    if(m_UsContextApi->isLooping())
    {
        value[0] = 1;
    }
    else
    {
        value[0] =0;
    }
    return UsOK;
}
