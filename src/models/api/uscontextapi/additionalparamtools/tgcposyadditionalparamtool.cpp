#include "tgcposyadditionalparamtool.h"
#include "bfpnames.h"
#include "iprobedataset.h"
#include "probeparameters.h"
#include "sonoparameters.h"
#include "uscontextapi.h"

TGCPosyAdditionalParamTool::TGCPosyAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{
}

TGCPosyAdditionalParamTool::~TGCPosyAdditionalParamTool()
{
}

UsRetCode TGCPosyAdditionalParamTool::execute(int *value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    int probeId = sp->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo &probeDataInfo = m_ProbeDataSet->getProbe(probeId);
    ProbeParameters probeParam(probeDataInfo, sp->pDV(BFPNames::StartDepthMMStr), sp->pDV(BFPNames::PixelSizeMMStr));
    int posy = qRound(probeParam.arcFloorDisMM() / sp->pDV(BFPNames::PixelSizeMMStr));
    value[0] = posy;
    return UsOK;
}
