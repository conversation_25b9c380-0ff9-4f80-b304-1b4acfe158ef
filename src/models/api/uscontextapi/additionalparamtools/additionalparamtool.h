#ifndef ADDITIONALPARAMTOOL_H
#define ADDITIONALPARAMTOOL_H

#include "iadditionalparamtool.h"

class UsContextApi;
class IProbeDataSet;
class AdditionalParamTool : public IAdditionalParamTool
{
  public:
    explicit AdditionalParamTool(UsContextApi *usContextApi);
    virtual ~AdditionalParamTool();

    virtual UsRetCode execute(QVariant &value);

    virtual UsRetCode execute(int *value);

  protected:
    UsContextApi *m_UsContextApi;
    IProbeDataSet *m_ProbeDataSet;
};

#endif // ADDITIONALPARAMTOOL_H
