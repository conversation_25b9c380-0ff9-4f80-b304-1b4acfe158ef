#include "probetypeadditionalparamtool.h"
#include "bfpnames.h"
#include "iprobedataset.h"
#include "sonoparameters.h"
#include "uscontextapi.h"

ProbeTypeAdditionalParamTool::ProbeTypeAdditionalParamTool(UsContextApi *usContextApi)
    : AdditionalParamTool(usContextApi)
{
}

ProbeTypeAdditionalParamTool::~ProbeTypeAdditionalParamTool()
{
}

UsRetCode ProbeTypeAdditionalParamTool::execute(int *value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    SonoParameters *gsp = m_UsContextApi->globalSonoParameters();
    // 0: 返回全局探头，当前连接探头的 1：当前探头 包括回调
    int probeID = (value[0] == 0 ? gsp->pIV(BFPNames::ProbeIdStr) : sp->pIV(BFPNames::ProbeIdStr));
    const ProbeDataInfo &dataInfo = m_ProbeDataSet->getProbe(probeID);
    value[0] = int(dataInfo.IsLinear) + (int(dataInfo.IsPhasedArray) << 1);
    return UsOK;
}
