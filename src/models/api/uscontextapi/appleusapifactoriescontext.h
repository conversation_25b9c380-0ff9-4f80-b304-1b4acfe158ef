#ifndef SONOAIRUSAPIFACTORIESCONTEXT_H
#define SONOAIRUSAPIFACTORIESCONTEXT_H

#include "baseusapifactoriescontext.h"

class BeamFormerApple;
class UltrasoundDevice;

/** 2024-06-05 Write by <PERSON><PERSON><PERSON>
 * @brief The SonoAirUSAPIFactoriesContext class
 */
class AppleUSAPIFactoriesContext : public BaseUSAPIFactoriesContext
{
    Q_OBJECT
public:
    explicit AppleUSAPIFactoriesContext(QObject *parent = NULL);
    ~AppleUSAPIFactoriesContext();

    /** 2024-06-05 Write by <PERSON><PERSON><PERSON>
     * @brief initialize 初始化
     * @param width
     * @param height
     */
    void initialize(int width = -1, int height = -1) override;

    /** 2024-06-11 Write by <PERSON><PERSON><PERSON>
     * @brief setCurrentProbe 设置当前探头
     * @param probeInfo
     * @return
     */
    bool setCurrentProbe(const ProbeDataInfo &probeInfo) override;

private:
    BeamFormerApple *m_BeamFormerApple;
};

#endif // SONOAIRUSAPIFACTORIESCONTEXT_H
