//
//  offscreen.h
//  lib
//
//  Created by chsionmac on 2020/5/20.
//

#ifndef offscreen_h
#define offscreen_h
#include <qcoreevent.h>
#include <qgraphicsscene.h>
#ifdef SYS_APPLE
#include <QOpenGLContext>
#endif
/*
 scene: 当前graphicsscene 指针
 buffer: 返回当前scene的显示图像的内容，buffer 外界申请
 */
uint8_t * offscreen_renderQt(QGraphicsScene * scene);
/*
 发送鼠标事件到graphicsevent 到scene
 eventtype:鼠标event 类型
 posx posy: 鼠标位置坐标
 m_scene: 当前scene
 */
void offscreen_qtevent_generateandSend(QEvent::Type eventtype,int posx,int posy, QGraphicsScene*m_scene);

#ifdef SYS_APPLE
//初始化，返回当前的context
QOpenGLContext * offscreen_init(QGraphicsScene * scene);
#endif
#endif /* offscreen_h */
