#include "uscontextapi.h"
#include "additionalparamnames.h"
#include "aiocontroller.h"
#include "appender.h"
#include "applogger.h"
#include "appsetting.h"
#include "beamformerbase.h"
#include "bfadfreqparameter.h"
#include "bfcoordtransform.h"
#include "bfdepthparameters.h"
#include "bfpnames.h"
#include "bufferstorer.h"
#include "chisonultrasoundcontext.h"
#include "colormapcontext.h"
#include "colormapmanager.h"
#include "colormaptypedef.h"
#include "coloroiposadditionalparamtool.h"
#include "databasemanager.h"
#include "displaystyleinfoloader.h"
#include "dopplerstartdepthmmglyphtool.h"
#include "droiposadditionalparamtool.h"
#include "dscanlineglyphtool.h"
#include "exammodepresethandlerfactory.h"
#include "framecontrol.h"
#include "freqbadditionalparamtool.h"
#include "functionhandlerfactory.h"
#include "generalinfo.h"
#include "glprocesscontext.h"
#include "glyphtool.h"
#include "iadditionalparamtool.h"
#include "icontroltable.h"
#include "ielementdetectalgorithom.h"
#include "ifunctionhandler.h"
#include "iglyphtool.h"
#include "ilinebuffer.h"
#include "ilinebuffermanager.h"
#include "isloadadditionalparamtool.h"
#include "iusapifactoriescontext.h"
#include "linecinelooper.h"
#include "logger.h"
#include "logmanager.h"
#include "measurecontext.h"
#include "modelconfig.h"
#include "physicalgeometrycontroller.h"
#include "physicallinecontroller.h"
#include "playingadditionalparamtool.h"
#include "presetconfig.h"
#include "presetconfigfactory.h"
#include "presetutilitytool.h"
#include "probeconfig.h"
#include "probedatasetfacade.h"
#include "probeparameters.h"
#include "probetypeadditionalparamtool.h"
#include "propertyconfigurator.h"
#include "qapp.h"
#include "renderlayoutconfigureloader.h"
#include "resource.h"
#include "roicontroller.h"
#include "scanmodeinfoconverter.h"
#include "setbiopsycolorglyphtool.h"
#include "sonoparameters.h"
#include "statemanagerfacade.h"
#include "steeringangleglyphtool.h"
#include "systemscanmodeclassifier.h"
#include "tgcposyadditionalparamtool.h"
#include "toolsfactorytemplate.h"
#include "usbudpadditionalparamtool.h"
#include <QCoreApplication>
#include <QGraphicsScene>
#include <QGraphicsTextItem>
#include <QStringList>
#ifdef SYS_APPLE
#include "dopplerscanlinewidget.h"
#include "fpgaupgrademodel_palm.h"
#include "glyphscontrolmanager.h"
#include "license_palm.h"
#include "modeluiconfig.h"
#include "platform_xx_resourcehelper.h"
#include "proxygroup.h"
#include "qstandardpaths.h"
#endif
#ifdef USE_VA
#include "vafunctionhandler.h"
#endif
#include "fpgainfo.h"
#include "ultrasounddevice.h"
#ifndef SYS_ANDROID
#include "elementtestcontroller.h"
#endif
#include <QApplication>
#include "imagesavehelper.h"
#include "elementdetectalgotithom.h"
#include "exammodepresetdatahandlerprovider.h"
#include "iexammodepresetdatahandler.h"
#include "util.h"

LOG4QT_DECLARE_STATIC_LOGGER(log, UsContextApi)

QHash<qint32, UsUltrasoundErrorCode> UsContextApi::ErrorCodeHash;

UsContextApi::UsContextApi(MachineType type)
    : m_IsPaused(true)
    , m_StateManager(NULL)
    , m_GlobalSonoParameters(NULL)
    , m_CurrentGlobalSonoParameters(NULL)
    , m_CurrentSonoParameters(NULL)
    , m_USAPIFactoriesContext(NULL)
    , m_ChisonUltrasoundContext(NULL)
    , m_LineCineLooper(NULL)
    , m_IsLoad(false)
    , m_Wit2LoopStop(false)
    , m_CurrentController(NULL)
    , m_MeasureContext(NULL)
    , m_BackupImageForMeasure(NULL)
    , m_SetScaleTimer(NULL)
    , m_VelocityDown(NULL)
    , m_VelocityUp(NULL)
    , m_AppStateChange(false)
    , m_CurrentShowIndex(0)
    , m_IsValidProbeConnected(true)
    , m_DataBaseManager(new DataBaseManager)
    , m_ProbeDataSet(new ProbeDataSetFacade)
    , m_PresetDataHandlerProvider(new ExamModePresetDataHandlerProvider)
    , m_ImageSaveHelper(new ImageSaveHelper)
    , m_ElementDetectAlgorithom(new ElementDetectAlgotithom)
{
#ifdef SYS_ANDROID
    //先判断标志存在，存在就不拷贝
    Util::copyDir(Resource::assetsDir, "./");
#endif
    initQApp();
    initErrorCode();
    AppSetting::setFunction(LicenseItemKey::KeyAnimal, (int)type);
    memset(m_CallbackInfo, 0, sizeof(m_CallbackInfo));
    startLogger();
    ScanModeInfoConverter::instance().read(Resource::scanModeInfoName);
    RenderLayoutConfigureLoader::instance().read(Resource::renderLayoutConfigName);
    DisplayStyleInfoLoader::instance().load(Resource::displayFormatName);
}

UsContextApi::~UsContextApi()
{
    if (m_USAPIFactoriesContext != NULL)
    {
        m_USAPIFactoriesContext->beamFormer()->close();
    }
    if (m_ChisonUltrasoundContext != NULL)
    {
        delete m_ChisonUltrasoundContext;
        m_ChisonUltrasoundContext = NULL;
    }
    if (m_USAPIFactoriesContext != NULL)
    {
        delete m_USAPIFactoriesContext;
    }

    Util::SafeDeletePtr(m_DataBaseManager);
    Util::SafeDeletePtr(m_ProbeDataSet);
    Util::SafeDeletePtr(m_StateManager);
    Util::SafeDeletePtr(m_ColorMapManager);
    Util::SafeDeletePtr(m_PresetDataHandlerProvider);
    Util::SafeDeletePtr(m_ImageSaveHelper);
    Util::SafeDeletePtr(m_ElementDetectAlgorithom);
}

void UsContextApi::initialize(int width, int height)
{
    m_StateManager = new StateManagerFacade;
    m_ColorMapManager = new ColorMapManager;

    m_USAPIFactoriesContext = IUSAPIFactoriesContext::factoriesContext(AppSetting::model());
    m_USAPIFactoriesContext->setStateManager(m_StateManager);
    m_USAPIFactoriesContext->setColorMapManager(m_ColorMapManager);
    m_USAPIFactoriesContext->initialize(width, height);
    connect(this, SIGNAL(presetChanged(PresetParameters)), m_USAPIFactoriesContext->beamFormer(),
            SLOT(setPreset(PresetParameters)));
    m_GlobalSonoParameters = m_USAPIFactoriesContext->beamFormer()->sonoParameters();
    m_CurrentGlobalSonoParameters = m_GlobalSonoParameters;
    m_CurrentSonoParameters = m_GlobalSonoParameters;
    initializeBeamformer();
    initializeProbePresetModel();
    initializeChisonUltrasoundContext();
    initializeGraphicsContext();
    sonoParametersChanged(m_GlobalSonoParameters);
    connectParametersChanged(m_CurrentSonoParameters);
    initializeColorMap();
    initializePreset();
    initializeSpecialParameters();
    initializeGlyphTools();
#ifdef USE_VA
    initializeVAContext();
#endif
    initializePositionParameterHandle();
    initializeAdditionalParamTools();
}

UsRetCode UsContextApi::humanAnimalSwitch(MachineType type)
{
    int value = AppSetting::function(LicenseItemKey::KeyAnimal);
    if (value != (int)type)
    {
        AppSetting::setFunction(LicenseItemKey::KeyAnimal, (int)type);
        QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
            m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
        presetConfig->readPresetConfig();
        ExamModePresetHandlerFactory::instance()->defaultHandler()->reset();
    }
    return UsOK;
}

UsRetCode UsContextApi::setCallbackInfo(UsContextApi::CallbackType type, void* callback, void* userData)
{
    if (type < UsCount_CT)
    {
        CallbackInfo ci;
        ci.first = callback;
        ci.second = userData;
        m_CallbackInfo[type] = ci;
        return UsRetCode::UsOK;
    }
    return UsRetCode::UsFailed;
}

void UsContextApi::setWifiWriteCallback(UsWifiWriteCallback callback, void* userData)
{
    m_USAPIFactoriesContext->setExternalIODeviceWriteCallback(callback, userData);
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
}

void UsContextApi::pushData(unsigned char* data, int size)
{
    m_USAPIFactoriesContext->pushExternalIODeviceData(data, size);
}

UsRetCode UsContextApi::start()
{
    if (!m_USAPIFactoriesContext->beamFormer()->open())
    {
        return UsFailed;
    }
    m_IsValidProbeConnected = false;
    m_ChisonUltrasoundContext->start();
    onColorMapChanged();
    m_USAPIFactoriesContext->beamFormer()->setPV(BFPNames::SystemScanModeStr, SystemScanModeB);
    m_USAPIFactoriesContext->beamFormer()->parameter(BFPNames::SystemScanModeStr)->update();
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
    return UsOK;
}

UsRetCode UsContextApi::stop()
{
    return UsFailed;
}

UsRetCode UsContextApi::setScanMode(UsScanMode scanMode)
{
    if (m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        return UsFailed;
    }
    UsScanMode oldScanMode = getScanMode();
    if ((oldScanMode != scanMode) ||
        (m_CurrentSonoParameters->pBV(BFPNames::BCImagesOnStr) && oldScanMode == UsSystemScanModeColorDoppler))
    {
        if (scanMode == UsSystemScanModeLRBBC)
        {
            m_USAPIFactoriesContext->beamFormer()->setSystemScanMode(SystemScanModeColorDoppler);
            m_CurrentSonoParameters->setPV(BFPNames::BCImagesOnStr, true);
        }
        else if (scanMode == UsSystemScanModeBPW || scanMode == UsSystemScanModeColorPW)
        {

            int size = m_ChisonUltrasoundContext->lineBuffer()->frameCount();
            int scpdvalue = m_CurrentSonoParameters->pIV(BFPNames::ScpdStr);
            // pw如果没有接收到c或者b的数据，则不进入激活状态
            if (size <= (scpdvalue + 1))
            {
                return UsFailed;
            }
            m_USAPIFactoriesContext->beamFormer()->setSystemScanMode((SystemScanMode)scanMode);
            pwDelayFreqSpectrumSetting(800);
            m_CurrentSonoParameters->parameter(BFPNames::BaseLineStr)->update();
        }
        else
        {
            if (scanMode == UsSystemScanModeB && oldScanMode == UsSystemScanModeColorPW)
            {
                m_USAPIFactoriesContext->beamFormer()->rOIController()->setActive(false);
            }
            m_USAPIFactoriesContext->beamFormer()->setSystemScanMode((SystemScanMode)scanMode);
            if (((oldScanMode == UsSystemScanModeE) || (oldScanMode == UsSystemScanModeLRBBC)) &&
                (scanMode != UsSystemScanModeE))
            {
                m_CurrentSonoParameters->setPV(BFPNames::BCImagesOnStr, false);
            }
        }
        if (scanMode != UsSystemScanModeBPW && scanMode != UsSystemScanModeColorPW)
        {
            m_USAPIFactoriesContext->beamFormer()->setPV(BFPNames::FreqSpectrumStr, false);
        }
        if (scanMode == UsSystemScanModeUDBM)
        {
            m_CurrentSonoParameters->setPV(BFPNames::FrameScapeEnableStr, false);
        }
        else
        {
            m_CurrentSonoParameters->setPV(BFPNames::FrameScapeEnableStr, true);
        }
        changeGeometryController((SystemScanMode)m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr));
        emit systemScanModeChanged((SystemScanMode)m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr));
    }
    return UsOK;
}

UsScanMode UsContextApi::getScanMode() const
{
    if (m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr) == UsSystemScanModeColorDoppler)
    {
        if (m_CurrentSonoParameters->pBV(BFPNames::BCImagesOnStr))
        {
            return UsSystemScanModeLRBBC;
        }
        else
        {
            return UsSystemScanModeColorDoppler;
        }
    }
    else
    {
        int value = m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr);
        if (value == SystemScanMode::SystemScanModeAV)
        {
            return UsSystemScanModeB;
        }
        else
        {
            return (UsScanMode)value;
        }
    }
}

UsRetCode UsContextApi::setLayout(UsLayout layout)
{
    if (m_GlobalSonoParameters->pBV(BFPNames::FreezeStr))
    {
        return UsFailed;
    }
    UsLayout oldLayout = getLayout();
    if (oldLayout != layout)
    {
        setActiveLayoutIndex(0);
        m_GlobalSonoParameters->setPV(BFPNames::LayoutStr, layout);
        emit layoutChanged(layout);
        return UsOK;
    }
    return UsFailed;
}

UsLayout UsContextApi::getLayout() const
{
    return (UsLayout)m_GlobalSonoParameters->pIV(BFPNames::LayoutStr);
}

UsRetCode UsContextApi::setActiveLayoutIndex(int activeIndex)
{
    if ((m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr) != activeIndex) &&
        (m_CurrentGlobalSonoParameters->pIV(BFPNames::LayoutStr) > Layout_1x1) &&
        (activeIndex < m_CurrentGlobalSonoParameters->pIV(BFPNames::LayoutStr)))
    {
        m_CurrentGlobalSonoParameters->setPV(BFPNames::ActiveBStr, activeIndex);
        disConnectParametersChanged(m_CurrentSonoParameters);
        m_CurrentSonoParameters = m_ChisonUltrasoundContext->lineBuffer()->getSonoParametersByLayoutIndex(activeIndex);
        connectParametersChanged(m_CurrentSonoParameters);
        emit activeIndexChanged(activeIndex);
        return UsOK;
    }
    return UsFailed;
}

int UsContextApi::getActiveLayoutIndex() const
{
    return m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr);
}

UsRetCode UsContextApi::setFreeze(int isFrozen)
{
    if ((isFrozen > 1) || (isFrozen < 0))
    {
        return UsFailed;
    }
    if (!isFrozen && !m_IsValidProbeConnected)
    {
        return UsNotSupportNow;
    }
    if (isFrozen == 1 && m_CurrentSonoParameters->isRealTime() && !m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        lazyFreeze();
    }
    else if (isFrozen == 0)
    {
        if (m_CurrentSonoParameters->isRealTime() && !m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
        {
            return UsOK;
        }
        disConnectParametersChanged(m_CurrentSonoParameters);
        bool dopplerscanlineshow = m_GlobalSonoParameters->pBV(BFPNames::IsDopplerScanLineVisibleStr);
        //[53787]:SR9多次​点击Freeze,只能冻结一次
        //解决办法：将freeze(false)操作放到endLoad()之前
        m_USAPIFactoriesContext->beamFormer()->freeze(false);
        endLoad(); // usloadcine usloadimage 需要endload 恢复
        connectParametersChanged(m_CurrentSonoParameters);
        //[53787]:SR9多次​点击Freeze,只能冻结一次
        //解决办法：将freeze(false)操作放到endLoad()之前
        // m_USAPIFactoriesContext->beamFormer()->freeze(false);
        m_USAPIFactoriesContext->beamFormer()->parameter(BFPNames::FreezeStr)->update();
        if (getScanMode() == UsSystemScanModeBPW || getScanMode() == UsSystemScanModeColorPW)
        {
            pwDelayFreqSpectrumSetting(500);
        }
        setColorVelocity();
        // TODO 在M和PW准备模式下如果回调后，回到实时 线不显示的问题解决
        m_USAPIFactoriesContext->beamFormer()->parameter(BFPNames::IsMLineVisibleStr)->update();
        m_CurrentSonoParameters->setPV(BFPNames::IsDopplerScanLineVisibleStr, dopplerscanlineshow);
        onImageTransformChanged();
    }
    return UsOK;
}

int UsContextApi::isFrozen() const
{
    return (int)m_USAPIFactoriesContext->beamFormer()->isFrozen();
}

UsRetCode UsContextApi::standby()
{
    m_LineCineLooper->standby();

    if (m_USAPIFactoriesContext->beamFormer()->standby())
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return UsOK;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
        return UsFailed;
    }
}

UsRetCode UsContextApi::wake()
{
    if (m_USAPIFactoriesContext->beamFormer()->wake())
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return UsOK;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
        return UsFailed;
    }
}

void UsContextApi::setProbeConnected(int isconnected)
{
    m_GlobalSonoParameters->setPV(BFPNames::ProbeConnectedStr, isconnected);
}

UsRetCode UsContextApi::setCurrentProbe(const char* probeName)
{
    if (probeName == NULL)
    {
        return UsFailed;
    }

    const ProbeDataInfo& dataInfo = m_ProbeDataSet->probe(probeName);
    if (dataInfo.isNull())
    {
        return UsFailed;
    }
    Log4Qt::Logger::rootLogger()->info("%1 probeid: %2 probename: %3", PRETTY_FUNCTION, dataInfo.Id, probeName);
    if (m_USAPIFactoriesContext->setCurrentProbe(dataInfo))
    {
        return UsOK;
    }
    else
    {
        return UsFailed;
    }
}

UsRetCode UsContextApi::getCurrentProbe(UsProbeDataInfo* probeDataInfo) const
{
    const ProbeDataInfo& dataInfo = m_USAPIFactoriesContext->beamFormer()->curProbe();
    if (dataInfo.isNull() || dataInfo.Name == ("DefaultProbe"))
    {
        memset(probeDataInfo, 0, sizeof(UsProbeDataInfo));
    }
    else
    {
        probeDataInfo->Code = dataInfo.Code;
        memset(probeDataInfo->Name, 0, MAX_PROBE_NAME_SIZE);
        memcpy(probeDataInfo->Name, dataInfo.Name.toStdString().c_str(),
               qMin(dataInfo.Name.length() + 1, MAX_PROBE_NAME_SIZE - 2));
        probeDataInfo->WaferNum = dataInfo.WaferNum;
        probeDataInfo->WaferLength = dataInfo.WaferLength;
        probeDataInfo->WaferRadius = dataInfo.WaferRadius;
        probeDataInfo->IsLinear = dataInfo.IsLinear;
        probeDataInfo->NoUseLine = dataInfo.NoUseLine;
    }
    return UsOK;
}

int UsContextApi::getAllSupportProbeNames(char** probeNames, const int arraySize, const int itemSize)
{
    ProbeConfig probeConfig(m_PresetAliasNames, m_ProbeDataSet);
    return probeConfig.getAllSupportProbeNames(probeNames, arraySize, itemSize);
}

UsRetCode UsContextApi::getProbeShowNameByProbeName(const char* const probename, char* probeShowName)
{
    ProbeConfig probeConfig(m_PresetAliasNames, m_ProbeDataSet);
    return probeConfig.getProbeShowNameByProbeName(probename, probeShowName);
}

UsRetCode UsContextApi::getCurrentProbeShowName(char* probeShowName)
{
    ProbeConfig probeConfig(m_PresetAliasNames, m_ProbeDataSet);
    return probeConfig.getCurrentProbeShowName(probeShowName, m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr));
}

UsRetCode UsContextApi::GetCurrentProbeModeName(char* probeModeName, const int itemSize)
{
    QString probeMName = m_CurrentSonoParameters->pV(BFPNames::ProbeModeNameStr).toString();
    memcpy(probeModeName, probeMName.toStdString().c_str(), qMin(probeMName.length() + 1, itemSize - 1));
    *(probeModeName + itemSize - 1) = '\0';
    return UsOK;
}

UsRetCode UsContextApi::GetCurrentPresetProbeName(char* presetProbeName, const int itemSize)
{
    QString presetPName = m_CurrentSonoParameters->pV(BFPNames::PresetProbeNameStr).toString();
    memcpy(presetProbeName, presetPName.toStdString().c_str(), qMin(presetPName.length() + 1, itemSize - 1));
    *(presetProbeName + itemSize - 1) = '\0';
    return UsOK;
}

UsRetCode UsContextApi::getProbeStatus(ProbeStatus* probestatus)
{
    ProbeConfig probeConfig(m_PresetAliasNames, m_ProbeDataSet);
    return probeConfig.getProbeStatus(probestatus);
}

UsRetCode UsContextApi::probeShutDown()
{
    return UsFailed;
}

UsRetCode UsContextApi::loadPreset(const char* const fileName)
{
    if ((fileName == NULL) || (!QFile::exists(fileName)))
    {
        return UsFailed;
    }

    Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 start ").arg(PRETTY_FUNCTION).arg(fileName));
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    PresetParameters* presetParameters = presetConfig->loadPresetParameters(QString::fromLocal8Bit(fileName));

    if (NULL == presetParameters)
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 failed ").arg(PRETTY_FUNCTION).arg(fileName));
        return UsFailed;
    }

    m_GlobalSonoParameters->setPreset(*presetParameters);
    // emit presetChanged(*presetParameters);

    delete presetParameters;
    presetParameters = NULL;

    Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 ok ").arg(PRETTY_FUNCTION).arg(fileName));
    return UsOK;
}

UsRetCode UsContextApi::savePreset(const char* const fileName)
{
    if (NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->savePresetParameters(QString::fromLocal8Bit(fileName), m_CurrentSonoParameters->preset()))
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 ok ").arg(PRETTY_FUNCTION).arg(fileName));
        return UsOK;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 failed ").arg(PRETTY_FUNCTION).arg(fileName));
        return UsFailed;
    }
}

int UsContextApi::getCurrentProbePreset(char** preset, const int arraySize, const int bufferSize)
{
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    return getProbePreset(probeID, preset, arraySize, bufferSize);
}

int UsContextApi::getProbePreset(const char* probeName, char** preset, const int arraySize, const int bufferSize)
{
    if (probeName == NULL)
    {
        return UsFailed;
    }
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->probe(probeName);
    if (dataInfo.isNull())
    {
        return UsFailed;
    }
    return getProbePreset(dataInfo.Id, preset, arraySize, bufferSize);
}

int UsContextApi::getProbePreset(const int probeID, char** preset, const int arraySize, const int bufferSize)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    return presetConfig->getProbePreset(probeID, preset, arraySize, bufferSize);
}

UsRetCode UsContextApi::getPresetNameByShowName(const char* presetShowname, char* presetName)
{
    int probeID = m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->getProbe(probeID);
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    QString realpresetname = presetConfig->getPrestName(dataInfo.Name, presetShowname);
    if (realpresetname == "")
    {
        return UsFailed;
    }
    memcpy(presetName, realpresetname.toStdString().c_str(), realpresetname.length() + 1);
    return UsOK;
}

bool UsContextApi::selectPreset(const char* presetName)
{
    if (presetName == NULL)
    {
        return false;
    }
    Log4Qt::Logger::rootLogger()->info("%1 name:%2 start", PRETTY_FUNCTION, presetName);
    clearFrameDsiplay();
    if (m_IsLoad)
    {
        onImageTransformChanged();
        setFreeze(false);
    }
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    QStringList emIds = handle->probeExamModesIdOrderList(probeID);
    QStringList emNames = handle->examModesNames(emIds);
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->getProbe(probeID);
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    QString realpresetname = presetConfig->getPrestName(dataInfo.Name, presetName);
    int index = emNames.indexOf(realpresetname);
    int freqShowId = updateBeamFormerFreqText(dataInfo.Name, realpresetname);
    if ((index >= 0) && (emIds.count() > index))
    {
        ExamMode mode;
        handle->probeExamMode(probeID, emIds[index], mode);
        m_GlobalSonoParameters->setPV(BFPNames::ExamModeIdStr, emIds[index]);
        setExamModeAliasName(mode, dataInfo);
        m_GlobalSonoParameters->setPV(BFPNames::ExamModeCaptionStr, mode.examModeName()); //预设值名称
        m_GlobalSonoParameters->setPreset(mode.paremeters());
        tryUnfreeze();
        m_GlobalSonoParameters->setPV(BFPNames::TGCStr, QByteArray(8, (char)127));
        if (freqShowId >= 0)
        {
            m_GlobalSonoParameters->setPV(BFPNames::FreqIndexBShowStr,
                                          freqShowId); //掌超ios 默认设置是中间的频率，可以保存到json文件中
            m_USAPIFactoriesContext->beamFormer()->parameter(BFPNames::FreqIndexBShowStr)->update();
            m_GlobalSonoParameters->setPV(BFPNames::LinePackageCountStr, 1); //掌超每次收1个package
#ifdef USE_COLOR_4_PACKAGESIZE
            m_GlobalSonoParameters->setPV(BFPNames::PacketSizeStr, 4); //掌超ios
#endif
            m_GlobalSonoParameters->setPV(BFPNames::ImageZoomCoefStr, 100); //
            m_GlobalSonoParameters->setPV(BFPNames::BloodEffectionStr, 0);  //掌超临时所有预设值值改成0
            m_GlobalSonoParameters->setPV(BFPNames::NeedleGainStr, 5);      //掌超needlegain 默认是5
            if (dataInfo.IsPhasedArray)
            {
                m_GlobalSonoParameters->setPV(BFPNames::LeftStr, false); //掌超都需要翻转
            }
        }
        m_USAPIFactoriesContext->beamFormer()->parameter(BFPNames::SyncModeStr)->update();
        setColorVelocity();
        onImageTransformChanged(true);

        Log4Qt::Logger::rootLogger()->info("%1 name:%2 ok", PRETTY_FUNCTION, presetName);
        return true;
    }

    Log4Qt::Logger::rootLogger()->info("%1 name:%2 failed", PRETTY_FUNCTION, presetName);
    return false;
}

int UsContextApi::getAllPreset(char** preset, const int arraySize, const int bufferSize)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    return presetConfig->getAllPreset(preset, arraySize, bufferSize);
}

bool UsContextApi::saveCurrentPreset()
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->saveCurrentPreset())
    {
        onSaveCurrentPreset();
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }

    Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
    return false;
}

bool UsContextApi::saveCurrentPresetAs(const char* presetName)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->saveCurrentPresetAs(presetName))
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
        return false;
    }
}

bool UsContextApi::renameCurrentPreset(const char* presetName)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->renameCurrentPreset(presetName))
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
        return false;
    }
}

bool UsContextApi::renamePreset(const char* oldPresetName, const char* newPresetName)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->renamePreset(oldPresetName, newPresetName))
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
        return false;
    }
}

bool UsContextApi::deleteCurrentPreset()
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->deleteCurrentPreset())
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
        return false;
    }
}

bool UsContextApi::deletePreset(const char* presetName)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->deletePresetByName(presetName))
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 ok ").arg(PRETTY_FUNCTION));
        return true;
    }
    else
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 failed ").arg(PRETTY_FUNCTION));
        return false;
    }
}

void UsContextApi::setExamModeAliasName(ExamMode& mode, const ProbeDataInfo& dataInfo)
{
    //掌超预设值是别名
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    QString showname = presetConfig->getPrestShowName(dataInfo.Name, mode.examModeName());
    mode.setExamModeName(showname);
}

UsRetCode UsContextApi::updateUserPreset(const char* oldpresetShowName, const char* newPresetShowname,
                                         const int* orderList, const int orderListLength, const int code)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    if (presetConfig->updateUserPreset(oldpresetShowName, newPresetShowname, orderList, orderListLength, code))
    {
        Log4Qt::Logger::rootLogger()->info(
            QString("%1 oldname:%2 newname:%3 ok ").arg(PRETTY_FUNCTION).arg(oldpresetShowName).arg(newPresetShowname));
        return UsOK;
    }
    Log4Qt::Logger::rootLogger()->info(
        QString("%1 oldname:%2 newname:%3 failed ").arg(PRETTY_FUNCTION).arg(oldpresetShowName).arg(newPresetShowname));
    return UsFailed;
}

bool UsContextApi::importPreset(const char* const destPath)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    return presetConfig->importPreset(destPath);
}

bool UsContextApi::exportPreset(const char* const destPath)
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    return presetConfig->exportPreset(destPath);
}

bool UsContextApi::restorePreset()
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    return presetConfig->restorePreset();
}

UsRetCode UsContextApi::saveCine(const char* const fileName, int startIndex, int endIndex)
{
    if (!isFrozen())
    {
        return UsFailed;
    }
    BufferStorer bufferStorer;
    ILineBufferManager* lineBufferManager = m_ChisonUltrasoundContext->lineBufferManager();

    if (!checkSave(bufferStorer, fileName, startIndex, endIndex))
    {
        return UsFailed;
    }

    Log4Qt::Logger::rootLogger()->info("%1 starindext:%2 endIndex:%3 start", PRETTY_FUNCTION, startIndex, endIndex);
    lineBufferManager->setStartIndex(startIndex);
    lineBufferManager->setEndIndex(endIndex);
#ifdef USE_VA
    QString lineDataDirPath = fileName;
    lineDataDirPath.remove(lineDataDirPath.length() - 4, 4);
    m_ChisonUltrasoundContext->saveVaResult(lineDataDirPath, startIndex, endIndex);
#endif
    UsRetCode ret = bufferStorer.save(fileName) ? UsOK : UsFailed;
    Log4Qt::Logger::rootLogger()->info(QString("%1 starindext:%2 endIndex:%3 code:%4 end")
                                           .arg(PRETTY_FUNCTION)
                                           .arg(startIndex)
                                           .arg(endIndex)
                                           .arg(ret));
    return ret;
}

UsRetCode UsContextApi::saveImage(const char* const fileName)
{

    BufferStorer bufferStorer;
    ILineBufferManager* lineBufferManager = m_ChisonUltrasoundContext->lineBufferManager();

    if (!checkSave(bufferStorer, fileName, lineBufferManager->currentIndex(), lineBufferManager->currentIndex()))
    {
        return UsFailed;
    }
#ifdef USE_VA
    QString lineDataDirPath = fileName;
    lineDataDirPath.remove(lineDataDirPath.length() - 4, 4);
    m_ChisonUltrasoundContext->saveVaResult(lineDataDirPath, lineBufferManager->currentIndex(),
                                            lineBufferManager->currentIndex());
#endif
    UsRetCode ret = bufferStorer.saveCurrentFrame(fileName) ? UsOK : UsFailed;

    Log4Qt::Logger::rootLogger()->info(QString("%1  code:%2 ").arg(PRETTY_FUNCTION).arg(ret));
    return ret;
}

UsRetCode UsContextApi::loadCine(const char* fileName)
{
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
    if (load(fileName))
    {
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::loadImage(const char* fileName)
{
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
    return load(fileName) ? UsOK : UsFailed;
}

UsRetCode UsContextApi::endLoad()
{
    if (m_LineCineLooper->isLooping())
    {
        m_LineCineLooper->stopLoop();
        processEvents(100);
    }
    if (!m_CurrentGlobalSonoParameters->isRealTime())
    {
        SonoParameters* toBeDelete = m_CurrentGlobalSonoParameters;
        if (m_IsLoad)
        {
            m_IsLoad = false;
            m_ChisonUltrasoundContext->onLoad(m_GlobalSonoParameters);
            m_ChisonUltrasoundContext->endOnLoad();
        }
        onCurrentSonoParametersChanging(m_GlobalSonoParameters);
        m_CurrentGlobalSonoParameters = m_GlobalSonoParameters;
        m_CurrentSonoParameters = m_ChisonUltrasoundContext->lineBuffer()->getSonoParametersByLayoutIndex(
            m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr));
        updateCustomGrayifNeeded();
        m_ColorMapContextManager.context(REALTIME_COLORMAP)->setSonoParameters(m_GlobalSonoParameters);
        if (toBeDelete != NULL)
        {
            delete toBeDelete;
        }
        m_ChisonUltrasoundContext->deleteLoadSonoparameters();
    }
    m_CurrentSonoParameters = m_ChisonUltrasoundContext->lineBuffer()->getSonoParametersByLayoutIndex(
        m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr));
    return UsOK;
}

int UsContextApi::getStartIndex() const
{
    return m_ChisonUltrasoundContext->lineBufferManager()->startIndex();
}

UsRetCode UsContextApi::setStartIndex(const int startIndex)
{
    if ((m_CurrentSonoParameters->pBV(BFPNames::FreezeStr)) && (startIndex >= 0) &&
        (startIndex < m_ChisonUltrasoundContext->lineBufferManager()->endIndex()))
    {
        m_ChisonUltrasoundContext->lineBufferManager()->setStartIndex(startIndex);
        return UsOK;
    }
    return UsFailed;
}

int UsContextApi::getEndIndex() const
{
    return m_ChisonUltrasoundContext->lineBufferManager()->endIndex();
}

UsRetCode UsContextApi::setEndIndex(const int endIndex)
{
    if ((m_CurrentSonoParameters->pBV(BFPNames::FreezeStr)) &&
        (endIndex >= m_ChisonUltrasoundContext->lineBufferManager()->startIndex()) &&
        (endIndex < m_ChisonUltrasoundContext->lineBufferManager()->frameCount()))
    {
        m_ChisonUltrasoundContext->lineBufferManager()->setEndIndex(endIndex);
        return UsOK;
    }
    return UsFailed;
}

int UsContextApi::getCurrentIndex() const
{
    int index = m_ChisonUltrasoundContext->lineBufferManager()->currentIndex();
    int frameCount = m_ChisonUltrasoundContext->lineBufferManager()->frameCount();
    if (frameCount > 0)
        index = index > frameCount - 1 ? frameCount - 1 : index;
    return index;
}

UsRetCode UsContextApi::setCurrentIndex(const int currentIndex)
{
    if ((m_CurrentSonoParameters->pBV(BFPNames::FreezeStr) && !m_USAPIFactoriesContext->externalIODeviceIsPaused()) &&
        (currentIndex >= m_ChisonUltrasoundContext->lineBufferManager()->startIndex()) &&
        (currentIndex <= m_ChisonUltrasoundContext->lineBufferManager()->endIndex()))
    {
        m_ChisonUltrasoundContext->lineBufferManager()->setCurrentIndex(currentIndex);
        return UsOK;
    }
    return UsFailed;
}

int UsContextApi::getFrameCount()
{
    return m_ChisonUltrasoundContext->lineBufferManager()->frameCount();
}

UsRetCode UsContextApi::startPlayLoop()
{
    if (m_CurrentSonoParameters->isRealTime() && !m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        return UsFailed;
    }
    if (!m_LineCineLooper->isLooping())
    {
        m_LineCineLooper->startLoop();
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::stopPlayLoop()
{
    if (!m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        return UsFailed;
    }
    if (m_LineCineLooper->isLooping())
    {
        m_Wit2LoopStop = true;
        m_LineCineLooper->delaystopLoop(
            !SystemScanModeClassifier::isLikeM((m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr))));
        m_Wit2LoopStop = false;
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::startPlayNext(int n)
{
    int setidx = (getCurrentIndex() + n) % getFrameCount();
    if (setidx < 0)
    {
        setidx = getFrameCount() + setidx;
    }
    return setCurrentIndex(setidx);
}

UsRetCode UsContextApi::setPlaySpeed(UsPlaySpeed speed)
{
    m_LineCineLooper->setFpsIndex(speed);
    return UsOK;
}

UsRetCode UsContextApi::setParameterIntAtPosition(const char* const name, int position, int value)
{
    if (name != NULL)
    {
        PositionParameterHandle handle = m_PositionHadnles.value(name, NULL);
        if (handle != NULL)
        {
            position = position * m_CurrentSonoParameters->pIV(BFPNames::PointNumPerLineStr) / 100;
            (this->*handle)(position, value);
            return UsOK;
        }
    }
    return UsFailed;
}

UsRetCode UsContextApi::setParameter(const char* const name, const QVariant& value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsFailed;
    }
    m_CurrentSonoParameters->setPV(name, value);
    return UsOK;
}

UsRetCode UsContextApi::getParameter(const char* const name, QVariant& value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsFailed;
    }
    value = m_CurrentSonoParameters->pV(name);
    return UsOK;
}

UsRetCode UsContextApi::getParameterIntMin(const char* const name, int* value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsInvalidArgs;
    }
    *value = m_CurrentSonoParameters->pMin(name);
    return UsOK;
}

UsRetCode UsContextApi::getParameterIntStep(const char* const name, int* value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsInvalidArgs;
    }
    *value = m_CurrentSonoParameters->pStep(name);
    return UsOK;
}

UsRetCode UsContextApi::getParameterIntMax(const char* const name, int* value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsInvalidArgs;
    }
    *value = m_CurrentSonoParameters->pMax(name);
    return UsOK;
}

UsRetCode UsContextApi::getParameterShowText(const char* const name, char* value, const int count)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsInvalidArgs;
    }
    Parameter* p = m_CurrentSonoParameters->parameter(name);
    if (p != NULL)
    {
        QString string = p->text();
        int retValue = qMin(string.length() + 1, count - 1);
        memcpy(value, string.toStdString().c_str(), retValue);
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::getParemeterShowValue(const char* const name, QVariant& value)
{
    if ((name == NULL) || m_CurrentSonoParameters->parameter(name)->isNull())
    {
        return UsInvalidArgs;
    }
    Parameter* p = m_CurrentSonoParameters->parameter(name);
    if (p != NULL)
    {
        value = p->showValue();
        return UsOK;
    }
    return UsInvalidArgs;
}

qreal UsContextApi::getRealPixelSizeMM()
{
    BFDepthParameters depthP(m_CurrentSonoParameters->pIV(BFPNames::ADFreqMHzStr),
                             m_CurrentSonoParameters->pV(BFPNames::ImageSizeStr).toSize().height(),
                             m_CurrentSonoParameters->pIV(BFPNames::CQYZStr), false);
    depthP.setPixelLen(BFADFreqParameter::pixelLenMM(m_CurrentSonoParameters->pIV(BFPNames::ADFreqMHzStr)) /
                       m_CurrentSonoParameters->pV(BFPNames::FixedSWImageZoomCofStr).toFloat());
    return depthP.pixelSizeMM();
}

UsRetCode UsContextApi::getAdditionalParamInt(const char* const name, int* value)
{
    if (name == NULL)
    {
        return UsFailed;
    }
    QMutexLocker locker(&m_AdditionalParamToolMutex);
    IAdditionalParamTool* tool = ToolsFactoryTemplate<IAdditionalParamTool>::instance()->tool(QString(name));
    if (tool != NULL)
    {
        return tool->execute(value);
    }
    return UsFailed;
}

UsRetCode UsContextApi::convertPhysicsToLogic(const double line, const double depth, UsLogicalPoint* logicalPoint)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    QPointF pt;
    convertPhysicsFToLogic(line, depth, pt);
    logicalPoint->UsLogicalPoint_INT.X = pt.toPoint().x();
    logicalPoint->UsLogicalPoint_INT.Y = pt.toPoint().y();

    return UsOK;
}

UsRetCode UsContextApi::convertPhysicsToLogicF(const double line, const double depth, UsLogicalPoint* logicalPoint)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    QPointF pt;
    convertPhysicsFToLogic(line, depth, pt);
    logicalPoint->UsLogicalPoint_DOUBLE.X = pt.x();
    logicalPoint->UsLogicalPoint_DOUBLE.Y = pt.y();

    return UsOK;
}

UsRetCode UsContextApi::convertLogicToPhysics(const UsLogicalPoint* logicalPoint, double* line, double* depth)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    convertLogicFToPhysics(QPointF(QPoint(logicalPoint->UsLogicalPoint_INT.X, logicalPoint->UsLogicalPoint_INT.Y)),
                           line, depth);
    return UsOK;
}

UsRetCode UsContextApi::convertLogicFToPhysics(const UsLogicalPoint* logicalPoint, double* line, double* depth)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    convertLogicFToPhysics(QPointF(logicalPoint->UsLogicalPoint_DOUBLE.X, logicalPoint->UsLogicalPoint_DOUBLE.Y), line,
                           depth);
    return UsOK;
}

UsRetCode UsContextApi::convertPhysicsToLogicInFullImage(const double line, const double depth,
                                                         UsLogicalPoint* logicalPoint)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    QPointF pt;
    convertPhysicsFToLogic(line, depth, pt);
    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    pt.setX(pt.x() + imageSize.width() / 2);
    logicalPoint->UsLogicalPoint_INT.X = pt.toPoint().x();
    logicalPoint->UsLogicalPoint_INT.Y = pt.toPoint().y();

    return UsOK;
}

UsRetCode UsContextApi::convertPhysicsToLogicFInFullImage(const double line, const double depth,
                                                          UsLogicalPoint* logicalPoint)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    QPointF pt;
    convertPhysicsFToLogic(line, depth, pt);
    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    pt.setX(pt.x() + imageSize.width() / 2);
    logicalPoint->UsLogicalPoint_DOUBLE.X = pt.x();
    logicalPoint->UsLogicalPoint_DOUBLE.Y = pt.y();
    return UsOK;
}

UsRetCode UsContextApi::convertLogicToPhysicsInFullImage(const UsLogicalPoint* logicalPoint, double* line,
                                                         double* depth)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }

    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    QPointF pf(QPoint(logicalPoint->UsLogicalPoint_INT.X - imageSize.width() / 2, logicalPoint->UsLogicalPoint_INT.Y));
    convertLogicFToPhysics(pf, line, depth);

    return UsOK;
}

UsRetCode UsContextApi::convertLogicFToPhysicsInFullImage(const UsLogicalPoint* logicalPoint, double* line,
                                                          double* depth)
{
    if (NULL == logicalPoint || NULL == m_CurrentSonoParameters)
    {
        return UsFailed;
    }
    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    QPointF pf(logicalPoint->UsLogicalPoint_DOUBLE.X - imageSize.width() / 2, logicalPoint->UsLogicalPoint_DOUBLE.Y);
    convertLogicFToPhysics(pf, line, depth);
    return UsOK;
}
UsScene UsContextApi::graphicsScene()
{
    return NULL;
}

UsOverlay UsContextApi::graphicsOverlay()
{
    return NULL;
}

UsGlyphsControl UsContextApi::glyphsControl(GlyphsControlType ctrType)
{
    return NULL;
}

UsRetCode UsContextApi::geometryMove(const int offsetX, const int offsetY)
{
    if (NULL == m_CurrentController || !m_CurrentController->isActive())
    {
        return UsFailed;
    }

    m_CurrentSonoParameters->setPV(BFPNames::ColorLineChangingStr, 1);
    m_CurrentController->move(QPoint(offsetX, offsetY));
    return UsOK;
}

UsRetCode UsContextApi::geometryChangeSize(const int sizeX, const int sizeY)
{
    if (NULL == m_CurrentController || !m_CurrentController->isActive())
    {
        return UsFailed;
    }

    m_CurrentSonoParameters->setPV(BFPNames::ColorLineChangingStr, 1);
    m_CurrentController->changeSize(QPoint(sizeX, sizeY));
    return UsOK;
}

UsRetCode UsContextApi::geometryScale(const int scaleX, const int scaleY)
{
    if (NULL == m_CurrentController || !m_CurrentController->isActive())
    {
        return UsFailed;
    }

    m_CurrentController->scale(QPoint(scaleX, scaleY));
    return UsOK;
}

UsRetCode UsContextApi::widgetUnitControlUpdate(const char* name, const int value)
{
    if (name == NULL)
    {
        return UsFailed;
    }
    QMutexLocker locker(&m_GlyphToolMutex);
    IGlyphTool* tool = ToolsFactoryTemplate<IGlyphTool>::instance()->tool(QString(name));
    if (tool != NULL)
    {
        return tool->execute(value);
    }
    return UsFailed;
}

UsRetCode UsContextApi::getCROIPoints(_UsLogicalPoint_INT* pointArray, const int count, int* validCount)
{
    return UsFailed;
}

UsRetCode UsContextApi::getBiopsyAngleList(int* angle, int* size)
{
    BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
    int ret = biopsyConfig.getBiopsyConfig(QString::number(-1), m_Currentbiopsy);
    if (!ret)
    {
        int i = 0;
        for (; i < m_Currentbiopsy.BiopsyAngleList.size(); i++)
        {
            angle[i] = m_Currentbiopsy.BiopsyAngleList[i].toUInt();
        }
        angle[i] = m_Currentbiopsy.LastGear;
        *size = m_Currentbiopsy.BiopsyAngleList.size() + 1;
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::getBiopsyAngle(int* posX, int* posY, int* angle)
{
    if (m_CurrentSonoParameters->pBV(BFPNames::IsBiopsyVisibleStr))
    {
        BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
        QPointF biopsyPos = biopsyConfig.getBiopsyPosition(getRealPixelSizeMM());
        *posX = biopsyPos.x();
        *posY = biopsyPos.y();
        int angleori = m_CurrentSonoParameters->pIV(BFPNames::BiopsyAngleStr);
        *angle = angleori + m_CurrentSonoParameters->pIV(BFPNames::BiopsyAngleOffsetStr);
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::selectBiopsyAngle(int angle, int* biopsyposX, int* biopsyposY, int* angleRange,
                                          int* offsetRangeX, int* offsetRangeY)
{
    BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
    int ret = biopsyConfig.getBiopsyConfig(QString::number(angle), m_Currentbiopsy);
    if (!ret)
    {
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyAngleStr, m_Currentbiopsy.BiopsyAngle);
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyAngleOffsetStr, m_Currentbiopsy.BiopsyAngleOffset);
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyXPosMMStr, m_Currentbiopsy.BiopsyXPosMM);
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyXPosMMOffsetStr, m_Currentbiopsy.BiopsyXPosMMOffset);
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyYPosMMStr, m_Currentbiopsy.BiopsyYPosMM);
        m_CurrentSonoParameters->setPV(BFPNames::BiopsyYPosMMOffsetStr, m_Currentbiopsy.BiopsyYPosMMOffset);

        QPointF biopsyPos = biopsyConfig.getBiopsyPosition(getRealPixelSizeMM());
        *biopsyposX = biopsyPos.x(); //放大变换后的位置
        *biopsyposY = biopsyPos.y();
        *angleRange = m_Currentbiopsy.BiopsyAngleOffsetRange;
#ifdef SYS_APPLE
        m_USAPIFactoriesContext->updateGraphicsItemColor((QGraphicsScene*)graphicsScene(), BIOPSY_TAG,
                                                         QColor(255, 255, 255));
#endif
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::saveCurrentBiopsyAngle()
{
    BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
    int probeID = m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr);
    if (m_Currentbiopsy.ProbeId == probeID && biopsyConfig.saveBiopsyConfig(m_Currentbiopsy))
    {
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::biopsyAngleMove(int angle, int biopsyposX, int biopsyposY)
{
    BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
    if (biopsyConfig.biopsyAngleMove(m_Currentbiopsy, getRealPixelSizeMM(), angle, biopsyposX, biopsyposY))
    {
        return UsOK;
    }
    return UsFailed;
}

void UsContextApi::biopsyAngleReset() //所有探头的配置
{
    BiopsyConfig biopsyConfig(m_CurrentSonoParameters, m_ProbeDataSet);
    biopsyConfig.biopsyAngleReset();
}

UsRetCode UsContextApi::getCurrentDscImageTransfromInformation(DscImageTransfromInformation* ImageDscInfo)
{
    if (ImageDscInfo == NULL)
    {
        return UsFailed;
    }
    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::RenderImageSizeStr).toSize();
    int probeID = m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->getProbe(probeID);
    ImageDscInfo->Dscwidth = imageSize.width();
    ImageDscInfo->Dscheight = imageSize.height();
    ImageDscInfo->StartLine = m_CurrentSonoParameters->pIV(BFPNames::StartLineStr);
    ImageDscInfo->StopLine = m_CurrentSonoParameters->pIV(BFPNames::StopLineStr);
    ImageDscInfo->PixelSizeMM = m_CurrentSonoParameters->pDV(BFPNames::PixelSizeMMStr);
    ImageDscInfo->LineSpacing = m_CurrentSonoParameters->pDV(BFPNames::LineSpacingMMStr) / 2;
    ImageDscInfo->WaferRadius = dataInfo.WaferRadius;
    ImageDscInfo->AngleSpacing = m_CurrentSonoParameters->pDV(BFPNames::AngleSpacingRadStr) / 2;
    ProbeParameters probeParameters(dataInfo);
    double depth = probeParameters.bottomDepthMM(m_CurrentSonoParameters->pDV(BFPNames::DepthMMStr));
    ImageDscInfo->Depthmm = depth;
    ImageDscInfo->Islinear = dataInfo.IsLinear;
    ImageDscInfo->IsPhased = dataInfo.IsPhasedArray;

    return UsOK;
}

UsRetCode UsContextApi::imageTransform(double scale, double translateX, double translateY)
{
    QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::RenderImageSizeStr).toSize();
    int originhalfheight = imageSize.height() / 2;
    m_CurrentSonoParameters->setPDV(BFPNames::ImageZoomCoefStr, scale * 100);
    m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateXStr, translateX);
    m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateYStr, translateY);
    m_CurrentSonoParameters->setPV(BFPNames::PanZoomOffsetDepthPixelStr,
                                   originhalfheight - originhalfheight * (1.0 / scale) +
                                       translateY * originhalfheight * (1 / scale));
    onImageTransformChanged();
    SystemScanMode scanMode = (SystemScanMode)m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr);
    if (scanMode == SystemScanModeB)
    {
        if (m_SetScaleTimer->isActive())
        {
            m_SetScaleTimer->stop();
        }
        m_SetScaleTimer->setInterval(200);
        m_SetScaleTimer->start();
    }
    return UsOK;
}

UsRetCode UsContextApi::imageTransformReset()
{
    m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateXStr, 0);
    m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateYStr, 0);
    m_CurrentSonoParameters->setPV(BFPNames::PanZoomOffsetDepthPixelStr, 0);
    int scale = m_CurrentSonoParameters->pIV(BFPNames::ImageZoomCoefStr);
    if (scale != 100)
    {
        m_CurrentSonoParameters->setPDV(BFPNames::ImageZoomCoefStr, 100);
        m_CurrentSonoParameters->parameter(BFPNames::ImageZoomCoefStr)->update();
    }
    onImageTransformChanged(false);
    return UsOK;
}

int UsContextApi::licenseRead(Plicense* pli, bool force_update)
{
#ifdef SYS_APPLE
    //每次app 启动 或者插上新的探头需要 同步下
    if (force_update)
    {
        license_palm::instance().setSonoParameters(m_GlobalSonoParameters);
        license_palm::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
        license_palm::instance().request_license();
    }
    FUNSTATUS fs;
    QString presetProbeName;
    int st = license_palm::instance().get_licencefunc_status(pli->sn, pli->probeModeName, presetProbeName,
                                                             pli->isanimal, pli->RentStatus, fs);
    memcpy(&pli->PW_ENABLE, &fs.PW_ENABLE,
           ((long)(&pli->CurvedToPhased_ENABLE) - (long)(&pli->PW_ENABLE) + sizeof(int)));
    m_GlobalSonoParameters->setPV(BFPNames::ProbeModeNameStr, pli->probeModeName);
    m_GlobalSonoParameters->setPV(BFPNames::PresetProbeNameStr, presetProbeName);
    return st;
#else
    return -1;
#endif
}

int UsContextApi::licenseWrite(char* key, char* sn, int keytype)
{
#ifdef SYS_APPLE
    QString keyqstr = QString(key).replace("-", "");
    QString snqstr = QString(sn);
    return license_palm::instance().set_licence_key(keyqstr, snqstr, keytype);
#else
    return -1;
#endif
}

void UsContextApi::version(char* dscVersion, char* ecVersion)
{
    QString dscV = GeneralInfo::instance().fpgaDSCVersion();
    QString escV = GeneralInfo::instance().fpgaECVersion();
    memcpy(dscVersion, dscV.toStdString().c_str(), dscV.length());
    memcpy(ecVersion, escV.toStdString().c_str(), escV.length());
}

UsRetCode UsContextApi::FinishUpgrade()
{
    return UsOK;
}

UsRetCode UsContextApi::StartUpgrade(char* fpga_binpath)
{
    return UsFailed;
}

UsRetCode UsContextApi::asyncStartUpgrade(char* fpga_binpath)
{
    return UsFailed;
}

UsRetCode UsContextApi::getHardwareVersion(QString& version)
{
    version = FPGAInfo::instance().isValidVersion() ? FPGAInfo::instance().fpgaVersion()
                                                    : GeneralInfo::instance().fpgaVersion();
    return UsOK;
}

// Debug 发货模式
UsRetCode UsContextApi::onDeliverMode()
{
#ifdef SYS_APPLE
    fpgaupgrademodel_palm::instance().setSonoParameters(m_GlobalSonoParameters);
    fpgaupgrademodel_palm::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
    fpgaupgrademodel_palm::instance().delivermode();

    return UsOK;
#endif
    return UsFailed;
}

UsRetCode UsContextApi::importControlTable(const char* filePath)
{
    if (m_USAPIFactoriesContext->beamFormer()->getControlTable()->importControlTable(QString(filePath)))
    {
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApi::importControlTable(const char* filePath, const int start, const int end)
{
    if ((start > end) || (start < 0))
    {
        return UsFailed;
    }
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly))
    {
        QByteArray all = file.readAll();
        file.close();
        if ((all.count() > end) &&
            (m_USAPIFactoriesContext->beamFormer()->getControlTable()->importControlTable(
                (const unsigned char*)all.data() + start, (unsigned int)(end - start + 1), (unsigned int)start)))
        {
            return UsOK;
        }
    }
    return UsFailed;
}

UsRetCode UsContextApi::setPenStyle(const int penStyle)
{
#ifdef SYS_APPLE
    if (ModelUiConfig::instance().value(ModelUiConfig::PenStyle).toInt() == penStyle)
        return UsOK;

    ModelUiConfig::instance().setValue(ModelUiConfig::PenStyle, penStyle);

    UsScanMode scanMode = getScanMode();

    if (scanMode == UsSystemScanModeBPW || scanMode == UsSystemScanModeColorPW ||
        m_CurrentSonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr)->boolValue())
    {
        m_CurrentSonoParameters->parameter(BFPNames::DScanLineStr)->update();
    }
    else if (scanMode == UsSystemScanModeUDBM)
    {
        m_CurrentSonoParameters->parameter(BFPNames::MScanLineStr)->update();
    }
#endif
    return UsOK;
}

UsRetCode UsContextApi::setROIPenStyle(bool isResizing)
{

#ifdef SYS_APPLE
    UsScanMode scanMode = getScanMode();

    if (scanMode != UsSystemScanModeColorDoppler && scanMode != UsSystemScanModeColorPW)
    {
        return UsOK;
    }

    ROIGlyphsControl* control =
        dynamic_cast<ROIGlyphsControl*>(GlyphsControlManager::instance().getGlyphsControl(GlyphsCtl::ROIGlyphsType));

    if (control != NULL)
    {
        control->setAtResizing(isResizing);
    }

    return UsOK;
#endif
    return UsOK;
}

void UsContextApi::setTgcShowTime(int time)
{
    Setting::instance().defaults().setTGCHideTime(time);
}

void UsContextApi::setBeforeZeusSaveFlag(bool beforeZeusSaveFlag)
{
    Setting::instance().defaults().setBeforeZeusSaveFlag(beforeZeusSaveFlag);
}

void UsContextApi::setAfterZeusSaveFlag(bool afterZeusSaveFlag)
{
    Setting::instance().defaults().setAfterZeusSaveFlag(afterZeusSaveFlag);
}

void UsContextApi::setBeforeIImageSaveFlag(bool beforeIImageSaveFlag)
{
    Setting::instance().defaults().setBeforeIImageSaveFlag(beforeIImageSaveFlag);
}

void UsContextApi::setAfterIImageSaveFlag(bool afterIImageSaveFlag)
{
    Setting::instance().defaults().setAfterIImageSaveFlag(afterIImageSaveFlag);
}

void UsContextApi::exportProperties()
{
    if (m_ChisonUltrasoundContext)
    {
        m_ChisonUltrasoundContext->exportProperties();
    }
}

UsRetCode UsContextApi::exportMedia(uint8_t* backgroundImage, int width, int height, int pastex, int pastey,
                                    int mediatype, int isdicom, UsConvertProcessCallback callback)
{
    return UsOK;
}

void UsContextApi::restoreAfterExportVideo(const char* const cinepath, int currentindex)
{
    //    if(cinepath ==  NULL)//没有加载图像 执行恢复冻结前的图像
    //    {
    //        SonoBuffers *s=  (SonoBuffers *)m_ChisonUltrasoundContext->lineBuffer();
    //        s->setRestoreFlag(true);
    //        endLoad();
    //        s->reStore();
    //        s->requestFlush();
    //        ProcessEvents(50);
    //        s->setRestoreFlag(false);
    //    }else
    //    {
    //        load(cinepath);
    //        setCurrentIndex(currentindex);
    //    }
}

void UsContextApi::processEvents(int time)
{
    QTime dieTime = QTime::currentTime().addMSecs(time);
    while (QTime::currentTime() < dieTime)
    {
        QCoreApplication::processEvents(QEventLoop::AllEvents);
    }
}

void UsContextApi::setBackGround(int state)
{
    UsScene usc = graphicsScene();
    QGraphicsScene* sc = reinterpret_cast<QGraphicsScene*>(usc);
    if (sc != NULL)
    {
        sc->blockSignals(state);
    }
    if (state)
    {
        m_AppStateChange = true;
    }
    else
    {
        m_AppStateChange = false;
    }
}

UsRetCode UsContextApi::AIO()
{
    AIOController::instance().setSonoParameters(m_CurrentSonoParameters);
    AIOController::instance().open();
    AIOController::instance().close();
    return UsOK;
}

UsMeasureContext UsContextApi::measureContext()
{
    return m_MeasureContext;
}

UsRetCode UsContextApi::updateMeasureContext(bool updateImage)
{
    return UsOK;
}

void UsContextApi::setImageColorFilter(float* filterArray)
{
    Q_UNUSED(filterArray)
}

UsRetCode UsContextApi::connect2ParamsAdjustMachine()
{
    return UsFailed;
}

SonoParameters* UsContextApi::currentSonoParameters() const
{
    return m_CurrentSonoParameters;
}

SonoParameters* UsContextApi::globalSonoParameters() const
{
    return m_GlobalSonoParameters;
}

bool UsContextApi::isLooping() const
{
    return m_LineCineLooper->isLooping();
}

bool UsContextApi::isLoad() const
{
    return m_IsLoad;
}

IUSAPIFactoriesContext* UsContextApi::usAPIFactoriesContext() const
{
    return m_USAPIFactoriesContext;
}

const QStringList UsContextApi::freqBShowText() const
{
    return m_FreqBShowText;
}

QHash<QString, QList<PresetAliasName>>& UsContextApi::presetAliasNames()
{
    return m_PresetAliasNames;
}

void UsContextApi::initializeBeamformer()
{
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(probeChanged(QVector<int>, QVector<bool>)), this,
            SLOT(onProbeChanged(QVector<int>, QVector<bool>)));
}

void UsContextApi::initializeProbePresetModel()
{
}

void UsContextApi::initializeChisonUltrasoundContext()
{
    m_ChisonUltrasoundContext = new ChisonUltrasoundContext(m_ImageSaveHelper);
    m_ChisonUltrasoundContext->setSonoParameters(m_GlobalSonoParameters);
    m_LineCineLooper = dynamic_cast<LineCineLooper*>(m_ChisonUltrasoundContext->cineLooper());

    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(imageStable()), m_ChisonUltrasoundContext,
            SLOT(onImageStable()));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(imageUnstable()), m_ChisonUltrasoundContext,
            SLOT(onImageUnstable()));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(imageShapeStable()), m_ChisonUltrasoundContext,
            SLOT(onImageShapeStable()));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(imageShapeUnstable()), m_ChisonUltrasoundContext,
            SLOT(onImageShapeUnstable()));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(updateImageData(ByteBuffer)), m_ChisonUltrasoundContext,
            SIGNAL(updateImageData(ByteBuffer)), Qt::DirectConnection);
    connect(m_ChisonUltrasoundContext, SIGNAL(newHWInfo(unsigned char*, int)), this,
            SLOT(onHWInfoUpdate(unsigned char*, int)), Qt::DirectConnection);
    connect(m_ChisonUltrasoundContext, SIGNAL(dopplerGateChanged()), m_USAPIFactoriesContext->beamFormer(),
            SLOT(onBCDGeometryChanged()), Qt::DirectConnection);
    connect(m_ChisonUltrasoundContext, SIGNAL(elementTesting(int)), this, SLOT(onElementTestingIndex(int)));
    connect(m_ChisonUltrasoundContext, SIGNAL(functionStateChanged(int, bool)), this,
            SLOT(onFunctionStateChanged(int, bool)));
}

void UsContextApi::initializeGraphicsContext()
{
}
#ifdef USE_VA
void UsContextApi::initializeVAContext()
{
    connect(m_ChisonUltrasoundContext, SIGNAL(vaResultUpdated(const QVariant&)), this,
            SLOT(onVAResultUpdated(const QVariant&)), Qt::DirectConnection);
}
#endif
void UsContextApi::initializeColorMap()
{
    m_ColorMapContextManager.setBFKitFactory(m_USAPIFactoriesContext->beamFormer());
    ColorMapContext* context = m_ColorMapContextManager.createContext();
    if (context != NULL)
    {
        context->setType(ColorMapContext::RealTime);
        context->setColorMapManager(m_ColorMapManager);
        context->setSonoParameters(m_CurrentSonoParameters);
        connect(context, SIGNAL(colorMapChanged()), this, SLOT(onColorMapChanged()));
    }
}

void UsContextApi::initializePositionParameterHandle()
{
}

void UsContextApi::initializePreset()
{
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    presetConfig->readPresetConfig();
    presetConfig->checkPresetStatusAndUpdate();
}

void UsContextApi::initializeSpecialParameters()
{
}

void UsContextApi::initializeGlyphTools()
{
    QMutexLocker locker(&m_GlyphToolMutex);
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(BFPNames::SteeringAngleStr,
                                                          new SteeringAngleGlyphTool(BFPNames::SteeringAngleStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(BFPNames::BaseLineStr,
                                                          new GlyphTool(BFPNames::BaseLineStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(BFPNames::DScanLineStr,
                                                          new DScanLineGlyphTool(BFPNames::DScanLineStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(BFPNames::MScanLineStr,
                                                          new GlyphTool(BFPNames::MScanLineStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(BFPNames::SampleVolumeMMStr,
                                                          new GlyphTool(BFPNames::SampleVolumeMMStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool(
        BFPNames::DopplerStartDepthMMStr, new DopplerStartDepthMMGlyphTool(BFPNames::DopplerStartDepthMMStr, this));
    ToolsFactoryTemplate<IGlyphTool>::instance()->addTool("SetBioPsyColor",
                                                          new SetBioPsyColorGlyphTool("SetBioPsyColor", this));
}

void UsContextApi::initializeAdditionalParamTools()
{
    QMutexLocker locker(&m_AdditionalParamToolMutex);
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::TGCPosyStr,
                                                                    new TGCPosyAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::FreqBStr,
                                                                    new FreqBAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::PlayingStr,
                                                                    new PlayingAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::IsLoadStr,
                                                                    new IsLoadAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::ColoRoiPosStr,
                                                                    new ColoRoiPosAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::DRoiPosStr,
                                                                    new DRoiPosAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::ProbeTypeStr,
                                                                    new ProbeTypeAdditionalParamTool(this));
    ToolsFactoryTemplate<IAdditionalParamTool>::instance()->addTool(AdditionalParamNames::USBUDPStr,
                                                                    new USBUDPAdditionalParamTool(this));
}

void UsContextApi::sonoParametersChanged(SonoParameters* parameters)
{
}

void UsContextApi::connectParametersChanged(SonoParameters* sonoParameters)
{
}

void UsContextApi::disConnectParametersChanged(SonoParameters* sonoParameters)
{
}

void UsContextApi::doingOnSettingFreeze()
{
}

int UsContextApi::updateBeamFormerFreqText(const QString& probeName, const QString& presetName)
{
    return -1;
}

void UsContextApi::onCurrentSonoParametersChanging(SonoParameters* sonoParameters)
{
}

void UsContextApi::updateCustomGrayifNeeded()
{
    m_USAPIFactoriesContext->updateCustomGrayifNeeded(m_CurrentSonoParameters);
}

void UsContextApi::onSaveCurrentPreset()
{
}

void UsContextApi::clearFrameDsiplay()
{
}

void UsContextApi::lazyFreeze()
{
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
}

void UsContextApi::startLogger()
{
    Logger4QT* logger = &Logger4QT::instance();
    if (AppLogger::fileLog())
    {
        if (!Setting::instance().defaults().logSavePathRevised())
        {
            modifyLoggerConfFile();
            Setting::instance().defaults().setLogSavePathRevised(true);
        }
#ifdef SYS_APPLE
        QString loggerFile = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/logger.conf";
        Log4Qt::PropertyConfigurator::configure(loggerFile);
#else
        QString logDir = "";
        {
            QSettings settings(Resource::loggerConf, QSettings::IniFormat);
            logDir = settings.value("LOGDIR").toString();
            QDir dir;
            if (!dir.exists(logDir))
            {
                dir.mkpath(logDir);
            }
        }
        Log4Qt::PropertyConfigurator::configure(Resource::loggerConf);
#endif
        Log4Qt::LogManager::setHandleQtMessages(true);
        Log4Qt::Logger* log = Log4Qt::Logger::rootLogger();
        log->info("                                                                               ");
        log->info("   @@@@@@@@@@        @@@@       @@@      @@@       @@@@@@@@     @@@      @@@   ");
        log->info("   @@@@@@@@@@@@     @@@@@@      @@@@@    @@@     @@@@@@@@@@@   @@@@      @@@   ");
        log->info("   @@@@     @@@    @@@ @@@      @@@@@@   @@@    @@@@           @@@@      @@@   ");
        log->info("   @@@@    @@@@    @@@  @@@     @@@ @@@  @@@   @@@@            @@@@      @@@   ");
        log->info("   @@@@@@@@@@@    @@@   @@@@    @@@  @@@ @@@   @@@@   @@@@@@   @@@@      @@@   ");
        log->info("   @@@@@@        @@@@@@@@@@@    @@@   @@@@@@   @@@@      @@@    @@@      @@@   ");
        log->info("   @@@@          @@@     @@@@   @@@    @@@@@    @@@@@    @@@    @@@@   @@@@@   ");
        log->info("   @@@@         @@@@      @@@@  @@@     @@@@      @@@@@@@@@@     @@@@@@@@@@    ");
        log->info("                                                                               ");
    }
}

void UsContextApi::modifyLoggerConfFile()
{
#ifdef SYS_APPLE
    QString dir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);

    QString data;
    QFile file(Resource::loggerConf);
    Q_ASSERT(file.exists());

    if (file.open(QIODevice::ReadOnly))
    {
        data = file.readAll();

        int start = data.indexOf('=', 0);
        int end = data.indexOf(QRegExp("\r\n|\r|\n"), 0);

        data.replace(start + 1, end - start, dir + "/\n");
    }
    file.close();

    QString loggerFile = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/logger.conf";
    QFile otherFile(loggerFile);
    if (otherFile.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        QTextStream stream(&otherFile);
        stream << data;
    }
    otherFile.close();
#endif
}

void UsContextApi::onHWInfoUpdate(unsigned char* probehwinfo, int len)
{
    m_USAPIFactoriesContext->beamFormer()->onUpdate((unsigned char*)probehwinfo, len);
}

void UsContextApi::onProbeChanged(QVector<int> probelist, QVector<bool> changes)
{
#define MAX_PROBE_NUMBER 16
    bool sendFlag = false;
    for (int i = 0; i < changes.count(); ++i)
    {
        if (changes[i])
        {
            sendFlag = true;
            break;
        }
    }
    if (sendFlag)
    {
        char* probeName[MAX_PROBE_NUMBER];
        char probeBameBuffer[MAX_PROBE_NUMBER * MAX_PROBE_NAME_SIZE];
        memset(probeBameBuffer, 0, MAX_PROBE_NUMBER * MAX_PROBE_NAME_SIZE);
        int probeNumber = qMin(changes.count(), MAX_PROBE_NUMBER);
        int probeCount = 0;
        for (int i = 0; i < probeNumber; ++i)
        {
            probeName[i] = probeBameBuffer + MAX_PROBE_NAME_SIZE * i;
            QString name = m_ProbeDataSet->probe(probelist[i]).Name.trimmed();
            if (!name.isEmpty())
            {
                memcpy(*(probeName + probeCount), name.toStdString().c_str(),
                       qMin(name.length(), MAX_PROBE_NAME_SIZE - 1));
                ++probeCount;
            }
        }
        if (probeCount > 0)
        {
            CallbackInfo ci = m_CallbackInfo[UsProbeInfo_CT];
            if (ci.first != NULL)
            {
                ((UsProbeInfoCallback)ci.first)(this, probeName, probeCount, ci.second);
            }
        }
    }
#undef MAX_PROBE_NUMBER
}

void UsContextApi::onParametersChanged()
{
    Parameter* parameter = dynamic_cast<Parameter*>(sender());
    CallbackInfo ci = m_CallbackInfo[UsPara_CT];
    if (ci.first != NULL && parameter != NULL)
    {
        ((UsParaChangedCallback)ci.first)(this, parameter->name().toStdString().c_str(),
                                          m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr), ci.second);
    }
}

void UsContextApi::onDoneSettingFreeze()
{
    m_USAPIFactoriesContext->setExternalIODevicePasuedStatus(false);
}

void UsContextApi::onScaleTimerOut()
{
    m_CurrentSonoParameters->parameter(BFPNames::ImageZoomCoefStr)->update();
}

void UsContextApi::fpgaUpgradeStatus(int ret, int process)
{
    CallbackInfo ci = m_CallbackInfo[UsFpgaUpgrade_CT];
    if (ci.first != NULL)
    {
        ((UsUpgradeProcessCallback)ci.first)(this, ret, process, ci.second);
    }
}

void UsContextApi::setColorVelocity()
{
    double maxVel = m_CurrentSonoParameters->pDV(BFPNames::CMaxVelCMSStr);
    int baseLine = m_CurrentSonoParameters->pIV(BFPNames::BaseLineColorStr);
    int maxBaseLine = m_CurrentSonoParameters->pMax(BFPNames::BaseLineColorStr);
    float fraction = (float)(maxBaseLine - baseLine) / maxBaseLine;
    float top = (1 - fraction) * maxVel;
    float bottom = -1 * fraction * maxVel;

    if (m_VelocityDown != nullptr)
    {
        m_VelocityDown->setPlainText(QString("%1").arg(bottom, 0, 'f', 1));
    }
    if (m_VelocityUp != nullptr)
    {
        m_VelocityUp->setPlainText(QString("%1").arg(top, 0, 'f', 1));
    }
}

void UsContextApi::onProbeConnectedChanged(const QVariant& value)
{
    Log4Qt::Logger::rootLogger()->info(QString("%1 value:%2").arg(PRETTY_FUNCTION).arg(value.toBool()));
    if (value.toBool() == false)
    {
        qDebug() << "Probe unconected............";
        unsigned char* noteinfo = new unsigned char[100];
        memset(noteinfo, 0, 100); //构造probeid==0的不存在探头，是下层可以知道探头断开了
        m_USAPIFactoriesContext->beamFormer()->onUpdate(noteinfo, 100);
        m_USAPIFactoriesContext->beamFormer()->onUpdate(noteinfo, 100);
        m_USAPIFactoriesContext->beamFormer()->onUpdate(noteinfo, 100); // probescaner 里面 M_SAMPLINGCOUNT 是3 所以三次
        delete[] noteinfo;
    }
}

void UsContextApi::onColorMapChanged()
{
    ColorMapContext* context = m_ColorMapContextManager.context(REALTIME_COLORMAP);
    setColorMap(context);
}

void UsContextApi::onNewImage(ImageEventArgs* imageEventArgs)
{
    CallbackInfo ci = m_CallbackInfo[UsData_CT];
    ((UsDataCallback)ci.first)(this, imageEventArgs->imageData(), imageEventArgs->width(), imageEventArgs->height(),
                               imageEventArgs->bitCount(), UsImageB, imageEventArgs->index(),
                               imageEventArgs->frameIndex(), ci.second);
}

void UsContextApi::onRawNewImage(void* data, int width, int height, int bitCount, int layoutIndex)
{
    CallbackInfo ci = m_CallbackInfo[UsRawData_CT];
    ((UsRawDataCallback)ci.first)(this, (unsigned char*)data, width, height, bitCount, UsImageB, layoutIndex,
                                  ci.second);
}
#ifdef USE_VA
void UsContextApi::onVAResultUpdated(const QVariant& list)
{
    QList<VADetectResult> info = list.value<QList<VADetectResult>>();
    int validCount = info.count();
    int convertCount = 0;
    if (validCount <= MaxVesselNum)
    {
        int halfWidth = (m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize().width() / 2);
        for (int i = 0; i < validCount; i++)
        {
            const VADetectResult& t = info[i];
            if (t.state == VesselState::Steady)
            {
                QPointF ptStart;
                convertPhysicsFToLogic(t.lineNoStart, t.depthStart, ptStart);
                m_vesselInfo[i].x = ptStart.x() + halfWidth;
                m_vesselInfo[i].y = ptStart.y();
                QPointF ptEnd;
                convertPhysicsFToLogic(t.lineNoEnd, t.depthEnd, ptEnd);
                m_vesselInfo[i].width = (ptEnd.x() - ptStart.x());
                m_vesselInfo[i].height = (ptEnd.y() - ptStart.y());
                m_vesselInfo[i].type = t.vessel;
                m_vesselInfo[i].state = UsVesselState::UsSteady;
                m_vesselInfo[i].typeProbability = t.typeProbability;

                //                QPointF pttopGate;
                //                convertPhysicsFToLogic(m_CurrentSonoParameters->pDV(BFPNames::DScanLineStr),
                //                                       m_CurrentSonoParameters->pDV(BFPNames::DopplerStartDepthMMStr),
                //                                       pttopGate);
                //                m_vesselInfo[i].topGateX = pttopGate.x() + halfWidth;
                //                m_vesselInfo[i].topGateY = pttopGate.y();

                //                QPointF ptbottomGate;
                //                convertPhysicsFToLogic(m_CurrentSonoParameters->pDV(BFPNames::DScanLineStr),
                //                                       m_CurrentSonoParameters->pDV(BFPNames::DopplerStartDepthMMStr)
                //                                       + m_CurrentSonoParameters->pDV(BFPNames::SampleVolumeMMStr),
                //                                       ptbottomGate);
                //                m_vesselInfo[i].bottomGateX = ptbottomGate.x() + halfWidth;
                //                m_vesselInfo[i].bottomGateY = ptbottomGate.y();

                convertCount++;
            }
            else if (t.state == VesselState::Moving)
            {
                m_vesselInfo[i].state = UsVesselState::UsMoving;
                m_vesselInfo[i].typeProbability = 0;
                convertCount++;
            }
        }
    }
    CallbackInfo ci = m_CallbackInfo[UsVAInfo_CT];
    if (ci.first != NULL)
    {
        ((UsVADetectInfoCallback)ci.first)(this, m_vesselInfo, convertCount, ci.second);
    }
}
#endif
void UsContextApi::setGeometrycontrol(const QVariant& v)
{
    Parameter* p = qobject_cast<Parameter*>(sender());
    if (p == NULL)
    {
        return;
    }
    if (p->name() == BFPNames::IsDopplerScanLineVisibleStr && v.toBool() &&
        getScanMode() == UsSystemScanModeColorDoppler)
    {
        if (m_CurrentController)
        {
            m_CurrentController->setActive(false);
        }
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->rOIAndDSamplingGateController();
        m_CurrentController->setActive(true);
    }
    else if (p->name() == BFPNames::IsMLineVisibleStr && v.toBool() && getScanMode() == UsSystemScanModeB)
    {
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->mLineController();
        m_CurrentController->setActive(true);
    }
    else if (p->name() == BFPNames::IsDopplerScanLineVisibleStr && v.toBool() && getScanMode() == UsSystemScanModeB)
    {
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->dSamplingGateController();
        m_CurrentController->setActive(true);
    }
}

void UsContextApi::onImageTransformChanged(bool preset)
{
    if (preset)
    {
        m_CurrentSonoParameters->setPV(BFPNames::PanZoomOffsetDepthPixelStr, 0);
        m_CurrentSonoParameters->setPV(BFPNames::ImageZoomCoefStr, 100);
        m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateXStr, false);
        m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateYStr, false);
    }
#if defined(SYS_APPLE) | defined(USE_GETREAMER)
    GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
    ctx->layoutTransformUpdate(m_CurrentSonoParameters->pDV(BFPNames::ImageZoomCoefStr) / 100,
                               m_CurrentSonoParameters->pDV(BFPNames::ImageTranslateXStr),
                               m_CurrentSonoParameters->pDV(BFPNames::ImageTranslateYStr), isFrozen());
#endif
}

void UsContextApi::sceneModified(const QList<QRectF>& qlistr)
{
    Q_UNUSED(qlistr)
}

void UsContextApi::onGraphicsChanged(uchar* graphicsData, int width, int height)
{
    ImageEventArgs args;
    args.setImageData(graphicsData);
    args.setWidth(width);
    args.setHeight(height);

    GraphicsLineImageArgs byteArgs;
    byteArgs.setImageType(GraphicsLineImageArgs::Type_Graphics);
    byteArgs.copyFrom(&args);
    emit graphicsChanged(&byteArgs);
}

void UsContextApi::onGraphicsChanged(int graphicsTextrue)
{
    ImageEventArgs args;
    args.setImageData((uchar*)&graphicsTextrue);

    GraphicsLineImageArgs grphicArgs;
    grphicArgs.copyFrom(&args);
    emit graphicsChanged(&grphicArgs);
}

void UsContextApi::onRecvParamsChgByImgAdjustMachine(QString& sonoparamstr, QVariant& sonoparamval)
{
    m_CurrentSonoParameters->setPV(sonoparamstr, sonoparamval);
}

#ifndef SYS_ANDROID
void UsContextApi::onElementTestingIndex(int elementIndex)
{
    CallbackInfo ci = m_CallbackInfo[UsProbeSelfTest_CT];
    if (ci.first != NULL)
    {
        ((UsProbeSelfTestCallback)ci.first)(this, elementIndex, ci.second);
    }
}

void UsContextApi::onElementTestProgressChanged(int progress, int elementNum)
{
    int ret = 1; // 进行中
    if (m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        ret = 2; //  失败
    }
    else if (progress == 100)
    {
        ret = 3; // 升级成功
    }
    CallbackInfo ci = m_CallbackInfo[UsProbeAssessmentProgress_CT];
    if (ci.first != NULL)
    {
        ((UsProbeAssessmentProcessCallback)ci.first)(this, ret, progress, ci.second);
    }
}

void UsContextApi::onElementTestResultUpdated(const QList<ElementDetectResult>& resultList)
{
    int isNotVaildCount = 0;
    QList<int> m_resultIds;
    for (int index = 0; index < resultList.count(); index++)
    {
        ElementDetectResult result = resultList[index];
        if (!result.isValid)
        {
            m_resultIds << result.elementID;
            isNotVaildCount++;
        }
    }

    CallbackInfo ci = m_CallbackInfo[UsProbeAssessmentResult_CT];
}
#endif

void UsContextApi::onFunctionStateChanged(int type, bool isOn)
{
    CallbackInfo ci = m_CallbackInfo[UsFunctionStateChanged_CT];
    if (ci.first != NULL)
    {
        ((UsFunctionStateChangedCallback)ci.first)(this, (UsFunctionType)type, isOn, ci.second);
    }
}

bool UsContextApi::checkSave(BufferStorer& bufferStorer, const char* const fileName, const int startIndex,
                             const int endIndex)
{
    ILineBufferManager* lineBufferManager = m_ChisonUltrasoundContext->lineBufferManager();
    if ((fileName == NULL) || (lineBufferManager->frameCount() <= 0) || (startIndex < 0) || (startIndex > endIndex) ||
        (endIndex > lineBufferManager->endIndex()))
    {
        return false;
    }
    StoredData storeData;
    storeData.setBufferManager(lineBufferManager);
    storeData.setSonoParameters(m_CurrentSonoParameters);
    bufferStorer.setStoredData(storeData);
    return true;
}

bool UsContextApi::load(const char* fileName)
{
    if ((fileName == NULL) || (!QFile::exists(fileName)))
    {
        return false;
    }
    Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 start... ").arg(PRETTY_FUNCTION).arg(fileName));
    StoredData storeData;
    storeData.setBufferManager(m_ChisonUltrasoundContext->lineBufferManager());
    storeData.setSonoParameters(m_CurrentSonoParameters);
    BufferStorer bufferStorer;
    bufferStorer.setStoredData(storeData);
    StoredData loadStoreData = bufferStorer.load(fileName);
    if (loadStoreData.isNull())
    {
        Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 failed ").arg(PRETTY_FUNCTION).arg(fileName));
        return false;
    }
    else
    {
        disConnectParametersChanged(m_CurrentSonoParameters);
        SonoParameters* toBeDelete = NULL;
        if (!m_CurrentGlobalSonoParameters->isRealTime())
        {
            toBeDelete = m_CurrentGlobalSonoParameters;
        }
        m_CurrentGlobalSonoParameters = loadStoreData.sonoParameters();
        m_ChisonUltrasoundContext->onLoad(loadStoreData.sonoParameters());

        m_CurrentSonoParameters = m_ChisonUltrasoundContext->lineBuffer()->getSonoParametersByLayoutIndex(
            m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr));
        // update custom gray map
        updateCustomGrayifNeeded();
        onImageTransformChanged();
        connectParametersChanged(m_CurrentSonoParameters);
        onCurrentSonoParametersChanging(m_CurrentSonoParameters);
        m_ColorMapContextManager.context(REALTIME_COLORMAP)->setSonoParameters(m_CurrentSonoParameters);
        m_ChisonUltrasoundContext->endOnLoad();
        m_CurrentSonoParameters->parameter(BFPNames::SystemScanModeStr)->update();
        m_CurrentSonoParameters->parameter(BFPNames::IsMLineVisibleStr)->update();

#ifdef USE_VA
        QString lineDataDirPath = fileName;
        lineDataDirPath.remove(lineDataDirPath.length() - 4, 4);
        m_ChisonUltrasoundContext->loadVaResult(lineDataDirPath);
#endif
        m_IsLoad = true;

        if (toBeDelete != NULL)
        {
            delete toBeDelete;
        }
        setColorVelocity();

        Log4Qt::Logger::rootLogger()->info(QString("%1 filename:%2 ok ").arg(PRETTY_FUNCTION).arg(fileName));
        return true;
    }
}

void UsContextApi::convertPhysicsFToLogic(const double line, const double depth, QPointF& pt)
{
    BFCoordTransform bfCoordTransform(m_CurrentSonoParameters);
    bfCoordTransform.convertPhysicsToLogic(line, depth, pt);
}

void UsContextApi::convertLogicFToPhysics(const QPointF& pt, double* line, double* depth)
{
    BFCoordTransform bfCoordTransform(m_CurrentSonoParameters);
    bfCoordTransform.convertPtToPhysics(pt, *line, *depth);
}

void UsContextApi::changeGeometryController(SystemScanMode mode)
{
    if (m_CurrentController != NULL)
    {
        m_CurrentController->setActive(false);
        m_CurrentController = NULL;
    }

    switch (mode)
    {
    case SystemScanModeM:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->mLineController();
        break;
    case SystemScanModeFourDPre:
    case SystemScanModeFreeHand3D:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->fourdROIController();
        break;
    case SystemScanMode2B:
    case SystemScanMode4B:
    case SystemScanModeE:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->rOIController();
        break;
    case SystemScanModeLRBM:
    case SystemScanModeColorLRBM:
    case SystemScanModePDLRBM:
    case SystemScanModeDPDLRBM:
    case SystemScanModeTDILRBM:
    case SystemScanModeUDBM:
    case SystemScanModeColorUDBM:
    case SystemScanModePDUDBM:
    case SystemScanModeDPDUDBM:
    case SystemScanModeTDIUDBM:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->mLineController();
        break;
    case SystemScanModeLRFreeM:
    case SystemScanModeUDFreeM:
        m_CurrentController =
            static_cast<PhysicalGeometryController*>(m_USAPIFactoriesContext->beamFormer()->freeMLineController());
        break;
    case SystemScanModeColorDoppler:
    case SystemScanModePowerDoppler:
    case SystemScanModeDPowerDoppler:
    case SystemScanModeTissueDoppler:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->rOIController();
        break;

    case SystemScanModeColorPW:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->rOIAndDSamplingGateController();
        break;
    case SystemScanModeBPW:
    case SystemScanModePowerPW:
    case SystemScanModeDPowerPW:
    case SystemScanModeTissuePW:
    case SystemScanModeCWD:
    case SystemScanModeCWDColorDoppler:
    case SystemScanModeCWDDirectionalPowerDoppler:
    case SystemScanModeCWDPowerDoppler:
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->dSamplingGateController();
        break;
    }

    if (m_CurrentController != NULL)
    {
        m_CurrentController->setActive(true);
    }
}

inline void UsContextApi::pwDelayFreqSpectrumSetting(int dms)
{
    Q_UNUSED(dms)
    m_USAPIFactoriesContext->beamFormer()->setPV(BFPNames::FreqSpectrumStr, true);
}

ImageEventArgs UsContextApi::getCurrentFrame()
{
    //    GLProcessContext *ctx = const_cast<GLProcessContext *>(m_GLProcessCreator.context(0));
    //    return ctx->requestFrame();
    return ImageEventArgs();
}

void UsContextApi::setColorMap(ColorMapContext* context, int displayIndex)
{
    if (NULL == context)
    {
        return;
    }
    uchar* map1 = NULL;
    uchar* map2 = NULL;
    context->getColorMaps((void**)&map1, (void**)&map2);
    m_ChisonUltrasoundContext->setColorMap(map1, map2);
}

void UsContextApi::tryUnfreeze()
{
    if (ModelConfig::instance().value(ModelConfig::CanFreezeSend, true).toBool() &&
        m_USAPIFactoriesContext->beamFormer()->isFrozen())
    {
        m_USAPIFactoriesContext->beamFormer()->freeze(false);
    }
}
bool UsContextApi::isSupportFunction(UsFunctionType funcType)
{
    IFunctionHandler* handler =
        FunctionHandlerFactory::instance().functionHandler((FunctionTypeDef::FunctionType)funcType);
    if (handler != NULL)
    {
        return handler->isSupportFunction();
    }
    return false;
}

bool UsContextApi::functionOpened(UsFunctionType funcType)
{
    IFunctionHandler* handler =
        FunctionHandlerFactory::instance().functionHandler((FunctionTypeDef::FunctionType)funcType);
    if (handler != NULL)
    {
        return handler->functionOpened();
    }
    return false;
}

UsRetCode UsContextApi::openFunction(UsFunctionType funcType)
{
    IFunctionHandler* functionhandler =
        FunctionHandlerFactory::instance().functionHandler((FunctionTypeDef::FunctionType)funcType);
    if (functionhandler != NULL)
    {
        tryUnfreeze();
        if (functionhandler->openFunction())
        {
            setColorVelocity();
            m_IsPaused = false;
            return UsRetCode::UsOK;
        }
        else
        {
            closeFunction(funcType);
        }
    }
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::closeFunction(UsFunctionType funcType)
{
    IFunctionHandler* functionhandler =
        FunctionHandlerFactory::instance().functionHandler((FunctionTypeDef::FunctionType)funcType);
    if (functionhandler != NULL)
    {
        tryUnfreeze();
        functionhandler->closeFunction();
        setColorVelocity();
        m_IsPaused = false;
        return UsRetCode::UsOK;
    }
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::setImageCheckTime(int timeSec)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getImageCheckTime(int* timeSec)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::setImageCheckState(bool open)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getImageCheckState(bool* open)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::setImageCheckSensitiveThreshold(float threshold)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getImageCheckSensitiveThreshold(float* threshold)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::autoProbeSelfTest(int type, int second)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::singleProbeSelfTest(int type, int num)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::stopProbeSelfTest()
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getProbeValidElementCounts(int* counts)
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::openProbeSelfTest()
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::closeProbeSelfTest()
{
    return UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getDepthMMByCQYZLevel(const QString& probename, int level, double* depth)
{
    return m_USAPIFactoriesContext->beamFormer()->getDepthMMByCQYZLevel(probename, level, depth) ? UsRetCode::UsOK
                                                                                                 : UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getDepthMMByCQYZ(const QString& probename, int level, double* depth)
{
    return m_USAPIFactoriesContext->beamFormer()->getDepthMMByCQYZ(probename, level, depth) ? UsRetCode::UsOK
                                                                                            : UsRetCode::UsFailed;
}

UsRetCode UsContextApi::getFocusPosDepthMM(int focusNum, int focusPos, float* depth)
{
    return m_USAPIFactoriesContext->beamFormer()->getFocusPosDepthMM(focusNum, focusPos, depth) ? UsRetCode::UsOK
                                                                                                : UsRetCode::UsFailed;
}

UsRetCode UsContextApi::setIImageType(int valueType)
{
    if (m_CurrentSonoParameters->parameter(BFPNames::IImageTypeStr)->isNull() || valueType < UsIImageType::ByPass ||
        valueType > UsIImageType::External)
    {
        return UsInvalidArgs;
    }
    Log4Qt::Logger::rootLogger()->info("%1 name: IImageType value: %2", PRETTY_FUNCTION, valueType);
    m_CurrentSonoParameters->setPV(BFPNames::IImageTypeStr, valueType);
    return UsOK;
}

UsRetCode UsContextApi::getIImageType(int* valueType)
{
    *valueType = (int)(UsIImageType::Internal);
    if (!m_CurrentSonoParameters->parameter(BFPNames::IImageTypeStr)->isNull())
    {
        *valueType = m_CurrentSonoParameters->pIV(BFPNames::IImageTypeStr);
    }
    return UsOK;
}

int UsContextApi::getAllParamterNames(char** paraNames, int arraySize, int itemSize)
{
    if (m_GlobalSonoParameters)
    {
        if (paraNames == NULL || arraySize == 0 || itemSize == 0)
        {
            return m_GlobalSonoParameters->parameters().count();
        }
        else
        {
            int count = qMin(m_GlobalSonoParameters->parameters().count(), arraySize);
            for (int index = 0; index < count; ++index)
            {
                QString name = m_GlobalSonoParameters->parameters()[index]->name();
                int len = (int)strlen(name.toStdString().c_str());
                int nameLen = itemSize > len ? len : itemSize - 1;
                strncpy(paraNames[index], name.toStdString().c_str(), nameLen);
            }
            return count;
        }
    }
    else
    {
        return UsFailed;
    }
}

IProbeDataSet* UsContextApi::probeDataSet() const
{
    return m_ProbeDataSet;
}

void UsContextApi::initErrorCode()
{
    UsContextApi::ErrorCodeHash.insert(UltrasoundDevice::EC_NO_DEVICE, UsUltrasoundErrorCode::UsLink_NO_Device);
    UsContextApi::ErrorCodeHash.insert(UltrasoundDevice::EC_NOT_OPEN, UsUltrasoundErrorCode::UsLink_NOT_Open);
    UsContextApi::ErrorCodeHash.insert(UltrasoundDevice::EC_NO_RESOURCE, UsUltrasoundErrorCode::UsLink_NO_Resource);
    UsContextApi::ErrorCodeHash.insert(UltrasoundDevice::EC_DEVICE_NO_DRIVER, UsUltrasoundErrorCode::UsLink_NO_Driver);
}

void UsContextApi::onFreezeChangedInProbeAssessment(const QVariant& value)
{
}
