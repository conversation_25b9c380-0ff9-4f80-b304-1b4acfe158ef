#include <QObject>
#include "modelconfig.h"
#include "colorbarmodel.h"
#include <QGraphicsRectItem> 
#include <QDebug>
#include <QPainter>
#include <functional>
#include "usapidef.h"
#include "colormaptypedef.h"
#define COLORBARPOSX 60
#define COLORBARPOSY 90
#define COLORBARWIDTH 30
#define COLORBARHEIGHT 256
#define COLORCAPTIONPOSX 47
#define COLORCAPTIONPOSY 20
class UsColorbar : public QObject,public QGraphicsRectItem
{
    Q_OBJECT
public:
    explicit UsColorbar( QList<ColorMapTypeDef::ColorMapType> typelist,QGraphicsItem *parent = 0);
    uint8_t *m_ColorMap;
    uint8_t *m_ColorMap2;
    std::function<UsScanMode()> getscanmodefunc;
public slots:
    void colormapChanged(ColorBarModel* cmp);
    void colormapgroupChanged(QList<ColorBarModel*> cmp);
private:
    bool ifcompoundbar();
    bool ifonlyonebar();
    QList<ColorMapTypeDef::ColorMapType> __typelist;
    void paint(<PERSON><PERSON><PERSON><PERSON> *painter, const QStyleOptionGraphicsItem *option, QWidget *widget);
};
