#import <Foundation/Foundation.h>
#import <AudioToolbox/AudioToolbox.h>
@interface PCMDataPlayer : NSObject

+ (instancetype)sharePlayer;
- (void)resetPlay;
- (void)closePlay;
@end

#define MIN_SIZE_PER_FRAME 1024   
#define QUEUE_BUFFER_SIZE  3      
#define SAMPLE_RATE        16000 

@interface PCMDataPlayer() {
    AudioQueueRef audioQueue;                                 
    AudioStreamBasicDescription _audioDescription;
    AudioQueueBufferRef audioQueueBuffers[QUEUE_BUFFER_SIZE]; 
    BOOL audioQueueBufferUsed[QUEUE_BUFFER_SIZE];            
    NSLock *sysnLock;
    NSMutableData *tempData;
    OSStatus osState;
    int dataNotReady;
    int playNotFinish;
    int running;
    long long  playindex;
}
@end

@implementation PCMDataPlayer
+ (instancetype)sharePlayer{
    static dispatch_once_t onceToken;
    static id shareInstance;
    dispatch_once(&onceToken, ^{
        shareInstance = [[self alloc] init];
    });
    return shareInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        sysnLock = [[NSLock alloc]init];
        
        _audioDescription.mSampleRate = SAMPLE_RATE;
        _audioDescription.mFormatID = kAudioFormatLinearPCM;
        
        _audioDescription.mFormatFlags = 0;
       
        _audioDescription.mChannelsPerFrame = 2;
       
        _audioDescription.mFramesPerPacket = 1;
       
        _audioDescription.mBitsPerChannel = 8;
        _audioDescription.mBytesPerFrame = (_audioDescription.mBitsPerChannel / 8) * _audioDescription.mChannelsPerFrame;
       
        _audioDescription.mBytesPerPacket = _audioDescription.mBytesPerFrame * _audioDescription.mFramesPerPacket;
       
        AudioQueueNewOutput(&_audioDescription, AudioPlayerAQInputCallback, (__bridge void * _Nullable)(self), nil, 0, 0, &audioQueue);
       
        AudioQueueSetParameter(audioQueue, kAudioQueueParam_Volume, 1.0);
       
        for (int i = 0; i < QUEUE_BUFFER_SIZE; i++) {
            audioQueueBufferUsed[i] = false;
            osState = AudioQueueAllocateBuffer(audioQueue, MIN_SIZE_PER_FRAME, &audioQueueBuffers[i]);
        }
        osState = AudioQueueStart(audioQueue, NULL);
        if (osState != noErr) {
            NSLog(@"AudioQueueStart Error");
        }
    }
    return self;
}

- (void)resetPlay {
    if (audioQueue != nil) {
        dataNotReady = 0;
        playNotFinish = 0;
        AudioQueueReset(audioQueue);
        for (int i = 0; i < QUEUE_BUFFER_SIZE; i++) {
            audioQueueBufferUsed[i] = false;
            memset(audioQueueBuffers[i] -> mAudioData, 128, MIN_SIZE_PER_FRAME);
        }
        AudioQueueStart(audioQueue, NULL);
    }
}
- (void)closePlay{
    running = 0;
    playindex = 0;
}
-(void)playWithDataptr:(const uint8_t *)data  size:(int)len{
    running = 1;
    [sysnLock lock];
    int i = 0;
    while (true) {
        if (!audioQueueBufferUsed[i]) {
            audioQueueBufferUsed[i] = true;
            break;
        }else {
            i++;
            if (i >= QUEUE_BUFFER_SIZE) {
                //printf("%s  play not ready!!\n",__func__,);
                playNotFinish ++;
                [sysnLock unlock];
                if(playNotFinish > 10){
                     [[PCMDataPlayer sharePlayer] resetPlay];
                }
                return;
            }
        }
    }
    playNotFinish = 0 ;
    //printf("%s current buf index  %d   empty count:%d\n",__func__, i, dataNotReady);
    audioQueueBuffers[i] -> mAudioDataByteSize =  (unsigned int)len;
    if(playindex++ > 5)
        memcpy(audioQueueBuffers[i] -> mAudioData, data, len);
    else
        memset(audioQueueBuffers[i] -> mAudioData, 128, MIN_SIZE_PER_FRAME);
    AudioQueueEnqueueBuffer(audioQueue, audioQueueBuffers[i], 0, NULL);
    [sysnLock unlock];
}

static void AudioPlayerAQInputCallback(void* inUserData,AudioQueueRef audioQueueRef, AudioQueueBufferRef audioQueueBufferRef) {
    
    PCMDataPlayer* player = (__bridge PCMDataPlayer*)inUserData;
    [player->sysnLock lock];
    [player resetBufferState:audioQueueRef and:audioQueueBufferRef];
    [player->sysnLock unlock];
}

- (void)resetBufferState:(AudioQueueRef)audioQueueRef and:(AudioQueueBufferRef)audioQueueBufferRef {
    int t = -1;
    for (int i = 0; i < QUEUE_BUFFER_SIZE; i++) {
        if (audioQueueBufferRef == audioQueueBuffers[i]) {
            audioQueueBufferUsed[i] = false;
            //printf("fish %d\n",i);
        }
        if(audioQueueBufferUsed[i] == true)
            t = i;
    }
    if(t == -1)
        dataNotReady++;
    if(running == 1 && dataNotReady > 10){
        AudioQueueReset(audioQueue);
        dataNotReady = 0;
    }
}


- (void)dealloc {
    
    if (audioQueue != nil) {
        AudioQueueStop(audioQueue,true);
    }
    audioQueue = nil;
    sysnLock = nil;
}

@end

namespace apple_ios_audio
{
    void  resetaudio()
    {
        [[PCMDataPlayer sharePlayer] resetPlay];
    }
    void closeaudio()
    {
        [[PCMDataPlayer sharePlayer] closePlay];
    }
    void  playWithData(const uint8_t *data, int audio_len)
    {
        [[PCMDataPlayer sharePlayer] playWithDataptr:data size:audio_len];
    }

}

