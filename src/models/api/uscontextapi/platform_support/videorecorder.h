#ifndef VIDEORECORDER_H
#define VIDEORECORDER_H
typedef unsigned char uint8_t;
namespace platform_videorecord {
enum SAVETYPE
{
    SAVEMP4 =0,
    SAVEIMG,
    SAVEVIDEODICOM,
    SAVEIMGDICOM,
    SAVETYPECOUNT
};
//基类负责导出图像
class VideoRecorder
{

public:
    enum RSTATE{
        STOP,
        RUN,
    };
    typedef struct _VRSTATE{
        RSTATE state;
        bool waitawhile;
        int framecount;
        double frametime;
        int currentidx;
    }VRSTATE;
    static constexpr double MAXRECORDFPS = 25.0;
    VideoRecorder(int sizew, int sizeh,int audiosize);
    VideoRecorder(int sizew, int sizeh);
    static void bgra2rgb(uint8_t * dest,  uint8_t * src, int w, int h);
    static void rgba2rgb(uint8_t * dest,  uint8_t * src, int w, int h);
    static void bgra2rgba(uint8_t * dest, int w, int h);
    int fileSize(const char * filePath);
    virtual void setuserdata(void * otherPtr = nullptr);
    virtual void start();
    virtual int finish(char * exportfile);
    virtual void stop(bool save2Local= true);
    virtual void add_img_frame(uint8_t * data, int size);
    virtual void add_video_frame(uint8_t * data, int size);
    virtual void add_video_frame(uint8_t * data, int size, double timestamp);
    virtual void add_audio_frame(uint8_t * data, int size);
    VRSTATE m_state;
protected:
    int video_width;
    int video_height;
    int audio_len;
    uint8_t * m_data;
};
VideoRecorder* videocontrolptr(int video_width, int video_height, SAVETYPE type);
#endif // VIDEORECORDER_H
}

