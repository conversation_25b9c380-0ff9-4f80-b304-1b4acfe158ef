#include "threaddummyiodevice.h"
#include "generalinfo.h"
#include <iostream>

IODeviceThread::IODeviceThread(ThreadDummyIODevice *device, QObject *parent)
    : QThread(parent)
    , m_Device(device)
{

}

IODeviceThread::~IODeviceThread()
{

}

void IODeviceThread::run()
{
    while(true)
    {
        m_Device->m_QueueMutex.lock();
        while(m_Device->m_QueueData.size() == 0)
        {
            m_Device->m_WaitSuspendCondt.wait(&(m_Device->m_QueueMutex));
        }
        if(m_Device->m_QueueData.size()>0)
        {
            QByteArray data;
            data = m_Device->m_QueueData.dequeue();
            m_Device->m_UsWifiWriteCallback(m_Device->m_UsContext,
                                            (unsigned char *)data.constData(),
                                            data.size(),
                                            m_Device->m_DataWriteCallbackUserData);
        }
        m_Device->m_QueueMutex.unlock();
        if(GeneralInfo::instance().m_probeTransmitType == 0) //UDP 需要控制发送间隔 太快容易接收不到
        {
           std::this_thread::sleep_for(std::chrono::microseconds(200));//udp 发送间隔控制下 不能太短
        }
    }
}


ThreadDummyIODevice::ThreadDummyIODevice(QObject *parent)
    : BaseDummyIODevice(parent)
    , m_Thread(this, parent)
{

}

ThreadDummyIODevice::~ThreadDummyIODevice()
{

}

qint32 ThreadDummyIODevice::asyncWrite(unsigned char *buf, int len)
{
    if (m_UsWifiWriteCallback != NULL)
    {
        QByteArray array;
        array.setRawData((const char*)&buf[0], len);
        array.insert(0, 0x01);
        m_QueueMutex.lock();
        m_QueueData.enqueue(array);
        m_WaitSuspendCondt.wakeAll();
        m_QueueMutex.unlock();
    }
    return EC_SUCCESS;
}

void ThreadDummyIODevice::start()
{
    m_Thread.start();
}

