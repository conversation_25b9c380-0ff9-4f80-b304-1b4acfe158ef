#ifndef THREADDUMMYIODEVICE_H
#define THREADDUMMYIODEVICE_H

#include "basedummyiodevice.h"
#include <QThread>
#ifdef SYS_ANDROID
#include <thread>
#endif
class ThreadDummyIODevice;

class IODeviceThread : public QThread
{
    Q_OBJECT
public:
    explicit IODeviceThread(ThreadDummyIODevice *device, QObject *parent = NULL);
    virtual ~IODeviceThread();

protected:
    void run() override;

private:
    ThreadDummyIODevice *m_Device;
};

class ThreadDummyIODevice : public BaseDummyIODevice
{
    Q_OBJECT
public:
    friend class IODeviceThread;

    explicit ThreadDummyIODevice(QObject *parent = NULL);
    virtual ~ThreadDummyIODevice();

    virtual qint32 asyncWrite(unsigned char *buf, int len);

    virtual void start();

private:
    QQueue<QByteArray>  m_QueueData;
    QWaitCondition m_WaitSuspendCondt;
    QMutex              m_QueueMutex;
    IODeviceThread      m_Thread;
};

#endif // THREADDUMMYIODEVICE_H
