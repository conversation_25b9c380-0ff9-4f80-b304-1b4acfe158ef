#include "glprocesscontext.h"
#include "bytelineimageargs.h"
#include "imageblendglbridge.h"
#include "imagecomposeglbridge.h"
#include "imageeventargs.h"
#include "util.h"
#include "videoframecomposeglbridge.h"
#include <QGLContext>
#include <QGLWidget>
#include <QThread>
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
#include <QOffscreenSurface>
#include <QOpenGLContext>
#endif

GLProcessContext::GLProcessContext(QGLWidget *glWidget, QSize size, QObject *parent, QThread *thread)
    : QObject(parent)
    , m_ComposeBridge(NULL)
    , m_BlendBridge(NULL)
    , m_VideoFrameBridge(NULL)
    , m_size(size)
    , m_CurrentGLContext(NULL)
    , m_GlWidget(glWidget)
    , m_IsShareContext(false)
    , m_GlWidget(nullptr)
    , _ctx(nullptr)
    , _thd(nullptr)
{
    if (thread != NULL)
    {
        moveToThread(thread);
        thread->start();
    }

    staticMetaObject.invokeMethod(this, "initGLContext", Qt::BlockingQueuedConnection);
}

GLProcessContext::GLProcessContext(QGLContext *sharedContext, QObject *parent, QThread *thread)
    : QObject(parent)
    , m_ComposeBridge(NULL)
    , m_BlendBridge(NULL)
    , m_VideoFrameBridge(NULL)
    , m_CurrentGLContext(sharedContext)
    , m_IsShareContext(true)
    , m_GlWidget(NULL)
    , _ctx(NULL)
    , _thd(NULL)
{
    if (thread != NULL)
    {
        moveToThread(thread);
        thread->start();
    }
}

GLProcessContext::GLProcessContext(QSize size, void *sharectx, QObject *parent, QThread *thread)
    : QObject(parent)
    , m_ComposeBridge(NULL)
    , m_BlendBridge(NULL)
    , m_VideoFrameBridge(NULL)
    , m_size(size)
    , m_CurrentGLContext(NULL)
    , m_IsShareContext(true)
{
    _thd = thread;
    _ctx = sharectx;
    if (thread != NULL)
    {
        moveToThread(thread);
        thread->start();
    }
    if (_ctx != nullptr)
    {
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
        QOpenGLContext *ctx = reinterpret_cast<QOpenGLContext *>(_ctx);
        auto mainSurface = ctx->surface();
        ctx->doneCurrent();
#endif
        staticMetaObject.invokeMethod(this, "initGLContext", Qt::BlockingQueuedConnection);
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
        ctx->makeCurrent(mainSurface);
#endif
    }
}

GLProcessContext::~GLProcessContext()
{
    Util::SafeDeletePtr(m_ComposeBridge);
    Util::SafeDeletePtr(m_BlendBridge);
    Util::SafeDeletePtr(m_VideoFrameBridge);
    if (!m_IsShareContext)
    {
        Util::SafeDeletePtr(m_CurrentGLContext);
    }
}

void GLProcessContext::setSonoParameters(SonoParameters *sonoParameters)
{
    if (NULL == m_ComposeBridge)
    {
        return;
    }

    staticMetaObject.invokeMethod(this, "onSetSonoparameters", Qt::BlockingQueuedConnection,
                                  Q_ARG(SonoParameters *, sonoParameters));
}

void GLProcessContext::setRenderLayoutNum(int layoutNum)
{
    if (NULL == m_ComposeBridge)
    {
        return;
    }

    m_ComposeBridge->setRenderLayoutNum(layoutNum);
}

void GLProcessContext::setSystemScanMode(int systemScanMode)
{
    if (NULL == m_ComposeBridge)
    {
        return;
    }

    m_ComposeBridge->setSystemScanMode(systemScanMode);
}

void GLProcessContext::setActiveIndex(int index)
{
    if (NULL == m_ComposeBridge)
    {
        return;
    }

    m_ComposeBridge->setActiveBIndex(index);
}

void GLProcessContext::setGeometry(const QRect &geometry)
{
    if (NULL == m_ComposeBridge || NULL == m_BlendBridge || m_VideoFrameBridge == NULL)
    {
        return;
    }
    m_ComposeBridge->setGeometry(geometry);
    m_ComposeBridge->reset(false);
    m_BlendBridge->setImageSize(geometry.width(), geometry.height());
    m_BlendBridge->reset(false);
}

const QGLContext *GLProcessContext::sharedContext() const
{
    return m_CurrentGLContext;
}

void GLProcessContext::layoutTransformUpdate(double scale, double translatex, double translatey, bool isfreeze)
{
    // QTransform transform2;
    // transform2.scale(scale, scale);
    // transform2.translate(translatex, translatey);
    // QTransform transform= QTransform
    //         (scale,                              0,                                     0,
    //          0,                                  scale,                                 0,
    //          scale*translatex + x*(1-scale),     scale*translatey + y*(1-scale),        1);
    QTransform transform = QTransform(scale, 0, 0, 0, scale, 0, translatex, translatey, 1);
    // qDebug()<<transform;
    // qDebug()<<transform2;
    emit transfromChanged(transform, isfreeze);
}
void GLProcessContext::colorFilterTransformUpdate(float *params, bool reset)
{
    QMatrix4x4 mat(params, 4, 4);
    mat = mat.transposed();
    emit colorFilterTransfromChanged(mat, reset);
}

ImageEventArgs GLProcessContext::requestFrame()
{
    return m_ComposeBridge->onRequestFrame();
}

void GLProcessContext::triggerrequestEmptyFrame()
{
    emit requestEmptyFrame();
}

void GLProcessContext::initGLContext()
{
#if (QT_VERSION >= QT_VERSION_CHECK(5, 0, 0))
    QOpenGLContext *ctx = reinterpret_cast<QOpenGLContext *>(_ctx);
    QOpenGLContext *m_context = new QOpenGLContext;
    m_context->setShareContext(ctx);
    if (!m_context->create())
    {
        printf("OverlayManager: Fatal! Failed to create context!");
    }
    QOffscreenSurface *surface_ = new QOffscreenSurface;
    surface_->create();
    m_context->moveToThread(_thd);
    m_context->makeCurrent(surface_);
    m_CurrentGLContext = QGLContext::fromOpenGLContext(m_context);
    ;
#else
    m_GlWidget->makeCurrent();
    QGLContext *glContext = const_cast<QGLContext *>(QGLContext::currentContext());
    m_GlWidget->doneCurrent();
    m_CurrentGLContext = new QGLContext(glContext->format(), glContext->device());
    m_CurrentGLContext->create(glContext);
    m_CurrentGLContext->makeCurrent();
#endif
    m_VideoFrameBridge = new VideoFrameComposeGLBridge(NULL, m_CurrentGLContext);
    m_BlendBridge = new ImageBlendGLBridge(NULL, m_CurrentGLContext);
    m_ComposeBridge = new ImageComposeGLBridge(m_BlendBridge, m_CurrentGLContext);
    m_VideoFrameBridge->initGlEnv();
    m_BlendBridge->initGlEnv();

    connect(m_VideoFrameBridge, SIGNAL(finishConvert(ImageEventArgs *)), this,
            SIGNAL(finishBlendImage(ImageEventArgs *)));
    connect(m_BlendBridge, SIGNAL(finishConvert(ImageEventArgs *)), this, SIGNAL(finishBlendImage(ImageEventArgs *)));
    connect(this, SIGNAL(transfromChanged(const QTransform &, bool)), m_ComposeBridge,
            SLOT(onTransfromChanged(const QTransform &, bool)), Qt::DirectConnection);
    connect(this, SIGNAL(colorFilterTransfromChanged(const QMatrix4x4 &, bool)), m_ComposeBridge,
            SLOT(onColorFilterTransfromChanged(const QMatrix4x4 &, bool)), Qt::DirectConnection);
    connect(this, SIGNAL(requestEmptyFrame()), m_ComposeBridge, SLOT(onRequestEmptyFrame()), Qt::DirectConnection);
    // connect(m_ComposeBridge, SIGNAL(finishConvert(ImageEventArgs*)),
    // this, SIGNAL(finishBlendImage(ImageEventArgs*)));

    setGeometry(QRect(QPoint(0, 0), m_size));
    onLayoutChanged(1);
}

void GLProcessContext::onSetSonoparameters(SonoParameters *sonoParameters)
{
    m_ComposeBridge->setSonoParameters(sonoParameters);
}

void GLProcessContext::onReceiveImageData(ImageEventArgs *imageEventArgs)
{
    if (NULL == imageEventArgs || imageEventArgs->isNull() || NULL == m_ComposeBridge)
    {
        return;
    }

    ByteLineImageArgs args;
    args.copyFrom(imageEventArgs);
    m_ComposeBridge->setData(&args);
}

void GLProcessContext::onReceiveGraphicsData(ByteLineImageArgs *lineImageArgs)
{
    if (NULL == lineImageArgs || NULL == m_BlendBridge)
    {
        return;
    }

    m_BlendBridge->setData(lineImageArgs);
}

void GLProcessContext::onReceiveVideoFrameData(ByteLineImageArgs *lineImageArgs)
{
    if (NULL == lineImageArgs || NULL == m_BlendBridge)
    {
        return;
    }

    m_VideoFrameBridge->setData(lineImageArgs);
}

void GLProcessContext::onLayoutChanged(int layoutNumber)
{
    setRenderLayoutNum(layoutNumber);
}

void GLProcessContext::onSystemScanModeChanged(int systemScanMode)
{
    setSystemScanMode(systemScanMode);
}

void GLProcessContext::onActiveIndexChanged(int activeIndex)
{
    setActiveIndex(activeIndex);
}

void GLProcessContext::videoframePiplineOn(QSize size, int x, int y)
{
    m_VideoFrameBridge->setImageSize(size.width(), size.height());
    m_VideoFrameBridge->reset(false);
    m_VideoFrameBridge->setPastePos(x, y);
    m_BlendBridge->setNextBridge(m_VideoFrameBridge);
}

void GLProcessContext::videoframePiplineOff()
{
    m_BlendBridge->setNextBridge(NULL);
    m_VideoFrameBridge->clearMemory();
}
