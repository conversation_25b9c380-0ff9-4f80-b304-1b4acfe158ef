#include "appleusapifactoriescontext.h"
#include "beamformerapple.h"
#include "fpgaupgrademodel_palm.h"
#include "presetdatamodel.h"
#include "probepresetmodel.h"
#include "xmlbfparameterloader.h"

AppleUSAPIFactoriesContext::AppleUSAPIFactoriesContext(QObject *parent)
    : BaseUSAPIFactoriesContext(parent)
    , m_BeamFormerApple(nullptr)
{
}

AppleUSAPIFactoriesContext::~AppleUSAPIFactoriesContext()
{
}

void AppleUSAPIFactoriesContext::initialize(int width, int height)
{
    Q_UNUSED(width)
    Q_UNUSED(height)
    XmlBFParameterLoader loader(":/xml/bfparameter_apple.xml");
    BFParameters bfPara = loader.build(this);
    bfPara.m_SonoParameters->setIsRealTime(true);

    m_BeamFormerApple = new BeamFormerApple(this);
    m_BeamFormerApple->setStateManager(m_StateManager);
    m_BeamFormerApple->setColorMapManager(m_ColorMapManager);
    m_BeamFormer = m_BeamFormerApple;
    m_BeamFormer->initialize();
    m_BeamFormer->setControlTableLen(bfPara.m_ControlTableLen);
    m_BeamFormer->setSonoParameters(bfPara.m_SonoParameters);
    fpgaupgrademodel_palm::instance().setControlTable(m_BeamFormerApple->getControlTable());

    m_ProbePresetModel = new ProbePresetModel(this);
    m_ProbePresetModel->setStateManager(m_StateManager);
    m_ProbePresetModel->setSonoParameters(bfPara.m_SonoParameters);
    m_ProbePresetModel->setBeamFormer(m_BeamFormer);

    m_PresetDataModel = new PresetDataModel(m_ProbePresetModel, this);
    m_PresetDataModel->setSonoParameters(bfPara.m_SonoParameters);
}

bool AppleUSAPIFactoriesContext::setCurrentProbe(const ProbeDataInfo &probeInfo)
{
    bool ret = false;
    QVector<int> codes = m_BeamFormerApple->allProbeCodesConnected();
    for (int index = 0; index < codes.size(); ++index)
    {
        if (codes[index] == probeInfo.Code)
        {
            m_BeamFormerApple->selectSocket(index);
            ret = true;
            break;
        }
    }
    if (!ret)
    {
        return ret;
    }
    return BaseUSAPIFactoriesContext::setCurrentProbe(probeInfo);
}
