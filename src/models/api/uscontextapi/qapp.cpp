#include <QApplication>
#include <QDateTime>
#include <QTime>
#include <QDebug>
#include <QDir>
#include <QJsonDocument>
#include <QJsonParseError>
#include <mutex>
#include <QTextStream>
#include "resource.h"

static std::once_flag _flag;
static QApplication * m_qApp;

void initQApp()
{
    std::call_once(_flag, []
    {
        int argc = 0;
        if(!QApplication::instance())
            m_qApp = new QApplication(argc, nullptr);
    });
}

void customMessageHandler(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QString txt;
    QString time = QDateTime::currentDateTime().toString("yyyy/MM/dd ") +
            QTime::currentTime().toString("hh:mm:ss.zzz");
    switch (type) {
    //debug
    case QtDebugMsg:
            txt = QString("%1[Info]: %2").arg(time).arg(msg);
            break;
    //warning
    case QtWarningMsg:
            txt = QString("%1[Warn]: %2").arg(time).arg(msg);
    break;
    //critical
    case QtCriticalMsg:
            txt = QString("%1[Critical]: %2").arg(time).arg(msg);
    break;
    //fatal
    case QtFatalMsg:
            txt = QString("%1[Fatal]: %2").arg(time).arg(msg);
            qDebug()<<txt;
            abort();
    }

    if(!QDir(Resource::logDir).exists())
    {
        QDir dir;
        dir.mkdir(Resource::logDir);
    }
    QFile outFile1(Resource::logName);
    QFile outFile2(Resource::logBakName);
    outFile1.open(QIODevice::WriteOnly | QIODevice::Append);
    //考虑48小时清理一次日志
    if(outFile1.size() >= 1024*1024 )
    {
       outFile1.close();
       outFile2.remove();
       QFile::copy(Resource::logName,Resource::logBakName);
       outFile1.remove();

       QFile outFile3(Resource::logName);
       outFile3.open(QIODevice::WriteOnly | QIODevice::Append);
       QTextStream ts(&outFile3);
       ts << txt << Qt::endl;
    }
    else
    {
        QTextStream ts(&outFile1);
        ts <<txt<<Qt::endl;
    }
}


int readJsonByQt5(const QString &jsonPath, QJsonDocument &jsonDoc, QIODevice::OpenMode mode)
{
    QFile loadFile(jsonPath);
    if(!loadFile.open(mode))
    {
        qDebug() << "could't open projects json";
        return -1;
    }
    QByteArray allData = loadFile.readAll();
    QJsonParseError jsonError;
    jsonDoc = QJsonDocument(QJsonDocument::fromJson(allData, &jsonError));
    if(jsonError.error != QJsonParseError::NoError)
    {
        qDebug() << "json error!" << jsonPath<< jsonError.errorString();
        return -1;
    }
    loadFile.close();
    return 0;
}
