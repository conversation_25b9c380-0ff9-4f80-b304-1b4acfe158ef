#include <functional>
#include "linedatahead.h"
#define USBPACKAGESIZE 1024
#define UDPPACKAGESIZE 1040
#define PALM_CODE 0x0
#define PALM_NOTICELEN 5  // 前4个放keypress信息  后一个放充电状态信息
#define EVENTSETFUNC(k) void setevent##k(int event){if(event##k!=event)changed=true;event##k=event;}
typedef struct _EVENTSTRCUT
{
        int event1;
        int event2;
        int event3;
        int event4;
        int event5;
        int changed;
        _EVENTSTRCUT(){ event1=event2=event3=event4=event5=0; changed=1;}
        EVENTSETFUNC(1)
        EVENTSETFUNC(2)
        EVENTSETFUNC(3)
        EVENTSETFUNC(4)
        EVENTSETFUNC(5)
}EVENTSTRCUT;
auto probe_palm = [](const uchar  *data, EVENTSTRCUT *infoOut)
            -> int 
            {
                    LineDataHWInfo *lineinfoPtr = (LineDataHWInfo *)data;
                    infoOut->changed = false;
                    infoOut->setevent1(((lineinfoPtr->ProbeKeyFreeze)    & 0x7)==5?1:0);//冻结解冻信息
                    infoOut->setevent2(((lineinfoPtr->ProbeKeySave)      & 0x7)==5?1:0);//保存按钮
                    infoOut->setevent3(((lineinfoPtr->ProbeKeyDepthUp)   & 0x7)==5?1:0);//降低深度
                    infoOut->setevent4(((lineinfoPtr->ProbeKeyDepthDown) & 0x7)==5?1:0);//增加深度

                    infoOut->setevent5(lineinfoPtr->ChargeStatus);                      //增充电状态

                    return infoOut->changed;
            };
static std::function<int (const uchar  *data,  EVENTSTRCUT *infoOut)>  probe_types[1]={probe_palm};
