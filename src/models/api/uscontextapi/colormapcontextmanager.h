#ifndef COLORMAPCONTEXTMANAGER_H
#define COLORMAPCONTEXTMANAGER_H

#include <QList>

class ColorMapContext;
class IBeamFormer;
class IBFKitFactory;
class SonoParameters;

const int REALTIME_COLORMAP = 0;

class ColorMapContextManager
{
public:
    ColorMapContextManager();
    ~ColorMapContextManager();
    void setBFKitFactory(IBeamFormer* beamformer);
    void createContexts(int size);
    ColorMapContext *createContext(SonoParameters *sonoParameters = NULL);
    ColorMapContext *context(int index) const;
    void removeContext(int index);
    void clear();
private:
    QList<ColorMapContext*> m_Contexts;
    IBFKitFactory *m_BFKitFactory;
};

#endif // COLORMAPCONTEXTMANAGER_H
