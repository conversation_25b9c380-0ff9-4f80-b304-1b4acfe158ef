add_definitions(-DUSCONTEXTAPI_LIBRARY)

include_depends(usf.i.imaging.usapi usf.i.peri.bfiodevice usf.i.exam.funcmodel usf.imaging.com.bs usf.exam.com.bs usf.program.statemanager usf.preset.bs)
include_directories(dummyiodevice confighelper additionalparamtools glyphtools datamodels)

if(${USE_P9API})
    set(targetfiles
        uscontextapip9.cpp
        )
else()
    include_depends(usf.i.imaging.com.ui usf.i.mark.ui.glyphs)
    set(targetfiles
        uscontextapiios.cpp

        graphicscontext.cpp
        glprocesscontext.cpp
        glprocesscreator.cpp
        uscolorbar.cpp)
endif()

if(APPLE)
    set(offscrfiles opengloffscreensurface.cpp)
    include_directories(../../corelib/licensetool/)
    include_directories(../../corelib/glyphs/)
    include_directories(../dicomapi/)
    set(applescrfiles
        platform_support/mp4Recorder4ios.mm
        platform_support/videorecorder.cpp
        platform_support/audioplay.mm
        )
endif()

set(dummyiodevice
    dummyiodevice/basedummyiodevice.cpp
    dummyiodevice/threaddummyiodevice.cpp
)

set(confighelper
    confighelper/biopsyconfig.cpp
    confighelper/presetconfig.cpp
    confighelper/probeconfig.cpp
    confighelper/ipresetcontroller.cpp
    confighelper/presetconfigbase.cpp
    confighelper/presetconfigp9.cpp
    confighelper/presetconfigfactory.cpp
)

set(additionalparamtools
    additionalparamtools/additionalparamnames.cpp
    additionalparamtools/iadditionalparamtool.cpp
    additionalparamtools/additionalparamtool.cpp
    additionalparamtools/tgcposyadditionalparamtool.cpp
    additionalparamtools/freqbadditionalparamtool.cpp
    additionalparamtools/playingadditionalparamtool.cpp
    additionalparamtools/isloadadditionalparamtool.cpp
    additionalparamtools/coloroiposadditionalparamtool.cpp
    additionalparamtools/droiposadditionalparamtool.cpp
    additionalparamtools/probetypeadditionalparamtool.cpp
    additionalparamtools/usbudpadditionalparamtool.cpp
)

set(glyphtools
    glyphtools/iglyphtool.cpp
    glyphtools/glyphtool.cpp
    glyphtools/steeringangleglyphtool.cpp
    glyphtools/dscanlineglyphtool.cpp
    glyphtools/dopplerstartdepthmmglyphtool.cpp
    glyphtools/setbiopsycolorglyphtool.cpp
)

set(datamodels
    datamodels/abstractdatamodel.cpp
    datamodels/presetdatamodel.cpp
    )

add_library_qt(uscontextapi
    usapi.cpp
    uscontextapi.cpp
    usapiinfostruct.cpp
    toolsfactorytemplate.h
    colormapcontext.cpp
    colormapcontextmanager.cpp
    qapp.cpp
    iusapifactoriescontext.cpp
    baseusapifactoriescontext.cpp
    sonoeyeusapifactoriescontext.cpp
    p9usapifactoriescontext.cpp
    usapiparameterfilter.cpp
    appleusapifactoriescontext.cpp
    ${dummyiodevice}
    ${confighelper}
    ${additionalparamtools}
    ${glyphtools}
    ${offscrfiles}
    ${targetfiles}
    ${applescrfiles}
    ${datamodels}
  )

if(BUILD_SYS STREQUAL "android")
    target_link_libraries(uscontextapi usf.com.core.utilitymodel usf.i.imaging.usapi usf.i.peri.bfiodevice usf.i.exam.funcmodel ${ANDROIDEXTRALIBS_LIBRARYS}
        usf.exam.com.bs usf.program.statemanager usf.preset.bs)
else()
    target_link_libraries(uscontextapi usf.com.core.utilitymodel usf.i.imaging.usapi usf.i.peri.bfiodevice usf.i.exam.funcmodel
        usf.exam.com.bs usf.program.statemanager usf.preset.bs usf.imaging.com.bs)
endif()

if(${USE_P9API})
    if(EXISTS "${THIRDPARTYLIB_PATH}/otherapp/")
    add_custom_command(TARGET uscontextapi
            POST_BUILD
            COMMAND cmake -DsourceDirector=${THIRDPARTYLIB_PATH}/otherapp/ -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
            )
    endif()
else()
    target_link_libraries(uscontextapi usf.i.imaging.com.ui glyphscontrol usf.i.preset.bs)
endif()

install(DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    DESTINATION bin
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    DESTINATION lib
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION include
    FILES_MATCHING PATTERN "*.h")
