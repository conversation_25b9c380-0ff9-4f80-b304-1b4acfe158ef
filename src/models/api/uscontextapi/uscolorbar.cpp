#include "uscolorbar.h"
void UsColorbar::colormapChanged(ColorBarModel* cmp)
{
    if(__typelist.contains(cmp->type())){
     {
        m_ColorMap = cmp->data().data();
        update();
     }
    }
    if(__typelist.contains(ColorMapTypeDef::CF)){
//        if(ifonlyonebar())
//             setVisible(false);
//        else{
//
//             setVisible(true);
//        }
        if(cmp->isCFType())
            m_ColorMap = cmp->data().data();
    }
//    update();
}
bool UsColorbar::ifcompoundbar(){
    UsScanMode mode=getscanmodefunc();
    if(mode == UsSystemScanModeBPW || mode==UsSystemScanModeUDBM || mode == UsSystemScanModeColorPW)
        return  true;
    else
        return false;
}
bool UsColorbar::ifonlyonebar(){
    UsScanMode mode=getscanmodefunc();
    if(mode == UsSystemScanModeB || mode == UsSystemScanModeBPW || mode==UsSystemScanModeUDBM)
        return  true;
    else
        return false;
}
void UsColorbar::colormapgroupChanged(QList<ColorBarModel *> cmp)
{
    //血流模式
    if(__typelist.contains(ColorMapTypeDef::CF)){
        if(cmp.length()>1 && cmp[1]->isCFType()){
            m_ColorMap = cmp[1]->data().data();
            setVisible(true);
        }else
            setVisible(false);
        //把血流的bar隐藏
        if(ifonlyonebar())
             setVisible(false);
    }else{//非血流
        if(cmp.length()>0)
            m_ColorMap = cmp[0]->data().data();
        if(ifcompoundbar()){
            if(cmp.length()>1)
                m_ColorMap2 = cmp[cmp.length()-1]->data().data();
        }

    }
    update();
}
void UsColorbar::paint(QPainter* painter, const QStyleOptionGraphicsItem*  opt,QWidget* wdgt){
    if(m_ColorMap == NULL )return;
    int index = 0;
    QColor color;
    //两个map组合
    if(ifcompoundbar()&&__typelist.length()==2){
        for(int i=0; i<128; i+=1){
            index = (255 - i*2) * 4;
            color = QColor(m_ColorMap[index + 0], m_ColorMap[index + 1], m_ColorMap[index + 2]);
            painter->fillRect(rect().x(), i+rect().y(), rect().width(), 1, color);
        }
        for(int i=0; i<128; i+=1){
            index = (255 - i*2) * 4;
            color = QColor(m_ColorMap2[index + 0], m_ColorMap2[index + 1], m_ColorMap2[index + 2]);
            painter->fillRect(rect().x(), 128+i+rect().y(), rect().width(), 1, color);
        }


    }else{
        for(int i=0; i<256; i++){
            index = (255 - i) * 4;
            if(__typelist.contains(ColorMapTypeDef::CF)){
                if(i<128)
                    index=(128-i)*4;
                else
                    index=i*4;
            }
            int r = m_ColorMap[index + 0];
            int g = m_ColorMap[index + 1];
            int b = m_ColorMap[index + 2];
            color = QColor(r<1?1:r, g<1?1:g, b<1?1:b);
            painter->fillRect(rect().x(), i+rect().y(), rect().width(), 1, color);
        }
    }

}
UsColorbar::UsColorbar(QList<ColorMapTypeDef::ColorMapType> typelist,QGraphicsItem *parent)
    : QGraphicsRectItem(parent){
    __typelist=typelist;
    m_ColorMap=NULL;
    m_ColorMap2=NULL;
    setTransform(QTransform::fromScale(0.5, 0.5));
}
