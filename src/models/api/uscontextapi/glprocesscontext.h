#ifndef GLPROCESSCONTEXT_H
#define GLPROCESSCONTEXT_H

#include <QRect>
#include <QObject>

class QGLWidget;
class QObject;
class ImageBlendGLBridge;
class ImageComposeGLBridge;
class VideoFrameComposeGLBridge;
class SonoParameters;
class ImageEventArgs;
class ByteLineImageArgs;
class QGLContext;

/**
 * @brief The GLProcessContext class 负责创建和管理图像数据的处理流程
 */
class GLProcessContext : public QObject
{
    Q_OBJECT
public:
    GLProcessContext(QGLWidget *glWidget, QSize size, QObject *parent = 0, QThread *thread = 0);

    GLProcessContext(QGLContext *sharedContext, QObject *parent = 0, QThread *thread = 0);

    GLProcessContext(QSize size,void *sharectx, QObject *parent = 0, QThread *thread = 0);

    ~GLProcessContext();
    /**
     * @brief createGLBridge 创建OpenGL的处理流程
     */
    void createGLBridge();
    void setSonoParameters(SonoParameters *sonoParameters);
    /**
     * @brief setRenderLayoutNum　设置绘制布局的数量
     * @param layoutNum　布局数量
     */
    void setRenderLayoutNum(int layoutNum);

    void setSystemScanMode(int systemScanMode);

    void setActiveIndex(int index);
    /**
     * @brief setGeometry 设置总体布局绘制的位置
     * @param geometry　布局位置
     */
    void setGeometry(const QRect &geometry);
    /**
     * @brief sharedContext 返回共享的上下文
     * @return
     */
    const QGLContext *sharedContext() const;

    /**
     * @brief layoutTransformUpdate 图像放大 移动
     */ 
    void layoutTransformUpdate(double scale, double translatex, double translatey, bool isfreeze);
    /**
     * @brief colorFilterTransformUpdate 图像滤镜参数
     */ 
    void colorFilterTransformUpdate(float *params, bool reset);
    /**
     * @brief 获取当前显示的帧
     */
    ImageEventArgs requestFrame();

    void triggerrequestEmptyFrame();
signals:
    void finishBlendImage(ImageEventArgs* args);

    void transfromChanged(const QTransform &transform, bool isfreeze);

    void colorFilterTransfromChanged(const QMatrix4x4 &transform, bool reset);

    void fpsUnstable(bool reset);

    void requestEmptyFrame();
public slots:
    Q_INVOKABLE void initGLContext();

    Q_INVOKABLE void onSetSonoparameters(SonoParameters *sonoParameters);

    /**
     * @brief onReceiveImageData 处理图像数据，图像进行组合再与图元叠加
     * @param imageEventArgs　图像数据
     */
    void onReceiveImageData(ImageEventArgs *imageEventArgs);
    /**
     * @brief onReceiveGraphicsData 处理图元数据
     * @param lineImageArgs　图像数据
     */
    void onReceiveGraphicsData(ByteLineImageArgs *lineImageArgs);
    /**
     * @brief onReceiveVideoFrameData 处理UI图元数据 用于保存视频
     * @param lineImageArgs　图像数据
     */
    Q_INVOKABLE void onReceiveVideoFrameData(ByteLineImageArgs *lineImageArgs);
    /**
     * @brief onLayoutChanged 处理布局数量变化
     * @param layoutNumber 布局数量
     */
    void onLayoutChanged(int layoutNumber);

    void onSystemScanModeChanged(int systemScanMode);

    void onActiveIndexChanged(int activeIndex);

    void videoframePiplineOn(QSize size, int x, int y);

    void videoframePiplineOff();
private:
    /**
     * @brief m_ComposeBridge 多布局图像组合处理流程
     */
    ImageComposeGLBridge *m_ComposeBridge;
    /**
     * @brief m_BlendBridge　图像和图元叠加融合处理流程
     */
    ImageBlendGLBridge *m_BlendBridge;
    /**
     * @brief m_VideoFrameBridge ui图像和有效图像的叠加
     */
    VideoFrameComposeGLBridge *m_VideoFrameBridge;
    /**
     * @brief m_CurrentGLContext 使用的GL上下文，可由外部共享获取
     */

    QGLContext *m_CurrentGLContext;
    QSize m_size;
    QGLWidget *m_GlWidget;
    bool m_IsShareContext;
    void *_ctx;
    QThread *_thd;
};

#endif // COLORMAPCONTEXT_H
