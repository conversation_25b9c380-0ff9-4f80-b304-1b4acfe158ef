#include <fstream>
#include <stdarg.h>
#if defined(SYS_APPLE) || defined(SYS_UNIX) || defined(SYS_ANDROID)
#include <sys/time.h>
#endif
#include <initializer_list>
#include <string>
#include <regex>
#include <ctype.h>
#include <ctime>
#include "platform_xx_resourcehelper.h"
#define API_LOG_PATH "apilog"
#define API_LOGFILE  "apitrace.log"
#define API_MAX_LINE 1000
static bool dir_exist = false;
static char pLogPath[256] = {0};
using namespace std;
static bool LOG_DIRCHECK = []() {
#ifdef SYS_APPLE
    sprintf(pLogPath, "%s/%s/", __ios_get_writeablepath(), API_LOG_PATH);
    if(__ios_directory_exist(pLogPath))
        dir_exist = true;
#endif
    return true;
}();
void delete_log_line(const char * filename, int deletelinenum )
{
    string deleteline;
    string line;
    char pLogPath_Temp[256] = {0};
#ifdef SYS_APPLE
    sprintf(pLogPath_Temp, "%s/%s/temp.txt", __ios_get_writeablepath(), API_LOG_PATH);
#endif
    ifstream fin;
    fin.open(filename);
    ofstream temp;
    temp.open(pLogPath_Temp);
   
    int n = 0;
    while (getline(fin, line))
    {
        if(n++ > deletelinenum)
            temp << line << endl;
    }

    temp.close();
    fin.close();
    remove(filename);
    rename(pLogPath_Temp, filename);
}
void API_TRACE_LOG(const char* func_name, initializer_list<string> params)
{
#if defined(SYS_APPLE) || defined(SYS_UNIX) || defined(SYS_ANDROID)
    if(dir_exist){
        static int index = 0;
        static long lasttime = 0;
        if(index == 0)
            remove(string(pLogPath).append(API_LOGFILE).c_str());
        struct tm sTM;
        tm *ltm = &sTM;
        struct timeval timeVal;
        gettimeofday(&timeVal, NULL);
        localtime_r(&timeVal.tv_sec, &sTM);
        char logmsgprefix[500] = {0};
        long currentmillseconds = timeVal.tv_sec*1000 + timeVal.tv_usec / 1000;
        long sleeptime =0 ;
        if(lasttime > 0)
            sleeptime= currentmillseconds- lasttime;
        sprintf(logmsgprefix, "%05d. %04d-%02d-%02d %02d:%02d:%02d  %lu ==> %06lu : %s(context ", index++,
                1900 + ltm->tm_year, 1 + ltm->tm_mon, ltm->tm_mday, ltm->tm_hour, ltm->tm_min, ltm->tm_sec, currentmillseconds,sleeptime,
                func_name);
        lasttime = currentmillseconds;
        string content("");
        if(params.size()>0)
            content.append(",");
        for (auto ptr = params.begin(); ptr != params.end(); ptr++)
        {
            string temp = *ptr;
            std::regex reg("-[0-9]+(.[0-9]+)?|[0-9]+(.[0-9]+)?");
            bool ret = std::regex_match(temp,reg);
            if(!ret)//数字需要加"
                content.append("\"");
            content.append(*ptr);
            if(!ret)
                content.append("\"");
            if((ptr +1 )!= params.end())
                content.append(",  ");
        }
        content.append(");");
        char log_prefix_context[512] = {0};
        sprintf(log_prefix_context, "%s  %s", logmsgprefix, content.c_str());
        
        ofstream write;
        write.open(string(pLogPath).append(API_LOGFILE), ios::app);
        write << log_prefix_context << endl;
        write.close();
        write.clear(); 

        if(index > API_MAX_LINE*2){//保留最新的1000行 删除前1000行
            delete_log_line(string(pLogPath).append(API_LOGFILE).c_str(), API_MAX_LINE);
            index = API_MAX_LINE;
            lasttime = 0;
        }
    }
#endif
}
