#include "usapi.h"
#ifdef SYS_APPLE
#include "uscontextapiios.h"
#else
#include "uscontextapip9.h"
#endif
#include "log_api.h"
#include <QByteArray>
#include <QDir>
#include <QSize>
#include <QVariant>
#define TRACE
#ifdef TRACE
#define API_TRACE(...) API_TRACE_LOG(__func__, __VA_ARGS__);
#else
#define API_TRACE(...)
#endif
#define T_S(x) to_string(x)
#ifdef __cplusplus
extern "C"
{
#endif

    _UsProbeDataInfo::_UsProbeDataInfo()
    {
        memset(Name, 0, sizeof(Name));
        Code = 0;
        WaferNum = 0;
        WaferLength = 0.0f;
        WaferRadius = 0.0f;
        IsLinear = 0;
        NoUseLine = 0;
    }
    _UsProbeDataInfo::_UsProbeDataInfo(_UsProbeDataInfo& probinfo)
    {
        Code = probinfo.Code;
        WaferNum = probinfo.WaferNum;
        WaferLength = probinfo.WaferLength;
        WaferRadius = probinfo.WaferRadius;
        IsLinear = probinfo.IsLinear;
        NoUseLine = probinfo.NoUseLine;
        memcpy(Name, probinfo.Name, MAX_PROBE_NAME_SIZE * sizeof(char));
    }
    UsLogicalPoint::UsLogicalPoint(){};
    UsLogicalPoint::UsLogicalPoint(UsLogicalPoint& uslogicpoint)
    {
        UsLogicalPoint_INT.X = uslogicpoint.UsLogicalPoint_INT.X;
        UsLogicalPoint_INT.Y = uslogicpoint.UsLogicalPoint_INT.Y;
        UsLogicalPoint_DOUBLE.X = uslogicpoint.UsLogicalPoint_DOUBLE.X;
        UsLogicalPoint_DOUBLE.Y = uslogicpoint.UsLogicalPoint_DOUBLE.Y;
    }

#ifdef SYS_APPLE
    UsContext UsCreate(int width, int height, int isAnimal)
    {
        API_TRACE({T_S(width), T_S(height), T_S(isAnimal)})
        UsContextApi* api = new UsContextApiIOS(width, height, (MachineType)isAnimal);
        api->initialize(width, height);
        return api;
    }

    UsRetCode UsHumanAnimalSwitch(UsContext context, int type)
    {
        API_TRACE({T_S(type)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->humanAnimalSwitch((MachineType)type);
        return UsOK;
    }

#else
UsContext UsCreate()
{
    UsContextApi* api = new UsContextApiP9();
    api->initialize();
    return api;
}
#endif

    void UsRelease(UsContext& context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api != NULL)
        {
            delete api;
            context = NULL;
        }
    }

    UsRetCode UsSetDataCallback(UsContext context, UsDataCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsData_CT, (void*)callback, userData);
    }

    void UsSetWifiWriteCallback(UsContext context, UsWifiWriteCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return;
        }
        api->setWifiWriteCallback(callback, userData);
    }

    UsRetCode UsSetProbeInfoCallback(UsContext context, UsProbeInfoCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsProbeInfo_CT, (void*)callback, userData);
    }

    UsRetCode UsSetProbeEventCallback(UsContext context, UsProbeEventCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsProbeEvent_CT, (void*)callback, userData);
    }

    UsRetCode UsSetParaChangedCallback(UsContext context, UsParaChangedCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsPara_CT, (void*)callback, userData);
    }

    UsRetCode UsSetVADetectDataCallback(UsContext context, UsVADetectInfoCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsVAInfo_CT, (void*)callback, userData);
    }

    UsRetCode UsSetUpgradeProcessCallback(UsContext context, UsUpgradeProcessCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsFpgaUpgrade_CT, (void*)callback, userData);
    }

    UsRetCode UsSetRawDataCallback(UsContext context, UsRawDataCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsRawData_CT, (void*)callback, userData);
    }

    void UsPushData(UsContext context, unsigned char* data, int size)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return;
        }
        api->pushData(data, size);
    }

    UsRetCode UsStart(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->start();
    }

    UsRetCode UsStop(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->stop();
    }

    UsRetCode UsSetScanMode(UsContext context, UsScanMode scanMode)
    {
        API_TRACE({T_S(scanMode)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setScanMode(scanMode);
    }

    int UsGetScanMode(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getScanMode();
    }

    UsRetCode UsSetLayout(UsContext context, UsLayout layout)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setLayout(layout);
    }

    int UsGetLayout(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getLayout();
    }

    UsRetCode UsSetActiveLayoutIndex(UsContext context, int activeIndex)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setActiveLayoutIndex(activeIndex);
    }

    int UsGetActiveLayoutIndex(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getActiveLayoutIndex();
    }

    UsRetCode UsSetFreeze(UsContext context, int isFrozen)
    {
        API_TRACE({T_S(isFrozen)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setFreeze(isFrozen);
    }

    int UsIsFrozen(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->isFrozen();
    }

    UsRetCode UsStandby(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->standby();
    }

    UsRetCode UsWake(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->wake();
    }

    UsRetCode UsSetCurrentProbe(UsContext context, const char* probeName)
    {
        API_TRACE({probeName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCurrentProbe(probeName);
    }

    UsRetCode UsGetCurrentProbe(UsContext context, UsProbeDataInfo* probeDataInfo)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getCurrentProbe(probeDataInfo);
    }

    UsRetCode UsGetCurrentDscImageTransfromInformation(UsContext context, DscImageTransfromInformation* ImageDscInfo)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getCurrentDscImageTransfromInformation(ImageDscInfo);
    }

    UsRetCode UsLoadPreset(UsContext context, const char* const fileName)
    {
        API_TRACE({fileName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->loadPreset(fileName);
    }

    UsRetCode UsSavePreset(UsContext context, const char* const fileName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->savePreset(fileName);
    }

    int UsGetCurrentProbePreset(UsContext context, char** preset, const int arraySize, const int bufferSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getCurrentProbePreset(preset, arraySize, bufferSize);
    }

    int UsGetProbePreset(UsContext context, const char* probeName, char** preset, const int arraySize,
                         const int bufferSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getProbePreset(probeName, preset, arraySize, bufferSize);
    }

    UsRetCode UsSelectPreset(UsContext context, const char* presetName)
    {
        API_TRACE({presetName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        if (!api->isProbeConnected())
        {
            return UsNotSupportNow;
        }
        return api->selectPreset(presetName) ? UsOK : UsFailed;
    }

    UsRetCode UsSaveCurrentPreset(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->saveCurrentPreset() ? UsOK : UsFailed;
    }

    UsRetCode UsSaveCurrentPresetAs(UsContext context, const char* presetName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->saveCurrentPresetAs(presetName) ? UsOK : UsFailed;
    }

    UsRetCode UsRenamePreset(UsContext context, const char* oldPresetName, const char* newPresetName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->renamePreset(oldPresetName, newPresetName) ? UsOK : UsFailed;
    }

    UsRetCode UsRenameCurrentPreset(UsContext context, const char* presetName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->renameCurrentPreset(presetName) ? UsOK : UsFailed;
    }

    UsRetCode UsDeleteCurrentPreset(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->deleteCurrentPreset() ? UsOK : UsFailed;
    }

    UsRetCode UsDeletePreset(UsContext context, const char* presetName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->deletePreset(presetName) ? UsOK : UsFailed;
    }

    int UsGetAllPreset(UsContext context, char** presets, const int arraySize, const int bufferSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getAllPreset(presets, arraySize, bufferSize);
    }

    UsRetCode UsSaveCine(UsContext context, const char* const fileName, const int startIndex, const int endIndex)
    {
        API_TRACE({fileName, T_S(startIndex), T_S(endIndex)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->saveCine(fileName, startIndex, endIndex);
    }

    UsRetCode UsSaveImage(UsContext context, const char* const fileName)
    {
        API_TRACE({fileName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->saveImage(fileName);
    }

    UsRetCode UsLoadCine(UsContext context, const char* fileName)
    {
        API_TRACE({fileName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->loadCine(fileName);
    }

    UsRetCode UsLoadImage(UsContext context, const char* fileName)
    {
        API_TRACE({fileName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->loadImage(fileName);
    }

    int UsGetStartIndex(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getStartIndex();
    }

    UsRetCode UsSetStartIndex(UsContext context, const int startIndex)
    {
        API_TRACE({T_S(startIndex)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setStartIndex(startIndex);
    }

    int UsGetEndIndex(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getEndIndex();
    }

    UsRetCode UsSetEndIndex(UsContext context, const int endIndex)
    {
        API_TRACE({T_S(endIndex)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setEndIndex(endIndex);
    }

    int UsGetCurrentIndex(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getCurrentIndex();
    }

    UsRetCode UsDeliverMode(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->onDeliverMode();
    }

    UsRetCode UsSetCurrentIndex(UsContext context, const int currentIndex)
    {
        API_TRACE({T_S(currentIndex)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCurrentIndex(currentIndex);
    }

    int UsGetFrameCount(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getFrameCount();
    }

    UsRetCode UsStartPlayLoop(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->startPlayLoop();
    }

    UsRetCode UsStopPlayLoop(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->stopPlayLoop();
    }

    US_API UsRetCode CT_CALL UsStartPlayNext(UsContext context, int n)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->startPlayNext(n);
    }

    UsRetCode UsSetPlaySpeed(UsContext context, UsPlaySpeed speed)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setPlaySpeed(speed);
    }

    UsRetCode UsSetParameterIntAtPosition(UsContext context, const char* const name, int position, int value)
    {
        API_TRACE({name, T_S(position), T_S(value)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setParameterIntAtPosition(name, position, value);
    }

    UsRetCode UsSetParameterInt(UsContext context, const char* const name, int value)
    {
        API_TRACE({name, T_S(value)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setParameter(name, value);
    }

    UsRetCode UsSetParameterDouble(UsContext context, const char* const name, double value)
    {
        API_TRACE({name, T_S(value)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setParameter(name, value);
    }

    UsRetCode UsSetParameterString(UsContext context, const char* const name, const char* const value)
    {
        API_TRACE({name, name, value})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setParameter(name, value);
    }

    UsRetCode UsSetParameterBytes(UsContext context, const char* const name, const char* const value, const int count)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QByteArray byteArray;
        for (int i = 0; i < count; i++)
        {
            byteArray.append(*(value + i));
        }
        return api->setParameter(name, byteArray);
    }

    UsRetCode UsSetParameterSize(UsContext context, const char* const name, const int width, const int height)
    {
        API_TRACE({name, name, T_S(width), T_S(height)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setParameter(name, QSize(width, height));
    }

    UsRetCode UsGetParameterInt(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QVariant variant;
        if (api->getParameter(name, variant))
        {
            *value = variant.toInt();
            return UsOK;
        }
        return UsFailed;
    }

    UsRetCode UsGetParameterIntMin(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getParameterIntMin(name, value);
    }

    UsRetCode UsGetParameterIntStep(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getParameterIntStep(name, value);
    }

    UsRetCode UsGetParameterIntMax(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getParameterIntMax(name, value);
    }

    UsRetCode UsGetParameterDouble(UsContext context, const char* const name, double* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QVariant variant;
        if (api->getParameter(name, variant))
        {
            *value = variant.toDouble();
            return UsOK;
        }
        return UsFailed;
    }

    int UsGetParameterString(UsContext context, const char* const name, char* value, const int count)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QVariant variant;
        if (api->getParameter(name, variant))
        {
            QString string = variant.toString();
            memset(value, 0, count);
            int retValue = (int)strlen(string.toStdString().c_str()) + 1;
            memcpy(value, string.toStdString().c_str(), retValue);
            return retValue;
        }
        return UsFailed;
    }

    int UsGetParameterBytes(UsContext context, const char* const name, char* value, const int count)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QVariant variant;
        if (api->getParameter(name, variant))
        {
            QByteArray byte = variant.toByteArray();
            int retValue = qMin(byte.length(), count);
            memcpy(value, byte.data(), retValue);
            return retValue;
        }
        return UsFailed;
    }

    int UsGetParameterShowText(UsContext context, const char* const name, char* value, const int count)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getParameterShowText(name, value, count);
    }

    UsRetCode UsGetParameterShowValue(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }

        QVariant variant;
        if (api->getParemeterShowValue(name, variant))
        {
            *value = variant.toInt();
            return UsOK;
        }

        return UsFailed;
    }

    UsRetCode UsGetParameterSize(UsContext context, const char* const name, int* width, int* height)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QVariant variant;
        if (api->getParameter(name, variant))
        {
            QSize size = variant.toSize();
            *width = size.width();
            *height = size.height();
            return UsOK;
        }
        return UsFailed;
    }

    UsRetCode UsConvertPhysicsToLogic(UsContext context, const double line, const double depth,
                                      UsLogicalPoint* logicalPoint)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertPhysicsToLogic(line, depth, logicalPoint);
    }

    UsRetCode UsConvertPhysicsToLogicF(UsContext context, const double line, const double depth,
                                       UsLogicalPoint* logicalPoint)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertPhysicsToLogicF(line, depth, logicalPoint);
    }

    UsRetCode UsConvertLogicToPhysics(UsContext context, const UsLogicalPoint* logicalPoint, double* line,
                                      double* depth)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertLogicToPhysics(logicalPoint, line, depth);
    }

    UsRetCode CT_CALL UsConvertLogicxyToPhysics(UsContext context, const int x, const int y, double* line,
                                                double* depth)
    {
        UsLogicalPoint logicalPoint;
        logicalPoint.UsLogicalPoint_INT.X = x;
        logicalPoint.UsLogicalPoint_INT.Y = y;
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertLogicToPhysics(&logicalPoint, line, depth);
    }

    UsRetCode UsConvertLogicFToPhysics(UsContext context, const UsLogicalPoint* logicalPoint, double* line,
                                       double* depth)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertLogicFToPhysics(logicalPoint, line, depth);
    }

    UsRetCode UsConvertPhysicsToLogicInFullImage(UsContext context, const double line, const double depth,
                                                 UsLogicalPoint* logicalPoint)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertPhysicsToLogicInFullImage(line, depth, logicalPoint);
    }

    UsRetCode UsConvertPhysicsToLogicFInFullImage(UsContext context, const double line, const double depth,
                                                  UsLogicalPoint* logicalPoint)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertPhysicsToLogicFInFullImage(line, depth, logicalPoint);
    }

    UsRetCode UsConvertLogicToPhysicsInFullImage(UsContext context, const UsLogicalPoint* logicalPoint, double* line,
                                                 double* depth)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertLogicToPhysicsInFullImage(logicalPoint, line, depth);
    }

    UsRetCode UsConvertLogicFToPhysicsInFullImage(UsContext context, const UsLogicalPoint* logicalPoint, double* line,
                                                  double* depth)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->convertLogicFToPhysicsInFullImage(logicalPoint, line, depth);
    }

    UsRetCode UsGeometryMove(UsContext context, const int offsetX, const int offsetY)
    {
        API_TRACE({T_S(offsetX), T_S(offsetY)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->geometryMove(offsetX, offsetY);
    }

    UsRetCode UsGeometryChangeSize(UsContext context, const int sizeX, const int sizeY)
    {
        API_TRACE({T_S(sizeX), T_S(sizeY)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->geometryChangeSize(sizeX, sizeY);
    }

    UsRetCode UsGeometryScale(UsContext context, const int scaleX, const int scaleY)
    {
        API_TRACE({T_S(scaleX), T_S(scaleY)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->geometryScale(scaleX, scaleY);
    }

    UsScene UsGraphicsScene(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return NULL;
        }

        return api->graphicsScene();
    }

    UsOverlay UsGetOverlay(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return NULL;
        }
        return api->graphicsOverlay();
    }

    UsGlyphsControl UsGetGlyphsControl(UsContext context, GlyphsControlType type)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return NULL;
        }
        return api->glyphsControl(type);
    }

    UsRetCode UsGetAdditionalParamInt(UsContext context, const char* const name, int* value)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getAdditionalParamInt(name, value);
    }

    UsRetCode UsConnect2ParamsAdjustMachine(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->connect2ParamsAdjustMachine();
    }

    UsRetCode UsWidgetUnitControlUpdate(UsContext context, const char* unitname, const int value)
    {
        API_TRACE({unitname, T_S(value)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->widgetUnitControlUpdate(unitname, value);
    }

    int UsGetProbeNames(UsContext context, char** probeNames, const int arraySize, const int itemSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getAllSupportProbeNames(probeNames, arraySize, itemSize);
    }

    int UsGetAllSupportProbeNames(UsContext context, char** probeNames, const int arraySize, const int itemSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getAllSupportProbeNames(probeNames, arraySize, itemSize);
    }

    UsRetCode UsGetProbeShowNameByProbeName(UsContext context, const char* const probename, char* probeShowName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getProbeShowNameByProbeName(probename, probeShowName);
    }

    UsRetCode UsSetPenStyle(UsContext context, const int penStyle)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setPenStyle(penStyle);
    }

    UsRetCode CT_CALL UsSetROIPenStyle(UsContext context, bool isResizing)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setROIPenStyle(isResizing);
    }

    UsRetCode UsAIO(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->AIO();
    }

    UsMeasureContext UsGetMeasureContext(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return NULL;
        }
        return api->measureContext();
    }

    UsRetCode UsUpdateMeasureContext(UsContext context, bool updateImage)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsFailed;
        }
        return api->updateMeasureContext(updateImage);
    }

    UsRetCode UsImportControlTable(UsContext context, const char* filePath)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsFailed;
        }
        return api->importControlTable(filePath);
    }

    UsRetCode UsImportControlTableRange(UsContext context, const char* filePath, const int start, const int end)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsFailed;
        }
        return api->importControlTable(filePath, start, end);
    }

    UsRetCode UsAsyncStartUpgrade(UsContext context, char* fpga_binpath)
    {
        API_TRACE({fpga_binpath})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        QString filepath = QString::fromUtf8(fpga_binpath);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        else if (filepath.isEmpty() || filepath.isNull() || !QDir(filepath).exists())
        {
            return UsFailed;
        }
        return api->asyncStartUpgrade(fpga_binpath);
    }

    UsRetCode UsStartUpgrade(UsContext context, char* fpga_binpath)
    {
        API_TRACE({fpga_binpath})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        QString filepath = QString::fromUtf8(fpga_binpath);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        else if (filepath.isEmpty() || filepath.isNull() || !QDir(filepath).exists())
        {
            return UsFailed;
        }
        return api->StartUpgrade(fpga_binpath);
    }

    UsRetCode UsFinishUpgrade(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->FinishUpgrade();
    }

    UsRetCode UsGetHardwareVersion(UsContext context, char* fpgaVersion, const int count)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        QString version;
        if (api->getHardwareVersion(version) == UsOK)
        {
            memset(fpgaVersion, 0, count);
            int retValue = (int)strlen(version.toStdString().c_str()) + 1;
            if (count >= retValue)
            {
                memcpy(fpgaVersion, version.toStdString().c_str(), retValue);
                return UsRetCode::UsOK;
            }
        }
        return UsFailed;
    }

    UsRetCode UsGetDepthMMByCQYZLevel(UsContext context, const char* probename, int level, double* depthShow)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getDepthMMByCQYZLevel(probename, level, depthShow);
    }

    UsRetCode UsGetDepthMMByCQYZ(UsContext context, const char* probename, int level, double* depthShow)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getDepthMMByCQYZ(probename, level, depthShow);
    }

    int UsLicenseRead(UsContext context, Plicense* pli, int forceudpate)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        int ret = api->licenseRead(pli, forceudpate);
        API_TRACE({T_S(ret), pli->sn, pli->probeModeName, T_S(pli->RentStatus), T_S(pli->isanimal)})
        return ret;
    }
    int UsLicenceWrite(UsContext context, char* key, char* sn, int keytype)
    {
        API_TRACE({key, sn, T_S(keytype)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        return api->licenseWrite(key, sn, keytype);
    }

    UsRetCode UsGetCurrentProbeShowName(UsContext context, char* probeShowName)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getCurrentProbeShowName(probeShowName);
    }

    UsRetCode UsGetCurrentProbeModeName(UsContext context, char* probeModeName, int itemSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->GetCurrentProbeModeName(probeModeName, itemSize);
    }

    UsRetCode UsGetCurrentPresetProbeName(UsContext context, char* presetProbeName, int itemSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->GetCurrentPresetProbeName(presetProbeName, itemSize);
    }

    UsRetCode UsImageTransform(UsContext context, double scale, double translateX, double translateY)
    {
        API_TRACE({T_S(scale), T_S(translateX), T_S(translateY)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->imageTransform(scale, translateX, translateY);
    }

    UsRetCode UsImageTransformReset(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->imageTransformReset();
    }

    UsRetCode UsExportMedia(UsContext context, unsigned char* backgroundImage, int width, int height, int pastex,
                            int pastey, int mediatype, int isdicom, UsConvertProcessCallback callback)
    {
        API_TRACE({T_S(width), T_S(height), T_S(pastex), T_S(pastey), T_S(mediatype), T_S(isdicom)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->exportMedia(backgroundImage, width, height, pastex, pastey, mediatype, isdicom, callback);
    }

    UsRetCode UsVersion(UsContext context, char* dscVersion, char* ecVersion)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->version(dscVersion, ecVersion);
        return UsOK;
    }

    UsRetCode CT_CALL UsGetProbeStatus(UsContext context, ProbeStatus* probestatus)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->getProbeStatus(probestatus);
        return UsOK;
    }

    UsRetCode CT_CALL UsProbeShutDown(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->probeShutDown();
        return UsOK;
    }

    UsRetCode CT_CALL UsSetTgcShowTime(UsContext context, int time)
    {
        API_TRACE({T_S(time)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setTgcShowTime(time);
        return UsOK;
    }

    UsRetCode CT_CALL UsProcessEvents(UsContext context, int time)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->processEvents(time);
        return UsOK;
    }

    UsRetCode UsProbePresetReorder(UsContext context, const int* orderlist, int size)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->updateUserPreset(NULL, NULL, orderlist, size, 3); //修改预设值顺序
    }

    UsRetCode UsProbePresetReset(UsContext context, int resetall)
    {
        API_TRACE({T_S(resetall)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        if (!resetall)
        {
            return api->updateUserPreset(NULL, NULL, NULL, 0, 4); //复位当前探头预设值
        }
        else
        {
            return api->updateUserPreset(NULL, NULL, NULL, 0, 5); //复位所有预设值
        }
    }

    UsRetCode UsProbePresetDelete(UsContext context, const char* probePrestShowname)
    {
        API_TRACE({probePrestShowname})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->updateUserPreset(probePrestShowname, probePrestShowname, NULL, 0, 1); //删除预设值
    }

    UsRetCode UsProbePresetNameUpdate(UsContext context, const char* oldPresetShowname, const char* newPrsetShowname)
    {
        API_TRACE({oldPresetShowname, newPrsetShowname})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->updateUserPreset(oldPresetShowname, newPrsetShowname, NULL, 0, 0); //修改名称
        return ret;
    }

    UsRetCode UsProbePresetAdd(UsContext context, const char* oldPresetShowname, const char* newPrsetShowname)
    {
        API_TRACE({oldPresetShowname, newPrsetShowname})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->updateUserPreset(oldPresetShowname, newPrsetShowname, NULL, 0, 2); //增加探头预设值
        return ret;
    }

    UsRetCode UsSelectBiopsyAngle(UsContext context, int angle, int* posX, int* posY, int* angleRange,
                                  int* offsetRangeX, int* offsetRangeY)
    {
        API_TRACE({T_S(angle)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->selectBiopsyAngle(angle, posX, posY, angleRange, offsetRangeX, offsetRangeY);
        return ret;
    }

    UsRetCode UsGetBiopsyAngleList(UsContext context, int* angle, int* size)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->getBiopsyAngleList(angle, size);
        return ret;
    }

    UsRetCode UsSaveCurrentBiopsyAngle(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->saveCurrentBiopsyAngle();
        return ret;
    }

    UsRetCode UsBiopsyAngleMove(UsContext context, int angle, int posX, int posY)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        UsRetCode ret = api->biopsyAngleMove(angle, posX, posY);
        return ret;
    }

    UsRetCode UsBiopsyAngleReset(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->biopsyAngleReset();
        return UsOK;
    }

    UsRetCode UsGetBiopsyAngle(UsContext context, int* posX, int* posY, int* angle)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->getBiopsyAngle(posX, posY, angle);
        return UsOK;
    }

    UsRetCode UsSetImageColorFilter(UsContext context, float* filterArray)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setImageColorFilter(filterArray);
        return UsOK;
    }

    UsRetCode UsEndLoad(UsContext context)
    {
        API_TRACE({})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->endLoad();
        return UsOK;
    }

    UsRetCode UsGetPresetOriNameByShowName(UsContext context, const char* presetShowName, char* presetOriName)
    {
        API_TRACE({presetShowName})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->getPresetNameByShowName(presetShowName, presetOriName);
        return UsOK;
    }

    UsRetCode UsSetProbeConnected(UsContext context, int isconnected)
    {
        API_TRACE({T_S(isconnected)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setProbeConnected(isconnected);
        return UsOK;
    }

    UsRetCode UsRestoreAfterExportVideo(UsContext context, const char* const cinepath, int playindex)
    {
        API_TRACE({T_S(playindex)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->restoreAfterExportVideo(cinepath, playindex);
        return UsOK;
    }

    UsRetCode UsSetBackGround(UsContext context, int state)
    {
        API_TRACE({T_S(state)})
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setBackGround(state);
        return UsOK;
    }

    bool UsIsSupportFunction(UsContext context, UsFunctionType funcType)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return false;
        }
        return api->isSupportFunction(funcType);
    }

    UsRetCode UsOpenFunction(UsContext context, UsFunctionType funcType)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsFailed;
        }
        return api->openFunction(funcType);
    }

    UsRetCode UsCloseFunction(UsContext context, UsFunctionType funcType)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsFailed;
        }
        return api->closeFunction(funcType);
    }

    UsRetCode UsGetImageCheckTimeInterval(UsContext context, int* timeSec)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getImageCheckTime(timeSec);
    }

    UsRetCode UsSetImageCheckTimeInterval(UsContext context, int timeSec)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setImageCheckTime(timeSec);
    }

    UsRetCode UsSetImageCheckState(UsContext context, bool open)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setImageCheckState(open);
    }

    UsRetCode UsGetImageCheckState(UsContext context, bool* open)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getImageCheckState(open);
    }

    UsRetCode UsSetImageNoChangeCallback(UsContext context, UsImgaeNoChangeCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsImageNoChange_CT, (void*)callback, userData);
    }

    UsRetCode UsSetImageCheckSensitiveThreshold(UsContext context, float threshold)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setImageCheckSensitiveThreshold(threshold);
    }

    UsRetCode UsGetImageCheckSensitiveThreshold(UsContext context, float* threshold)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getImageCheckSensitiveThreshold(threshold);
    }

    UsRetCode UsAutoProbeSelfTest(UsContext context, int type, int second)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->autoProbeSelfTest(type, second);
    }

    UsRetCode UsSingleProbeSelfTest(UsContext context, int type, int num)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->singleProbeSelfTest(type, num);
    }

    UsRetCode UsStopProbeSelfTest(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->stopProbeSelfTest();
    }

    UsRetCode UsSetProbeSelfTestCallback(UsContext context, UsProbeSelfTestCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsProbeSelfTest_CT, (void*)callback, userData);
    }

    UsRetCode UsGetProbeValidElementCounts(UsContext context, int* counts)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getProbeValidElementCounts(counts);
    }

    UsRetCode UsOpenProbeSelfTest(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->openProbeSelfTest();
    }

    UsRetCode UsCloseProbeSelfTest(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->closeProbeSelfTest();
    }

    UsRetCode UsSetCROIChangedCallback(UsContext context, UsCROIChangedCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsCROIChanged_CT, (void*)callback, userData);
    }

    UsRetCode UsGetCROIPoints(UsContext context, _UsLogicalPoint_INT* pointArray, const int count, int* validCount)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        if (count >= 4)
        {
            return api->getCROIPoints(pointArray, count, validCount);
        }
        return UsInvalidArgs;
    }

    UsRetCode UsSetFunctionStateChangedCallback(UsContext context, UsFunctionStateChangedCallback callback,
                                                void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsFunctionStateChanged_CT, (void*)callback, userData);
    }

    UsRetCode UsImportPreset(UsContext context, const char* const filePath)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->importPreset(filePath) ? UsOK : UsFailed;
    }

    UsRetCode UsExportPreset(UsContext context, const char* const filePath)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->exportPreset(filePath) ? UsOK : UsFailed;
    }

    UsRetCode UsRestorePreset(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->restorePreset() ? UsOK : UsFailed;
    }

    UsRetCode UsSetCommunicationLinkErrorCallback(UsContext context, UsCommunicationLinkErrorCallback callback,
                                                  void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsCommunicationError_CT, (void*)callback, userData);
    }

    UsRetCode UsSetFanErrorCallback(UsContext context, UsFanErrorCallback callback, void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsFanError_CT, (void*)callback, userData);
    }

    UsRetCode UsGetFocusPosDepthMM(UsContext context, int focusNum, int focusPos, float* depth)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getFocusPosDepthMM(focusNum, focusPos, depth);
    }

    UsRetCode UsSetIImageType(UsContext context, int typeValue)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setIImageType(typeValue);
    }

    UsRetCode UsGetIImageType(UsContext context, int* typeValue)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getIImageType(typeValue);
    }

    UsRetCode UsSetProbeAssessmentProgressCallback(UsContext context, UsProbeAssessmentProcessCallback callback,
                                                   void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsProbeAssessmentProgress_CT, (void*)callback, userData);
    }

    UsRetCode UsSetProbeAssessmentResultCallback(UsContext context, UsProbeAssessmentResultCallback callback,
                                                 void* userData)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->setCallbackInfo(UsContextApi::UsProbeAssessmentResult_CT, (void*)callback, userData);
    }

    UsRetCode UsStartProbeAssessment(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        return UsInvalidContext;
    }

    UsRetCode UsStopProbeAssessment(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        return UsInvalidContext;
    }

    int UsGetAllParamterNames(UsContext context, char** paraNames, int arraySize, int itemSize)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        return api->getAllParamterNames(paraNames, arraySize, itemSize);
    }

    UsRetCode UsSetBeforeZeusSaveFlag(UsContext context, bool beforeZeusSaveFlag)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setBeforeZeusSaveFlag(beforeZeusSaveFlag);
        return UsOK;
    }

    UsRetCode UsSetAfterZeusSaveFlag(UsContext context, bool afterZeusSaveFlag)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setAfterZeusSaveFlag(afterZeusSaveFlag);
        return UsOK;
    }

    UsRetCode UsSetBeforeIImageSaveFlag(UsContext context, bool beforeIImageSaveFlag)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setBeforeIImageSaveFlag(beforeIImageSaveFlag);
        return UsOK;
    }

    UsRetCode UsSetAfterIImageSaveFlag(UsContext context, bool afterIImageSaveFlag)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->setAfterIImageSaveFlag(afterIImageSaveFlag);
        return UsOK;
    }

    UsRetCode UsExportProperties(UsContext context)
    {
        UsContextApi* api = reinterpret_cast<UsContextApi*>(context);
        if (api == NULL)
        {
            return UsInvalidContext;
        }
        api->exportProperties();
        return UsOK;
    }

#ifdef __cplusplus
}
#endif
