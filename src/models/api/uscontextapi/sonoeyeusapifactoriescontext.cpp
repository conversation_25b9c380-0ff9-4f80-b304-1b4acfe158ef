#include "sonoeyeusapifactoriescontext.h"
#include "beamformersonoeye.h"
#include "bfpnames.h"
#include "colormaptypedef.h"
#include "fpgaupgrademodel_palm.h"
#include "linedatahead.h"
#include "sonoparameters.h"
#include "threaddummyiodevice.h"
#include "xmlbfparameterloader.h"
#ifdef SYS_APPLE
#include "license_palm.h"
#include "usprobeinfowrap.h"
#endif

SonoEyeUSAPIFactoriesContext::SonoEyeUSAPIFactoriesContext(QObject *parent)
    : BaseUSAPIFactoriesContext(parent)
    , m_DummyIODevice(nullptr)
    , m_BeamFormerSonoeye(nullptr)
{
}

void SonoEyeUSAPIFactoriesContext::initialize(int width, int height)
{
    XmlBFParameterLoader loader(":/xml/bfparameter_sonoeye.xml");
    BFParameters bfPara = loader.build(this);
    bfPara.m_SonoParameters->setIsRealTime(true);

    m_BeamFormerSonoeye = new BeamFormerSonoeye(this);
    if ((width != -1) && (height != -1))
    {
        m_BeamFormerSonoeye->updatemodelsettings(width, height);
    }
    m_BeamFormer = m_BeamFormerSonoeye;
    m_DummyIODevice = new ThreadDummyIODevice();
    m_BeamFormer->setBFIODevice(m_DummyIODevice);
    m_DummyIODevice->addObserver(m_BeamFormer);
    m_DummyIODevice->start();
    m_BeamFormer->initialize();
    m_BeamFormer->setControlTableLen(bfPara.m_ControlTableLen);
    m_BeamFormer->setSonoParameters(bfPara.m_SonoParameters);
}

QVector<float> SonoEyeUSAPIFactoriesContext::dopplerSteeringAngles() const
{
    return m_BeamFormerSonoeye->getDopplerSteeringAngles();
}

void SonoEyeUSAPIFactoriesContext::updateFreqIndexBText(const QStringList &freqtxt, const QList<int> &freqidx)
{
    m_BeamFormerSonoeye->updateFreqIndexBText(freqtxt, freqidx);
}

void SonoEyeUSAPIFactoriesContext::updateCustomGrayifNeeded(SonoParameters *sonoParameters)
{
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::B, sonoParameters->pIV(BFPNames::BGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::BSamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::BTHI, sonoParameters->pIV(BFPNames::BTHIGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::BTHISamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::M, sonoParameters->pIV(BFPNames::MGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::MSamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::PW, sonoParameters->pIV(BFPNames::PwGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::PwSamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::PWTDI, sonoParameters->pIV(BFPNames::PwTDIGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::PwTDISamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::CWD, sonoParameters->pIV(BFPNames::CwdGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::CwdSamplePointsStr));
    m_BeamFormerSonoeye->setcustomgraymap(ColorMapTypeDef::Elasto, sonoParameters->pIV(BFPNames::EGrayCurveIndexStr),
                                          sonoParameters->pV(BFPNames::ESamplePointsStr));
}

bool SonoEyeUSAPIFactoriesContext::externalIODeviceIsPaused() const
{
    return m_DummyIODevice->isPaused();
}

void SonoEyeUSAPIFactoriesContext::setExternalIODeviceWriteCallback(UsWifiWriteCallback callback, void *userData)
{
    m_DummyIODevice->initWifiWriteCallback(this, callback, userData);
}

void SonoEyeUSAPIFactoriesContext::pushExternalIODeviceData(unsigned char *data, int size)
{
#ifdef SYS_APPLE
    if (data[2] == LineData::Package_Info)
    {
        static int PACKAGESIZE;
        Q_ASSERT(size % 66560 != 0); //区分usb 和 udp 的数据包 有线探头可能烧录无线版本的fpga
        if (size % UDPPACKAGESIZE == 0)
            PACKAGESIZE = UDPPACKAGESIZE;
        else
            PACKAGESIZE = USBPACKAGESIZE;
        for (int i = 0; i < size / PACKAGESIZE; i++)
        {
            fpgaupgrademodel_palm::instance().udpateState(data + i * PACKAGESIZE + sizeof(LineDataHead));
            license_palm::instance().udpateState(data + i * PACKAGESIZE + sizeof(LineDataHead));
        }
    }
#endif
    m_DummyIODevice->pushExternalUsbData(data, size);
}

void SonoEyeUSAPIFactoriesContext::setExternalIODevicePasuedStatus(const bool status)
{
    m_DummyIODevice->setPaused(status);
}
