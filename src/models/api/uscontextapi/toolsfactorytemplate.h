#ifndef TOOLSFACTORYTEMPLATE_H
#define TOOLSFACTORYTEMPLATE_H

#include <QHash>
#include <QString>

template <class Interface>
class ToolsFactoryTemplate
{
public:
    ~ToolsFactoryTemplate()
    {
        qDeleteAll(m_Tools.values());
    }

    static ToolsFactoryTemplate *instance()
    {
        static ToolsFactoryTemplate factory;
        return &factory;
    }

    void addTool(const QString &name, Interface *tool)
    {
        Q_ASSERT(!m_Tools.contains(name));
        m_Tools.insert(name, tool);
    }

    Interface *tool(const QString &name)
    {
        if(m_Tools.contains(name))
        {
            return m_Tools[name];
        }
        return NULL;
    }

private:
    ToolsFactoryTemplate()
    {

    }

    QHash<QString, Interface*> m_Tools;
};

#endif // TOOLSFACTORYTEMPLATE_H
