#include "uscontextapiios.h"
#include "beamformersonoeye.h"
#include "bffocusparameters.h"
#include "bfpnames.h"
#include "bftgcparameters.h"
#include "chisonultrasoundcontext.h"
#include "colormapcontext.h"
#include "colormaptypedef.h"
#include "exammode.h"
#include "fpgaupgrademodel_palm.h"
#include "glprocesscontext.h"
#include "glyphscontrolmanager.h"
#include "ilinebuffer.h"
#include "imagerender.h"
#include "iprobedataset.h"
#include "iusapifactoriescontext.h"
#include "offscreen.h"
#include "physicalgeometrycontroller.h"
#include "probeparameters.h"
#include "uscolorbar.h"
#include "usprobeinfowrap.h"
#include <QGraphicsScene>
#ifdef SYS_APPLE
#include "dicomapi.h"
#include "fpgaupgrademodel_palm.h"
#include "platform_xx_resourcehelper.h"
#endif
#include "exammodepresethandlerfactory.h"
#include "framecontrol.h"
#include "measurecontext.h"
#include "presetconfig.h"
#include "presetconfigfactory.h"
#include "resource.h"
#include "setting.h"
#include "sonobuffers.h"
#include <QStandardPaths>
#include <mutex>
#include <thread>

UsContextApiIOS::UsContextApiIOS(int width, int height, MachineType type)
    : UsContextApi(type)
    , m_OffScreenBuffer(NULL)
    , m_GLProcessContext(NULL)
    , m_BaritemGray(NULL)
    , m_BaritemColor(NULL)
    , m_recordLib(NULL)
    , m_GraphicsContext(NULL)
    , m_exporttype(SAVEMP4)
    , m_ImageCount(0)
    , m_ImageReceiveCount(0)
{
    ExamModePresetHandlerFactory::instance()->setDataHandlerProvider(m_PresetDataHandlerProvider);
    ExamModePresetHandlerFactory::instance()->create(PresetModule::DataHandlerType::DataHandler, m_DataBaseManager,
                                                     m_ProbeDataSet);
    if (width != -1 && height != -1)
    {
        ModelConfig::instance().setValue(ModelConfig::RenderImageSize, QSize(width, height), false, 0);
        ModelConfig::instance().setValue(ModelConfig::DSCImageSize, QSize(width, height), false, 0);
        ModelConfig::instance().setValue(ModelConfig::MainWindowResolution, QSize(width, height), false, 0);
        ModelConfig::instance().setValue(ModelConfig::RenderWidgetSize, QSize(width, height), false, 0);
    }
    std::fill(m_TGCSegmentPosition, m_TGCSegmentPosition + TGC_COUNT + 1, 0);
}

UsContextApiIOS::~UsContextApiIOS()
{
}

void UsContextApiIOS::setWifiWriteCallback(UsWifiWriteCallback callback, void* userData)
{
    m_USAPIFactoriesContext->setExternalIODeviceWriteCallback(callback, userData);
    setFreeze(true);
}

UsRetCode UsContextApiIOS::probeShutDown()
{
#ifdef SYS_APPLE
    fpgaupgrademodel_palm::instance().setSonoParameters(m_GlobalSonoParameters);
    fpgaupgrademodel_palm::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
    fpgaupgrademodel_palm::instance().shutdown();
#endif
    return UsOK;
}

UsScene UsContextApiIOS::graphicsScene()
{
    return (UsScene)m_GraphicsContext->scene();
}

UsOverlay UsContextApiIOS::graphicsOverlay()
{
    return (UsOverlay)m_GraphicsContext->overlay();
}

UsRetCode UsContextApiIOS::exportMedia(uint8_t* backgroundImage, int width, int height, int pastex, int pastey,
                                       int mediatype, int isdicom, UsConvertProcessCallback callback)
{
    processEvents(500);
#ifdef SYS_APPLE
    m_exporttype = (SAVETYPE)((isdicom << 1) + mediatype);
    if (true)
    {
        m_recordLib = videocontrolptr(width, height, m_exporttype);
        // std::function<void(uint8_t *data, int size)> writeaudio = [&](uint8_t *data, int size)
        //         -> void {
        //                 m_recordLib->add_audio_frame(data,size);
        //             };
        // FUNCLASS<void,uint8_t *,int> *func = new  FUNCLASS<void,uint8_t *,int>();
        // func->set(writeaudio);
        // qlonglong address = (long)func;
        // platform_setvalue(PLATFOM_INDEX_RECORDER,QVariant::fromValue(address));
    }
    m_GLProcessContext->videoframePiplineOn(QSize(width, height), pastex, pastey);
    ImageEventArgs args;
    args.setImageData(backgroundImage);
    args.setWidth(width);
    args.setHeight(height);
    ByteLineImageArgs byteArgs;
    byteArgs.copyFrom(&args);
    staticMetaObject.invokeMethod(m_GLProcessContext, "onReceiveVideoFrameData", Qt::BlockingQueuedConnection,
                                  Q_ARG(ByteLineImageArgs*, &byteArgs));

    if (mediatype == 1) //图片使用ui传过来的图
    {
        m_recordLib->add_img_frame(backgroundImage, width * height * 4);
    }
    std::thread RecordThread(
        [&](UsConvertProcessCallback callback, int mediatype, int isdicom) {
            int startindex = getStartIndex();
            int stopindex = getEndIndex();
            SystemScanMode scanMode = (SystemScanMode)m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr);
            m_recordLib->start();
            m_recordLib->m_state.state = VideoRecorder::RUN;
            m_recordLib->m_state.waitawhile = false; // TO DO
            m_recordLib->m_state.framecount = getFrameCount();
            int framestep = 1;
            m_ImageCount = 1;
            switch (scanMode)
            {
            case SystemScanModeUDBM:
            {
                double mfps = 1 / m_CurrentSonoParameters->pDV(BFPNames::MPixelSizeSecStr);
                if (mfps > VideoRecorder::MAXRECORDFPS)
                { //最大支持25fps
                    m_recordLib->m_state.frametime = 1000 / VideoRecorder::MAXRECORDFPS;
                    framestep = mfps / VideoRecorder::MAXRECORDFPS;
                }
                m_ImageCount = 2;
            }
            break;
            case SystemScanModeColorPW:
            case SystemScanModeBPW:
            {
                int pwfps = 1 / m_CurrentSonoParameters->pDV(BFPNames::DPixelSizeSecStr);
                if (pwfps > VideoRecorder::MAXRECORDFPS)
                { // pw也按照按照25fps
                    m_recordLib->m_state.frametime = 1000 / VideoRecorder::MAXRECORDFPS;
                    framestep = pwfps / VideoRecorder::MAXRECORDFPS;
                }
                m_ImageCount = 2;
            }
            break;
            default:
            {
                m_recordLib->m_state.frametime =
                    1000 / m_CurrentSonoParameters->pDV(BFPNames::FPSStr); // B 和 C 采用默认帧率
            }
            break;
            }
            m_recordLib->m_state.currentidx = 0;
            bool status = true;
            if (isdicom == 1)
            {
                DcmInfoStruct info;
                QList<SonoParameters*> sonolists({m_CurrentSonoParameters, m_CurrentSonoParameters});
                info.sonoParaList = sonolists;
                info.pixelSizeMMWithZoom = m_CurrentSonoParameters->pDV(BFPNames::PixelSizeMMStr);
                info.frametime = m_recordLib->m_state.frametime;
                info.framerate = 1000 / info.frametime;
                info.posTop = pastey;
                m_recordLib->setuserdata((void*)&info);
            }
            while (mediatype == 0 && !m_AppStateChange && startindex <= stopindex)
            {
                m_ImageReceiveCount = 0;
                qDebug() << __PRETTY_FUNCTION__ << startindex << stopindex;
                ((SonoBuffers*)m_ChisonUltrasoundContext->lineBuffer())->requestFlush(0, startindex);
                startindex += framestep;
                m_FrameReaderLocker.lock();
                if (!m_FrameReaderWaiter.wait(&m_FrameReaderLocker, 25000))
                {
                    if (m_ImageReceiveCount <= m_ImageCount)
                    {
                        qDebug() << PRETTY_FUNCTION << " QWaitCondition time out";
                        m_FrameReaderLocker.unlock();
                        m_recordLib->stop(false);
                        status = false;
                        break;
                    }
                }
                m_FrameReaderLocker.unlock();
                int processsStep = (m_recordLib->m_state.currentidx * framestep * 100) / stopindex;
                callback(this, m_exporttype, NULL, processsStep > 99 ? 99 : processsStep);
                m_recordLib->m_state.currentidx += 1;
            }
            if (m_AppStateChange)
                status = false;
            char fileexport[256];
            memset(fileexport, 0, sizeof(fileexport));
            int finishstatus = m_recordLib->finish(fileexport);
            m_recordLib->m_state.waitawhile = true;
            processEvents(500);
            m_recordLib->m_state.waitawhile = false;
            qDebug() << PRETTY_FUNCTION << "finish";
            m_recordLib->stop(status);
            m_recordLib->m_state.state = VideoRecorder::STOP;
            m_GLProcessContext->videoframePiplineOff();
            float params[16] = {
                1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1,
            };
            GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
            ctx->layoutTransformUpdate(m_GlobalSonoParameters->pDV(BFPNames::ImageZoomCoefStr) / 100,
                                       m_GlobalSonoParameters->pDV(BFPNames::ImageTranslateXStr),
                                       m_GlobalSonoParameters->pDV(BFPNames::ImageTranslateYStr), false);
            ctx->colorFilterTransformUpdate(params, true);
            if (status && finishstatus > 0)
                callback(this, m_exporttype, fileexport, 100);
            else
                callback(this, m_exporttype, fileexport, -1);
        },
        callback, mediatype, isdicom);
    RecordThread.detach();
#endif
    return UsOK;
}

void UsContextApiIOS::restoreAfterExportVideo(const char* const cinepath, int currentindex)
{
    if (cinepath == NULL) //没有加载图像 执行恢复冻结前的图像
    {
        SonoBuffers* s = (SonoBuffers*)m_ChisonUltrasoundContext->lineBuffer();
        s->setRestoreFlag(true);
        endLoad();
        s->reStore();
        s->requestFlush();
        processEvents(50);
        s->setRestoreFlag(false);
    }
    else
    {
        load(cinepath);
        setCurrentIndex(currentindex);
    }
}

void UsContextApiIOS::setImageColorFilter(float* filterArray)
{
#ifdef SYS_APPLE
    GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
    float params[16] = {
        filterArray[0],  filterArray[1],  filterArray[2],  filterArray[4], // c#传入的是4x5的矩阵，这里只需要部分即可
        filterArray[5],  filterArray[6],  filterArray[7],  filterArray[9],  filterArray[10], filterArray[11],
        filterArray[12], filterArray[14], filterArray[15], filterArray[16], filterArray[17], filterArray[18],
    };
    ctx->colorFilterTransformUpdate(params, false);
#endif
}

UsRetCode UsContextApiIOS::connect2ParamsAdjustMachine()
{
    return UsOK;
}

UsMeasureContext UsContextApiIOS::measureContext()
{
    if (m_MeasureContext == NULL)
    {
        m_MeasureContext = new MeasureContext();
    }

    return m_MeasureContext;
}

UsRetCode UsContextApiIOS::updateMeasureContext(bool updateImage)
{
    m_MeasureContext->setSonoParameter(m_CurrentSonoParameters);

    if (updateImage)
    {
        if (m_BackupImageForMeasure != nullptr)
        {
            m_MeasureContext->setImage(QImage(m_BackupImageForMeasure->imageData(), m_BackupImageForMeasure->width(),
                                              m_BackupImageForMeasure->height(), QImage::Format_RGB32));
        }
        m_BackupImageForMeasure = nullptr;
    }

    return UsOK;
}

void UsContextApiIOS::initializeColorMap()
{
    UsContextApi::initializeColorMap();
    UsScene usc = graphicsScene();
    QGraphicsScene* sc = reinterpret_cast<QGraphicsScene*>(usc);
    connect(sc, SIGNAL(changed(const QList<QRectF>&)), this, SLOT(sceneModified(const QList<QRectF>&)));

    //添加colorbar
    auto getscanmode = [this]() -> UsScanMode {
        UsScanMode usScanMode = (UsScanMode)m_CurrentGlobalSonoParameters->pIV(BFPNames::SystemScanModeStr);
        if (usScanMode == UsSystemScanModeColorDoppler || usScanMode == UsSystemScanModeColorPW)
        {
            m_VelocityDown->setVisible(true);
            m_VelocityUp->setVisible(true);
        }
        else
        {
            m_VelocityDown->setVisible(false);
            m_VelocityUp->setVisible(false);
        }
        return usScanMode;
    };
    QList<ColorMapTypeDef::ColorMapType> graybartype;
    graybartype << ColorMapTypeDef::B << ColorMapTypeDef::BTHI;
    m_BaritemGray = new UsColorbar(graybartype);
    m_BaritemGray->setRect(COLORBARPOSX, COLORBARPOSY, COLORBARWIDTH, COLORBARHEIGHT);
    m_BaritemGray->getscanmodefunc = getscanmode;
    sc->addItem(m_BaritemGray);

    QList<ColorMapTypeDef::ColorMapType> colorbartype;
    colorbartype << ColorMapTypeDef::CF;
    m_BaritemColor = new UsColorbar(colorbartype);
    m_BaritemColor->setRect(m_BaritemGray->rect().x() + m_BaritemGray->rect().width() + 30, m_BaritemGray->rect().y(),
                            m_BaritemGray->rect().width(), m_BaritemGray->rect().height());
    m_BaritemColor->getscanmodefunc = getscanmode;
    sc->addItem(m_BaritemColor);

    { //速度上限
        m_VelocityUp = new QGraphicsTextItem;
        m_VelocityUp->setPos(COLORCAPTIONPOSX, COLORCAPTIONPOSY);
        m_VelocityUp->setPlainText(" 8.0");
        m_VelocityUp->setDefaultTextColor(QColor(255, 255, 255, 255));
        QFont font = m_VelocityUp->font();
        font.setPixelSize(16);
        font.setBold(true);
        m_VelocityUp->setFont(font);
        m_VelocityUp->setVisible(false);
        sc->addItem(m_VelocityUp);
    }
    { //速度下限
        m_VelocityDown = new QGraphicsTextItem;
        m_VelocityDown->setPos(COLORCAPTIONPOSX, COLORBARHEIGHT / 2 + 44);
        m_VelocityDown->setPlainText("-8.0");
        m_VelocityDown->setDefaultTextColor(QColor(255, 255, 255, 255));
        QFont font = m_VelocityDown->font();
        font.setPixelSize(16);
        font.setBold(true);
        m_VelocityDown->setFont(font);
        m_VelocityDown->setVisible(false);
        sc->addItem(m_VelocityDown);
    }

    ColorMapContext* context = m_ColorMapContextManager.context(0);
    connect(context->imageRender(), SIGNAL(colorBarChanged(ColorBarModel*)), m_BaritemGray,
            SLOT(colormapChanged(ColorBarModel*)));
    connect(context->imageRender(), SIGNAL(colorBarChanged(ColorBarModel*)), m_BaritemColor,
            SLOT(colormapChanged(ColorBarModel*)));
    connect(context->imageRender(), SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)), m_BaritemGray,
            SLOT(colormapgroupChanged(QList<ColorBarModel*>)));
    connect(context->imageRender(), SIGNAL(colorBarGroupChanged(QList<ColorBarModel*>)), m_BaritemColor,
            SLOT(colormapgroupChanged(QList<ColorBarModel*>)));
}

void UsContextApiIOS::initializeGraphicsContext()
{
    m_GraphicsContext = new GraphicsContext();
    m_GraphicsContext->setSonoParameters(m_GlobalSonoParameters);
    m_GraphicsContext->setBufferManager(m_ChisonUltrasoundContext->lineBuffer());
    m_GraphicsContext->setSceneSize(m_USAPIFactoriesContext->beamFormer()->bImageSize());
    GlyphsControlManager::instance().createControls(m_GraphicsContext->modeGlyphsWidget());

    m_OffScreenBuffer = NULL;
    UsScene usc = graphicsScene();
    QGraphicsScene* sc = reinterpret_cast<QGraphicsScene*>(usc);
#ifdef SYS_APPLE
    QOpenGLContext* offcontxt = offscreen_init(sc);
    m_GLProcessContext = m_GLProcessCreator.createContext(m_GlobalSonoParameters, offcontxt);
#endif

    connect(m_ChisonUltrasoundContext, SIGNAL(newImage(ImageEventArgs*)), m_GLProcessContext,
            SLOT(onReceiveImageData(ImageEventArgs*)), Qt::DirectConnection);
    connect(this, SIGNAL(graphicsChanged(ByteLineImageArgs*)), m_GLProcessContext,
            SLOT(onReceiveGraphicsData(ByteLineImageArgs*)), Qt::DirectConnection);
    connect(m_GLProcessContext, SIGNAL(finishBlendImage(ImageEventArgs*)), this, SLOT(onNewImage(ImageEventArgs*)),
            Qt::DirectConnection);

    connect(this, SIGNAL(layoutChanged(int)), m_GLProcessContext, SLOT(onLayoutChanged(int)));
    connect(this, SIGNAL(systemScanModeChanged(int)), m_GLProcessContext, SLOT(onSystemScanModeChanged(int)));
    connect(this, SIGNAL(activeIndexChanged(int)), m_GLProcessContext, SLOT(onActiveIndexChanged(int)));

    connect(m_GlobalSonoParameters->parameter(BFPNames::IsDopplerScanLineVisibleStr),
            SIGNAL(valueChanged(const QVariant&)), this, SLOT(setGeometrycontrol(const QVariant&)));
    connect(m_GlobalSonoParameters->parameter(BFPNames::IsMLineVisibleStr), SIGNAL(valueChanged(const QVariant&)), this,
            SLOT(setGeometrycontrol(const QVariant&)));

    sc->setBackgroundBrush(QBrush(QColor(0, 0, 0, 255)));
}

void UsContextApiIOS::initializePositionParameterHandle()
{
    TgcAreaArrayPointer offset = &m_TGCSegmentPosition;
    int imageHeight = m_CurrentSonoParameters->pV(BFPNames::PointNumPerLineStr).toInt();
    BFTGCParameters::scaledTgcAreaY(offset, imageHeight);
    m_PositionHadnles.insert(BFPNames::TGCStr,
                             reinterpret_cast<PositionParameterHandle>(&UsContextApiIOS::setTGCValue));
    m_PositionHadnles.insert("Focus", reinterpret_cast<PositionParameterHandle>(&UsContextApiIOS::setFocusPosValue));
}

void UsContextApiIOS::initializeSpecialParameters()
{
    connect(m_ChisonUltrasoundContext, SIGNAL(datainfo(void*, int, int, int)), &FrameControl::instance(),
            SLOT(datainfo(void*, int, int, int)), Qt::DirectConnection);
    FrameControl::instance().setSonoParameters(m_ChisonUltrasoundContext->sonoParameters());

    connect(m_CurrentSonoParameters->parameter(BFPNames::SampleRateDopShowStr), SIGNAL(valueChanged(const QVariant&)),
            this, SLOT(setColorVelocity()));

    connect(&fpgaupgrademodel_palm::instance(), SIGNAL(upgradeProgress(int, int)), this,
            SLOT(fpgaUpgradestatus(int, int)));
    m_SetScaleTimer = new QTimer(this);
    connect(m_SetScaleTimer, SIGNAL(timeout()), this, SLOT(onScaleTimerOut()));
    m_SetScaleTimer->setSingleShot(true);
}

void UsContextApiIOS::fpgaUpgradestatus(int ret, int process)
{
    CallbackInfo ci = m_CallbackInfo[UsFpgaUpgrade_CT];
    if (ci.first != NULL)
    {
        ((UsUpgradeProcessCallback)ci.first)(this, ret, process, ci.second);
    }
}

void UsContextApiIOS::sonoParametersChanged(SonoParameters* parameters)
{
    if (m_GraphicsContext != NULL)
    {
        m_GraphicsContext->setSonoParameters(m_GlobalSonoParameters);
    }
}
// BM PW 模式界面刷新晚于接收帧频，这里等待一下不接收数据 等界面刷完 完全冻结
void UsContextApiIOS::doingOnSettingFreeze()
{
#ifdef SYS_APPLE
    UsScanMode m = getScanMode();
    switch (m)
    {
    case UsSystemScanModeB:
        platform_noblock_sleep(150);
        break;
    case UsSystemScanModeColorDoppler:
        platform_noblock_sleep(300);
        break;
    case UsSystemScanModeUDBM:
        platform_noblock_sleep(150);
        break;
    case UsSystemScanModeBPW:
    case UsSystemScanModeColorPW:
        platform_noblock_sleep(500);
        break;
    default:
        break;
    }
#endif
}

int UsContextApiIOS::updateBeamFormerFreqText(const QString& probeName, const QString& presetName)
{
    int selectFreq = 1;
    if (m_PresetAliasNames.contains(probeName))
    {
        QStringList freqtxt;
        QList<int> freqidx;
        for (int i = 0; i < m_PresetAliasNames[probeName].size(); i++)
        {
            if (m_PresetAliasNames[probeName][i].PresetName == presetName)
            {
                freqtxt = m_PresetAliasNames[probeName][i].FreqShowNames;
                freqidx = m_PresetAliasNames[probeName][i].FreqBIndexs;
                selectFreq = m_PresetAliasNames[probeName][i].SelectFreq;
            }
        }
        Q_ASSERT(freqtxt.size() > 0);
        m_USAPIFactoriesContext->updateFreqIndexBText(freqtxt, freqidx);
        m_FreqBShowText = freqtxt;
    }
    else
    {
        Q_ASSERT(false);
    }
    return selectFreq;
}

void UsContextApiIOS::onSaveCurrentPreset()
{
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->getProbe(probeID);
    QString presetShowname = m_GlobalSonoParameters->pV(BFPNames::ExamModeCaptionStr).toString();
    PresetConfig presetConfig(m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    QString realpresetname = presetConfig.getPrestName(dataInfo.Name, presetShowname);
    saveCurrentFreqIndexBToJson(dataInfo.Name, realpresetname);
}

void UsContextApiIOS::onCurrentSonoParametersChanging(SonoParameters* sonoParameters)
{
    m_GraphicsContext->setSonoParameters(sonoParameters);
}

void UsContextApiIOS::clearFrameDsiplay()
{
#ifdef SYS_APPLE
    GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
    if (ctx != NULL)
    {
        ctx->triggerrequestEmptyFrame();
    }
#endif
}

void UsContextApiIOS::lazyFreeze()
{
    //模式界面刷新晚于接收帧频，这里等待一下不接收数据 等界面刷完 完全冻结
    // TO .DO
    m_USAPIFactoriesContext->setExternalIODevicePasuedStatus(true);
    int ft = getFrameCount();
    auto tstart = std::chrono::steady_clock::now();
    while (1)
    {
        if (m_CurrentShowIndex >= ft - 1)
        {
            break;
        }
        else
        {
#ifdef SYS_APPLE
            platform_noblock_sleep(10);
#endif
        }
        auto tcur = std::chrono::steady_clock::now();
        if (std::chrono::duration<double, std::milli>(tcur - tstart).count() > 500) //最大500ms
        {
            break;
        }
    }
#ifdef SYS_APPLE
    platform_noblock_sleep(10);
#endif
    auto tend = std::chrono::steady_clock::now();
    double dr_ms = std::chrono::duration<double, std::milli>(tend - tstart).count();
    m_USAPIFactoriesContext->beamFormer()->freeze(true);
    QTimer::singleShot(500, this, [this] { m_USAPIFactoriesContext->setExternalIODevicePasuedStatus(false); });
}

ImageEventArgs UsContextApiIOS::getCurrentFrame()
{
#ifdef SYS_APPLE
    GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
    return ctx->requestFrame();
#else
    return ImageEventArgs();
#endif
}

void UsContextApiIOS::modifyLoggerConfFile()
{
    QString dir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString filepath = Resource::loggerConf;
    QString data;

    QFile file(filepath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        data = file.readAll();
        int start = data.indexOf('=', 0);
        int end = data.indexOf(QRegExp("\r\n|\r|\n"), 0);
        data.replace(start + 1, end - start, dir + "/\n");
    }
    file.close();

    if (file.open(QIODevice::WriteOnly | QIODevice::Truncate))
    {
        QTextStream stream(&file);
        stream << data;
    }
    file.close();
}

void UsContextApiIOS::onHWInfoUpdate(unsigned char* probehwinfo, int len)
{
    m_USAPIFactoriesContext->beamFormer()->onUpdate((unsigned char*)probehwinfo, 100);
    static EVENTSTRCUT* noteinfo = new EVENTSTRCUT();
    int changed = probe_types[PALM_CODE](probehwinfo, noteinfo);
    CallbackInfo ci = m_CallbackInfo[UsProbeEvent_CT];
    if (ci.first != NULL && changed)
    {
        ((UsProbeEventCallback)ci.first)(this, (unsigned int*)noteinfo, PALM_NOTICELEN, ci.second);
    }
}

void UsContextApiIOS::onProbeChanged(QVector<int> probelist, QVector<bool> changes)
{
    UsContextApi::onProbeChanged(probelist, changes);
    std::once_flag _flag;
    std::call_once(_flag, [this] {
        connect(m_GlobalSonoParameters->parameter(BFPNames::ProbeConnectedStr), SIGNAL(valueChanged(const QVariant&)),
                this, SLOT(onProbeConnectedChanged(const QVariant&)));
    });
}

void UsContextApiIOS::onNewImage(ImageEventArgs* imageEventArgs)
{
    m_BackupImageForMeasure = imageEventArgs;
    CallbackInfo ci = m_CallbackInfo[UsData_CT];
    if (ci.first != NULL)
    {
#ifdef SYS_APPLE
        if (m_recordLib != NULL && m_recordLib->m_state.state == VideoRecorder::RUN)
        {
            qDebug() << "@@@@@@@@@@@@@@@@" << m_recordLib->m_state.currentidx << imageEventArgs->index()
                     << imageEventArgs->frameIndex();
            if (m_recordLib->m_state.waitawhile == false)
            {
                m_ImageReceiveCount += 1;
                if (m_ImageReceiveCount >= m_ImageCount)
                {
                    int idx = m_recordLib->m_state.currentidx;
                    // writebinarydata((unsigned char
                    // *)imageEventArgs->imageData(),imageEventArgs->width()*imageEventArgs->height()*4,"/2222.bin");
                    if (m_exporttype == SAVEVIDEODICOM)
                    {
                        uint8_t* src = imageEventArgs->imageData();
                        m_recordLib->add_video_frame(src, imageEventArgs->width() * imageEventArgs->height() * 4, 0);
                    }
                    else if (m_exporttype == SAVEMP4)
                    {
                        m_recordLib->add_video_frame(imageEventArgs->imageData(),
                                                     imageEventArgs->width() * imageEventArgs->height() * 4,
                                                     idx * m_recordLib->m_state.frametime);
                    }
                    m_FrameReaderWaiter.wakeAll();
                }
            }
        }
        else
        {
            *(int*)(ci.second) = imageEventArgs->needFreeze();
            ((UsDataCallback)ci.first)(this, imageEventArgs->imageData(), imageEventArgs->width(),
                                       imageEventArgs->height(), imageEventArgs->bitCount(), UsImageB,
                                       imageEventArgs->index(), imageEventArgs->frameIndex(), ci.second);
            m_CurrentShowIndex = imageEventArgs->frameIndex();
        }
#endif
    }
}

void UsContextApiIOS::onImageTransformChanged(bool preset)
{
    if (preset)
    {
        m_CurrentSonoParameters->setPV(BFPNames::PanZoomOffsetDepthPixelStr, 0);
        m_CurrentSonoParameters->setPV(BFPNames::ImageZoomCoefStr, 100);
        m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateXStr, false);
        m_CurrentSonoParameters->setPV(BFPNames::ImageTranslateYStr, false);
    }
#ifdef SYS_APPLE
    GLProcessContext* ctx = const_cast<GLProcessContext*>(m_GLProcessCreator.context(0));
    ctx->layoutTransformUpdate(m_CurrentSonoParameters->pDV(BFPNames::ImageZoomCoefStr) / 100,
                               m_CurrentSonoParameters->pDV(BFPNames::ImageTranslateXStr),
                               m_CurrentSonoParameters->pDV(BFPNames::ImageTranslateYStr), isFrozen());
#endif
}

void UsContextApiIOS::setGeometrycontrol(const QVariant& v)
{
    Parameter* p = qobject_cast<Parameter*>(sender());
    if (p->name() == BFPNames::IsDopplerScanLineVisibleStr && v.toBool() &&
        getScanMode() == UsSystemScanModeColorDoppler)
    {
        if (m_CurrentController)
        {
            m_CurrentController->setActive(false);
        }
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->rOIAndDSamplingGateController();
        m_CurrentController->setActive(true);
    }
    else if (p->name() == BFPNames::IsMLineVisibleStr && v.toBool() && getScanMode() == UsSystemScanModeB)
    {
        if (m_CurrentController)
        {
            m_CurrentController->setActive(false);
        }
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->mLineController();
        m_CurrentController->setActive(true);
    }
    else if (p->name() == BFPNames::IsDopplerScanLineVisibleStr && v.toBool() && getScanMode() == UsSystemScanModeB)
    {
        if (m_CurrentController)
        {
            m_CurrentController->setActive(false);
        }
        m_CurrentController = m_USAPIFactoriesContext->beamFormer()->dSamplingGateController();
        m_CurrentController->setActive(true);
    }
}

extern int acquireApplicationState();
void UsContextApiIOS::sceneModified(const QList<QRectF>& qlistr)
{
#ifdef SYS_APPLE
    if (acquireApplicationState() != 0)
    {
        return;
    }
    UsScene usc = graphicsScene();
    QGraphicsScene* sc = reinterpret_cast<QGraphicsScene*>(usc);
    m_OffScreenBuffer = offscreen_renderQt(sc);
    onGraphicsChanged((uchar*)m_OffScreenBuffer, sc->width(), sc->height());
#endif
}

void UsContextApiIOS::setTGCValue(const int position, const QVariant& value)
{
    int tgcpos = 0;
    getAdditionalParamInt("TGCPOSY", &tgcpos);
    int realposition = position - m_CurrentSonoParameters->pIV(BFPNames::PointNumPerLineStr) *
                                      ((tgcpos * 1.0) / ((QGraphicsScene*)graphicsScene())->height());
    if (realposition < 0)
        return;

    Setting::instance().defaults().setTGCNeedShow(1);
    int intValue = value.toInt();
    QByteArray byteArray = m_CurrentSonoParameters->pV(BFPNames::TGCStr).toByteArray();
    for (int i = 1; i < TGC_COUNT + 1; ++i)
    {
        if (realposition <= m_TGCSegmentPosition[i])
        {
            intValue += (unsigned char)byteArray[i - 1];
            byteArray[i - 1] = qBound(0, intValue, 255);
            break;
        }
    }
    m_CurrentSonoParameters->setPV(BFPNames::TGCStr, byteArray);
    Setting::instance().defaults().setTGCNeedShow(0);
}

void UsContextApiIOS::setFocusPosValue(const int position, const QVariant& value)
{
    int probeID = m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr);
    int imageHeight = m_CurrentSonoParameters->pIV(BFPNames::PointNumPerLineStr);
    double depth = position * m_CurrentSonoParameters->pDV(BFPNames::DepthMMStr) / imageHeight;
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->probe(probeID);
    BFFocusParameters bfFocusParameters;
    int focusPos = bfFocusParameters.getFocusPosByDepthMM(dataInfo, depth, value.toInt());
    m_CurrentSonoParameters->setPV(BFPNames::FocusNumBStr, value.toInt());
    m_CurrentSonoParameters->setPV(BFPNames::FocusPosBStr, focusPos);
}

void UsContextApiIOS::onGraphicsChanged(uchar* graphicsData, int width, int height)
{
    ImageEventArgs args;
    args.setImageData(graphicsData);
    args.setWidth(width);
    args.setHeight(height);

    GraphicsLineImageArgs byteArgs;
    byteArgs.setImageType(GraphicsLineImageArgs::Type_Graphics);
    byteArgs.copyFrom(&args);
    emit graphicsChanged(&byteArgs);
}

void UsContextApiIOS::onGraphicsChanged(int graphicsTextrue)
{
    ImageEventArgs args;
    args.setImageData((uchar*)&graphicsTextrue);

    GraphicsLineImageArgs grphicArgs;
    grphicArgs.copyFrom(&args);
    emit graphicsChanged(&grphicArgs);
}

void UsContextApiIOS::saveCurrentFreqIndexBToJson(const QString& probename, const QString& presetname)
{
    /**
     * @brief UsContextApi::saveCurrentFreqIndexBToJson 保存freqIndexB中的频率索引到probe_preset_freqs.json文件中，
     * 在掌超中预设值中的freqIndexB不正确，掌超统一用json文件中间的频率
     * @param probename
     * @param presetname
     */
    QSharedPointer<IPresetController> presetConfig = PresetConfigFactory::getInstance(
        m_PresetAliasNames, m_ProbeDataSet, m_CurrentSonoParameters, m_GlobalSonoParameters);
    QString userpresetJsonPath = Resource::userPresetJsonPath();
    int freqid = m_CurrentSonoParameters->pIV(BFPNames::FreqIndexBShowStr);
    if (m_PresetAliasNames.contains(probename))
    {
        QStringList freqtxt;
        QList<int> freqidx;
        for (int i = 0; i < m_PresetAliasNames[probename].size(); i++)
        {
            if (m_PresetAliasNames[probename][i].PresetName == presetname)
            {
                freqtxt = m_PresetAliasNames[probename][i].FreqShowNames;
                freqidx = m_PresetAliasNames[probename][i].FreqBIndexs;
                if (freqid != m_PresetAliasNames[probename][i].SelectFreq)
                {
                    presetConfig->updatePresetAndShowName(userpresetJsonPath, probename, presetname, "", "", -1);
                    presetConfig->readPresetConfig();
                }
            }
        }
    }
}

UsRetCode UsContextApiIOS::FinishUpgrade()
{
#ifdef SYS_APPLE
    fpgaupgrademodel_palm::instance().finishUpgrade();
#endif
    return UsOK;
}

UsRetCode UsContextApiIOS::StartUpgrade(char* fpga_binpath)
{
#ifdef SYS_APPLE
    fpgaupgrademodel_palm::instance().setSonoParameters(m_GlobalSonoParameters);
    fpgaupgrademodel_palm::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
    if (fpgaupgrademodel_palm::instance().startUpgrade(fpga_binpath))
        return UsOK;
    else
        return UsFailed;
#endif
    return UsOK;
}
