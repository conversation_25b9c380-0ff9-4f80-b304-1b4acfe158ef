#include "dscanlineglyphtool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "uscontextapi.h"

DScanLineGlyphTool::DScanLineGlyphTool(const QString &name, UsContextApi *usContextApi)
    : GlyphTool(name, usContextApi)
{

}

DScanLineGlyphTool::~DScanLineGlyphTool()
{

}

UsRetCode DScanLineGlyphTool::execute(const int value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    int startline = sp->pV(BFPNames::StartLineStr).toInt();
    int stopline =  sp->pV(BFPNames::StopLineStr).toInt();
    int currentdline = sp->pV(m_Name).toInt();
    int setline = currentdline +  value;
    if(setline < startline)
    {
        setline = startline;
    }
    else if(setline > stopline)
    {
        setline = stopline;
    }
    sp->setPV(m_Name, setline);
    return UsOK;
}
