#include "dscanlineglyphtool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "uscontextapi.h"

DScanLineGlyphTool::DScanLineGlyphTool(const QString &name, UsContextApi *usContextApi)
    : GlyphTool(name, usContextApi)
{

}

DScanLineGlyphTool::~DScanLineGlyphTool()
{

}

UsRetCode DScanLineGlyphTool::execute(const int value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();

    // 2025-01-14 Fix by AI Assistant: 在colorm模式下使用正确的扫描线范围，解决扫查线调节范围异常问题
    int startline, stopline;
    int syncMode = sp->pV(BFPNames::SyncModeStr).toInt();
    if ((syncMode & 0x02) == 0x02 || (syncMode & 0x04) == 0x04) // Sync_C or Sync_D
    {
        // colorm模式下使用StartLineColorStr/StopLineColorStr作为调节边界
        startline = sp->pV(BFPNames::StartLineColorStr).toInt();
        stopline = sp->pV(BFPNames::StopLineColorStr).toInt();
    }
    else
    {
        // B模式下使用StartLineStr/StopLineStr作为调节边界
        startline = sp->pV(BFPNames::StartLineStr).toInt();
        stopline = sp->pV(BFPNames::StopLineStr).toInt();
    }

    int currentdline = sp->pV(m_Name).toInt();
    int setline = currentdline +  value;
    if(setline < startline)
    {
        setline = startline;
    }
    else if(setline > stopline)
    {
        setline = stopline;
    }
    sp->setPV(m_Name, setline);
    return UsOK;
}
