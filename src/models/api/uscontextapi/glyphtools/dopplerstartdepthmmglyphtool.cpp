#include "dopplerstartdepthmmglyphtool.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "uscontextapi.h"

DopplerStartDepthMMGlyphTool::DopplerStartDepthMMGlyphTool(const QString &name, UsContextApi *usContextApi)
    : GlyphTool(name, usContextApi)
{

}

DopplerStartDepthMMGlyphTool::~DopplerStartDepthMMGlyphTool()
{

}

UsRetCode DopplerStartDepthMMGlyphTool::execute(const int value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    double valuestep = value/10.0;
    double dopplerstartdepth = sp->pV(m_Name).toDouble();
    double depth = sp->pV(BFPNames::DepthMMStr).toDouble();
    double setdepth = dopplerstartdepth+valuestep;
    if(setdepth < 0)
    {
        setdepth = 0;
    }
    else if(setdepth > depth )
    {
        setdepth = depth;
    }
    sp->setPV(m_Name, setdepth);
    return UsOK;
}
