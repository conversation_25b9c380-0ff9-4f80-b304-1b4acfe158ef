#include "glyphtool.h"
#include "sonoparameters.h"
#include "uscontextapi.h"

GlyphTool::GlyphTool(const QString &name, UsContextApi *usContextApi)
    : m_Name(name)
    , m_UsContextApi(usContextApi)
{

}

GlyphTool::~GlyphTool()
{

}

QString GlyphTool::name() const
{
    return m_Name;
}

UsRetCode GlyphTool::execute(const int value)
{
    SonoParameters *sp = m_UsContextApi->currentSonoParameters();
    int currentv = sp->pV(m_Name).toInt();
    int maxv = sp->pMax(m_Name);
    int minv = sp->pMin(m_Name);
    if(currentv + value >= minv && currentv + value <= maxv)
    {
        sp->setPV(m_Name, currentv + value);
        return UsOK;
    }
    else
    {
        return UsFailed;
    }
}
