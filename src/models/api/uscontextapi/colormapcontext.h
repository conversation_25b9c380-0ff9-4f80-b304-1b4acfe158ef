#ifndef COLORMAPCONTEXT_H
#define COLORMAPCONTEXT_H

#include <QObject>

class ImageRender;
class IBFRawData2ShowData;
class SonoParameters;
class IMuiltSonoParametersBuffer;
class IColorMapManager;

class ColorMapContext : public QObject
{
    Q_OBJECT
  public:
    enum ColorMapUsedType
    {
        RealTime,
        Non_RealTime
    };
    ColorMapContext(QObject *parent = 0);
    ~ColorMapContext();
    void create(IBFRawData2ShowData *value);
    void setType(ColorMapUsedType type);
    void getColorMaps(void **map1, void **map2);
    void setSonoParameters(SonoParameters *sonoParameters);
    void onSetSonoParameters();
    ImageRender *imageRender() const;

    /** 2024-08-26 Write by AlexWang
     * @brief setColorMapManager
     * @param value
     */
    void setColorMapManager(IColorMapManager *value);

  public slots:
    void onFreezeChanged(const QVariant &value);
    void onActiveBChanged(const QVariant &value);
  signals:
    void colorMapChanged();

  private:
    void connectParameterSignals();
    bool isMultiLayout();

  private:
    ImageRender *m_ImageRender;
    ColorMapUsedType m_Type;
    IMuiltSonoParametersBuffer *m_BufferManager;
    SonoParameters *m_SonoParameters;
};

#endif // COLORMAPCONTEXT_H
