#ifndef USCONTEXTAPI_H
#define USCONTEXTAPI_H

#include "biopsyconfig.h"
#include "colormapcontextmanager.h"
#include "imageeventargs.h"
#include "infostruct.h"
#include "usapidef.h"
#include "usapiinfostruct.h"
#include <QMutex>
#include <QObject>
#include <QString>
#include <QTimer>
#include <QVariant>

#if defined(SYS_APPLE) | defined(USE_GETREAMER)
#include "glprocesscreator.h"
#include "roiglyphscontrol.h"
#endif

class IUSAPIFactoriesContext;
class ExamMode;
class LineCineLooper;
class SonoParameters;
class ChisonUltrasoundContext;
class BufferStorer;
class PhysicalGeometryController;
class PresetParameters;
class ByteLineImageArgs;
class MeasureContext;
class QGraphicsTextItem;
class IDataBaseManager;
class IProbeDataSet;
class IStateManager;
class IColorMapManager;
class IExamModePresetDataHandlerProvider;
class IImageSaveHelper;
class IElementDetectAlgorithom;

class UsContextApi : public QObject
{
    Q_OBJECT
public:
    enum CallbackType
    {
        UsData_CT = 0,
        UsPara_CT,
        UsProbeInfo_CT,
        UsProbeEvent_CT,
        UsFpgaUpgrade_CT,
        UsVAInfo_CT,
        UsImageNoChange_CT,
        UsProbeSelfTest_CT,
        UsCROIChanged_CT,
        UsFunctionStateChanged_CT,
        UsCommunicationError_CT,
        UsFanError_CT,
        UsRawData_CT,
        UsProbeAssessmentProgress_CT,
        UsProbeAssessmentResult_CT,
        UsCount_CT
    };
    typedef void (UsContextApi::*PositionParameterHandle)(const int position, const QVariant& value);

    explicit UsContextApi(MachineType type = HumanMachine);
    virtual ~UsContextApi();

    virtual void initialize(int width = -1, int height = -1);

    UsRetCode humanAnimalSwitch(MachineType type);

    UsRetCode setCallbackInfo(CallbackType type, void* callback, void* userData);

    virtual void setWifiWriteCallback(UsWifiWriteCallback callback, void* userData);

    virtual void pushData(unsigned char* data, int size);

    virtual UsRetCode start();

    UsRetCode stop();

    virtual UsRetCode setScanMode(UsScanMode scanMode);

    UsScanMode getScanMode() const;

    virtual UsRetCode setLayout(UsLayout layout);

    UsLayout getLayout() const;

    virtual UsRetCode setActiveLayoutIndex(int activeIndex);

    int getActiveLayoutIndex() const;

    UsRetCode setFreeze(int isFrozen);

    int isFrozen() const;

    UsRetCode standby();

    UsRetCode wake();

    /**
     * Probe 相关接口
     */
    void setProbeConnected(int isconnected);

    UsRetCode setCurrentProbe(const char* probeName);

    UsRetCode getCurrentProbe(UsProbeDataInfo* probeDataInfo) const;

    int getAllSupportProbeNames(char** probeNames, const int arraySize, const int itemSize);

    UsRetCode getProbeShowNameByProbeName(const char* const probename, char* probeShowName);

    UsRetCode getCurrentProbeShowName(char* probeShowName);

    UsRetCode GetCurrentProbeModeName(char* probeModeName, const int itemSize);

    UsRetCode GetCurrentPresetProbeName(char* probeModeName, const int itemSize);

    UsRetCode getProbeStatus(ProbeStatus* probestatus);

    virtual UsRetCode probeShutDown();
    /**
     * 预设值相关接口
     */
    UsRetCode loadPreset(const char* const fileName);

    UsRetCode savePreset(const char* const fileName);

    int getCurrentProbePreset(char** preset, const int arraySize, const int bufferSize);

    int getProbePreset(const char* probeName, char** preset, const int arraySize, const int bufferSize);

    int getProbePreset(const int probeID, char** preset, const int arraySize, const int bufferSize);

    UsRetCode getPresetNameByShowName(const char* presetShowname, char* presetName);

    virtual bool selectPreset(const char* presetName);

    virtual int getAllPreset(char** preset, const int arraySize, const int bufferSize);

    virtual bool saveCurrentPreset();

    virtual bool saveCurrentPresetAs(const char* presetName);

    virtual bool renameCurrentPreset(const char* presetName);

    virtual bool renamePreset(const char* oldPresetName, const char* newPresetName);

    virtual bool deleteCurrentPreset();

    virtual bool deletePreset(const char* presetName);

    virtual UsRetCode updateUserPreset(const char* oldPresetShowName, const char* newPresetShowname,
                                       const int* orderList, const int orderListLength, const int code);

    virtual bool importPreset(const char* const destPath);

    virtual bool exportPreset(const char* const destPath);

    virtual bool restorePreset();

    /**
     * 电影保存、回调、索引获取、设置相关
     */
    UsRetCode saveCine(const char* const fileName, int startIndex, int endIndex);

    UsRetCode saveImage(const char* const fileName);

    UsRetCode loadCine(const char* fileName);

    UsRetCode loadImage(const char* fileName);

    UsRetCode endLoad();

    int getStartIndex() const;

    UsRetCode setStartIndex(const int startIndex);

    int getEndIndex() const;

    UsRetCode setEndIndex(const int endIndex);

    int getCurrentIndex() const;

    UsRetCode setCurrentIndex(const int currentIndex);

    int getFrameCount();

    UsRetCode startPlayLoop();

    UsRetCode stopPlayLoop();

    UsRetCode startPlayNext(int n);

    UsRetCode setPlaySpeed(UsPlaySpeed speed);

    /**
     * 参数设置相关
     */
    virtual UsRetCode setParameterIntAtPosition(const char* const name, int position, int value);

    virtual UsRetCode setParameter(const char* const name, const QVariant& value);

    virtual UsRetCode getParameter(const char* const name, QVariant& value);

    virtual UsRetCode getParameterIntMin(const char* const name, int* value);

    virtual UsRetCode getParameterIntStep(const char* const name, int* value);

    virtual UsRetCode getParameterIntMax(const char* const name, int* value);

    virtual UsRetCode getParameterShowText(const char* const name, char* value, const int count);

    virtual UsRetCode getParemeterShowValue(const char* const name, QVariant& value);

    qreal getRealPixelSizeMM();

    UsRetCode getAdditionalParamInt(const char* const name, int* value);

    /**
     * 坐标转换相关  （0，0）的位置是图像横坐标的一半
     */
    UsRetCode convertPhysicsToLogic(const double line, const double depth, UsLogicalPoint* logicalPoint);

    UsRetCode convertPhysicsToLogicF(const double line, const double depth, UsLogicalPoint* logicalPoint);

    UsRetCode convertLogicToPhysics(const UsLogicalPoint* logicalPoint, double* line, double* depth);

    UsRetCode convertLogicFToPhysics(const UsLogicalPoint* logicalPoint, double* line, double* depth);

    /**
     * 坐标转换相关  （0，0）的位置是图像左上角
     */
    UsRetCode convertPhysicsToLogicInFullImage(const double line, const double depth, UsLogicalPoint* logicalPoint);

    UsRetCode convertPhysicsToLogicFInFullImage(const double line, const double depth, UsLogicalPoint* logicalPoint);

    UsRetCode convertLogicToPhysicsInFullImage(const UsLogicalPoint* logicalPoint, double* line, double* depth);

    UsRetCode convertLogicFToPhysicsInFullImage(const UsLogicalPoint* logicalPoint, double* line, double* depth);
    /**
     * 图元相关
     */
    virtual UsScene graphicsScene();

    virtual UsOverlay graphicsOverlay();

    virtual UsGlyphsControl glyphsControl(GlyphsControlType ctrType);

    virtual UsRetCode geometryMove(const int offsetX, const int offsetY);

    virtual UsRetCode geometryChangeSize(const int sizeX, const int sizeY);

    UsRetCode geometryScale(const int scaleX, const int scaleY);

    virtual UsRetCode widgetUnitControlUpdate(const char* name, const int value);

    virtual UsRetCode getCROIPoints(_UsLogicalPoint_INT* pointArray, const int count, int* validCount);
    /**
     * biopsy
     */
    UsRetCode getBiopsyAngleList(int* angle, int* size);

    UsRetCode getBiopsyAngle(int* posX, int* posY, int* angle);

    UsRetCode selectBiopsyAngle(int angle, int* biopsyposX, int* biopsyposY, int* angleRange, int* offsetRangeX,
                                int* offsetRangeY);

    UsRetCode saveCurrentBiopsyAngle();

    UsRetCode biopsyAngleMove(int angle, int biopsyposX, int biopsyposY);

    void biopsyAngleReset();

    /**
     * panzoom 参数
     */
    UsRetCode getCurrentDscImageTransfromInformation(DscImageTransfromInformation* ImageDscInfo);

    virtual UsRetCode imageTransform(double scale, double translateX, double translateY);

    virtual UsRetCode imageTransformReset();

    /**
     * license and version
     */
    int licenseRead(Plicense* pli, bool force_update);

    int licenseWrite(char* key, char* sn, int keytype);

    void version(char* dscVersion, char* ecVersion);

    /**
     * fpga update
     */
    virtual UsRetCode FinishUpgrade();

    virtual UsRetCode StartUpgrade(char* fpga_binpath);

    virtual UsRetCode asyncStartUpgrade(char* fpga_binpath);

    virtual UsRetCode getHardwareVersion(QString& version);

    /**
     * 调试支持
     */
    UsRetCode importControlTable(const char* filePath);

    UsRetCode importControlTable(const char* filePath, const int start, const int end);

    /**
     * 配置修改
     */
    UsRetCode setPenStyle(const int penStyle);

    UsRetCode setROIPenStyle(bool isResizing);

    void setTgcShowTime(int time);

    /** 2024-08-05 Write by AlexWang
     * @brief setBeforeZeusSaveFlag
     * @param beforeZeusSaveFlag
     */
    void setBeforeZeusSaveFlag(bool beforeZeusSaveFlag);

    /** 2024-08-05 Write by AlexWang
     * @brief setAfterZeusSaveFlag
     * @param afterZeusSaveFlag
     */
    void setAfterZeusSaveFlag(bool afterZeusSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief setBeforeIImageSaveFlag
     * @param beforeIImageSaveFlag
     */
    void setBeforeIImageSaveFlag(bool beforeIImageSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief setAfterIImageSaveFlag
     * @param afterIImageSaveFlag
     */
    void setAfterIImageSaveFlag(bool afterIImageSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief exportProperties
     */
    void exportProperties();

    /**
     * 其他杂项
     */
    // 视频导出
    virtual UsRetCode exportMedia(uint8_t* backgroundImage, int width, int height, int pastex, int pastey,
                                  int mediatype, int isdicom, UsConvertProcessCallback callback);

    virtual void restoreAfterExportVideo(const char* const cinepath, int currentindex);

    // 处理事件
    void processEvents(int time);
    // 关闭、打开QGraphicsScene相关信号
    void setBackGround(int state);

    // 调用aio功能
    UsRetCode AIO();

    // 获取测量接口
    virtual UsMeasureContext measureContext();

    virtual UsRetCode updateMeasureContext(bool updateImage);

    // 图像补偿 图像红绿蓝调整系数
    virtual void setImageColorFilter(float* filterArray);
    // 联机调图
    UsRetCode connect2ParamsAdjustMachine();

    SonoParameters* currentSonoParameters() const;
    SonoParameters* globalSonoParameters() const;
    bool isLooping() const;
    bool isLoad() const;
    IUSAPIFactoriesContext* usAPIFactoriesContext() const;
    const QStringList freqBShowText() const;
    QHash<QString, QList<PresetAliasName>>& presetAliasNames();

    virtual bool functionOpened(UsFunctionType funcType);
    virtual bool isSupportFunction(UsFunctionType funcType);
    virtual UsRetCode openFunction(UsFunctionType funcType);
    virtual UsRetCode closeFunction(UsFunctionType funcType);
    //发货模式
    UsRetCode onDeliverMode();

    virtual UsRetCode setImageCheckTime(int timeSec);
    virtual UsRetCode getImageCheckTime(int* timeSec);

    virtual UsRetCode setImageCheckState(bool open);
    virtual UsRetCode getImageCheckState(bool* open);
    virtual UsRetCode setImageCheckSensitiveThreshold(float threshold);
    virtual UsRetCode getImageCheckSensitiveThreshold(float* tThreshold);

    virtual UsRetCode autoProbeSelfTest(int type, int second);
    virtual UsRetCode singleProbeSelfTest(int type, int num);
    virtual UsRetCode stopProbeSelfTest();
    virtual UsRetCode getProbeValidElementCounts(int* counts);
    virtual UsRetCode openProbeSelfTest();
    virtual UsRetCode closeProbeSelfTest();
    virtual UsRetCode getDepthMMByCQYZLevel(const QString& probename, int level, double* depth);
    virtual UsRetCode getDepthMMByCQYZ(const QString& probename, int level, double* depth);
    virtual UsRetCode getFocusPosDepthMM(int focusNum, int focusPos, float* depth);
    virtual UsRetCode setIImageType(int valueType);
    virtual UsRetCode getIImageType(int* valueType);

    bool isProbeConnected() const
    {
        return m_IsValidProbeConnected;
    }

    /** 2024-06-20 Write by AlexWang
     * @brief getAllParamterNames
     * @param paraNames
     * @param arraySize
     * @param itemSize
     * @return
     */
    int getAllParamterNames(char** paraNames, int arraySize, int itemSize);

    IProbeDataSet* probeDataSet() const;

protected:
    virtual void initializeBeamformer();

    /** 2024-06-18 Write by AlexWang
     * @brief initializeProbePresetModel
     */
    virtual void initializeProbePresetModel();

    virtual void initializeChisonUltrasoundContext();
    virtual void initializeGraphicsContext();
    virtual void initializeColorMap();
    virtual void initializePositionParameterHandle();
    virtual void initializePreset();
    virtual void initializeSpecialParameters();
    virtual void initializeGlyphTools();
    virtual void initializeAdditionalParamTools();

    virtual void sonoParametersChanged(SonoParameters* parameters);
    virtual void connectParametersChanged(SonoParameters* sonoParameters);
    virtual void disConnectParametersChanged(SonoParameters* sonoParameters);

    virtual void doingOnSettingFreeze();

    virtual int updateBeamFormerFreqText(const QString& probeName, const QString& presetName);

    virtual void setExamModeAliasName(ExamMode& mode, const ProbeDataInfo& dataInfo);

    virtual void onCurrentSonoParametersChanging(SonoParameters* sonoParameters);

    virtual void updateCustomGrayifNeeded();

    virtual void onSaveCurrentPreset();
    /**
     * @brief clearFrameDsiplay
     * 清除当前显示的图像 包括图元部分
     */
    virtual void clearFrameDsiplay();
#ifdef USE_VA
    virtual void initializeVAContext();
#endif
    virtual void lazyFreeze();

    virtual void startLogger();
    virtual void modifyLoggerConfFile();

protected slots:
    virtual void onHWInfoUpdate(unsigned char* probehwinfo, int len);

    virtual void onProbeChanged(QVector<int> probelist, QVector<bool> changes);

    virtual void onParametersChanged();

    virtual void onDoneSettingFreeze();

    void onScaleTimerOut();
    // fpga 升级回调
    void fpgaUpgradeStatus(int ret, int process);

    void setColorVelocity();

    void onProbeConnectedChanged(const QVariant& value);

    void onColorMapChanged();

    virtual void onNewImage(ImageEventArgs* imageEventArgs);
    virtual void onRawNewImage(void* data, int width, int height, int bitCount, int layoutIndex);

    void setGeometrycontrol(const QVariant& v);

    virtual void onImageTransformChanged(bool preset = false);

    virtual void sceneModified(const QList<QRectF>& qlistr);

    void onGraphicsChanged(uchar* graphicsData, int width, int height);

    void onGraphicsChanged(int graphicsTextrue);

    // 联机调图
    void onRecvParamsChgByImgAdjustMachine(QString& sonoparamstr, QVariant& sonoparamval);
#ifdef USE_VA
    virtual void onVAResultUpdated(const QVariant&);
#endif

    void onFunctionStateChanged(int type, bool isOn);
#ifdef USE_VA
    virtual void onVAResultUpdated(const QVariant&);
#endif

#ifndef SYS_ANDROID
    void onElementTestingIndex(int elementIndex);

    void onElementTestProgressChanged(int progress, int elementNum);
    void onElementTestResultUpdated(const QList<ElementDetectResult>& result);
#endif

protected:
    bool checkSave(BufferStorer& bufferStorer, const char* const fileName, const int startIndex, const int endIndex);
    bool load(const char* fileName);

    virtual void convertPhysicsFToLogic(const double line, const double depth, QPointF& pt);
    virtual void convertLogicFToPhysics(const QPointF& pt, double* line, double* depth);
    void changeGeometryController(SystemScanMode mode);

    inline void pwDelayFreqSpectrumSetting(int dms);

    virtual ImageEventArgs getCurrentFrame();

    void setColorMap(ColorMapContext* context, int displayIndex = 0);

    void tryUnfreeze();
    void initErrorCode();
signals:
    void presetChanged(const PresetParameters& value);
    void graphicsChanged(ByteLineImageArgs*);
    void layoutChanged(int layoutNumber);
    void systemScanModeChanged(int systemScanMode);
    void activeIndexChanged(int activeIndex);

private slots:
    void onFreezeChangedInProbeAssessment(const QVariant& value);

protected:
    typedef QPair<void*, void*> CallbackInfo;
    CallbackInfo m_CallbackInfo[UsCount_CT];
    QHash<QString, PositionParameterHandle> m_PositionHadnles;
    /**
     * @brief m_IsPaused 标记当前是否输出图像
     */
    bool m_IsPaused;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
    SonoParameters* m_GlobalSonoParameters;
    SonoParameters* m_CurrentGlobalSonoParameters;
    SonoParameters* m_CurrentSonoParameters;

    IUSAPIFactoriesContext* m_USAPIFactoriesContext;
    ChisonUltrasoundContext* m_ChisonUltrasoundContext;
    LineCineLooper* m_LineCineLooper;
    // 穿刺线
    BiopsyConfig::Biopsy m_Currentbiopsy;
    // 预设值别名
    QHash<QString, QList<PresetAliasName>> m_PresetAliasNames;

    ColorMapContextManager m_ColorMapContextManager;
    /**
     * @brief m_IsLoad 标记当前是否回调了数据
     */
    bool m_IsLoad;
    bool m_Wit2LoopStop;
    PhysicalGeometryController* m_CurrentController; // 当前的图形控制
    MeasureContext* m_MeasureContext;
    ImageEventArgs* m_BackupImageForMeasure;
    QMutex m_GlyphToolMutex;
    QMutex m_AdditionalParamToolMutex;
    QTimer* m_SetScaleTimer;
    QGraphicsTextItem* m_VelocityDown;
    QGraphicsTextItem* m_VelocityUp;
    QStringList m_FreqBShowText;
    int m_CurrentShowIndex;
    bool m_AppStateChange;
#if defined(SYS_APPLE) | defined(USE_GETREAMER)
    GLProcessCreator m_GLProcessCreator;
#endif
#ifdef USE_VA
    const static int MaxVesselNum = 10;
    UsVesselInfo m_vesselInfo[MaxVesselNum];
#endif
    static QHash<qint32, UsUltrasoundErrorCode> ErrorCodeHash;
    bool m_IsValidProbeConnected;
    const static int MaxElementNum = 200;
    IDataBaseManager* m_DataBaseManager;
    IProbeDataSet* m_ProbeDataSet;
    IExamModePresetDataHandlerProvider* m_PresetDataHandlerProvider;
    IImageSaveHelper* m_ImageSaveHelper;
    IElementDetectAlgorithom* m_ElementDetectAlgorithom;
};

#endif // USCONTEXTAPI_H
