#ifndef IUSAPIFACTORIESCONTEXT_H
#define IUSAPIFACTORIESCONTEXT_H

#include "bfparameterloader.h"
#include "usapidef.h"

class BeamFormerBase;
class QGraphicsLineItem;
class QGraphicsScene;
class SonoParameters;
class ProbeDataInfo;
class IProbePresetModel;
class IStateManager;
class IColorMapManager;

class IUSAPIFactoriesContext
{
public:
    static IUSAPIFactoriesContext* factoriesContext(const QString& name, QObject* parent = nullptr);

    virtual ~IUSAPIFactoriesContext();

    virtual void initialize(int width = -1, int height = -1) = 0;

    virtual BeamFormerBase* beamFormer() const = 0;

    virtual QVector<float> dopplerSteeringAngles() const = 0;

    virtual void updateFreqIndexBText(const QStringList& freqtxt, const QList<int>& freqidx) = 0;

    virtual QGraphicsLineItem* updateGraphicsItemColor(QGraphicsScene* scene, int tagid, const QColor& color) = 0;

    virtual void updateCustomGrayifNeeded(SonoParameters* sonoParameters) = 0;

    virtual bool externalIODeviceIsPaused() const = 0;
    virtual void setExternalIODeviceWriteCallback(UsWifiWriteCallback callback, void* userData) = 0;
    virtual void pushExternalIODeviceData(unsigned char* data, int size) = 0;
    virtual void setExternalIODevicePasuedStatus(const bool status) = 0;

    /** 2024-06-11 Write by AlexWang
     * @brief setCurrentProbe 设置当前探头
     * @param probeInfo
     * @return
     */
    virtual bool setCurrentProbe(const ProbeDataInfo& probeInfo) = 0;

    /** 2024-06-18 Write by AlexWang
     * @brief probePresetModel
     * @return
     */
    virtual IProbePresetModel* probePresetModel() = 0;

    /** 2024-08-15 Write by AlexWang
     * @brief setStateManager
     * @param value
     */
    virtual void setStateManager(IStateManager* value) = 0;

    /** 2024-08-26 Write by AlexWang
     * @brief setColorMapManager
     * @param value
     */
    virtual void setColorMapManager(IColorMapManager* value) = 0;
};

#endif // IUSAPIFACTORIESCONTEXT_H
