#include "uscontextapip9.h"
#include "applogger.h"
#include "beamformerbase.h"
#include "bfpnames.h"
#include "chisonultrasoundcontext.h"
#include "chisonultrasoundtypes.h"
#include "glprocesscontext.h"
#include "glprocesscreator.h"
#include "iusapifactoriescontext.h"
#include "log4qt.h"
#include "resource.h"
#include "setting.h"
#include "sonoparameters.h"
#include <QApplication>
#include <QDir>
#include <QtGlobal>
#ifdef USE_IIMAGE
#include "iimageprocess.h"
#endif
#include "autofreezesetting.h"
#include "bfcoordtransform.h"
#include "databasemanager.h"
#include "exammodepresethandlerfactory.h"
#include "fpgaupgrademodel.h"
#include "framecontrol.h"
#include "infostruct.h"
#include "modelconfig.h"
#include "physicalgeometrycontroller.h"
#include "probedatasetfacade.h"
#include "probepresetmodel.h"
#include "qapp.h"
#include "util.h"
#include <QtMath>

#include "presetconfigfactory.h"
UsContextApiP9::UsContextApiP9()
    : UsContextApi()
    , m_UsApiParameterFilter(new UsApiParameterFilter)
    , m_ProbeKeyState(NULL)
    , m_ProbeKeyCount(0)
{
    m_UsApiParameterFilter->setProbeDataSet(m_ProbeDataSet);

    m_IsValidProbeConnected = false;
    m_ProbeKeyCount = ModelConfig::instance().value(ModelConfig::MaxProbeButtonCount, 0).toInt();
    if (m_ProbeKeyCount > 0)
    {
        m_ProbeKeyState = new unsigned int[m_ProbeKeyCount];
    }

    ExamModePresetHandlerFactory::instance()->setDataHandlerProvider(m_PresetDataHandlerProvider);
    ExamModePresetHandlerFactory::instance()->create(
        ModelConfig::instance().value(ModelConfig::ModelFilesUseIni, true).toBool()
            ? PresetModule::DataHandlerType::DataHandler
            : PresetModule::DataHandlerType::FileHandler,
        m_DataBaseManager, m_ProbeDataSet);
    if (!QDir(Resource::logDir).exists())
    {
        QDir dir;
        dir.mkdir(Resource::logDir);
    }
#ifdef USE_IIMAGE
    IImageProcess::instance()->config();
#endif
    qRegisterMetaType<int>("int&");
    qRegisterMetaType<QVector<quint8>>("QVector<quint8>");
    qRegisterMetaType<QVector<quint8>>("QVector<quint8>&");
    Logger4QT::instance();
    //    if(AppLogger::fileLog())
    //    {
    //        qInstallMessageHandler(customMessageHandler);
    //    }

    // SR9 API 由于需要开放参数给客户设置，自动调节焦点功能可能会导致FocusPosB参数设置值无效，先SR9API上关闭该功能
    Setting::instance().defaults().setIntelligentFocus(false);
}

UsRetCode UsContextApiP9::start()
{
    UsRetCode code = UsContextApi::start();
    m_IsValidProbeConnected = false;
    return code;
}

UsContextApiP9::~UsContextApiP9()
{
    if (m_UsApiParameterFilter != NULL)
    {
        delete m_UsApiParameterFilter;
    }
    if (m_ProbeKeyState != NULL)
    {
        delete[] m_ProbeKeyState;
    }

    if (m_DataBaseManager != NULL)
    {
        delete m_DataBaseManager;
        m_DataBaseManager = NULL;
    }
}

UsRetCode UsContextApiP9::setScanMode(UsScanMode scanMode)
{
    if (m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        return UsFailed;
    }
    if (scanMode != UsSystemScanModeB && scanMode != UsSystemScanModeColorDoppler)
    {
        return UsFailed;
    }
    if (!m_IsValidProbeConnected)
    {
        return UsNotSupportNow;
    }
    if (UsContextApi::functionOpened(UsFunctionType::UsVADetect))
    {
        closeFunction(UsFunctionType::UsVADetect);
    }
    UsScanMode oldScanMode = getScanMode();
    if (oldScanMode != scanMode)
    {
        Log4Qt::Logger::rootLogger()->info("%1 scanMode: from  %2 to %3", PRETTY_FUNCTION, oldScanMode, scanMode);
        m_USAPIFactoriesContext->beamFormer()->setSystemScanMode((SystemScanMode)scanMode);
        changeGeometryController((SystemScanMode)m_CurrentSonoParameters->pIV(BFPNames::SystemScanModeStr));
    }
    return UsOK;
}

UsRetCode UsContextApiP9::geometryMove(const int offsetX, const int offsetY)
{
    if (NULL == m_CurrentController || !m_CurrentController->isActive())
    {
        return UsFailed;
    }
    m_CurrentController->move(QPoint(offsetX, offsetY));
    return UsOK;
}

UsRetCode UsContextApiP9::geometryChangeSize(const int sizeX, const int sizeY)
{
    if (NULL == m_CurrentController || !m_CurrentController->isActive())
    {
        return UsFailed;
    }
    m_CurrentController->changeSize(QPoint(sizeX, sizeY));
    return UsOK;
}
UsRetCode UsContextApiP9::setLayout(UsLayout layout)
{
    return UsFailed;
}

UsRetCode UsContextApiP9::setActiveLayoutIndex(int activeIndex)
{
    return UsFailed;
}

UsRetCode UsContextApiP9::setParameter(const char* const name, const QVariant& value)
{
    if (name == NULL || m_CurrentSonoParameters->parameter(name)->isNull() ||
        !m_CurrentSonoParameters->checkValueValid(name, value))
    {
        return UsInvalidArgs;
    }
    if (!m_UsApiParameterFilter->activeable(name) || !m_IsValidProbeConnected)
    {
        return UsNotSupportNow;
    }
    FrameControl::instance().resetStartTime();
    Log4Qt::Logger::rootLogger()->info("%1 name: %2 value: %3", PRETTY_FUNCTION, name,
                                       value.type() == QVariant::ByteArray ? value.toByteArray().toHex() : value);
    m_CurrentSonoParameters->setPV(name, value);
    return UsOK;
}

UsRetCode UsContextApiP9::getParameter(const char* const name, QVariant& value)
{
    if (m_UsApiParameterFilter->contains(name))
    {
        return UsContextApi::getParameter(name, value) ? UsOK : UsFailed;
    }
    return UsInvalidArgs;
}

UsRetCode UsContextApiP9::getParameterIntMin(const char* const name, int* value)
{
    if (!m_UsApiParameterFilter->contains(name))
    {
        return UsInvalidArgs;
    }

    return UsContextApi::getParameterIntMin(name, value);
}

UsRetCode UsContextApiP9::getParameterIntStep(const char* const name, int* value)
{
    if (!m_UsApiParameterFilter->contains(name))
    {
        return UsInvalidArgs;
    }

    return UsContextApi::getParameterIntStep(name, value);
}

UsRetCode UsContextApiP9::getParameterIntMax(const char* const name, int* value)
{
    if (!m_UsApiParameterFilter->contains(name))
    {
        return UsInvalidArgs;
    }

    return UsContextApi::getParameterIntMax(name, value);
}

UsRetCode UsContextApiP9::getParameterShowText(const char* const name, char* value, const int count)
{
    if (!m_UsApiParameterFilter->contains(name))
    {
        return UsInvalidArgs;
    }
    return UsContextApi::getParameterShowText(name, value, count);
}

void UsContextApiP9::initializeGraphicsContext()
{
    connect(m_ChisonUltrasoundContext, SIGNAL(newImage(ImageEventArgs*)), this, SLOT(onNewImage(ImageEventArgs*)),
            Qt::DirectConnection);
    connect(m_ChisonUltrasoundContext, SIGNAL(rawDataInfo(void*, int, int, int, int)), this,
            SLOT(onRawNewImage(void*, int, int, int, int)), Qt::DirectConnection);
#ifdef USE_IIMAGE
    Util::Mkdir("cl_cache");
#endif
}
void UsContextApiP9::sonoParametersChanged(SonoParameters* parameters)
{
    m_UsApiParameterFilter->setSonoParameters(parameters);
}

void UsContextApiP9::connectParametersChanged(SonoParameters* sonoParameters)
{
    QList<QString> parameterNames = m_UsApiParameterFilter->parameters();
    foreach (const QString& pName, parameterNames)
    {
        connect(sonoParameters->parameter(pName), SIGNAL(valueChanged(QVariant)), this, SLOT(onParametersChanged()),
                Qt::DirectConnection);
    }
}

void UsContextApiP9::disConnectParametersChanged(SonoParameters* sonoParameters)
{
    QList<QString> parameterNames = m_UsApiParameterFilter->parameters();
    foreach (const QString& pName, parameterNames)
    {
        disconnect(sonoParameters->parameter(pName), SIGNAL(valueChanged(QVariant)), this, SLOT(onParametersChanged()));
    }
}

void UsContextApiP9::onParametersChanged()
{
    Parameter* parameter = dynamic_cast<Parameter*>(sender());
    CallbackInfo ci = m_CallbackInfo[UsPara_CT];
    if ((m_UsApiParameterFilter->contains(parameter->name())) && (ci.first != NULL))
    {
        ((UsParaChangedCallback)ci.first)(this, parameter->name().toStdString().c_str(),
                                          m_CurrentGlobalSonoParameters->pIV(BFPNames::ActiveBStr), ci.second);
    }
}

bool UsContextApiP9::selectPreset(const char* presetName)
{
    if (!UsContextApi::selectPreset(presetName))
    {
        return false;
    }
    m_GlobalSonoParameters->setPV(BFPNames::TrapezoidalModeStr, false);
    return true;
}

UsRetCode UsContextApiP9::FinishUpgrade()
{

    return UsOK;
}

UsRetCode UsContextApiP9::StartUpgrade(char* fpga_binpath)
{
    if (!isFrozen())
    {
        m_ChisonUltrasoundContext->prepareLoadImage();
        setFreeze(1);
    }
    FPGAUpgradeModel::instance().setSonoParameters(m_GlobalSonoParameters);
    FPGAUpgradeModel::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
    QString fileFolderPath = fpga_binpath;
    if (FPGAUpgradeModel::instance().checkMD5(fileFolderPath))
    {
        Log4Qt::Logger::rootLogger()->info("%1 fpga_binpath: %2", PRETTY_FUNCTION, fpga_binpath);
        FPGAUpgradeModel::instance().upgradeFpga(fileFolderPath);
        return UsOK;
    }
    return UsFailed;
}

UsRetCode UsContextApiP9::asyncStartUpgrade(char* fpga_binpath)
{
    if (!isFrozen())
    {
        m_ChisonUltrasoundContext->prepareLoadImage();
        setFreeze(1);
    }
    FPGAUpgradeModel::instance().setSonoParameters(m_GlobalSonoParameters);
    FPGAUpgradeModel::instance().setControlTable(m_USAPIFactoriesContext->beamFormer()->getControlTable());
    QString fileFolderPath = fpga_binpath;
    if (FPGAUpgradeModel::instance().checkMD5(fileFolderPath))
    {
        Log4Qt::Logger::rootLogger()->info("%1 fpga_binpath: %2", PRETTY_FUNCTION, fileFolderPath);
        FPGAUpgradeModel::instance().asyncupgradeFpga(fileFolderPath);
        return UsOK;
    }
    return UsFailed;
}

void UsContextApiP9::initializeBeamformer()
{
    UsContextApi::initializeBeamformer();
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(probeKeyStateChanged(QVector<quint8>)), this,
            SLOT(onProbeKeyStateChanged(QVector<quint8>)));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(BFIODeviceOpenError(int)), this,
            SLOT(onDeviceOpenError(int)));
    connect(m_USAPIFactoriesContext->beamFormer(), SIGNAL(fanSpeedUpdated(QVector<LineData::FanInfo>)), this,
            SLOT(onFanSpeedUpdated(QVector<LineData::FanInfo>)));
}

void UsContextApiP9::initializeProbePresetModel()
{
    if (m_USAPIFactoriesContext->probePresetModel())
    {
        connect(m_USAPIFactoriesContext->probePresetModel(), SIGNAL(currentProbeDisconnected()), this,
                SLOT(onAutoFreeze()));
    }
}

void UsContextApiP9::initializeSpecialParameters()
{
    connect(m_ChisonUltrasoundContext, SIGNAL(datainfo(void*, int, int, int, int)), &FrameControl::instance(),
            SLOT(datainfo(void*, int, int, int, int)), Qt::DirectConnection);
    FrameControl::instance().setSonoParameters(m_ChisonUltrasoundContext->sonoParameters());
    connect(&FrameControl::instance(), SIGNAL(imageNoChanged()), this, SLOT(onImageNoChanged()));

    connect(&FPGAUpgradeModel::instance(), SIGNAL(progressBarValueChanged(int)), this, SLOT(fpgaUpgradeProcess(int)));
    connect(&FPGAUpgradeModel::instance(), SIGNAL(upgradeFinalState(int)), this, SLOT(upgradeFinalState(int)));
}

void UsContextApiP9::fpgaUpgradeProcess(int process)
{
    CallbackInfo ci = m_CallbackInfo[UsFpgaUpgrade_CT];
    if (ci.first != NULL)
    {
        Log4Qt::Logger::rootLogger()->info("%1 process: %2", PRETTY_FUNCTION, process);
        ((UsUpgradeProcessCallback)ci.first)(this, 1, process, ci.second);
    }
}

void UsContextApiP9::upgradeFinalState(int code)
{
    CallbackInfo ci = m_CallbackInfo[UsFpgaUpgrade_CT];
    if (ci.first != NULL)
    {
        Log4Qt::Logger::rootLogger()->info("%1 code: %2", PRETTY_FUNCTION, code);
        if (code != FPGAUpdateResDef::Update_Success)
        {
            ((UsUpgradeProcessCallback)ci.first)(this, 3, 0, ci.second);
        }
        else
        {
            ((UsUpgradeProcessCallback)ci.first)(this, 4, 100, ci.second);
        }
    }
}

void UsContextApiP9::onProbeChanged(QVector<int> probelist, QVector<bool> changes)
{
#define MAX_PROBE_NUMBER 16
    bool sendFlag = false;
    for (int i = 0; i < changes.count(); ++i)
    {
        if (changes[i])
        {
            sendFlag = true;
            break;
        }
    }
    if (sendFlag)
    {
        char* probeName[MAX_PROBE_NUMBER];
        char probeBameBuffer[MAX_PROBE_NUMBER * MAX_PROBE_NAME_SIZE];
        memset(probeBameBuffer, 0, MAX_PROBE_NUMBER * MAX_PROBE_NAME_SIZE);
        int probeNumber = qMin(changes.count(), MAX_PROBE_NUMBER);
        int probeCount = 0;
        for (int i = 0; i < probeNumber; ++i)
        {
            probeName[i] = probeBameBuffer + MAX_PROBE_NAME_SIZE * i;
            QString name = m_ProbeDataSet->probe(probelist[i]).Name.trimmed();
            if (!name.isEmpty())
            {
                memcpy(*(probeName + probeCount), name.toStdString().c_str(),
                       qMin(name.length(), MAX_PROBE_NAME_SIZE - 1));
                ++probeCount;
            }
        }
        m_IsValidProbeConnected = probeCount > 0 ? true : false;
        CallbackInfo ci = m_CallbackInfo[UsProbeInfo_CT];
        if (ci.first != NULL)
        {
            ((UsProbeInfoCallback)ci.first)(this, probeName, probeCount, ci.second);
        }
    }
#undef MAX_PROBE_NUMBER
}

void UsContextApiP9::convertPhysicsFToLogic(const double line, const double depth, QPointF& pt)
{
    BFCoordTransform bfCoordTransform(m_ProbeDataSet->getProbe(m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr)),
                                      m_CurrentSonoParameters->pDV(BFPNames::StartDepthMMStr),
                                      m_CurrentSonoParameters->pDV(BFPNames::PixelSizeMMStr),
                                      m_CurrentSonoParameters->pIV(BFPNames::B_RX_LNUMStr));
    bfCoordTransform.convertPhysicsToLogic(line, depth, pt);
}

void UsContextApiP9::convertLogicFToPhysics(const QPointF& pt, double* line, double* depth)
{
    BFCoordTransform bfCoordTransform(m_ProbeDataSet->getProbe(m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr)),
                                      m_CurrentSonoParameters->pDV(BFPNames::StartDepthMMStr),
                                      m_CurrentSonoParameters->pDV(BFPNames::PixelSizeMMStr),
                                      m_CurrentSonoParameters->pIV(BFPNames::B_RX_LNUMStr));
    bfCoordTransform.convertPtToPhysics(pt, *line, *depth);
}

UsRetCode UsContextApiP9::setImageCheckTime(int timeSec)
{
    if (timeSec <= 0)
    {
        return UsRetCode::UsFailed;
    }
    AutoFreezeSetting::instance().setValue(AutoFreezeSetting::AutoFreezeTime, timeSec);
    FrameControl::instance().resetStartTime();
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::getImageCheckTime(int* timeSec)
{
    *timeSec = AutoFreezeSetting::instance().value(AutoFreezeSetting::AutoFreezeTime, 30).toInt();
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::setImageCheckState(bool open)
{
    AutoFreezeSetting::instance().setValue(AutoFreezeSetting::AutoFreezeSwitch, open);
    FrameControl::instance().resetStartTime();
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::getImageCheckState(bool* open)
{
    *open = AutoFreezeSetting::instance().value(AutoFreezeSetting::AutoFreezeSwitch, false).toBool();
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::setImageCheckSensitiveThreshold(float threshold)
{
    if (threshold <= 0 || threshold > 1.0F)
    {
        return UsRetCode::UsFailed;
    }
    Log4Qt::Logger::rootLogger()->info("%1 pixelThrshold: %2", PRETTY_FUNCTION, threshold);
    AutoFreezeSetting::instance().setValue(AutoFreezeSetting::AutoFreezeSensitiveThreshold,
                                           QString::number(threshold, 'f', 3));
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::getImageCheckSensitiveThreshold(float* threshold)
{
    *threshold = AutoFreezeSetting::instance().value(AutoFreezeSetting::AutoFreezeSensitiveThreshold, 0.25f).toFloat();
    return UsRetCode::UsOK;
}

UsRetCode UsContextApiP9::openFunction(UsFunctionType funcType)
{
    if (getScanMode() != UsScanMode::UsSystemScanModeB)
    {
        return UsRetCode::UsFailed;
    }
    FrameControl::instance().resetStartTime();
    return UsContextApi::openFunction(funcType);
}

void UsContextApiP9::onImageNoChanged()
{
    CallbackInfo ci = m_CallbackInfo[UsImageNoChange_CT];
    if (ci.first != NULL)
    {
        ((UsImgaeNoChangeCallback)ci.first)(this, ci.second);
    }
}

void UsContextApiP9::onProbeKeyStateChanged(QVector<quint8> statelist)
{
    CallbackInfo ci = m_CallbackInfo[UsProbeEvent_CT];
    int size = statelist.count();
    if (ci.first != NULL && size > 0 && m_ProbeKeyState != NULL && m_ProbeKeyCount >= size)
    {
        for (int i = 0; i < size; i++)
        {
            m_ProbeKeyState[i] = statelist[i];
        }

        ((UsProbeEventCallback)ci.first)(this, m_ProbeKeyState, size, ci.second);
    }
}

void UsContextApiP9::onDeviceOpenError(int error)
{
    CallbackInfo ci = m_CallbackInfo[UsCommunicationError_CT];
    if (ci.first != NULL)
    {
        if (UsContextApi::ErrorCodeHash.contains(error))
        {
            UsUltrasoundErrorCode code = UsContextApi::ErrorCodeHash[error];
            if (code)
            {
                ((UsCommunicationLinkErrorCallback)ci.first)(this, code, ci.second);
            }
        }
    }
}

void UsContextApiP9::onNewImage(ImageEventArgs* imageEventArgs)
{
    UsContextApi::onNewImage(imageEventArgs);
    CallbackInfo cROI = m_CallbackInfo[UsCROIChanged_CT];
    if (cROI.first != NULL)
    {
        int validCount = this->getROIPointsInColor(m_CROI, 4);
        ((UsCROIChangedCallback)cROI.first)(this, m_CROI, validCount, cROI.second);
    }
}

void UsContextApiP9::onImageTransformChanged(bool preset)
{
}

void UsContextApiP9::onFanSpeedUpdated(QVector<LineData::FanInfo> fans)
{
    CallbackInfo ci = m_CallbackInfo[UsFanError_CT];
    for (LineData::FanInfo fan : fans)
    {
        // http://*************:8090/pages/viewpage.action?pageId=12125211 风扇速度计算
        int speed = fan.speed == 0 ? 0 : 90000 * 60 / fan.speed;
        if (speed < ModelConfig::instance().value(ModelConfig::FanWarningThreshold, 100).toInt() && ci.first != NULL)
        {
            if (fan.type == LineData::Fan_CPU)
            {
                ((UsFanErrorCallback)ci.first)(this, UsFanErrorCode::UsFan_Error_CPU, ci.second);
            }
            else if (fan.type == LineData::Fan_Sys)
            {
                ((UsFanErrorCallback)ci.first)(this, UsFanErrorCode::UsFan_Error_Sys, ci.second);
            }
        }
    }
}

void UsContextApiP9::onAutoFreeze()
{
    if (m_CurrentSonoParameters->isRealTime() && !m_CurrentSonoParameters->pBV(BFPNames::FreezeStr))
    {
        lazyFreeze();
    }
}

UsRetCode UsContextApiP9::imageTransform(double scale, double translateX, double translateY)
{
    return UsFailed;
}

UsRetCode UsContextApiP9::imageTransformReset()
{
    return UsFailed;
}

UsRetCode UsContextApiP9::getCROIPoints(_UsLogicalPoint_INT* pointArray, const int count, int* validCount)
{
    (*validCount) = this->getROIPointsInColor(pointArray, count);
    if ((*validCount) > 0)
    {
        return UsRetCode::UsOK;
    }
    return UsRetCode::UsFailed;
}

int UsContextApiP9::getROIPointsInColor(_UsLogicalPoint_INT* roi, int size)
{
    if (getScanMode() == UsSystemScanModeColorDoppler && m_CurrentSonoParameters != NULL &&
        !m_CurrentSonoParameters->parameter(BFPNames::LeftStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::UpStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::RoiMidLineStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::RoiHalfLinesStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::RoiMidDepthMMStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::RoiHalfDepthMMStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::SteeringAngleStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::ImageZoomCoefStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::DSCImageSizeStr)->isNull() &&
        !m_CurrentSonoParameters->parameter(BFPNames::RotationStr)->isNull() && size >= 4)
    {
        int left = m_CurrentSonoParameters->pIV(BFPNames::LeftStr);
        int up = m_CurrentSonoParameters->pIV(BFPNames::UpStr);

        int roiMidLine = m_CurrentSonoParameters->pIV(BFPNames::RoiMidLineStr);
        int roiHalfLine = m_CurrentSonoParameters->pIV(BFPNames::RoiHalfLinesStr);

        int roiStartLine = roiMidLine - roiHalfLine;
        int roiStopLine = roiMidLine + roiHalfLine;

        double roiMidDepthMM = m_CurrentSonoParameters->pDV(BFPNames::RoiMidDepthMMStr);
        double roiHalfDepthMM = m_CurrentSonoParameters->pDV(BFPNames::RoiHalfDepthMMStr);

        double roiStartDepth = roiMidDepthMM - roiHalfDepthMM;
        double roiStopDepth = roiMidDepthMM + roiHalfDepthMM;

        int steeringAngle = m_CurrentSonoParameters->parameter(BFPNames::SteeringAngleStr)->showValue().toInt();
        qreal steerAngleCos = (qreal)steeringAngle / 180 * M_PI;

        int ratio = m_CurrentSonoParameters->pIV(BFPNames::ImageZoomCoefStr);
        float ratioZoomf = ratio / 100.00f;
        const int xSign = left == 1 ? 1 : -1;
        const int ySign = up ? 1 : -1;

        QSize imageSize = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
        QTransform transformFlip = QTransform::fromTranslate(-imageSize.width() / 2, -imageSize.height() / 2) *
                                   QTransform(xSign, 0, 0, 0, ySign, 0, 0, 0, 1) *
                                   QTransform::fromTranslate(imageSize.width() / 2, imageSize.height() / 2);

        QTransform rotateFlip = QTransform::fromTranslate(-imageSize.width() / 2, -imageSize.height() / 2) *
                                QTransform().rotate(m_CurrentSonoParameters->pIV(BFPNames::RotationStr)) *
                                QTransform::fromTranslate(imageSize.width() / 2, imageSize.height() / 2);

        QTransform transformZoomFlip =
            QTransform::fromTranslate(0, (1 - ratioZoomf) / 2 * imageSize.height()) * transformFlip * rotateFlip;

        QPointF lefttop = convertPhysicsToLogicF(roiStartLine, roiStartDepth, steerAngleCos, transformZoomFlip);
        roi[0].X = lefttop.x();
        roi[0].Y = lefttop.y();

        QPointF righttop = convertPhysicsToLogicF(roiStopLine, roiStartDepth, steerAngleCos, transformZoomFlip);
        roi[1].X = righttop.x();
        roi[1].Y = righttop.y();

        QPointF rightbottom = convertPhysicsToLogicF(roiStopLine, roiStopDepth, steerAngleCos, transformZoomFlip);
        roi[2].X = rightbottom.x();
        roi[2].Y = rightbottom.y();

        QPointF leftbottom = convertPhysicsToLogicF(roiStartLine, roiStopDepth, steerAngleCos, transformZoomFlip);
        roi[3].X = leftbottom.x();
        roi[3].Y = leftbottom.y();
        return 4;
    }
    return 0;
}

QPointF UsContextApiP9::convertPhysicsToLogicF(int line, double depth, qreal angle, const QTransform& transform)
{
    UsLogicalPoint point;
    convertPhysicsToLogicFInFullImage(line, depth, &point);
    return transform.map(QPointF(point.UsLogicalPoint_DOUBLE.X - point.UsLogicalPoint_DOUBLE.Y * std::sin(angle),
                                 point.UsLogicalPoint_DOUBLE.Y * std::cos(angle)));
}
