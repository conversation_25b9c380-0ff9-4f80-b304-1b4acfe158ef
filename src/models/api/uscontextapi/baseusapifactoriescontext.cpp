#include "baseusapifactoriescontext.h"
#include "beamformerbase.h"
#include "bfpnames.h"
#include "istatemanager.h"
#include "resource.h"
#include "ultrasounddevice.h"
#include "util.h"
#include <QColor>
#include <QGraphicsItem>
#include <QGraphicsScene>
#include <QPen>
#include <probepresetmodel.h>

#ifdef SYS_ANDROID
#include <QtAndroid>
#endif

BaseUSAPIFactoriesContext::BaseUSAPIFactoriesContext(QObject* parent)
    : QObject(parent)
    , m_BeamFormer(NULL)
    , m_ProbePresetModel(NULL)
    , m_PresetDataModel(NULL)
    , m_StateManager(NULL)
    , m_ColorMapManager(NULL)
{
}

BaseUSAPIFactoriesContext::~BaseUSAPIFactoriesContext()
{
}

BeamFormerBase* BaseUSAPIFactoriesContext::beamFormer() const
{
    return m_BeamFormer;
}

QVector<float> BaseUSAPIFactoriesContext::dopplerSteeringAngles() const
{
    return QVector<float>();
}

void BaseUSAPIFactoriesContext::updateFreqIndexBText(const QStringList& freqtxt, const QList<int>& freqidx)
{
    Q_UNUSED(freqtxt)
    Q_UNUSED(freqidx)
}

QGraphicsLineItem* BaseUSAPIFactoriesContext::updateGraphicsItemColor(QGraphicsScene* scene, int tagid,
                                                                      const QColor& color)
{
    //设置颜色 遍历QGraphicsScene 找到你要设置的item 通过 item->setData(key,value)方式
    foreach (QGraphicsItem* item, scene->items())
    {
        QGraphicsLineItem* itemLine = dynamic_cast<QGraphicsLineItem*>(item);
        if (itemLine && !itemLine->data(tagid).toString().isEmpty())
        {
            QPen pen = itemLine->pen();
            pen.setColor(color);
            itemLine->setPen(pen);
            return itemLine;
        }
    }
    return NULL;
}

void BaseUSAPIFactoriesContext::updateCustomGrayifNeeded(SonoParameters* sonoParameters)
{
}

bool BaseUSAPIFactoriesContext::externalIODeviceIsPaused() const
{
    return false;
}

void BaseUSAPIFactoriesContext::setExternalIODeviceWriteCallback(UsWifiWriteCallback callback, void* userData)
{
}

void BaseUSAPIFactoriesContext::pushExternalIODeviceData(unsigned char* data, int size)
{
}

void BaseUSAPIFactoriesContext::setExternalIODevicePasuedStatus(const bool status)
{
}

bool BaseUSAPIFactoriesContext::setCurrentProbe(const ProbeDataInfo& probeInfo)
{
    m_BeamFormer->setCurProbe(probeInfo);
    m_BeamFormer->parameter(BFPNames::CQYZStr)->update();
    return true;
}

IProbePresetModel* BaseUSAPIFactoriesContext::probePresetModel()
{
    return m_ProbePresetModel;
}

void BaseUSAPIFactoriesContext::setStateManager(IStateManager* value)
{
    m_StateManager = value;
}

void BaseUSAPIFactoriesContext::setColorMapManager(IColorMapManager* value)
{
    m_ColorMapManager = value;
}
