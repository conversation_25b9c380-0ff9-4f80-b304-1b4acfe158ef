#ifndef PRESETDATAMODEL_H
#define PRESETDATAMODEL_H

#include "abstractdatamodel.h"

class IExamModePresetDataHandler;
class IProbePresetModel;
class SonoParameters;

/** 2024-06-19 Write by <PERSON><PERSON><PERSON>
 * @brief The PresetDataModel class
 */
class PresetDataModel : public AbstractDataModel
{
    Q_OBJECT
public:
    explicit PresetDataModel(IProbePresetModel* model, QObject* parent = NULL);
    ~PresetDataModel();

    /** 2024-06-19 Write by <PERSON><PERSON><PERSON>
     * @brief setCurrentExamModeID
     * @param value
     */
    void setCurrentExamModeID(const QString& value);

    /** 2024-06-19 Write by <PERSON><PERSON><PERSON>
     * @brief setCurrentExamModeName
     * @param value
     */
    void setCurrentExamModeName(const QString& value);

    /** 2024-06-19 Write by Alex<PERSON><PERSON>
     * @brief currentOrganismName
     * @return
     */
    const QString currentOrganismName() const;

private Q_SLOTS:

    /** 2024-06-19 Write by <PERSON><PERSON><PERSON>
     * @brief onBeforeCurrentProbeChanged
     */
    void onBeforeCurrentProbeChanged();

    /** 2024-06-19 Write by AlexWang
     * @brief onCurrentProbeChanged
     */
    void onCurrentProbeChanged();

private:
    IProbePresetModel* m_ProbePresetModel;
    IExamModePresetDataHandler* m_ExamModePreset;
};

#endif // PRESETDATAMODEL_H
