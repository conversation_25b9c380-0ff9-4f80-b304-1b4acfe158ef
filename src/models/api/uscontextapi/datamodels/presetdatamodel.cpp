#include "presetdatamodel.h"
#include "iprobepresetmodel.h"
#include "exammodepresethandlerfactory.h"
#include "sonoparameters.h"
#include "bfpnames.h"
#include "appsetting.h"
#include "iexammodepresetdatahandler.h"
#include "infostruct.h"

PresetDataModel::PresetDataModel(IProbePresetModel* model, QObject* parent)
    : AbstractDataModel(parent)
    , m_ProbePresetModel(model)
{
    m_ExamModePreset = ExamModePresetHandlerFactory::instance()->defaultHandler();
    connect(m_ProbePresetModel, SIGNAL(beforeCurrentProbeAutoChanged()), this, SLOT(onBeforeCurrentProbeChanged()));
    connect(m_ProbePresetModel, SIGNAL(currentProbeAutoChanged()), SLOT(onCurrentProbeChanged()));
}

PresetDataModel::~PresetDataModel()
{
}

void PresetDataModel::setCurrentExamModeID(const QString& value)
{
    setPV(BFPNames::ExamModeIdStr, value);
}

void PresetDataModel::setCurrentExamModeName(const QString& value)
{
    if (AppSetting::isHuman())
    {
        setPV(BFPNames::ExamModeCaptionStr, value);
    }
    else
    {
        QString organismName = QString("%1(%2)").arg(currentOrganismName()).arg(value);
        setPV(BFPNames::ExamModeCaptionStr, organismName);
    }
}

const QString PresetDataModel::currentOrganismName() const
{
    QString OrganismName = QString();
    if (AppSetting::isAnimal())
    {
        OrganismName = m_ExamModePreset->examModeOrganismName(pSV(BFPNames::ExamModeIdStr));
    }
    return OrganismName;
}

void PresetDataModel::onBeforeCurrentProbeChanged()
{
    m_ProbePresetModel->beginChangePreset();
}

void PresetDataModel::onCurrentProbeChanged()
{
    if (!m_ExamModePreset)
    {
        return;
    }

    const ProbeDataInfo& probeinfo = m_ProbePresetModel->currentProbe();

    if (!probeinfo.isNull())
    {
        QString emId = m_ExamModePreset->probeDefaultExamModeID(probeinfo.Id);
        setCurrentExamModeID(emId);
        setCurrentExamModeName(m_ExamModePreset->examModeName(emId));

        ExamMode mode;
        m_ExamModePreset->probeExamMode(probeinfo.Id, emId, mode);
        m_ProbePresetModel->setPreset(mode.paremeters());
    }
    m_ProbePresetModel->endChangePreset();
}
