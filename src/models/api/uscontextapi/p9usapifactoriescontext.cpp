#include "p9usapifactoriescontext.h"
#include "xmlbfparameterloader.h"
#include "beamformerphoenix.h"

P9USAPIFactoriesContext::P9USAPIFactoriesContext(QObject *parent)
    : BaseUSAPIFactoriesContext(parent)
{

}

void P9USAPIFactoriesContext::initialize(int width, int height)
{
    Q_UNUSED(width)
    Q_UNUSED(height)
    XmlBFParameterLoader loader(":/xml/bfparameter_phoenix.xml");
    BFParameters bfPara = loader.build(this);
    bfPara.m_SonoParameters->setIsRealTime(true);

    m_BeamFormer = new BeamFormerPhoenix(this);
    m_BeamFormer->initialize();
    m_BeamFormer->setControlTableLen(bfPara.m_ControlTableLen);
    m_BeamFormer->setSonoParameters(bfPara.m_SonoParameters);
}

P9USAPIFactoriesContext::~P9USAPIFactoriesContext()
{
    if(m_BeamFormer != NULL)
    {
        delete m_BeamFormer;
        m_BeamFormer = NULL;
    }
}
