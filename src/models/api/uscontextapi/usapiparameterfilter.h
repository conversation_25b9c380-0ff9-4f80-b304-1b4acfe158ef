#ifndef USAPIPARAMETERFILTER_H
#define USAPIPARAMETERFILTER_H

#include <QHash>
#include <QObject>
#include <QString>
#include <QVariant>

class SonoParameters;
class IProbeDataSet;

class UsApiParameterFilter : public QObject
{
    Q_OBJECT
  public:
    UsApiParameterFilter(QObject *parent = NULL);

    void setSonoParameters(SonoParameters *sonoParameters);

    void setProbeDataSet(IProbeDataSet *probeDataSet);

    bool contains(const QString &parameterName) const;

    bool activeable(const QString &parameterName) const;

    QList<QString> parameters() const;

    bool parametersSettable(const QString &parameterName) const;

  private:
    void initSonoParameters();
    void connectSonoParameters();
    void disConnectSonoParameters();
    void initModeNoSettableParameters();

  private slots:
    void onFreezeValueChanged(const QVariant &value);
    void onBSteeringScanValueChanged(const QVariant &value);
    void onTrapezoidalModeValueChanged(const QVariant &value);
    void onScpdOnValueChanged(const QVariant &value);
    void onProbeIdValueChanged(const QVariant &value);

  private:
    SonoParameters *m_SonoParameters;
    QHash<QString, bool> m_Parameters;
    QMultiHash<QString, QString> m_ModeNoSettableParameters;
    IProbeDataSet *m_ProbeDataSet;
};

#endif // USAPIPARAMETERFILTER_H
