#ifndef BASEUSAPIFACTORIESCONTEXT_H
#define BASEUSAPIFACTORIESCONTEXT_H

#include "iusapifactoriescontext.h"
class PresetDataModel;
class ProbePresetModel;

class BaseUSAPIFactoriesContext : public QObject, public IUSAPIFactoriesContext
{
    Q_OBJECT
public:
    BaseUSAPIFactoriesContext(QObject* parent = NULL);

    ~BaseUSAPIFactoriesContext();

    BeamFormerBase* beamFormer() const;

    QVector<float> dopplerSteeringAngles() const;

    void updateFreqIndexBText(const QStringList& freqtxt, const QList<int>& freqidx);

    QGraphicsLineItem* updateGraphicsItemColor(QGraphicsScene* scene, int tagid, const QColor& color);

    void updateCustomGrayifNeeded(SonoParameters* sonoParameters);

    bool externalIODeviceIsPaused() const;
    void setExternalIODeviceWriteCallback(UsWifiWriteCallback callback, void* userData);
    void pushExternalIODeviceData(unsigned char* data, int size);
    void setExternalIODevicePasuedStatus(const bool status);

    /** 2024-06-11 Write by AlexWang
     * @brief setCurrentProbe 设置当前探头
     * @param probeInfo
     * @return
     */
    bool setCurrentProbe(const ProbeDataInfo& probeInfo) override;

    /** 2024-06-18 Write by AlexWang
     * @brief probePresetModel
     * @return
     */
    IProbePresetModel* probePresetModel() override;

    /** 2024-08-15 Write by AlexWang
     * @brief setStateManager
     * @param value
     */
    void setStateManager(IStateManager* value) override;

    /** 2024-08-26 Write by AlexSWang
     * @brief setColorMapManager
     * @param value
     */
    void setColorMapManager(IColorMapManager* value) override;

protected:
    BeamFormerBase* m_BeamFormer;
    ProbePresetModel* m_ProbePresetModel;
    PresetDataModel* m_PresetDataModel;
    IStateManager* m_StateManager;
    IColorMapManager* m_ColorMapManager;
};

#endif // BASEUSAPIFACTORIESCONTEXT_H
