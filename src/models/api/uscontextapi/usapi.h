#ifndef USAPI_H
#define USAPI_H

#include "usapidef.h"
#ifdef __cplusplus
extern "C"
{
#endif

#ifdef SYS_APPLE
    /**
     * @brief UsCreate 创建句柄
     * width,height 图像尺寸
     * isAnimal :是否兽超 1:是 0:否
     * @return 若为NULL,则创建失败
     */
    US_API UsContext CT_CALL UsCreate(int width = -1, int height = -1, int isAnimal = 0);
    /**
     * @brief UsAnimalSwitch
     * @param type 1：兽  0：人
     * @return
     */
    US_API UsRetCode CT_CALL UsHumanAnimalSwitch(UsContext context, int type);
#else
/**
 * @brief UsCreate 创建句柄
 * @return 若为NULL,则创建失败
 */
US_API UsContext CT_CALL UsCreate();
#endif

    /**
     * @brief UsRelease 销毁句柄
     * @param context
     * @return
     */
    US_API void CT_CALL UsRelease(UsContext &context);
    /**
     * @brief UsSetDataCallback 设置图像数据回调函数
     * @param context       句柄
     * @param callback      回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetDataCallback(UsContext context, UsDataCallback callback, void *userData);

    /**
     * @brief UsSetRawDataCallback 设置优化前图像数据回调函数
     * @param context       句柄
     * @param callback      回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetRawDataCallback(UsContext context, UsRawDataCallback callback, void *userData);

    /**
     * @brief UsWifiWriteCallback 设置图像数据回调函数
     * @param context       句柄
     * @param callback      wifiwrite回调函数
     * @param userData      用户数据
     * @return
     */
    US_API void CT_CALL UsSetWifiWriteCallback(UsContext context, UsWifiWriteCallback callback, void *userData);
    /**
     * @brief UsSetProbeInfoCallback 设置探头
     * @param context       句柄
     * @param callback      插槽探头信息回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeInfoCallback(UsContext context, UsProbeInfoCallback callback, void *userData);

    /**
     * @brief UsSetButtonClickedCallback 设置探头
     * @param context       句柄
     * @param callback      探头按键信息回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeEventCallback(UsContext context, UsProbeEventCallback callback, void *userData);
    /**
     * @brief UsSetParaChangedCallback  设置参数变化回调函数
     * @param context                   句柄
     * @param callback                  回调函数
     * @param userData                  用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetParaChangedCallback(UsContext context, UsParaChangedCallback callback,
                                                      void *userData);

    /**
     * @brief UsSetVADetectDataCallback  设置VA检测数据变化回调函数
     * @param context                   句柄
     * @param callback                  回调函数
     * @param userData                  用户数据
     * @return
     */
    US_API UsRetCode UsSetVADetectDataCallback(UsContext context, UsVADetectInfoCallback callback, void *userData);

    /**
     * @brief UsSetFunctionStateChangedCallback  设置功能状态变化回调函数
     * @param context                   句柄
     * @param callback                  回调函数
     * @param userData                  用户数据
     * @return
     */
    US_API UsRetCode UsSetFunctionStateChangedCallback(UsContext context, UsFunctionStateChangedCallback callback,
                                                       void *userData);

    US_API UsRetCode UsSetUpgradeProcessCallback(UsContext context, UsUpgradeProcessCallback callback, void *userData);
    /**
     * @brief UsSetImageNoChangeCallback 设置图像没发生变化的回调
     * @param context       句柄
     * @param callback      图像没有发生变化回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetImageNoChangeCallback(UsContext context, UsImgaeNoChangeCallback callback,
                                                        void *userData);

    /**
     * @brief UsSetImageNoChangeCallback 设置Color模式下ROI框发生变化的回调
     * @param context       句柄
     * @param callback      ROI发生变化回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetCROIChangedCallback(UsContext context, UsCROIChangedCallback callback,
                                                      void *userData);

    /**
     * @brief UsSetCommunicationLinkErrorCallback  设置通信异常回调函数
     * @param context                   句柄
     * @param callback                  回调函数
     * @param userData                  用户数据
     * @return
     */
    US_API UsRetCode UsSetCommunicationLinkErrorCallback(UsContext context, UsCommunicationLinkErrorCallback callback,
                                                         void *userData);

    /**
     * @brief UsSetFanErrorCallback  设置风扇回调函数
     * @param context                   句柄
     * @param callback                  回调函数
     * @param userData                  用户数据
     * @return
     */
    US_API UsRetCode UsSetFanErrorCallback(UsContext context, UsFanErrorCallback callback, void *userData);

    /**
     * @brief UsPushData 推入数据
     * @param context       句柄
     * @param data          数据指针
     * @param size          数据大小
     * @return
     */
    US_API void CT_CALL UsPushData(UsContext context, unsigned char *data, int size);
    /**
     * @brief UsStart 开始运行
     * @return 正常:0 错误:错误码
     */
    US_API UsRetCode CT_CALL UsStart(UsContext context);
#ifdef SYS_APPLE
    /**
     * @brief UsStop 停止运行
     * @return 正常:0 错误:错误码
     */
    US_API UsRetCode CT_CALL UsStop(UsContext context);
#endif
    /**
     * @brief UsSetScanMode     设置扫查模式
     * @param context           句柄
     * @param scanMode          扫查模式
     * @return 成功:0, 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetScanMode(UsContext context, UsScanMode scanMode);
    /**
     * @brief UsGetScanMode     获取扫查模式
     * @param context           句柄
     * @return 成功：扫查模式， 失败：错误码
     */
    US_API int CT_CALL UsGetScanMode(UsContext context);
    /**
     * @brief UsSetLayout       设置Layout模式
     * @param context           句柄
     * @param layout            layout布局方式
     * @return 成功:0, 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetLayout(UsContext context, UsLayout layout);
    /**
     * @brief UsGetScanMode     获取扫查模式
     * @param context           句柄
     * @return 成功Layout布局方式， 失败：错误码
     */
    US_API int CT_CALL UsGetLayout(UsContext context);
    /**
     * @brief UsSetActiveLayoutIndex    设置Layout模式
     * @param context                   句柄
     * @param activeIndex               布局区域索引
     * @return 成功:0, 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetActiveLayoutIndex(UsContext context, int activeIndex);
    /**
     * @brief UsGetActiveLayoutIndex     获取Layout模式
     * @param context           句柄
     * @return 成功：当前激活区域的LayoutIndex， 失败：错误码
     */
    US_API int CT_CALL UsGetActiveLayoutIndex(UsContext context);
    /**
     * @brief UsSetFreeze          设置冻结状态
     * @param context           句柄
     * @param isFrozen          冻结:1, 非冻结:0
     * @return 成功:0, 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetFreeze(UsContext context, int isFrozen);
    /**
     * @brief UsIsFrozen        判断探头冻结
     * @param context           句柄
     * @return 成功: 冻结:1 非冻结:0 失败：错误码
     */
    US_API int CT_CALL UsIsFrozen(UsContext context);
    /**
     * @brief UsStandby     软件待机
     * @param context           句柄
     * @return 成功:0  失败: 错误码
     */
    US_API UsRetCode CT_CALL UsStandby(UsContext context);
    /**
     * @brief UsWake        软件唤醒
     * @param context           句柄
     * @return 成功:0  失败: 错误码
     */
    US_API UsRetCode CT_CALL UsWake(UsContext context);
    //==============================================Probe
    /**
     * @brief UsSetCurrentProbe 设置探头信息
     * @param context           句柄
     * @param probeName         探头名称
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetCurrentProbe(UsContext context, const char *probeName);
    /**
     * @brief UsSetProbeConnected
     * @param context
     * @param isconnected 0:断开  1：连接上
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeConnected(UsContext context, int isconnected);
    /**
     * @brief UsGetCurrentProbe 获取探头信息
     * @param context           句柄
     * @param probeDataInfo     探头信息
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetCurrentProbe(UsContext context, UsProbeDataInfo *probeDataInfo);
    /**
     * @brief UsGetCurrentProbe 获取图像dsc处理的一些信息， 用于ui层绘制放大缩小
     * @param context           句柄
     * @param ImageDscInfo      图像变换的信息
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetCurrentDscImageTransfromInformation(UsContext context,
                                                                      DscImageTransfromInformation *ImageDscInfo);
    /**
     * @brief UsGetProbeNames   获取硬件插槽上的探头名称
     * @param context           句柄
     * @param probeNames        探头名字数组
     * @param arraySize         数组大小
     * @param itemSize          数组子项内存长度(单位字节)
     * @return 成功:0 失败:错误码
     */
    US_API int CT_CALL UsGetProbeNames(UsContext context, char **probeNames, const int arraySize, const int itemSize);

    /**
     * @brief UsGetProbeNames   获取当前机型支持的所有探头名称
     * @param context           句柄
     * @param probeNames        探头名字数组
     * @param arraySize         数组大小
     * @param itemSize          数组子项内存长度(单位字节)
     * @return 返回实际探头数目
     */
    US_API int CT_CALL UsGetAllSupportProbeNames(UsContext context, char **probeNames, const int arraySize,
                                                 const int itemSize);

    //==============================================Preset
    /**
     * @brief UsLoadPreset 加载预设值
     * @param context      句柄
     * @param fileName     预设值文件名
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsLoadPreset(UsContext context, const char *const fileName);
    /**
     * @brief UsSavePreset  当前修改内容保存到预设值文件
     * @param context       句柄
     * @param fileName      预设值文件名
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSavePreset(UsContext context, const char *const fileName);
    /**
     * @brief UsGetProbeShowNameByProbeName  根据探头名字返回探头showname（别名）
     * @param context       句柄
     * @param probename      探头名称
     * @param probeShowName 返回探头的showname
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetProbeShowNameByProbeName(UsContext context, const char *const probename,
                                                           char *probeShowName);
    /**
     * @brief UsGetCurrentProbeShowName  返回当前探头，包括加载的预设值
     * @param context       句柄
     * @param probeShowName 返回探头的showname
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetCurrentProbeShowName(UsContext context, char *probeShowName);
    /**
     * @brief UsGetCurrentProbeModeName  返回当前探头的modename
     * @param context       句柄
     * @param probeShowName 返回探头的modename
     * @param itemSize      长度
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetCurrentProbeModeName(UsContext context, char *probeShowName, int itemSize);
    /**
     * @brief UsGetCurrentPresetProbeName  返回当前探头的presetProbename
     * @param context       句柄
     * @param probeShowName 返回当前探头的presetProbename
     * @param itemSize      长度
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetCurrentPresetProbeName(UsContext context, char *presetProbeName, int itemSize);
    /**
     * @brief UsProbePresetNameUpdate  预设值修改和增加
     * @param context       句柄
     * @param oldPresetShowname      探头预设值旧名称
     * @param newPrsetShowname       探头预设值的新名字
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsProbePresetNameUpdate(UsContext context, const char *oldPresetShowname,
                                                     const char *newPrsetShowname);
    /**
     * @brief UsProbePresetAdd  预设值增加
     * @param context       句柄
     * @param oldPresetShowname      基于哪个预设值名字新增的
     * @param newPrsetShowname       探头预设值的新名字
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsProbePresetAdd(UsContext context, const char *oldPresetShowname,
                                              const char *newPrsetShowname);
    /**
     * @brief UsProbePresetDelete  预设值删除
     * @param context       句柄
     * @param probePrestShowname      探头预设值名称
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsProbePresetDelete(UsContext context, const char *probePrestShowname);
    /**
     * @brief UsProbePresetReorder  预设值排序
     * @param context       句柄
     * @param orderlist      预设值顺序
     * @param size           预设值顺序列表长度
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsProbePresetReorder(UsContext context, const int *orderlist, int size);
    /**
     * @brief UsProbePresetReset  预设值复位 需要重启app
     * @param context       句柄
     * @param resetall      是否reset所有探头 0：当前探头  1：所有探头
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsProbePresetReset(UsContext context, int resetall);
    /**
     * @brief UsGetCurrentProbePreset 获取当前探头所有预设值名称
     * @param context           句柄
     * @param preset            char* preset[arraySize]; preset[index] = new char[bufferSize];
     * @param arraySize         数组大小；软件最多拷贝arraySize个预设值名称
     * @param bufferSize        字符串长度大小；每个预设值名称最多拷贝bufferSize-1个字符
     * @return 成功：拷贝成功的预设值名称个数 失败，错误码
     */
    US_API int CT_CALL UsGetCurrentProbePreset(UsContext context, char **presets, const int arraySize,
                                               const int bufferSize);
    /**
     * @brief UsGetProbePreset      获取指定探头名的所有预设值
     * @param probeName             探头名称
     */
    US_API int CT_CALL UsGetProbePreset(UsContext context, const char *probeName, char **presets, const int arraySize,
                                        const int bufferSize);
    /**
     * @brief UsSelectPreset            选择预设值
     * @param context                   句柄
     * @param presetName                预设值名称；预设值名称必须为当前探头的预设值
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsSelectPreset(UsContext context, const char *presetName);
    /**
     * @brief UsGetPresetOriNameByShowName 根据预设值显示名称，获取原始名称
     * @param context
     * @param presetShowName
     * @param presetOriName
     * @return
     */
    US_API UsRetCode CT_CALL UsGetPresetOriNameByShowName(UsContext context, const char *presetShowName,
                                                          char *presetOriName);
    /**
     * @brief UsSaveCurrentPreset   保存当前的预设值信息到数据库
     * @param context               句柄
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsSaveCurrentPreset(UsContext context);
    /**
     * @brief UsSaveCurrentPresetAs 当前预设值信息另存为
     * @param context               句柄
     * @param presetName            预设值名称
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsSaveCurrentPresetAs(UsContext context, const char *presetName);
    /**
     * @brief UsRenameCurrentPreset 重命名预设值
     * @param context               句柄
     * @param oldPresetName         旧预设值名称
     * @param newPresetName         新预设值名称
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsRenamePreset(UsContext context, const char *oldPresetName, const char *newPresetName);
    /**
     * @brief UsRenameCurrentPreset 重命名当前预设值
     * @param context               句柄
     * @param presetName            预设值名称
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsRenameCurrentPreset(UsContext context, const char *presetName);
    /**
     * @brief UsDeleteCurrentPreset 删除当前预设值
     * @param context               句柄
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsDeleteCurrentPreset(UsContext context);

    /**
     * @brief UsDeletePreset 删除指定预设值
     * @param context               句柄
     * @param presetName            预设值名称
     * @return 成功：0 失败：错误码
     */
    US_API UsRetCode CT_CALL UsDeletePreset(UsContext context, const char *presetName);
    /**
     * @brief UsGetAllPreset 获取所有预设值
     * @param context               句柄
     * @param preset            char* preset[arraySize]; preset[index] = new char[bufferSize];
     * @param arraySize         数组大小；软件最多拷贝arraySize个预设值名称
     * @param bufferSize        字符串长度大小；每个预设值名称最多拷贝bufferSize-1个字符
     * @return 成功：拷贝成功的预设值名称个数 失败，错误码
     */
    US_API int CT_CALL UsGetAllPreset(UsContext context, char **presets, const int arraySize, const int bufferSize);
    //==============================================Cine
    /**
     * @brief UsSaveCine       冻结下保存电影文件
     * @param context          句柄
     * @param fileName         保存文件名
     * @param startIndex       开始帧索引号
     * @param endIndex         结束帧索引号
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSaveCine(UsContext context, const char *const fileName, const int startIndex,
                                        const int endIndex);
    /**
     * @brief UsSaveImage      保存当前帧
     * @param context          句柄
     * @param fileName         保存文件名
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSaveImage(UsContext context, const char *const fileName);
    /**
     * @brief UsLoadCine/UsLoadImage       加载文件
     * @param context          句柄
     * @param fileName         加载文件名
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsLoadCine(UsContext context, const char *fileName);
    /**
     * @brief UsEndLoad 结束加载
     * @param context
     * @return
     */
    US_API UsRetCode CT_CALL UsEndLoad(UsContext context);
    US_API UsRetCode CT_CALL UsLoadImage(UsContext context, const char *fileName);
    /**
     * @brief UsGetStartIndex                   获取当前激活区域开始帧索引
     * @param context                           句柄
     * @return 开始帧索引
     */
    US_API int CT_CALL UsGetStartIndex(UsContext context);
    /**
     * @brief UsSetStartIndex                   设置当前激活区域开始帧索引
     * @param context                           句柄
     * @param startIndex                        开始帧索引
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetStartIndex(UsContext context, const int startIndex);
    /**
     * @brief UsGetEndIndex                 获取当前激活区域结束帧索引
     * @param context                       句柄
     * @return 结束帧索引
     */
    US_API int CT_CALL UsGetEndIndex(UsContext context);
    /**
     * @brief UsSetEndIndex                 设置当前激活区域结束帧索引
     * @param context                       句柄
     * @param endIndex                      结束帧索引
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetEndIndex(UsContext context, const int endIndex);
    /**
     * @brief UsGetCurrentIndex                     获取当前激活区域当前帧索引
     * @param context                               句柄
     * @return 当前帧索引
     */
    US_API int CT_CALL UsGetCurrentIndex(UsContext context);
    /**
     * @brief UsDeliverMode                     开启debug发货模式
     * @param context                               句柄
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsDeliverMode(UsContext context);
    /**
     * @brief UsSetCurrentIndex                     设置当前激活区域当前帧索引
     * @param context                               句柄
     * @param currentIndex                          当前帧索引
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetCurrentIndex(UsContext context, const int currentIndex);
    /**
     * @brief UsGetFrameCount                     获取当前激活区域总帧数
     * @param context                               句柄
     * @return 总帧数
     */
    US_API int CT_CALL UsGetFrameCount(UsContext context);
    /**
     * @brief UsStartPlayLoop/UsSopPlayLoop   开始/停止自动播放
     * @param context                           句柄
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsStartPlayLoop(UsContext context);
    US_API UsRetCode CT_CALL UsStopPlayLoop(UsContext context);

    /**
     * @brief UsStartPlayNext   向前播放 像后 n 帧 向前 -n帧
     * @param context                           句柄
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsStartPlayNext(UsContext context, int n);

    /**
     * @brief UsSetPlaySpeed                 设置自动播放速度
     * @param context                        句柄
     * @param speed                          当前帧索引
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetPlaySpeed(UsContext context, UsPlaySpeed speed);
    //==============================================Parameters
    /**
     * @brief UsSetParameterIntAtPosition 设置当前激活区域的指定位置的参数
     * @param context               句柄
     * @param name                  名称
     * @param position              位置，(图像区域当前位置到图像顶端所占图像区域的%，以0~100表示)
     * @param value                 设置值
     * @return
     */
    US_API UsRetCode CT_CALL UsSetParameterIntAtPosition(UsContext context, const char *const name, int position,
                                                         int value);
    /**
     * @brief UsSetParameter*       设置当前激活区域的超声参数
     * @param context               句柄
     * @param name                  名称
     * @param value                 值
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsSetParameterInt(UsContext context, const char *const name, int value);
    US_API UsRetCode CT_CALL UsSetParameterDouble(UsContext context, const char *const name, double value);
    US_API UsRetCode CT_CALL UsSetParameterString(UsContext context, const char *const name, const char *const value);
    US_API UsRetCode CT_CALL UsSetParameterBytes(UsContext context, const char *const name, const char *const value,
                                                 const int count);
    US_API UsRetCode CT_CALL UsSetParameterSize(UsContext context, const char *const name, const int width,
                                                const int height);
    /**
     * @brief UsGetParameter*       获取当前激活区域的超声参数,用户需要对所提供的指针确保有足够的内存空间
     * @param context               句柄
     * @param name                  名称
     * @param value                 获取的值
     * @return 成功:0 失败:错误码
     */
    US_API UsRetCode CT_CALL UsGetParameterInt(UsContext context, const char *const name, int *value);
    US_API UsRetCode CT_CALL UsGetParameterIntMin(UsContext context, const char *const name, int *value);
    US_API UsRetCode CT_CALL UsGetParameterIntStep(UsContext context, const char *const name, int *value);
    US_API UsRetCode CT_CALL UsGetParameterIntMax(UsContext context, const char *const name, int *value);
    US_API UsRetCode CT_CALL UsGetParameterDouble(UsContext context, const char *const name, double *value);
    US_API UsRetCode CT_CALL UsGetAdditionalParamInt(UsContext context, const char *const name, int *value);
    /**
     * 对于字符串最大拷贝count - 1个字节大小
     * 对于字节数组最大拷贝count个字节大小
     * @param value                 缓存地址
     * @param count                 缓存区大小
     * @return 成功:返回拷贝成功的字节数 失败:错误码
     */
    US_API int CT_CALL UsGetParameterString(UsContext context, const char *const name, char *value, const int count);
    US_API int CT_CALL UsGetParameterBytes(UsContext context, const char *const name, char *value, const int count);
    US_API int CT_CALL UsGetParameterShowText(UsContext context, const char *const name, char *value, const int count);
    US_API UsRetCode CT_CALL UsGetParameterShowValue(UsContext context, const char *const name, int *value);
    US_API UsRetCode CT_CALL UsGetParameterSize(UsContext context, const char *const name, int *width, int *height);

    //--------------------------------Geometric Coordinate Transformation-------------------------------
    /**
     * @brief UsConvertPhysicsToLogic
     * @param context
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @param logicalPoint 整数逻辑坐标（屏幕坐标）
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertPhysicsToLogic(UsContext context, const double line, const double depth,
                                                     UsLogicalPoint *logicalPoint);
    /**
     * @brief UsConvertPhysicsToLogicF
     * @param context
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @param logicalPoint 浮点数逻辑坐标（屏幕坐标）
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertPhysicsToLogicF(UsContext context, const double line, const double depth,
                                                      UsLogicalPoint *logicalPoint);
    /**
     * @brief UsConvertLogicToPhysics
     * @param context
     * @param logicalPoint 整数逻辑坐标（屏幕坐标）
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertLogicToPhysics(UsContext context, const UsLogicalPoint *logicalPoint,
                                                     double *line, double *depth);
    /**
     * @brief UsConvertLogicToPhysics
     * @param context
     * @param x y 整数逻辑坐标（屏幕坐标）
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertLogicxyToPhysics(UsContext context, const int x, const int y, double *line,
                                                       double *depth);
    /**
     * @brief UsConvertLogicFToPhysics
     * @param context
     * @param logicalPoint 浮点数逻辑坐标（屏幕坐标）
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertLogicFToPhysics(UsContext context, const UsLogicalPoint *logicalPoint,
                                                      double *line, double *depth);

    /**
     * @brief UsConvertPhysicsToLogicInFullImage,(0,0)是图像左上角
     * @param context
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @param logicalPoint 整数逻辑坐标（屏幕坐标）
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertPhysicsToLogicInFullImage(UsContext context, const double line,
                                                                const double depth, UsLogicalPoint *logicalPoint);
    /**
     * @brief UsConvertPhysicsToLogicFInFullImage (0,0)是图像左上角
     * @param context
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @param logicalPoint 浮点数逻辑坐标（屏幕坐标）
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertPhysicsToLogicFInFullImage(UsContext context, const double line,
                                                                 const double depth, UsLogicalPoint *logicalPoint);
    /**
     * @brief UsConvertLogicToPhysicsInFullImage (0,0)是图像左上角
     * @param context
     * @param logicalPoint 整数逻辑坐标（屏幕坐标）
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertLogicToPhysicsInFullImage(UsContext context, const UsLogicalPoint *logicalPoint,
                                                                double *line, double *depth);
    /**
     * @brief UsConvertLogicFToPhysicsInFullImage (0,0)是图像左上角
     * @param context
     * @param logicalPoint 浮点数逻辑坐标（屏幕坐标）
     * @param line 物理坐标线号
     * @param depth 物理坐标深度(毫米)
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsConvertLogicFToPhysicsInFullImage(UsContext context, const UsLogicalPoint *logicalPoint,
                                                                 double *line, double *depth);
    //--------------------------------PhysicalGeometryController-------------------------------
    /**
     * @brief UsGeometryMove 控制图形移动
     * @param context
     * @param offsetX x方向位移
     * @param offsetY y方向位移
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGeometryMove(UsContext context, const int offsetX, const int offsetY);
    /**
     * @brief UsGeometryChangeSize 控制图形尺寸变化
     * @param context
     * @param sizeX x方向尺寸
     * @param sizeY y方向尺寸
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGeometryChangeSize(UsContext context, const int sizeX, const int sizeY);
    /**
     * @brief UsGeometryChangeSize 控制图形缩放
     * @param context
     * @param scaleX x方向缩放
     * @param scaleY y方向缩放
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGeometryScale(UsContext context, const int scaleX, const int scaleY);
    /**
     * @brief UsWidgetUnitControlUpdate 采样门 baseline 等小部件位置的控制
     * @param context
     * @param value 增加value个档位 降低档位
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsWidgetUnitControlUpdate(UsContext context, const char *unitname, const int value);
    /**
     * @brief UsAIO AIO触发
     * @param context
     */
    US_API UsRetCode CT_CALL UsAIO(UsContext context);

    /**
     * @brief UsSave2Video 保存到视频(mp4,PNG,DCM)
     * @param context
     * backgroundImage: ui层传入的背景模板，rgba的raw数据 存储视频，需要把图像贴到固定的模板中
     * width: 背景的宽度
     * height: 背景的高度
     * pastex: 图像贴到的位置x
     * pastey: 图像贴到的位置y
     * mediatype :0:mp4  1:png
     *
     * isdicom: 0:非dicom  1：dicom
     */
    US_API UsRetCode CT_CALL UsExportMedia(UsContext context, unsigned char *backgroundImage, int width, int height,
                                           int pastex, int pastey, int mediatype, int isdicom,
                                           UsConvertProcessCallback callback);
    /**
     * @brief UsSetImageColorFilter
     * @param context
     * @param filterArray  滤镜数组 4X5 c#中的 SKPaint.ColorFilter
     *    | M11 M12 M13 M14 M15 |    | R |   | R' |
     *   | M21 M22 M23 M24 M25 |    | G |   | G' |
     *  | M31 M32 M33 M34 M35 |  × | B | = | B' |
     * | M41 M42 M43 M44 M45 |    | A |   | A' |
     *                               | 1 |
     * @return
     */
    /**
     * @brief UsRestoreAfterExportVideo
     * @param context
     * @param cinepath 导出前是否load了一个电影，如果是 则传入电影的路径 如果没有 就传递 NULL
     * @param playindex 上次电影播放的位置
     * @return
     */
    US_API UsRetCode CT_CALL UsRestoreAfterExportVideo(UsContext context, const char *const cinepath, int playindex);

    US_API UsRetCode CT_CALL UsSetImageColorFilter(UsContext context, float *filterArray);
    //-------------------------------------UsGraphicsScene---------------------------------------
    US_API UsScene CT_CALL UsGraphicsScene(UsContext context);

    US_API UsOverlay CT_CALL UsGetOverlay(UsContext context);

    US_API UsGlyphsControl CT_CALL UsGetGlyphsControl(UsContext context, GlyphsControlType type);

    US_API UsRetCode CT_CALL UsImageTransform(UsContext context, double scale, double translateX, double translateY);

    US_API UsRetCode CT_CALL UsImageTransformReset(UsContext context);

    /**
     * @brief UsSetPenStyle 设置当前画笔样式为虚线点
     * @param context
     * @param penStyle 画笔样式，0：是实线，1：虚线
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsSetPenStyle(UsContext context, const int penStyle);

    /**
     * @brief UsSetPenStyle 设置ROI框当前画笔样式为虚线点
     * @param context
     * @param penStyle 画笔样式，0：是实线，1：虚线

     */
    US_API UsRetCode CT_CALL UsSetROIPenStyle(UsContext context, bool isResizing);

    /**
     * @brief UsGetCROIPoints 获取CROI框相对于图像的四个坐标点
     * @param context
     * @param pointArray 坐标数组首地址
     * @param count 数组size，需要至少为4
     */
    US_API UsRetCode CT_CALL UsGetCROIPoints(UsContext context, _UsLogicalPoint_INT *pointArray, const int count,
                                             int *validCount);
    // ----------------------------------测量参数计算-------------------------------------------
    /**
     * @brief 每次进入测量状态时，需要重新获取该接口，原因是当前最新的超声参数会更新测量参数。目前不支持实时测量。
     */
    US_API UsMeasureContext CT_CALL UsGetMeasureContext(UsContext context);

    US_API UsRetCode CT_CALL UsUpdateMeasureContext(UsContext context, bool updateImage);

    //------------------------------------掌超调图专用--------------------------------------------
    /**
     * 创建和调图机器的连接
     */
    US_API UsRetCode CT_CALL UsConnect2ParamsAdjustMachine(UsContext context);

    US_API UsRetCode CT_CALL UsImportControlTable(UsContext context, const char *filePath);

    US_API UsRetCode CT_CALL UsImportControlTableRange(UsContext context, const char *filePath, const int start,
                                                       const int end);

    /**
     * @brief UsAsyncStartUpgrade 升级fpga固件(异步)
     * @param context
     * @param callback fpga固件路径
     * @return UsRetCode 0表示成功，-1表示失败文件超过8M了
     */
    US_API UsRetCode CT_CALL UsAsyncStartUpgrade(UsContext context, char *fpga_binpath);

    /**
     * @brief UsStartUpgrade 升级fpga固件
     * @param context
     * @param callback fpga固件路径
     * @return UsRetCode 0表示成功，-1表示失败文件超过8M了
     */
    US_API UsRetCode CT_CALL UsStartUpgrade(UsContext context, char *fpga_binpath);
    /**
     * @brief UsGeometryChangeSize 升级结束 释放资源
     * @param context
     */
    US_API UsRetCode CT_CALL UsFinishUpgrade(UsContext context);

    /**
     * @brief UsGetHardwareVersion 获取当前FPGA版本
     * @param context
     * @param fpgaVersion
     * @param count
     */
    US_API UsRetCode CT_CALL UsGetHardwareVersion(UsContext context, char *fpgaVersion, const int count);

    /**
     * @brief UsGetDepthMMByCQYZLevel 根据探头名称，CQYZLevel档位获取对应的深度值
     * @param context
     * @param probename 探头名称
     * @param level cqyzlevel档位值
     * @param depthShow 深度值
     */
    US_API UsRetCode CT_CALL UsGetDepthMMByCQYZLevel(UsContext context, const char *probename, int level,
                                                     double *depthShow);

    /**
     * @brief UsGetDepthMMByCQYZ 根据探头名称，CQYZLevel档位获取对应的深度值
     * @param context
     * @param probename 探头名称
     * @param level cqyz档位值
     * @param depthShow 深度值
     */
    US_API UsRetCode CT_CALL UsGetDepthMMByCQYZ(UsContext context, const char *probename, int level, double *depthShow);

    //-----------------------------------掌超license专用----------------------------
    /**
     * @param context
     * @param sn 返回sn编号
     * @param forceupdate: 更新标记，app启动后第一次读取需要强制更新从探头获取，或者app
     *断开重连需要，以后读取不需要设置了
     * @return -3 license 损坏 或者 license读取失败 0:ok
     **/
    US_API int CT_CALL UsLicenseRead(UsContext context, Plicense *pli, int forceupdate);
    /**
     * @param context
     * @param key key值
     * @param sn   sn编号
     * @return：
     *  -9: licese  key 还未从fpaga同步过来
     *  -1: license key 是非法的
     *  -2: 当前的key 不是 指定的类型 见参数keytype（租赁，肺，pw...），keytype==-1 自动判断类型
     *  -3: 当前的key 日期不合法 key的制作日期>当前日期+7day
     *  -4: 当前key的日期 早于上次key烧录的日期
     *  -5: 当前key不在当前版本管理中
     *  -6: 当前key本来就是关闭的
     *  -7: 当前key无法开启当前功能，超出了当前功能数量范围，或者已经开启了
     *  -8: 当前key是标配和不配置的，不需要操作
     *  -9: 未知错误，
     *  =0  写入成功
     **/
    US_API int CT_CALL UsLicenceWrite(UsContext context, char *key, char *sn, int keytype);
    //---------------------------------------------------------------------------------
    /**
     * @param context
     * @param dscVersion T3.0.5 e.g
     * @param ecVersion  2.0  e.g
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsVersion(UsContext context, char *dscVersion, char *ecVersion);
    /**
     * @param context
     * @param probestatus 返回探头当前的电池 电量等信息
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsGetProbeStatus(UsContext context, ProbeStatus *probestatus);
    /**
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsProbeShutDown(UsContext context);
    /**
     * @param context
     * @param time  tgc 显示时间 <0 :永久显示  0:永不显示 >0:显示时间 time sec后 隐藏
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsSetTgcShowTime(UsContext context, int time);
    /**
     * @param context
     * @param angle
     * @param posX  posY  穿刺线起始点的坐标 注意 Left 偏转标记
     * @param angleRange 角度范围 angle- angleRange ~  angle + angleRange
     * @param offsetRangeX offsetRangeY 位置移动范围 posX -  offsetRangeX ~   posX +  offsetRangeX 原始未放大的范围
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsSelectBiopsyAngle(UsContext context, int angle, int *posX, int *posY, int *angleRange,
                                                 int *offsetRangeX, int *offsetRangeY);
    /**
     * @param context
     * @param angle 返回值   当前biosy angle 的位置和角度
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsGetBiopsyAngle(UsContext context, int *posX, int *posY, int *angle);
    /**
     * @param context
     * @param angle 返回值   角度列表 和上次选择的角度
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsGetBiopsyAngleList(UsContext context, int *angle, int *size);
    /**
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsSaveCurrentBiopsyAngle(UsContext context);
    /**
     * @param context
     * @param angle 当前角度  posX posY 当前位置
     * @return UsRetCode 0表示成功，-1表示失败（超过范围）
     **/
    US_API UsRetCode CT_CALL UsBiopsyAngleMove(UsContext context, int angle, int posX, int posY);
    /**
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsBiopsyAngleReset(UsContext context);
    /**
     * @param context
     * @param time  超时时间 ms
     * @return UsRetCode 0表示成功，-1表示失败
     **/
    US_API UsRetCode CT_CALL UsProcessEvents(UsContext context, int time);
    /**
     * @brief UsSetBackGround
     * @param context
     * @param state 1:进入后台  0:非后台
     * @return
     */
    US_API UsRetCode CT_CALL UsSetBackGround(UsContext context, int state);

    //------------------------------------功能--------------------------------------------
    /**
     * @brief UsIsSupportFunction   获取当前选择的探头下当前预设是否支持该功能
     * @param context
     * @param funcType 功能类型
     * @return 返回是否支持 true 支持 false 不支持
     */
    US_API bool CT_CALL UsIsSupportFunction(UsContext context, UsFunctionType funcType);

    /**
     * @brief UsVAOpen 打开功能
     * @param context
     * @param funcType 功能类型
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsOpenFunction(UsContext context, UsFunctionType funcType);

    /**
     * @brief UsVAOpen 关闭功能
     * @param context
     * @param funcType 功能类型
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsCloseFunction(UsContext context, UsFunctionType funcType);

    /**
     * @brief UsSetImageCheckTimeInterval 设置检测图像变化的时间间隔
     * @param context
     * @param timeSec 以秒为单位
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsSetImageCheckTimeInterval(UsContext context, int timeSec);

    /**
     * @brief UsGetImageCheckTimeInterval 获取检测图像变化的时间间隔
     * @param context
     * @param timeSec 以秒为单位
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGetImageCheckTimeInterval(UsContext context, int *timeSec);

    /**
     * @brief UsSetImageCheckState 设置检测图像功能开闭状态
     * @param context
     * @param open 是否打开
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsSetImageCheckState(UsContext context, bool open);

    /**
     * @brief UsSetImageCheckState 获取检测图像功能开闭状态
     * @param context
     * @param open 是否打开
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGetImageCheckState(UsContext context, bool *open);

    /**
     * @brief UsSetImageCheckSensitiveThreshold 设置检测图像算法敏感度阈值
     * @param context
     * @param thrshold 0-1取值范围，值越大，算法检测敏感度越高，默认0.5
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsSetImageCheckSensitiveThreshold(UsContext context, float thrshold);

    /**
     * @brief UsGetImageCheckSensitiveThreshold 获取检测图像敏感度阈值
     * @param context
     * @param thrshold
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGetImageCheckSensitiveThreshold(UsContext context, float *thrshold);
    /**
     * @brief UsSetProbeSelfTestCallback 设置基元测试回调
     * @param context       句柄
     * @param callback      回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeSelfTestCallback(UsContext context, UsProbeSelfTestCallback callback,
                                                        void *userData);
    /**
     * @brief UsAutoProbeSelfTest  基元自动测试
     * @param context
     * @param type    类型，1 channel,2 element
     * @param second  时间间隔
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsAutoProbeSelfTest(UsContext context, int type, int second);
    /**
     * @brief UsSingleProbeSelfTest  基元单个测试
     * @param context
     * @param type    类型，1 channel,2 element
     * @param num     第几基元
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsSingleProbeSelfTest(UsContext context, int type, int num);
    /**
     * @brief UsStopProbeSelfTest  停止基元自动测试
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsStopProbeSelfTest(UsContext context);

    /**
     * @brief UsOpenProbeSelfTest  打开基元测试
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsOpenProbeSelfTest(UsContext context);

    /**
     * @brief UsCloseProbeSelfTest  关闭基元测试
     * @param context
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsCloseProbeSelfTest(UsContext context);

    /**
     * @brief UsGetProbeValidElementCounts 获取探头有效基元总数
     * @param context
     * @param counts 基元数
     * @return UsRetCode 0表示成功，-1表示失败
     */
    US_API UsRetCode CT_CALL UsGetProbeValidElementCounts(UsContext context, int *counts);

    /**
     * @brief UsImportPreset 导入预设
     * @param context
     * @param filePath 导入文件，使用绝对路径
     * @return
     */
    US_API UsRetCode CT_CALL UsImportPreset(UsContext context, const char *const filePath);

    /**
     * @brief UsExportPreset 导出预设
     * @param context
     * @param filePath 导出文件，使用绝对路径
     * @return
     */
    US_API UsRetCode CT_CALL UsExportPreset(UsContext context, const char *const filePath);

    /**
     * @brief UsRestorePreset 恢复默认预设
     * @param context
     * @return
     */
    US_API UsRetCode CT_CALL UsRestorePreset(UsContext context);

    /**
     * @brief UsGetFocusPosDepth 根据focusPos,focusNum 获取焦点深度
     * @param context
     * @param focusNum
     * @param focusPos
     * @param depth
     * @return
     */
    US_API UsRetCode CT_CALL UsGetFocusPosDepthMM(UsContext context, int focusNum, int focusPos, float *depth);

    /**
     * @brief UsSetIImageType 设置优化类型，默认是0
     * @param context
     * @param typeValue
     * @return
     */
    US_API UsRetCode CT_CALL UsSetIImageType(UsContext context, int typeValue);
    /**
     * @brief UsGetIImageType 获取优化类型
     * @param context
     * @param typeValue
     * @return
     */
    US_API UsRetCode CT_CALL UsGetIImageType(UsContext context, int *typeValue);

    /**
     * @brief UsSetProbeAssessmentProgressCallback  设置基元检测进度回调函数
     * @param context       句柄
     * @param callback      基元检测进度回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeAssessmentProgressCallback(UsContext context,
                                                                  UsProbeAssessmentProcessCallback callback,
                                                                  void *userData);

    /**
     * @brief UsSetProbeAssessmentResultCallback   设置基元检测回调函数
     * @param context       句柄
     * @param callback      基元检测结果回调函数
     * @param userData      用户数据
     * @return
     */
    US_API UsRetCode CT_CALL UsSetProbeAssessmentResultCallback(UsContext context,
                                                                UsProbeAssessmentResultCallback callback,
                                                                void *userData);

    /**
     * @brief UsStartProbeAssessment   开启基元检测
     * @param context      句柄
     * @return
     */
    US_API UsRetCode CT_CALL UsStartProbeAssessment(UsContext context);

    /**
     * @brief UsStopProbeAssessment    关闭基元检测
     * @param context      句柄
     * @return
     */
    US_API UsRetCode CT_CALL UsStopProbeAssessment(UsContext context);

    /** 2024-06-20 Write by AlexWang
     * @brief UsGetAllParamterNames
     * @param context
     * @param paraNames
     * @param arraySize
     * @param itemSize
     * @return
     */
    US_API int CT_CALL UsGetAllParamterNames(UsContext context, char **paraNames, int arraySize, int itemSize);

    /** 2024-08-05 Write by AlexWang
     * @brief UsSetBeforeZeusSaveFlag
     * @param context
     * @param beforeZeusSaveFlag
     * @return
     */
    US_API UsRetCode CT_CALL UsSetBeforeZeusSaveFlag(UsContext context, bool beforeZeusSaveFlag);

    /** 2024-08-05 Write by AlexWang
     * @brief UsSetAfterZeusSaveFlag
     * @param context
     * @param afterZeusSaveFlag
     * @return
     */
    US_API UsRetCode CT_CALL UsSetAfterZeusSaveFlag(UsContext context, bool afterZeusSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief UsSetBeforeIImageSaveFlag
     * @param context
     * @param beforeIImageSaveFlag
     * @return
     */
    US_API UsRetCode CT_CALL UsSetBeforeIImageSaveFlag(UsContext context, bool beforeIImageSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief UsSetAfterIImageSaveFlag
     * @param context
     * @param afterIImageSaveFlag
     * @return
     */
    US_API UsRetCode CT_CALL UsSetAfterIImageSaveFlag(UsContext context, bool afterIImageSaveFlag);

    /** 2024-08-07 Write by AlexWang
     * @brief UsExportProperties
     * @param context
     * @return
     */
    US_API UsRetCode CT_CALL UsExportProperties(UsContext context);

#ifdef __cplusplus
}
#endif

#endif // USAPI_H
