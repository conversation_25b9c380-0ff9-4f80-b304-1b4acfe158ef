#ifndef USAPIDEF_H
#define USAPIDEF_H

#if !defined(SAG_COM) && (defined(WIN64) || defined(_WIN64) || defined(__WIN64__))
#define C_OS_WIN32
#define C_OS_WIN64
#elif !defined(SAG_COM) && (defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__))
#if defined(WINCE) || defined(_WIN32_WCE)
#define C_OS_WINCE
#else
#define C_OS_WIN32
#endif
#elif defined(__linux__) || defined(__linux)
#define C_OS_LINUX
#elif defined(__APPLE__)
#define C_OS_APPLE
#include <string.h>
#else
#error "API has not been ported to this OS"
#endif

#if defined(C_OS_WIN32) || defined(C_OS_WIN64) || defined(C_OS_WINCE)
#define C_OS_WIN
#endif

#ifndef C_DECL_EXPORT
#if defined(C_OS_WIN)
#define C_DECL_EXPORT __declspec(dllexport)
#endif
#ifndef C_DECL_EXPORT
#define C_DECL_EXPORT __attribute__((visibility("default")))
#endif
#endif
#ifndef C_DECL_IMPORT
#if defined(C_OS_WIN)
#define C_DECL_IMPORT __declspec(dllimport)
#else
#define C_DECL_IMPORT __attribute__((visibility("default")))
#endif
#endif

#if defined(USCONTEXTAPI_LIBRARY)
#define US_API C_DECL_EXPORT
#else
#define US_API C_DECL_IMPORT
#endif

//#ifdef C_OS_WIN
//#define CT_CALL __stdcall
//#else
#define CT_CALL
//#endif

#ifdef __cplusplus
extern "C"
{
#endif
#include <string.h>

    typedef void *UsContext;
    typedef void *UsScene;
    typedef void *UsOverlay;
    typedef void *UsGlyphsControl;
    typedef void *UsMeasureContext;
    typedef unsigned char quint8; /* 8 bit unsigned */
    //错误码
    enum UsRetCode
    {
        UsNotSupportNow = -4,
        UsInvalidArgs = -3,
        UsInvalidContext = -2,
        UsFailed = -1,
        UsOK = 0,
    };

    enum UsUltrasoundErrorCode
    {
        UsLink_NO_Device = 400,
        UsLink_NOT_Open = 401,
        UsLink_NO_Resource = 402,
        UsLink_NO_Driver = 403,
    };

    enum UsFanErrorCode
    {
        UsFan_Error_CPU = 600,
        UsFan_Error_Sys = 601,
    };

    //播放速度
    enum UsPlaySpeed
    {
        UsPlaySpeed_X_1_2 = 0, // 1/2播放
        UsPlaySpeed_X_1 = 1,   //正常播放
        UsPlaySpeed_X_2        // 2倍速播放
    };

    // Image输出图像类型
    enum UsImageType
    {
        UsImageB,
        UsImageC,
        UsImageD,
        UsImageM,
        UsImageECG,
        UsImageElasto,
        UsImageCurvedPan,
        UsImageFourD
    };
    //功能类型
    enum UsFunctionType
    {
        UsVADetect = 1,
    };
    enum MachineType
    {
        HumanMachine = 0,
        AnimalMachine = 1
    };
    // Image 优化类型
    enum UsIImageType
    {
        ByPass = -1,
        Internal = 0,
        External = 1
    };
    typedef struct US_API _ProbeStatus
    {
        float fpgaTemp;      // fpag温度
        int batteryCapacity; //电池容量
        int adapterStatus;   //适配器状态
        float batteryTemp;   //电池温度
        int batteryVol;      //电池电压    mv
        int batteryCurrent;  //电池电流 mA
        float boardTemp;     //主板温度
        int chargeStatus;    //充电状态
        _ProbeStatus(_ProbeStatus &)
            : fpgaTemp(0.0f)
            , batteryCapacity(0)
            , adapterStatus(0)
            , batteryTemp(0.0f)
            , batteryVol(0)
            , batteryCurrent(0)
            , boardTemp(0.0f)
            , chargeStatus(0)
        {
        }
        _ProbeStatus()
            : fpgaTemp(0.0f)
            , batteryCapacity(0)
            , adapterStatus(0)
            , batteryTemp(0.0f)
            , batteryVol(0)
            , batteryCurrent(0)
            , boardTemp(0.0f)
            , chargeStatus(0)
        {
        }
    } ProbeStatus;
    typedef struct US_API _Plicense
    {
        char sn[32];
        char probeModeName[32];
        int isanimal;
        int RentStatus; //租赁  -1: 永久  >=0:剩余时间
        //功能  状态 低0-7bit: （1:打开） (0:关闭) ; 8-15bit: 0:不配置  1:标配  2:选配 ;  16-23bit:(keytype的值)
        int PW_ENABLE;
        int DICOM_ENABLE;
        int CFM_ENABLE;
        int IMT_ENABLE;
        int SuperNeedle_ENABLE;
        int Lung_ENABLE;
        int AV_ENABLE;
        int AIO_ENABLE;
        int Biopsy_ENABLE;
        int Language_ENABLE;
        int BM_ENABLE;
        int FHI_ENABLE;
        int ChinaUI_ENABLE; // 同台式机上的CMD
        int Remote_ENABLE;
        int AutoEF_ENABLE;
        int CurvedToPhased_ENABLE; //凸阵转相控阵
        _Plicense(_Plicense &)
            : isanimal(0)
            , RentStatus(-1)
            , PW_ENABLE(0)
            , DICOM_ENABLE(0)
            , CFM_ENABLE(0)
            , IMT_ENABLE(0)
            , SuperNeedle_ENABLE(0)
            , Lung_ENABLE(0)
            , AV_ENABLE(0)
            , AIO_ENABLE(0)
            , Biopsy_ENABLE(0)
            , Language_ENABLE(0)
            , BM_ENABLE(0)
            , FHI_ENABLE(0)
            , ChinaUI_ENABLE(0)
            , Remote_ENABLE(0)
            , AutoEF_ENABLE(0)
            , CurvedToPhased_ENABLE(0)
        {
            memset(sn, 0, sizeof(sn));
            memset(probeModeName, 0, sizeof(probeModeName));
        }
        _Plicense()
            : isanimal(0)
            , RentStatus(-1)
            , PW_ENABLE(0)
            , DICOM_ENABLE(0)
            , CFM_ENABLE(0)
            , IMT_ENABLE(0)
            , SuperNeedle_ENABLE(0)
            , Lung_ENABLE(0)
            , AV_ENABLE(0)
            , AIO_ENABLE(0)
            , Biopsy_ENABLE(0)
            , Language_ENABLE(0)
            , BM_ENABLE(0)
            , FHI_ENABLE(0)
            , ChinaUI_ENABLE(0)
            , Remote_ENABLE(0)
            , AutoEF_ENABLE(0)
            , CurvedToPhased_ENABLE(0)
        {
            memset(sn, 0, sizeof(sn));
            memset(probeModeName, 0, sizeof(probeModeName));
        }
    } Plicense;
    //数据出口定义
    /**
     * @param context                   句柄
     * @param data                      图像数据
     * @param width                     图像宽度
     * @param height                    图像高度
     * @param byteCount                 像素位数
     * @param imageType                 图像类型
     * @param layoutInfo                图像区域索引取值0~3
     * @param frameIndex                图像帧索引
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsDataCallback)(UsContext context, unsigned char *data, unsigned int width,
                                           unsigned int height, unsigned int byteCount, UsImageType imageType,
                                           const int layoutIndex, unsigned int frameIndex, void *userData);
    //参数变化通知
    /**
     * @param context                   句柄
     * @param paraName                  参数名称
     * @param layoutIndex               参数区域索引
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsParaChangedCallback)(UsContext context, const char *paraName, const int layoutIndex,
                                                  void *userData);

    //参数变化通知
    /**
     * @param context                   句柄
     * @param ret                       升级准备中:0; 升级过程中：1; 升级确认中:2; 升级失败 :3  升级成功：4
     * @param process                   升级进度:1 ~100 匹配 ret == 1
     */
    typedef void (*CT_CALL UsUpgradeProcessCallback)(UsContext context, const int ret, const int process,
                                                     void *userData);

    /**
     * @param context                   句柄
     * @param ret                       升级准备中:0; 升级过程中：1; 升级确认中:2; 升级失败 :3  升级成功：4
     * @param process                   升级进度:1 ~100 匹配 ret == 1
     */
    typedef void (*CT_CALL UsProbeAssessmentProcessCallback)(UsContext context, const int ret, const int process,
                                                             void *userData);

    /**
     * @param context                   句柄
     * @param type                      转换类型
     * @param process                   转换进度:1 ~100
     */
    typedef void (*CT_CALL UsConvertProcessCallback)(UsContext context, const int type, const char *exportfilepath,
                                                     const int process);
    // wifi发送函数上层提供
    /**
     * @param context                   句柄
     * @param data                      待写入数据
     * @param size                      待写入数据大小
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsWifiWriteCallback)(UsContext context, unsigned char *data, const int size, void *userData);
    //探头信息变化通知
    /**
     * @param context                   句柄
     * @param info                      探头名称信息
     * @param size                      探头名称个数
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsProbeInfoCallback)(UsContext context, char **info, const int size, void *userData);
    //探头按键信息通知
    /**
     * @param context                   句柄
     * @param info                      按键信息数组5个int的数组 前4个字节是按键信息  后面是充电状态
     * @param size                      数组大小
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsProbeEventCallback)(UsContext context, unsigned int *info, const int size, void *userData);

    /**
     * @param context                   句柄
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsImgaeNoChangeCallback)(UsContext context, void *userData);

    //探头基元测试通知
    /**
     * @param context                   句柄
     * @param index                     当前基元
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsProbeSelfTestCallback)(UsContext context, const int index, void *userData);

    //探头基元测试通知
    /**
     * @param context                   句柄
     * @param resultData                用户数据
     */
    typedef void (*CT_CALL UsProbeAssessmentResultCallback)(UsContext context, unsigned int *info, const int size,
                                                            void *userData);

    /**
     * @param context                   句柄
     * @param data                      图像数据
     * @param width                     图像宽度
     * @param height                    图像高度
     * @param byteCount                 像素位数
     * @param imageType                 图像类型
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsRawDataCallback)(UsContext context, unsigned char *data, unsigned int width,
                                              unsigned int height, unsigned int byteCount, UsImageType imageType,
                                              const int layoutIndex, void *userData);

#define MAX_PROBE_NAME_SIZE 128
    //探头信息
    typedef struct US_API _UsProbeDataInfo
    {
        /**
         * @brief 探头名称
         */
        char Name[MAX_PROBE_NAME_SIZE];
        /**
         * @brief Code 探头码
         */
        int Code;
        /*!
         * 探头基元数
         */
        int WaferNum;
        /*!
         * 探头基元长度
         */
        float WaferLength;
        /*!
         * 探头基元半径
         */
        float WaferRadius;
        /*!
         * 该探头是否为线阵
         */
        int IsLinear;

        int NoUseLine;
        _UsProbeDataInfo(_UsProbeDataInfo &);
        _UsProbeDataInfo();
    } UsProbeDataInfo;
    typedef struct US_API _DscImageTransfromInformation
    {
        int Dscwidth;
        int Dscheight;
        int StartLine;
        int StopLine;
        double PixelSizeMM;  //一个像素多少MM
        double LineSpacing;  //线间距MM
        double WaferRadius;  //探头半径 凸阵
        double AngleSpacing; //凸阵 探头 或者 相控阵 相邻线之间的弧度
        double Depthmm;      //当前深度
        bool Islinear;       //是否是线阵
        bool IsPhased;       //是否是相控阵
        _DscImageTransfromInformation(_DscImageTransfromInformation &)
            : Dscwidth(0)
            , Dscheight(0)
            , StartLine(0)
            , StopLine(0)
            , PixelSizeMM(0.0f)
            , LineSpacing(0.0f)
            , WaferRadius(0.0f)
            , AngleSpacing(0.0f)
            , Depthmm(0.0f)
            , Islinear(false)
            , IsPhased(false)
        {
        }
        _DscImageTransfromInformation()
            : Dscwidth(0)
            , Dscheight(0)
            , StartLine(0)
            , StopLine(0)
            , PixelSizeMM(0.0f)
            , LineSpacing(0.0f)
            , WaferRadius(0.0f)
            , AngleSpacing(0.0f)
            , Depthmm(0.0f)
            , Islinear(false)
            , IsPhased(false)
        {
        }
    } DscImageTransfromInformation;

    struct US_API _UsLogicalPoint_INT
    {
        int X;
        int Y;
        _UsLogicalPoint_INT()
            : X(0)
            , Y(0)
        {
        }
        _UsLogicalPoint_INT(_UsLogicalPoint_INT &)
            : X(0)
            , Y(0)
        {
        }
    };
    struct US_API _UsLogicalPoint_DOUBLE
    {
        double X;
        double Y;
        _UsLogicalPoint_DOUBLE()
            : X(0.0f)
            , Y(0.0f)
        {
        }
        _UsLogicalPoint_DOUBLE(_UsLogicalPoint_DOUBLE &)
            : X(0.0f)
            , Y(0.0f)
        {
        }
    };

    class US_API UsLogicalPoint
    {
      public:
#ifndef SYS_APPLE
        union
        {
#endif
            _UsLogicalPoint_INT UsLogicalPoint_INT;
            _UsLogicalPoint_DOUBLE UsLogicalPoint_DOUBLE;
#ifndef SYS_APPLE
        };
#endif
        UsLogicalPoint(UsLogicalPoint &);
        UsLogicalPoint();
    };

    // C模式下ROI变化通知
    /**
     * @param context                   句柄
     * @param pointArray              ROI四个点数组首地址
     * @param count                   数组中中有效数据大小
     * @param userData                用户数据
     */
    typedef void (*CT_CALL UsCROIChangedCallback)(UsContext context, const _UsLogicalPoint_INT *pointArray,
                                                  const int count, void *userData);

    typedef struct US_API _UsVesselInfo
    {
        int x; // left
        int y; // top
        int width;
        int height;
        int type;  // vessel type 0：Artery 1:Vein
        int state; // 0-steady 1-moving
        float typeProbability;
    } UsVesselInfo;

    enum UsVesselType
    {
        UsArtery = 0,
        UsVein = 1
    };
    enum UsVesselState
    {
        UsSteady = 0,
        UsMoving = 1
    };

    // VA检测结果更新通知
    /**
     * @param context                   句柄
     * @param info                      血管检测信息
     * @param size                      血管个数
     * @param userData                  用户数据
     */
    typedef void (*CT_CALL UsVADetectInfoCallback)(UsContext context, UsVesselInfo *info, const int size,
                                                   void *userData);

    //功能开关状态变化通知
    /**
     * @param context                   句柄
     * @param on                      是否打开
     * @param userData                用户数据
     */
    typedef void (*CT_CALL UsFunctionStateChangedCallback)(UsContext context, UsFunctionType functionType, bool on,
                                                           void *userData);

    //通信异常回调
    /**
     * @param context                   句柄
     * @param errorcode                 异常码
     * @param userData                用户数据
     */
    typedef void (*CT_CALL UsCommunicationLinkErrorCallback)(UsContext context, int errorcode, void *userData);

    //风扇异常回调
    /**
     * @param context                   句柄
     * @param errorcode                 异常码
     * @param userData                用户数据
     */
    typedef void (*CT_CALL UsFanErrorCallback)(UsContext context, int errorcode, void *userData);

    enum UsLayout
    {
        UsLayout_1X1 = 1,
        UsLayout_1X2 = 2,
        UsLayout_2X2 = 4,
        UsLayoutNone
    };

    enum UsScanMode
    {
        UsSystemScanModeB,
        UsSystemScanMode2B,
        UsSystemScanMode4B,
        UsSystemScanModeLRBM,
        UsSystemScanModeUDBM,
        UsSystemScanModeM,
        UsSystemScanModeColorDoppler,
        UsSystemScanModePowerDoppler,
        UsSystemScanModeDPowerDoppler, // Directional Power Doppler
        UsSystemScanModeBPW,
        UsSystemScanModeColorPW,
        UsSystemScanModePowerPW,
        UsSystemScanModeDPowerPW,
        UsSystemScanModeCWD,             // Continuous-Wave Doppler
        UsSystemScanModeCWDColorDoppler, // CWD + CF
        UsSystemScanModeCWDDirectionalPowerDoppler,
        UsSystemScanModeCWDPowerDoppler,
        UsSystemScanModeTissueDoppler, // TDI,Tissue Doppler Imaging
        UsSystemScanModeTissuePW,      // TDI+PW
        UsSystemScanModeColorM,
        UsSystemScanModeColorLRBM,
        UsSystemScanModeColorUDBM,
        UsSystemScanModePDM,
        UsSystemScanModePDLRBM,
        UsSystemScanModePDUDBM,
        UsSystemScanModeDPDM,
        UsSystemScanModeDPDLRBM,
        UsSystemScanModeDPDUDBM,
        UsSystemScanModeTDIM,
        UsSystemScanModeTDILRBM,
        UsSystemScanModeTDIUDBM,
        UsSystemScanModeE,
        UsSystemScanModeLRFreeM,
        UsSystemScanModeUDFreeM,
        UsSystemScanModeFourDPre,
        UsSystemScanModeFourDLive,
        UsSystemScanModeFourD,
        UsSystemScanModeLRBBC,
        UsSystemScanModeElasto,
        UsSystemScanModeBBE,
        UsSystemScanModeUDBBC,
        UsSystemScanModeFreeHand3D,
        UsSystemScanModeCP, // Curved Panoramic
        UsSystemScanModeNone
    };

    enum GlyphsControlType
    {
        ROIGlyphsCtr,
        CommentGlyphsCtr,
        ZoomSelectGlyphsCtr,
        BodyMarkGlyphsCtr,
        LineMeasureGlyphsCtr,
        EllipseMeasureGlyphsCtr,
        TraceMeasureGlyphsCtr,
        AssistLineMeasureGlyphsCtr,
        AutoCubicSplineGlyphsCtr,
        A4CTraceMeasureGlyphsCtr,
        ArrowGlyphsCtr,
        RectGlyphsCtr,
        PointMeasureGlyphsCtr,
        EnvelopeTraceMeasureGlyphsCtr,
        AutoTraceMeasureGlyphsCtr,
        HIPLineGlyphsCtr,
        AssistLineCubeGlyphsCtr,
        AssistLineFixedGlyphsCtr,
        ImtGlyphsCtr,
        TwoPointMeasureGlyphsCtr,
        FreeHand3DRoiGlyphsCtr,
        TraceLMeasureGlyphsCtr,
        AutoTraceLGlyphsCtr,
        RealTimeAutoTraceMeasureGlyphsCtr
    };

#ifdef __cplusplus
}
#endif

#endif // USAPIDEF_H
