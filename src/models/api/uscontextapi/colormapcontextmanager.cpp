#include "colormapcontextmanager.h"
#include "ibeamformer.h"
#include "ibfkitfactory.h"
#include "colormapcontext.h"
#include "util.h"

ColorMapContextManager::ColorMapContextManager()
    : m_BFKitFactory(NULL)
{
}

ColorMapContextManager::~ColorMapContextManager()
{
    qDeleteAll(m_Contexts);
    m_Contexts.clear();
    Util::SafeDeletePtr(m_BFKitFactory);
}

void ColorMapContextManager::setBFKitFactory(IBeamFormer *beamformer)
{
    m_BFKitFactory = beamformer->createBFKitFactory();
}

ColorMapContext *ColorMapContextManager::createContext(SonoParameters *sonoParameters)
{
    if(NULL == m_BFKitFactory)
    {
        return NULL;
    }

    ColorMapContext *context = new ColorMapContext();
    context->create(m_BFKitFactory->createBFRawData2ShowData());
    context->setSonoParameters(sonoParameters);
    m_Contexts.append(context);
    return context;
}

void ColorMapContextManager::removeContext(int index)
{
    m_Contexts.removeAt(index);
}

ColorMapContext *ColorMapContextManager::context(int index) const
{
    if(index < 0 && index >= m_Contexts.size())
    {
        return NULL;
    }

    return m_Contexts[index];
}

void ColorMapContextManager::clear()
{
    qDeleteAll(m_Contexts);
    m_Contexts.clear();
}

void ColorMapContextManager::createContexts(int size)
{
    for(int i = 0; i < size ; ++i)
    {
        createContext();
    }
}
