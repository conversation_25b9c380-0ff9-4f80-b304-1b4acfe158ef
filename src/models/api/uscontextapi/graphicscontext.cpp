#include "graphicscontext.h"
#include <QGraphicsScene>
#include "pimplglyphswidget.h"
#include "overlay.h"
#include "util.h"
#include "sonoparameters.h"

GraphicsContext::GraphicsContext()
    : m_GraphicsScene(new QGraphicsScene()),
      m_ModeGlyphsWidget(new PimplGlyphsWidget(m_GraphicsScene))
{
}

GraphicsContext::~GraphicsContext()
{
    Util::SafeDeletePtr(m_GraphicsScene);
    Util::SafeDeletePtr(m_ModeGlyphsWidget);
}

QGraphicsScene *GraphicsContext::scene() const
{
    return m_GraphicsScene;
}

void GraphicsContext::setSonoParameters(SonoParameters *sonoParameters)
{
    m_ModeGlyphsWidget->setSonoParameters(sonoParameters);
}

void GraphicsContext::setBufferManager(IMuiltSonoParametersBuffer *bufferManager)
{
    m_ModeGlyphsWidget->setBufferManager(bufferManager);
}

void GraphicsContext::setSceneSize(const QSize &size)
{
    m_ModeGlyphsWidget->overlay().setSize(size);
}

PimplGlyphsWidget *GraphicsContext::modeGlyphsWidget()
{
    return m_ModeGlyphsWidget;
}

Overlay *GraphicsContext::overlay()
{
    return &m_ModeGlyphsWidget->overlay();
}
