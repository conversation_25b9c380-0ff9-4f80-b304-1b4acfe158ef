#include "presetconfigbase.h"
#include "resource.h"
//#include <QJsonObject>
//#include <QJsonDocument>
//#include <QJsonArray>
#include "bfpnames.h"
#include "crypticfile.h"
#include "iexammodepresetdatahandler.h"
#include "exammodepresethandlerfactory.h"
#include "exammodepresetdatahandlerbase.h"
#include "iprobedataset.h"
#include "presetutilitytool.h"
#include "qapp.h"
#include "sonoparameters.h"
#include "json/reader.h"
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>

PresetConfigBase::PresetConfigBase(QHash<QString, QList<PresetAliasName>>& presetAliasNames,
                                   IProbeDataSet* probeDataSet, SonoParameters* currentSonoParameters,
                                   SonoParameters* globalSonoParameters)
    : m_PresetAliasNames(presetAliasNames)
    , m_CurrentSonoParameters(currentSonoParameters)
    , m_GlobalSonoParameters(globalSonoParameters)
    , m_ProbeDataSet(probeDataSet)
    , m_DataBaseManager(NULL)
{
}

PresetConfigBase::~PresetConfigBase()
{
}

void PresetConfigBase::readPresetConfig()
{
    return;
}

bool PresetConfigBase::saveCurrentPreset()
{
    bool avOpened = m_GlobalSonoParameters->pBV(BFPNames::SonoAVStr);
    QString emID = m_GlobalSonoParameters->pV(BFPNames::ExamModeIdStr).toString();
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    handle->saveProbeExamMode(probeID, emID, m_GlobalSonoParameters->preset().presetParameters(),
                              avOpened == 1 ? FunctionTypeDef::VADetect : FunctionTypeDef::Standard);
    return true;
}

bool PresetConfigBase::saveCurrentPresetAs(const char* presetName, int type)
{
    if (presetName == NULL)
    {
        return false;
    }
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    QString newEMId = PresetUtilityTool::uuid("exammode", presetName);
    ExamMode mode;
    mode.setExamModeID(newEMId);
    mode.setExamModeName(presetName);
    mode.setExamModeType(type);

    QList<PresetParameter> ps = m_GlobalSonoParameters->preset().presetParameters();
    foreach (PresetParameter t, ps)
    {
        mode.addOnePresetParameter(t);
    }
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    QString parentEMID = m_GlobalSonoParameters->pSV(BFPNames::ExamModeIdStr);
    ExamModeRelateModuleID moduleID;
    handle->getExamModeRelateModuleID(parentEMID, moduleID);
    handle->saveAsProbeExamMode(probeID, mode, moduleID.cm_type_Id, moduleID.bm_type_Id, moduleID.ms_type_Id);
    return true;
}

bool PresetConfigBase::renameCurrentPreset(const char* presetName)
{
    if (presetName == NULL)
    {
        return false;
    }

    QString emID = m_GlobalSonoParameters->pSV(BFPNames::ExamModeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    handle->renameExamModeName(emID, presetName);
    m_GlobalSonoParameters->setPV(BFPNames::ExamModeCaptionStr, presetName); //预设值名称
    return true;
}

bool PresetConfigBase::renamePreset(const char* oldPresetName, const char* newPresetName)
{
    if (newPresetName == NULL || oldPresetName == NULL)
    {
        return false;
    }

    if (!isPresetExist(oldPresetName) || isPresetExist(newPresetName))
    {
        return false;
    }

    // QString emID = m_GlobalSonoParameters->pSV(BFPNames::ExamModeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    QStringList emIDs = handle->examModeIds(oldPresetName);
    handle->renameExamModeName(emIDs.first(), newPresetName);
    // m_GlobalSonoParameters->setPV(BFPNames::ExamModeCaptionStr, newPresetName); //预设值名称
    return true;
}

bool PresetConfigBase::deleteCurrentPreset()
{
    QString emID = m_GlobalSonoParameters->pSV(BFPNames::ExamModeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    handle->removeExamModeFromLibByProbeID(QString::number(probeID), QStringList() << emID);
    handle->removeEMIdFromEMListFile(probeID, emID);
    return true;
}

bool PresetConfigBase::deletePresetByName(const char* presetName)
{
    if (presetName == NULL)
    {
        return false;
    }
    QString emID = m_GlobalSonoParameters->pSV(BFPNames::ExamModeIdStr);
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    int probeID = m_GlobalSonoParameters->pIV(BFPNames::ProbeIdStr);
    QStringList emIds = handle->probeExamModesIdOrderList(probeID);
    QStringList emNames = handle->examModesNames(emIds);
    int index = emNames.indexOf(presetName);
    handle->removeExamModeFromLib(QStringList() << emIds[index]);
    return true;
}

PresetParameters* PresetConfigBase::loadPresetParameters(const QString& fileName)
{
    QFile file(fileName);

    if (!file.open(QIODevice::ReadOnly))
    {
        return NULL;
    }

    QDataStream in(&file);
    PresetParameters* presetParameters = new PresetParameters();
    presetParameters->load(in);

    return presetParameters;
}

bool PresetConfigBase::savePresetParameters(const QString& fileName, const PresetParameters& presetParameters)
{
    QFile file(fileName);

    if (!file.open(QIODevice::WriteOnly))
    {
        return false;
    }

    QDataStream out(&file);
    presetParameters.save(out);

    file.close();
    Util::sync();

    return true;
}

int PresetConfigBase::getProbePreset(const int probeID, char** preset, const int arraySize, const int bufferSize)
{
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    QStringList emIds = handle->probeExamModesIdOrderList(probeID);
    QStringList emNames = handle->examModesNames(emIds);
    emNames = handle->examModesNamesFilter(emIds); // 过滤不需要展示的预设，目前限制基元检测预设不可见
    const ProbeDataInfo& dataInfo = m_ProbeDataSet->getProbe(probeID);

    int j = 0;
    foreach (QString emName, emNames)
    {
        QString showname = getPrestShowName(dataInfo.Name, emName);
        if (showname == "")
        {
            continue;
        }
        memset(*(preset + j), 0, bufferSize);
        memcpy(*(preset + j), showname.toStdString().c_str(), qMin(showname.length() + 1, bufferSize));
        j++;
        if (j >= arraySize)
        {
            break;
        }
    }
    return j;
}

int PresetConfigBase::getAllPreset(char** preset, const int arraySize, const int bufferSize)
{
    QList<ExamModeAttr> ret;
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    handle->allExamModeAttr(ret);

    int j = 0;
    foreach (ExamModeAttr attr, ret)
    {
        QString showname = attr.name;
        if (showname == "")
        {
            continue;
        }
        memset(*(preset + j), 0, bufferSize);
        memcpy(*(preset + j), showname.toStdString().c_str(), qMin(showname.length() + 1, bufferSize));
        j++;
        if (j >= arraySize)
        {
            break;
        }
    }
    return j;
}

bool PresetConfigBase::updateUserPreset(const char* oldpresetShowName, const char* newPresetShowname,
                                        const int* orderlist, int orderlistLength, int code)
{
    return false;
}

bool PresetConfigBase::isPresetExist(const char* presetName) const
{
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    return handle->examModeNameExists(presetName);
}

bool PresetConfigBase::isExamModeSysLevel(const QString& emID) const
{
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    return handle->isExamModeSysLevel(emID);
}

bool PresetConfigBase::getEmIDByPresetName(const char* presetName, QString& emID) const
{
    QList<ExamModeAttr> ret;
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    handle->allExamModeAttr(ret);
    foreach (ExamModeAttr examModeAttr, ret)
    {
        if (!examModeAttr.name.compare(presetName))
        {
            emID = examModeAttr.emId;
            return true;
        }
    }
    return false;
}

QString PresetConfigBase::getPrestShowName(const QString& probeName, const QString& presetShowName)
{
    if (m_PresetAliasNames.size() == 0)
    {
        return presetShowName;
    }
    if (m_PresetAliasNames.contains(probeName))
    {
        QString presetshown = "";
        for (int i = 0; i < m_PresetAliasNames[probeName].size(); i++)
        {
            if (m_PresetAliasNames[probeName][i].PresetName == presetShowName)
            {
                presetshown = m_PresetAliasNames[probeName][i].PresetShowName;
                break;
            }
        }
        return presetshown;
    }
    return ""; //不显示的预设值
}

void PresetConfigBase::checkPresetStatusAndUpdate()
{
}

QString PresetConfigBase::getPrestName(const QString& probeName, const QString& presetName)
{
    if (m_PresetAliasNames.size() == 0)
    {
        return presetName;
    }
    if (m_PresetAliasNames.contains(probeName))
    {
        for (int i = 0; i < m_PresetAliasNames[probeName].size(); i++)
        {
            if (m_PresetAliasNames[probeName][i].PresetShowName == presetName)
            {
                return m_PresetAliasNames[probeName][i].PresetName;
            }
        }
    }
    Q_ASSERT(false);
    return "";
}

int PresetConfigBase::updatePresetAndShowName(QString jsonPath, QString probeName, QString presetName,
                                              QString newpresetName, QString newPresetShowname, int code,
                                              QList<int> presetOrder)
{
    return -1;
}

bool PresetConfigBase::importPreset(const QString& destPath)
{
    return false;
}

bool PresetConfigBase::exportPreset(const QString& destPath)
{
    return false;
}

bool PresetConfigBase::restorePreset()
{
    return false;
}

void PresetConfigBase::setDataBaseManager(IDataBaseManager* value)
{
    m_DataBaseManager = value;
}
