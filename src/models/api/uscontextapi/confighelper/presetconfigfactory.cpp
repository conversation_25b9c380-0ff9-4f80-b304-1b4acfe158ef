#include "presetconfigfactory.h"
#include "presetconfig.h"
#include "presetconfigp9.h"
#include "sonoparameters.h"

QSharedPointer<IPresetController> PresetConfigFactory::getInstance(
    QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet,
    SonoParameters *currentSonoParameters, SonoParameters *globalSonoParameters)
{
#ifdef SYS_APPLE
    return QSharedPointer<IPresetController>(
        new PresetConfig(presetAliasNames, probeDataSet, currentSonoParameters, globalSonoParameters));
#endif

    return QSharedPointer<IPresetController>(
        new PresetConfigP9(presetAliasNames, probeDataSet, currentSonoParameters, globalSonoParameters));
}

PresetConfigFactory::PresetConfigFactory()
{
}
