#ifndef BIOPSYCONFIG_H
#define BIOPSYCONFIG_H

#include <QIODevice>
#include <QString>
#include <QStringList>

class QGraphicsLineItem;
class SonoParameters;
class QJsonDocument;
class ProbeDataInfo;
class IProbeDataSet;

class BiopsyConfig
{
  public:
    typedef struct Biopsy
    {
        int LastGear;
        QStringList BiopsyAngleList; //当前可供选择的angle列表
        int ProbeId;
        int BiopsyAngle;
        int BiopsyAngleOffset;
        int BiopsyAngleOffsetRange;
        double BiopsyXPosMM;
        double BiopsyXPosMMOffset;
        double BiopsyXPosMMOffsetRange;
        double BiopsyYPosMM;
        double BiopsyYPosMMOffset;
        double BiopsyYPosMMOffsetRange;
        double DepthMMori;
        int PosStartX;
        int PosStartY;
        QGraphicsLineItem *Line;
    } Biopsy;

    BiopsyConfig(SonoParameters *sonoParameters, IProbeDataSet *probeDataSet);
    ~BiopsyConfig();

    int getBiopsyConfig(const QString &angle, Biopsy &biopsy);

    int saveBiopsyConfig(const Biopsy &biopsy);

    QPointF biopsyTransfrom(const qreal pixelSizeMM, const QPointF &origLogic, const ProbeDataInfo &probeDataInfo);

    QPointF getBiopsyPosition(const qreal pixelSizeMM);

    bool biopsyAngleMove(Biopsy &biopsy, qreal realPixelSizeMM, int angle, int biopsyposX, int biopsyposY);

    void biopsyAngleReset();

  private:
    SonoParameters *m_CurrentSonoParameters;
    IProbeDataSet *m_ProbeDataSet;
};

#endif // BIOPSYCONFIG_H
