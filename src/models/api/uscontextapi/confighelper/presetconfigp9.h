#ifndef PRESETCONFIGP9_H
#define PRESETCONFIGP9_H

#include "presetconfigbase.h"

class IProbeDataSet;
class PresetConfigP9 : public PresetConfigBase
{
  public:
    PresetConfigP9(QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet,
                   SonoParameters *currentSonoParameters = NULL, SonoParameters *globalSonoParameters = NULL);

    bool saveCurrentPreset();

    bool saveCurrentPresetAs(const char *presetName, int type = USER_TYPE);

    bool renameCurrentPreset(const char *presetName);

    bool deletePresetByName(const char *presetName);

    bool importPreset(const QString &destPath);

    bool exportPreset(const QString &destPath);

    bool restorePreset();
};

#endif // PRESETCONFIGP9_H
