#ifndef PRESETCONFIG_H
#define PRESETCONFIG_H

#include "presetconfigbase.h"
#include "usapiinfostruct.h"
#include <QHash>
#include <QList>
#include <QPair>
#include <QString>
#include <QStringList>

class SonoParameters;
class PresetParameters;

class PresetConfig : public PresetConfigBase
{
  public:
    enum OperatorType
    {
        Min = -1,
        SaveBModeFreq = Min,
        Modify = 0,
        Delete,
        Append,
        Sort,
        RestoreCurrent,
        RestoreAll,
        Max = RestoreAll
    };

    explicit PresetConfig(QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet,
                          SonoParameters *currentSonoParameters = NULL, SonoParameters *globalSonoParameters = NULL);
    ~PresetConfig();

    void readPresetConfig();

    bool saveCurrentPresetAs(const char *presetName, int type = USER_TYPE);

    int getProbePreset(const int probeID, char **preset, const int arraySize, const int bufferSize);

    bool updateUserPreset(const char *oldpresetShowName, const char *newPresetShowname, const int *orderlist,
                          int orderlistLength, int code);
    /**
     * @brief checkPresetStatusAndUpdate
     * 升级预设值的时候 比较预设值配置文件的md5值，有变化则用最新的（注:用户之前的预设值会被覆盖掉,用升级后的预设值）
     */
    void checkPresetStatusAndUpdate();

    int getPrestFreqIndexB(const QString &probeName, const QString &presetName);

    int updatePresetAndShowName(QString jsonPath, QString probeName, QString presetName, QString newpresetName,
                                QString newPresetShowname, int code, QList<int> presetOrder = QList<int>());

  private:
    int clearPreset(const QString &probeName, const QString &userJsonPath, const QString &defaultJsonPath);
};

#endif // PRESETCONFIG_H
