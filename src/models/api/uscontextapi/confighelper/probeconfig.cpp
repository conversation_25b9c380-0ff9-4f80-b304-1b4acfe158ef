#include "probeconfig.h"
#include "generalinfo.h"
#include "infostruct.h"
#include "iprobedataset.h"

ProbeConfig::ProbeConfig(const QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet)
    : m_PresetAliasNames(presetAliasNames)
    , m_ProbeDataSet(probeDataSet)
{
}

ProbeConfig::~ProbeConfig()
{
}

UsRetCode ProbeConfig::getFirstSupportProbe(UsProbeDataInfo *probeDataInfo) const
{
    if (probeDataInfo == NULL)
    {
        return UsFailed;
    }

    ProbeDataInfo dataInfo = m_ProbeDataSet->firstProbe();
    probeDataInfo->Code = dataInfo.Id;
    probeDataInfo->Code = dataInfo.Code;
    memset(probeDataInfo->Name, 0, MAX_PROBE_NAME_SIZE);
    memcpy(probeDataInfo->Name, dataInfo.Name.toStdString().c_str(),
           qMin(dataInfo.Name.length() + 1, MAX_PROBE_NAME_SIZE - 2));
    probeDataInfo->WaferNum = dataInfo.WaferNum;
    probeDataInfo->WaferLength = dataInfo.WaferLength;
    probeDataInfo->WaferRadius = dataInfo.WaferRadius;
    probeDataInfo->IsLinear = dataInfo.IsLinear;
    probeDataInfo->NoUseLine = dataInfo.NoUseLine;

    return UsOK;
}

int ProbeConfig::getAllSupportProbeNames(char **probeNames, const int arraySize, const int itemSize)
{
    const QList<ProbeDataInfo *> probeList = m_ProbeDataSet->probes();
    int copyCount = qMin(probeList.size(), arraySize);
    int j = 0;
    for (int i = 0; i < copyCount; i++)
    {
        if (isSupportProbe(probeList[i]->Name))
        {
            memset(*(probeNames + j), 0, itemSize);
            memcpy(*(probeNames + j), probeList[i]->Name.toStdString().c_str(),
                   qMin(probeList[i]->Name.length() + 1, itemSize));
            j++;
        }
    }
    return j;
}

UsRetCode ProbeConfig::getProbeShowNameByProbeName(const char *const probename, char *probeShowName)
{
    const ProbeDataInfo &dataInfo = m_ProbeDataSet->probe(QString(probename));
    QString probeshowname = dataInfo.ShowName;
    memcpy(probeShowName, probeshowname.toStdString().c_str(), probeshowname.length() + 1);
    return UsOK;
}

UsRetCode ProbeConfig::getCurrentProbeShowName(char *probeShowName, int probeID)
{
    const ProbeDataInfo &dataInfo = m_ProbeDataSet->getProbe(probeID);
    QString probeshowname = dataInfo.ShowName;
    memcpy(probeShowName, probeshowname.toStdString().c_str(), probeshowname.length() + 1);
    return UsOK;
}

UsRetCode ProbeConfig::getProbeStatus(ProbeStatus *probestatus)
{
    probestatus->fpgaTemp = GeneralInfo::instance().m_fpgaTemp.toFloat();
    probestatus->batteryCapacity = GeneralInfo::instance().m_batteryCapacity.toInt();
    probestatus->adapterStatus = GeneralInfo::instance().m_adapterStatus.toInt();
    probestatus->batteryTemp = GeneralInfo::instance().m_batteryTemp.toFloat();
    probestatus->batteryVol = GeneralInfo::instance().m_batteryVol.toInt();
    probestatus->batteryCurrent = GeneralInfo::instance().m_batteryCurrent.toInt();
    probestatus->boardTemp = GeneralInfo::instance().m_boardTemp.toFloat();
    probestatus->chargeStatus = GeneralInfo::instance().m_chargeStatus;
    return UsOK;
}

bool ProbeConfig::isSupportProbe(const QString &probeName)
{
    if (m_PresetAliasNames.size() == 0)
    {
        return true;
    }
    else if (m_PresetAliasNames.contains(probeName))
    {
        return true;
    }
    return false;
}
