#ifndef IPRESETCONTROLLER_H
#define IPRESETCONTROLLER_H

#include "presetutilitytool.h"
#include <QList>
#include <QString>

class PresetParameters;
class IDataBaseManager;

class IPresetController
{
  public:
    virtual ~IPresetController();

    virtual void readPresetConfig() = 0;

    virtual bool saveCurrentPreset() = 0;

    virtual bool saveCurrentPresetAs(const char *presetName, int type = USER_TYPE) = 0;

    virtual bool renameCurrentPreset(const char *presetName) = 0;

    virtual bool renamePreset(const char *oldPresetName, const char *newPresetName) = 0;

    virtual bool deleteCurrentPreset() = 0;

    virtual bool deletePresetByName(const char *presetName) = 0;

    virtual PresetParameters *loadPresetParameters(const QString &fileName) = 0;

    virtual bool savePresetParameters(const QString &fileName, const PresetParameters &presetParameters) = 0;

    virtual int getProbePreset(const int probeID, char **preset, const int arraySize, const int bufferSize) = 0;

    virtual int getAllPreset(char **preset, const int arraySize, const int bufferSize) = 0;

    virtual bool updateUserPreset(const char *oldpresetShowName, const char *newPresetShowname, const int *orderlist,
                                  int orderlistLength, int code) = 0;

    virtual QString getPrestShowName(const QString &probeName, const QString &presetName) = 0;

    virtual QString getPrestName(const QString &probeName, const QString &presetShowName) = 0;

    virtual void checkPresetStatusAndUpdate() = 0;

    virtual int updatePresetAndShowName(QString jsonPath, QString probeName, QString presetName, QString newpresetName,
                                        QString newPresetShowname, int code, QList<int> presetOrder = QList<int>()) = 0;

    virtual bool importPreset(const QString &destPath) = 0;

    virtual bool exportPreset(const QString &destPath) = 0;

    virtual bool restorePreset() = 0;

    virtual void setDataBaseManager(IDataBaseManager *value) = 0;
};

#endif // IPRESETCONTROLLER_H
