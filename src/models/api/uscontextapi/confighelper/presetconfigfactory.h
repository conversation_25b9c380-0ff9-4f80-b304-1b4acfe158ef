#ifndef PRESETCONFIGFACTORY_H
#define PRESETCONFIGFACTORY_H

#include "ipresetcontroller.h"
#include "qsharedpointer.h"
#include "usapiinfostruct.h"
#include <QHash>
#include <QList>

class SonoParameters;
class IProbeDataSet;

class PresetConfigFactory
{
  public:
    static QSharedPointer<IPresetController> getInstance(QHash<QString, QList<PresetAliasName>> &presetAliasNames,
                                                         IProbeDataSet *probeDataSet,
                                                         SonoParameters *currentSonoParameters = NULL,
                                                         SonoParameters *globalSonoParameters = NULL);

  private:
    PresetConfigFactory();
};

#endif // PRESETCONFIGFACTORY_H
