#include "presetconfigp9.h"
#include "bfpnames.h"
#include "crypticfile.h"
#include "iexammodepresetdatahandler.h"
#include "exammodepresethandlerfactory.h"
#include "infostruct.h"
#include "iprobedataset.h"
#include "presetutilitytool.h"
#include "qapp.h"
#include "resource.h"
#include "sonoparameters.h"
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>

PresetConfigP9::PresetConfigP9(QHash<QString, QList<PresetAliasName>>& presetAliasNames, IProbeDataSet* probeDataSet,
                               SonoParameters* currentSonoParameters, SonoParameters* globalSonoParameters)
    : PresetConfigBase(presetAliasNames, probeDataSet, currentSonoParameters, globalSonoParameters)
{
}

bool PresetConfigP9::saveCurrentPreset()
{
    return PresetConfigBase::saveCurrentPreset();
}

bool PresetConfigP9::saveCurrentPresetAs(const char* presetName, int type)
{
    if (presetName == NULL || isPresetExist(presetName))
    {
        return false;
    }
    return PresetConfigBase::saveCurrentPresetAs(presetName, type);
}

bool PresetConfigP9::renameCurrentPreset(const char* presetName)
{
    if (presetName == NULL || isPresetExist(presetName))
    {
        return false;
    }

    return PresetConfigBase::renameCurrentPreset(presetName);
}

bool PresetConfigP9::deletePresetByName(const char* presetName)
{
    if (presetName == NULL || !isPresetExist(presetName))
    {
        return false;
    }

    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    const QList<ProbeDataInfo*> probeList = m_ProbeDataSet->probes();
    QStringList emIds = handle->examModeIds(presetName);
    handle->removeExamModeFromLib(emIds);
    foreach (ProbeDataInfo* probeInfo, probeList)
    {
        handle->removeEMIdFromEMListFile(probeInfo->Id, emIds.first());
    }

    return true;
}

bool PresetConfigP9::importPreset(const QString& destPath)
{
    if (destPath.isEmpty())
    {
        return false;
    }

    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    return handle->importExamMode(destPath.endsWith(".zip", Qt::CaseInsensitive) ? destPath : destPath + ".zip");
}

bool PresetConfigP9::exportPreset(const QString& destPath)
{
    if (destPath.isEmpty())
    {
        return false;
    }

    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    return handle->exportExamMode(destPath);
}

bool PresetConfigP9::restorePreset()
{
    IExamModePresetDataHandler* handle = ExamModePresetHandlerFactory::instance()->defaultHandler();
    return handle->restoreExamMode();
}
