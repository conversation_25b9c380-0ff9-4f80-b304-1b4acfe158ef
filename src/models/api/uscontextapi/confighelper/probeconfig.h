#ifndef PROBECONFIG_H
#define PROBECONFIG_H

#include "usapidef.h"
#include "usapiinfostruct.h"
#include <QHash>
#include <QString>

class IProbeDataSet;
class ProbeConfig
{
  public:
    ProbeConfig(const QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet);
    virtual ~ProbeConfig();

    UsRetCode getFirstSupportProbe(UsProbeDataInfo *probeDataInfo) const;

    int getAllSupportProbeNames(char **probeNames, const int arraySize, const int itemSize);

    UsRetCode getProbeShowNameByProbeName(const char *const probename, char *probeShowName);

    UsRetCode getCurrentProbeShowName(char *probeShowName, int probeID);

    UsRetCode getProbeStatus(ProbeStatus *probestatus);

  private:
    bool isSupportProbe(const QString &probeName);

  private:
    const QHash<QString, QList<PresetAliasName>> &m_PresetAliasNames;
    IProbeDataSet *m_ProbeDataSet;
};

#endif // PROBECONFIG_H
