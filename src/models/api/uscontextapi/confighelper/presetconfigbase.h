#ifndef PRESETCONFIGBASE_H
#define PRESETCONFIGBASE_H

#include "ipresetcontroller.h"
#include "usapiinfostruct.h"
#include <QHash>
#include <QList>
#include <QPair>
#include <QString>
#include <QStringList>

class SonoParameters;
class PresetParameters;
class IProbeDataSet;

class PresetConfigBase : public IPresetController
{

    enum OperatorType
    {
        Min = -1,
        SaveBModeFreq = Min,
        Modify = 0,
        Delete,
        Append,
        Sort,
        RestoreCurrent,
        RestoreAll,
        Max = RestoreAll
    };

  public:
    explicit PresetConfigBase(QHash<QString, QList<PresetAliasName>> &presetAliasNames, IProbeDataSet *probeDataSet,
                              SonoParameters *currentSonoParameters = NULL,
                              SonoParameters *globalSonoParameters = NULL);

    virtual ~PresetConfigBase();

    virtual void readPresetConfig();

    virtual bool saveCurrentPreset();

    virtual bool saveCurrentPresetAs(const char *presetName, int type = USER_TYPE);

    virtual bool renameCurrentPreset(const char *presetName);

    virtual bool renamePreset(const char *oldPresetName, const char *newPresetName);

    virtual bool deleteCurrentPreset();

    virtual bool deletePresetByName(const char *presetName);

    virtual PresetParameters *loadPresetParameters(const QString &fileName);

    virtual bool savePresetParameters(const QString &fileName, const PresetParameters &presetParameters);

    virtual int getProbePreset(const int probeID, char **preset, const int arraySize, const int bufferSize);

    virtual int getAllPreset(char **preset, const int arraySize, const int bufferSize);

    virtual bool updateUserPreset(const char *oldpresetShowName, const char *newPresetShowname, const int *orderlist,
                                  int orderlistLength, int code);

    virtual QString getPrestName(const QString &probeName, const QString &presetName);

    virtual QString getPrestShowName(const QString &probeName, const QString &presetShowName);

    virtual void checkPresetStatusAndUpdate();

    virtual int updatePresetAndShowName(QString jsonPath, QString probeName, QString presetName, QString newpresetName,
                                        QString newPresetShowname, int code, QList<int> presetOrder = QList<int>());

    virtual bool importPreset(const QString &destPath);

    virtual bool exportPreset(const QString &destPath);

    virtual bool restorePreset();

  protected:
    /** 2024-08-26 Write by AlexWang
     * @brief setDataBaseManager
     * @param value
     */
    void setDataBaseManager(IDataBaseManager *value) override;

  protected:
    virtual bool isPresetExist(const char *presetName) const;

    virtual bool isExamModeSysLevel(const QString &emID) const;

    virtual bool getEmIDByPresetName(const char *presetName, QString &emID) const;

  protected:
    QHash<QString, QList<PresetAliasName>> &m_PresetAliasNames;
    SonoParameters *m_CurrentSonoParameters;
    SonoParameters *m_GlobalSonoParameters;
    IProbeDataSet *m_ProbeDataSet;
    IDataBaseManager *m_DataBaseManager;
};

#endif // PRESETCONFIGBASE_H
