#include "biopsyconfig.h"
#include "bfcoordtransform.h"
#include "bfpnames.h"
#include "formula.h"
#include "iprobedataset.h"
#include "probeparameters.h"
#include "qapp.h"
#include "resource.h"
#include "sonoparameters.h"
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QTransform>

BiopsyConfig::BiopsyConfig(SonoParameters *sonoParameters, IProbeDataSet *probeDataSet)
    : m_CurrentSonoParameters(sonoParameters)
    , m_ProbeDataSet(probeDataSet)
{
}

BiopsyConfig::~BiopsyConfig()
{
}

int BiopsyConfig::getBiopsyConfig(const QString &angle, Biopsy &biopsy)
{
    int probeID = m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr);
    QString probeBiopsyfileName = QString("%1%2%3_biopsy.bsy").arg(Resource::biopsyConfigPath()).arg("/").arg(probeID);
    QFile file(probeBiopsyfileName);
    if (file.exists())
    {
        QJsonDocument jsonDoc;
        if (!readJsonByQt5(probeBiopsyfileName, jsonDoc))
        {
            QJsonObject rootObj = jsonDoc.object();
            QJsonObject angleObj = rootObj.value("biopsyGeers").toObject();
            biopsy.LastGear = rootObj["currentGear"].toString().toInt();
            biopsy.BiopsyAngleList = angleObj.keys();
            if (angle == "-1") //获取上次的角度
            {
                return 0;
            }
            if (!angleObj.contains(angle))
            {
                return -1;
            }
            QJsonObject biopsyDetailObj = angleObj.value(angle).toObject();
            biopsy.ProbeId = probeID;
            biopsy.BiopsyAngle = biopsyDetailObj["BiopsyAngle"].toInt();
            biopsy.BiopsyAngleOffset = biopsyDetailObj["BiopsyAngleOffset"].toInt();
            biopsy.BiopsyAngleOffsetRange = biopsyDetailObj["BiopsyAngleOffsetRange"].toInt();
            biopsy.BiopsyXPosMM = biopsyDetailObj["BiopsyXPosMM"].toDouble();
            biopsy.BiopsyXPosMMOffset = biopsyDetailObj["BiopsyXPosMMOffset"].toDouble();
            biopsy.BiopsyXPosMMOffsetRange = biopsyDetailObj["BiopsyXPosMMOffsetRange"].toDouble();
            biopsy.BiopsyYPosMM = biopsyDetailObj["BiopsyYPosMM"].toDouble();
            biopsy.BiopsyYPosMMOffset = biopsyDetailObj["BiopsyYPosMMOffset"].toDouble();
            biopsy.BiopsyYPosMMOffsetRange = biopsyDetailObj["BiopsyYPosMMOffsetRange"].toDouble();
            return 0;
        }
    }
    return -1;
}

int BiopsyConfig::saveBiopsyConfig(const Biopsy &biopsy)
{
    QString probeBiopsyfileName =
        QString("%1%2%3_biopsy.bsy").arg(Resource::biopsyConfigPath()).arg("/").arg(biopsy.ProbeId);
    QFile file(probeBiopsyfileName);
    QString angle = QString::number(biopsy.BiopsyAngle);
    if (file.exists())
    {
        QJsonDocument jsonDoc;
        if (!readJsonByQt5(probeBiopsyfileName, jsonDoc))
        {
            QJsonObject rootObj = jsonDoc.object();
            QJsonObject angleObj = rootObj.value("biopsyGeers").toObject();
            if (!angleObj.contains(angle))
            {
                return -1;
            }
            QJsonObject biopsyDetailObj = angleObj.value(angle).toObject();
            biopsyDetailObj["BiopsyAngleOffset"] = biopsy.BiopsyAngleOffset;
            biopsyDetailObj["BiopsyXPosMMOffset"] = biopsy.BiopsyXPosMMOffset;
            biopsyDetailObj["BiopsyYPosMMOffset"] = biopsy.BiopsyYPosMMOffset;
            angleObj[angle] = biopsyDetailObj;
            rootObj["biopsyGeers"] = angleObj;
            rootObj["currentGear"] = angle;
            if (!file.open(QIODevice::WriteOnly))
            {
                return -1;
            }
            file.seek(0);
            jsonDoc.setObject(rootObj);
            file.write(jsonDoc.toJson());
            file.close();
            return 0;
        }
    }
    return -1;
}

QPointF BiopsyConfig::biopsyTransfrom(const qreal pixelSizeMM, const QPointF &origLogic,
                                      const ProbeDataInfo &probeDataInfo)
{
    BFCoordTransform coordTransform =
        BFCoordTransform(probeDataInfo, 0, pixelSizeMM, m_CurrentSonoParameters->pIV(BFPNames::B_RX_LNUMStr));
    BFCoordTransform secondStage(m_CurrentSonoParameters);
    ProbePhysicalPointF probePhysicalPoint = coordTransform.convertPtToPhysics(origLogic);
    QPointF screenPos = secondStage.convertPhysicsToLogic(probePhysicalPoint);
    bool up = m_CurrentSonoParameters->pBV(BFPNames::UpStr);
    bool left = m_CurrentSonoParameters->pBV(BFPNames::LeftStr);
    QSize sizeimg = m_CurrentSonoParameters->pV(BFPNames::DSCImageSizeStr).toSize();
    double coef = m_CurrentSonoParameters->pDV(BFPNames::ImageZoomCoefStr);
    QTransform flipAndRectZoomCoefTrans = Formula::flipAndRectZoomCoefTransform(up, left, sizeimg, coef / 100.0);

    int transX = m_CurrentSonoParameters->pV(BFPNames::ImageTranslateXStr).toDouble() * sizeimg.width() / 2;
    int transY = -m_CurrentSonoParameters->pV(BFPNames::ImageTranslateYStr).toDouble() * sizeimg.height() / 2;

    QPointF newPos(screenPos * flipAndRectZoomCoefTrans * QTransform::fromTranslate(transX, transY));

    return newPos;
}

QPointF BiopsyConfig::getBiopsyPosition(const qreal pixelSizeMM)
{
    const ProbeDataInfo &probeDataInfo = m_ProbeDataSet->getProbe(m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr));
    ProbeParameters probeParam(probeDataInfo, m_CurrentSonoParameters->pDV(BFPNames::StartDepthMMStr), pixelSizeMM);
    QPointF origLogic((m_CurrentSonoParameters->pDV(BFPNames::BiopsyXPosMMStr) +
                       m_CurrentSonoParameters->pDV(BFPNames::BiopsyXPosMMOffsetStr)) /
                          pixelSizeMM,
                      (m_CurrentSonoParameters->pDV(BFPNames::BiopsyYPosMMStr) + probeParam.arcFloorDisMM() +
                       m_CurrentSonoParameters->pDV(BFPNames::BiopsyYPosMMOffsetStr)) /
                          pixelSizeMM);
    QPointF biopsyPos = biopsyTransfrom(pixelSizeMM, origLogic, probeDataInfo);
    return biopsyPos;
}

bool BiopsyConfig::biopsyAngleMove(Biopsy &biopsy, qreal realPixelSizeMM, int angle, int biopsyposX, int biopsyposY)
{
    int angleori = m_CurrentSonoParameters->pIV(BFPNames::BiopsyAngleStr);
    double pixelSizeMM = m_CurrentSonoParameters->pDV(BFPNames::PixelSizeMMStr);

    const ProbeDataInfo &probeDataInfo = m_ProbeDataSet->getProbe(m_CurrentSonoParameters->pIV(BFPNames::ProbeIdStr));
    ProbeParameters probeParam(probeDataInfo, m_CurrentSonoParameters->pDV(BFPNames::StartDepthMMStr), realPixelSizeMM);

    QPointF ReferencePos(0, 0 + probeParam.arcFloorDisMM() / realPixelSizeMM);
    QPointF ReferencePos2 = biopsyTransfrom(realPixelSizeMM, ReferencePos, probeDataInfo);

    bool left = m_CurrentSonoParameters->pBV(BFPNames::LeftStr);
    bool up = m_CurrentSonoParameters->pBV(BFPNames::UpStr);

    double posoffstX = left ? (biopsyposX - (ReferencePos2.x())) * pixelSizeMM - biopsy.BiopsyXPosMM
                            : -((biopsyposX - (ReferencePos2.x())) * pixelSizeMM + biopsy.BiopsyXPosMM);
    double posoffstY = up ? (biopsyposY - ReferencePos2.y()) * pixelSizeMM - biopsy.BiopsyYPosMM
                          : (biopsyposY - ReferencePos2.y()) * pixelSizeMM + biopsy.BiopsyYPosMM;

    //小于一个像素的移动忽略掉
    if (fabs(posoffstX / pixelSizeMM) < 1)
    {
        posoffstX = 0;
    }
    if (fabs(posoffstY / pixelSizeMM) < 1)
    {
        posoffstY = 0;
    }

    if (angle - angleori < -biopsy.BiopsyAngleOffsetRange || angle - angleori > biopsy.BiopsyAngleOffsetRange)
    {
        return false;
    }

    if (posoffstX < -biopsy.BiopsyXPosMMOffsetRange || posoffstX > biopsy.BiopsyXPosMMOffsetRange)
    {
        return false;
    }
    if (posoffstY < -biopsy.BiopsyYPosMMOffsetRange || posoffstY > biopsy.BiopsyYPosMMOffsetRange)
    {
        return false;
    }

    m_CurrentSonoParameters->setPV(BFPNames::BiopsyAngleOffsetStr, angle - angleori);
    m_CurrentSonoParameters->setPV(BFPNames::BiopsyXPosMMOffsetStr, posoffstX);
    m_CurrentSonoParameters->setPV(BFPNames::BiopsyYPosMMOffsetStr, posoffstY);
    biopsy.BiopsyAngleOffset = m_CurrentSonoParameters->pIV(BFPNames::BiopsyAngleOffsetStr);
    biopsy.BiopsyXPosMMOffset = m_CurrentSonoParameters->pDV(BFPNames::BiopsyXPosMMOffsetStr);
    biopsy.BiopsyYPosMMOffset = m_CurrentSonoParameters->pDV(BFPNames::BiopsyYPosMMOffsetStr);
    return true;
}

void BiopsyConfig::biopsyAngleReset()
{
    QString probeBiopsyDir = Resource::biopsyConfigPath();
    QDir dir;
    dir.setPath(probeBiopsyDir);
    bool ret = dir.removeRecursively();
    qDebug() << PRETTY_FUNCTION << probeBiopsyDir << ret;
    QString temp = Resource::biopsyConfigPath(); // biopsyConfigPath()会重新拷贝默认的配置文件
}
