#include "iusapifactoriescontext.h"
#include "sonoeyeusapifactoriescontext.h"
#include "p9usapifactoriescontext.h"
#include "appleusapifactoriescontext.h"

IUSAPIFactoriesContext *IUSAPIFactoriesContext::factoriesContext(const QString &name, QObject *parent)
{
    if (name == "sonoeye")
    {
        return new SonoEyeUSAPIFactoriesContext(parent);
    }
    else if (name == "sr9")
    {
        return new P9USAPIFactoriesContext(parent);
    }
    else if (name == "apple")
    {
        return new AppleUSAPIFactoriesContext(parent);
    }
    return nullptr;
}

IUSAPIFactoriesContext::~IUSAPIFactoriesContext()
{

}
