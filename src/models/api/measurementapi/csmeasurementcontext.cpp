#include "csmeasurementcontext.h"
#include "cspackagesmeasurement.h"
#include "private/measurementcontextdispather.h"
#include "private/mouseactiondispather.h"
#include "private/screenmeasresultsdispather.h"
#include "usapi.h"
#include <assert.h>
#include <string>

CSMeasurementContext::CSMeasurementContext()
    : m_Dispather(new MeasurementContextDispather)
{
}

CSMeasurementContext::~CSMeasurementContext()
{
    delete m_Dispather;
}

MeasureStateType CSMeasurementContext::loadPackage(const char *path)
{
    return m_Dispather->loadPackage(path);
}

/** TODO:
 * @brief register ultrasound parameter changed callback function, for realtime measurement, when ultrasound parameter
 * changed, measuement calculation parameter also required changed.
 */
MeasureStateType CSMeasurementContext::initialize(UsContext usContext)
{
    return m_Dispather->initialize(usContext);
}

void CSMeasurementContext::resetContext()
{
    m_Dispather->resetContext();
}

MeasurementParameters *CSMeasurementContext::measurementParameters()
{
    return m_Dispather->measurementParameters();
}

void CSMeasurementContext::startMeasurement()
{
    m_Dispather->startMeasure();
}

void CSMeasurementContext::stopMeasurement()
{
    m_Dispather->stopMeasure();
}

void CSMeasurementContext::setMouseHandle(MouseHandle_t handle)
{
    MouseActionDispather *dispather = reinterpret_cast<MouseActionDispather *>(handle);
    m_Dispather->setMouseActionDispather(dispather);
}

void CSMeasurementContext::registerMeasureStateChangedCallBack(void *object, CBMeasureStateChanged callback)
{
    m_Dispather->setMeasureStateChangedCallBack(object, callback);
}

void CSMeasurementContext::registerCurrentMeasurementChangedCallBack(void *object, CBCurrentMeasurementChanged callback)
{
    m_Dispather->setCurrentMeasurementChangedCallBack(object, callback);
}

void CSMeasurementContext::registerMeasuredChanged(void *object, CBMeasuredChanged callback)
{
    m_Dispather->setMeasuredChangedCallBack(object, callback);
}

CSPackagesMeasurement *CSMeasurementContext::packagesMeasurement()
{
    return m_Dispather->packagesMeasurement();
}

void CSMeasurementContext::registerScreenResultsChangedCallBack(void *object, CBScreenMeasResultsChanged callback)
{
    m_Dispather->screenMeasResultsDispather()->setScreenMeasResultsChangedCallBack(object, callback);
}

void CSMeasurementContext::registerClearScreenResultsCallBack(void *object, CBClearScreenMeasResults callback)
{
    m_Dispather->screenMeasResultsDispather()->setClearScreenMeasResultsCallBack(object, callback);
}

void CSMeasurementContext::registerDisplayMeasureTipsCallBack(void *object, CBDisplayMeasureTips callback)
{
    m_Dispather->setDisplayMeasureTipsCallBack(object, callback);
}

CSScreenMeasResults CSMeasurementContext::screenMeasResults()
{
    return m_Dispather->screenMeasResultsDispather()->screenMeasResults();
}

void CSMeasurementContext::removeMeasureResult(const char *id)
{
    m_Dispather->removeMeasureResult(id);
}

void CSMeasurementContext::setActivedGlyphs(const char *id)
{
    m_Dispather->setActivedGlyphs(id);
}

void CSMeasurementContext::clearMeasurementInfo()
{
    m_Dispather->clearMeasurementInfo();
}

void CSMeasurementContext::clearGlyphs()
{
    m_Dispather->clearGlyphs();
}

void CSMeasurementContext::clearGlyphsAndNextMeasurement()
{
    m_Dispather->clearGlyphsAndNextMeasurement();
}

void CSMeasurementContext::saveGlyphs(const char *fileName)
{
    m_Dispather->saveGlyphs(fileName);
}

void CSMeasurementContext::loadGlyphs(const char *fileName)
{
    m_Dispather->loadGlyphs(fileName);
}

bool CSMeasurementContext::saveImage(const char *fileName)
{
    return m_Dispather->saveImage(fileName);
}

bool CSMeasurementContext::loadImage(const char *fileName, bool loadGlyphs)
{
    return m_Dispather->loadImage(fileName, loadGlyphs);
}

void CSMeasurementContext::setBSA(float value)
{
    m_Dispather->setBSA(value);
}

void CSMeasurementContext::setHeartRate(float value)
{
    m_Dispather->setHeartRate(value);
}

CSMeasurementContext::CSMeasurementContext(const CSMeasurementContext &context)
    : m_Dispather(nullptr)
{
}
