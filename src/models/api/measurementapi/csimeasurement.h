#ifndef CSIMEASUREMENT_H
#define CSIMEASUREMENT_H

#include "measurementapi_global.h"

class IMeasurement;
class StudyMeasurement;
class CSIMeasurementPrivate;
class MEASUREMENTAPISHARED_EXPORT CSIMeasurement
{
public:
    CSIMeasurement();
    CSIMeasurement(CSIMeasurement *other);
    CSIMeasurement(const CSIMeasurement& other);
    ~CSIMeasurement();

    const char* id() const;
    const char* composedId() const;

    /**
     * @brief return Measurement Type in APIMeasureType.
     */
    int type() const;

    CSIMeasurement* parentMeasurement() const;
    void setMeasurement(IMeasurement *measure);
    void setMeasurement(StudyMeasurement *measure);
    IMeasurement *measurement();

    bool hasMeasurementItem() const;

    const char* packageId() const;
    bool isModePackage() const;

    int imageType() const;
    int region() const;
    const char* caption() const;

    bool hasLeftRight() const;

    bool hasFarMidNear() const;

    int multiFetusCount() const;
    void setMultiFetusCount(int value);

    int leftRight() const;
    void setLeftRight(int value);

    int farMidNear() const;
    void setFarMidNear(int value);

    int fetusIndex() const;
    void setFetusIndex(int value);

    bool isOpened() const;
    bool isVisible() const;
    bool isEnabled() const;
    bool measured() const;
    bool isActive() const;

    /**
     * @brief 非连续的studymeasurement, 仅仅作为一个组的目录
     */
    bool isFolder() const;

    void stop();
    bool run();

    bool hasMeasuredResultAt(int lrIndex, int fmnIndex) const;

    bool hasMeasuredResults(bool validate = true);

    CSMeasResults getMeasResults(int lrIndex, int fmnIndex) const;

    int count() const;

    CSIMeasurement *at(int index) const;
    CSIMeasurement* defaultMeasure() const;

    int rulerIndex() const;

private:
//    IMeasurement *m_IMeasurement;
    CSIMeasurementPrivate *m_Data;
};

#endif // CSIMEASUREMENT_H
