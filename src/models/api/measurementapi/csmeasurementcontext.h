#ifndef CSMEASUREMENTCONTEXT_H
#define CSMEASUREMENTCONTEXT_H

#include "measurementapi_global.h"
#include "measurecallbackfuncs.h"
#include "mouseeventcontext.h"
#include "csscreenmeasresult.h"

class ScreenMeasResults;
class MeasurementParameters;
class CSPackagesMeasurement;
class MeasurementContextDispather;

class MEASUREMENTAPISHARED_EXPORT CSMeasurementContext
{
public:
    CSMeasurementContext();
    ~CSMeasurementContext();

    /**
     * @brief Load packages from path
     */
    MeasureStateType loadPackage(const char* path);

    /**
     * @brief initialize all measurement related work flow, like load measurement package, bind ruler , glyph control etc.
     * @param usContext, used for calculating measurement result.
     */
    MeasureStateType initialize(UsContext usContext);

    void resetContext();

    /**
     * @brief get the root package
     */
    CSPackagesMeasurement *packagesMeasurement();

    MeasurementParameters *measurementParameters();

    /**
     * @brief before start measurement, call this function.
     */
    void startMeasurement();

    /**
     * @brief before stop measurement, call this function.
     */
    void stopMeasurement();

    void setMouseHandle(MouseHandle_t handle);

    /**
     * @brief register MeasureStateChanged CallBack function.
     */
    void registerMeasureStateChangedCallBack(void *object, CBMeasureStateChanged callback);

    /**
     * @brief register CurrentMeasurementChanged CallBack function.
     */
    void registerCurrentMeasurementChangedCallBack(void *object, CBCurrentMeasurementChanged callback);

    /**
     * @brief register MeasuredChanged callback function.
     */
    void registerMeasuredChanged(void *object, CBMeasuredChanged callback);

    /**
     * @brief register callback function of screen display measurement results,
     *  whenever it exists add, remove or change measurement result situation,
     *  this callback function will be executed.
     */
    void registerScreenResultsChangedCallBack(void *object, CBScreenMeasResultsChanged callback);

    /**
     * @brief register callback function of screen display measurement results,
     * when measurement result cleared, this callback function will be executed.
     */
    void registerClearScreenResultsCallBack(void *object, CBClearScreenMeasResults callback);

    void registerDisplayMeasureTipsCallBack(void *object, CBDisplayMeasureTips callback);

    /**
     * @brief get screen display result
     */
    CSScreenMeasResults screenMeasResults();

    /**
     * @brief UI Layer Arrange to remove one of the sreen measure result
     */
    void removeMeasureResult(const char* id);

    void setActivedGlyphs(const char* id);

    void clearMeasurementInfo();

    void clearGlyphs();
    void saveGlyphs(const char* fileName);
    void loadGlyphs(const char* fileName);
    void clearGlyphsAndNextMeasurement();

    bool saveImage(const char* fileName);

    bool loadImage(const char* fileName, bool loadGlyphs=false);

    void setBSA(float value);

    void setHeartRate(float value);

private:
    CSMeasurementContext(const CSMeasurementContext &context);

private:
    MeasurementContextDispather *m_Dispather;
};

#endif // CSMEASUREMENTCONTEXT_H
