#include "csbodymarkcontext.h"
#include "private/mouseactiondispather.h"
#include "usapidef.h"
#include "bodymarkglyphscontrol.h"
#include "imagelabelinfo.h"
#include "glyphscontrolmanager.h"
#include "usapi.h"

class CSBodyMarkContextPrivate
{
public:
    CSBodyMarkContextPrivate()
        : infoFactory(new ImageInfoFactory())
        , dispather(nullptr), usCxt(nullptr), glyphsControl(nullptr)
    {
    }
    ~CSBodyMarkContextPrivate(){delete infoFactory; infoFactory = nullptr;}
    ImageInfoFactory *infoFactory;
    MouseActionDispather *dispather;
    UsContext usCxt;
    BodyMarkGlyphsControl *glyphsControl;
};

CSBodyMarkContext::CSBodyMarkContext()
    : d(new CSBodyMarkContextPrivate)
{

}

CSBodyMarkContext::~CSBodyMarkContext()
{
    delete d;
}

void CSBodyMarkContext::startBodyMark()
{
    if(d->glyphsControl == nullptr)
    {
        Q_ASSERT(d->usCxt != nullptr);

        d->glyphsControl = GlyphsControlManager::instance(
                    ).getChildGlyphsControl<BodyMarkGlyphsControl>(GlyphsCtl::BodyMarkGlyphsType);
    }
    Q_ASSERT(d->glyphsControl != nullptr);
    d->glyphsControl->onBeginAction();
}

void CSBodyMarkContext::stopBodyMark()
{
    d->glyphsControl->onEndAction();
}

void CSBodyMarkContext::setMouseHandle(MouseHandle_t handle)
{
    MouseActionDispather *dispather = reinterpret_cast<MouseActionDispather*>(handle);
    if(dispather != nullptr)
    {
        d->dispather = dispather;
    }
}

void CSBodyMarkContext::setUsContext(UsContext context)
{
    d->usCxt = context;
}

void CSBodyMarkContext::setCurrentBodyMark(const char *bmPath)
{
    Q_ASSERT(d->dispather != nullptr);
    d->infoFactory->setImageName(bmPath);
    IimageInfo *imageInfo = d->infoFactory->create();
    d->glyphsControl->imageSelectChanged(imageInfo);
    delete imageInfo;
}

