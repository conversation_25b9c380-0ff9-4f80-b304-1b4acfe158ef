#include "csimeasurement.h"
#include "imeasurement.h"
#include "measurementdef.h"
#include "basechildmeasurement.h"
#include "studymeasurement.h"
#include "packagemeasurement.h"
#include "packagesmeasurement.h"
#include "touchmeasuredispather.h"
#include "measmeasurement.h"
#include "measurementmodelutil.h"
#include <string>
#include <QString>

class CSIMeasurementPrivate
{
public:
    CSIMeasurementPrivate()
        : measurement(NULL)
        , wrapMeasurement(NULL){}

    ~CSIMeasurementPrivate()
    {
        if(wrapMeasurement != NULL)
        {
            delete wrapMeasurement;
        }
        wrapMeasurement = NULL;
    }
    IMeasurement *measurement;
    CSIMeasurement *wrapMeasurement;
    MeasResults measResults;
    std::string proxyAttribute; // 防止直接返回QString造成内存释放奔溃
};

CSIMeasurement::CSIMeasurement()
    : m_Data(new CSIMeasurementPrivate())
{
}

CSIMeasurement::CSIMeasurement(CSIMeasurement *other)
    : m_Data(new CSIMeasurementPrivate())
{
    m_Data->measurement = other->m_Data->measurement;
}

CSIMeasurement::CSIMeasurement(const CSIMeasurement &other)
    : m_Data(new CSIMeasurementPrivate())
{
    m_Data->measurement = other.m_Data->measurement;
}

CSIMeasurement::~CSIMeasurement()
{
    delete m_Data;
}

const char *CSIMeasurement::id() const
{
    Q_ASSERT(m_Data->measurement != NULL);
    m_Data->proxyAttribute = m_Data->measurement->id().toStdString();
    return m_Data->proxyAttribute.c_str();
}

const char *CSIMeasurement::composedId() const
{
    Q_ASSERT(m_Data->measurement != NULL);
    m_Data->proxyAttribute = m_Data->measurement->composedId().toStdString();
    return m_Data->proxyAttribute.c_str();
}

int CSIMeasurement::type() const
{
    return m_Data->measurement->type();
}

CSIMeasurement *CSIMeasurement::parentMeasurement() const
{
    if(m_Data->wrapMeasurement == NULL)
    {
        m_Data->wrapMeasurement = new CSIMeasurement();
    }
    m_Data->wrapMeasurement->setMeasurement(m_Data->measurement->parentMeasurement());
    return m_Data->wrapMeasurement;
}

void CSIMeasurement::setMeasurement(IMeasurement *measure)
{
    Q_ASSERT(measure != NULL);
    if(measure != NULL)
    {
        m_Data->measurement = measure;
    }
    else
    {
        m_Data->measurement = NULL;
    }
}

void CSIMeasurement::setMeasurement(StudyMeasurement *measure)
{
    Q_ASSERT(measure != NULL);
    if(measure != NULL)
    {
        m_Data->measurement = measure;
    }
    else
    {
        m_Data->measurement = NULL;
    }
}

IMeasurement *CSIMeasurement::measurement()
{
    return m_Data->measurement;
}

bool CSIMeasurement::hasMeasurementItem() const
{
    return qobject_cast<BaseChildMeasurement*>(m_Data->measurement) != NULL;
}

const char *CSIMeasurement::packageId() const
{
    m_Data->proxyAttribute = m_Data->measurement->packageId().toStdString();
    return m_Data->proxyAttribute.c_str();
}

bool CSIMeasurement::isModePackage() const
{
    return m_Data->measurement->isModePackage();
}

int CSIMeasurement::imageType() const
{
    return m_Data->measurement->imageType();
}

const char *CSIMeasurement::caption() const
{
    m_Data->proxyAttribute = m_Data->measurement->caption().toStdString();
    return m_Data->proxyAttribute.c_str();
}

bool CSIMeasurement::hasLeftRight() const
{
    return m_Data->measurement->hasLeftRight() == Measurement::Bool_True;
}

bool CSIMeasurement::hasFarMidNear() const
{
    return m_Data->measurement->hasFarMidNear() == Measurement::Bool_True;
}

int CSIMeasurement::multiFetusCount() const
{
    return m_Data->measurement->multiFetusCount();
}

void CSIMeasurement::setMultiFetusCount(int value)
{
    m_Data->measurement->setMultiFetusCount(value);
}

int CSIMeasurement::leftRight() const
{
    return m_Data->measurement->leftRight();
}

void CSIMeasurement::setLeftRight(int value)
{
    IMeasurement *modePackage = m_Data->measurement->modePackage();
    if(modePackage != nullptr)
    {
        if(modePackage->hasLeftRight(true) == Measurement::Bool_True)
        {
            modePackage->setLeftRight(value);
        }
    }
}

int CSIMeasurement::farMidNear() const
{
    return m_Data->measurement->farMidNear();
}

void CSIMeasurement::setFarMidNear(int value)
{
    IMeasurement *modePackage = m_Data->measurement->modePackage();
    if(modePackage != nullptr)
    {
        if(modePackage->hasFarMidNear(true) == Measurement::Bool_True)
        {
            modePackage->setHasFarMidNear(value);
        }
    }
}

int CSIMeasurement::fetusIndex() const
{
    return m_Data->measurement->fetusIndex();
}

void CSIMeasurement::setFetusIndex(int value)
{
    m_Data->measurement->setFetusIndex(value);
}

bool CSIMeasurement::isOpened() const
{
    return m_Data->measurement->isOpened();
}

bool CSIMeasurement::isVisible() const
{
    Q_ASSERT(m_Data->measurement != NULL);
    return m_Data->measurement->isVisible() == Measurement::Bool_True;
}

bool CSIMeasurement::isEnabled() const
{
    return m_Data->measurement->isEnabled();
}

bool CSIMeasurement::measured() const
{
    return m_Data->measurement->measured();
}

bool CSIMeasurement::isActive() const
{
    return m_Data->measurement->isActive();
}

bool CSIMeasurement::isFolder() const
{
    if(m_Data->measurement->type() == MeasureType_Study)
    {
        StudyMeasurement *studyMeas = qobject_cast<StudyMeasurement*>(m_Data->measurement);
        Q_ASSERT(studyMeas != NULL);
        if(!studyMeas->isContinue() && studyMeas->measOrder() != Measurement::Order_Repeated)
        {
            return true;
        }
    }
    return false;
}

void CSIMeasurement::stop()
{
   MeasurementModelUtil::stopMeasurement();
}

bool CSIMeasurement::run()
{
    return MeasurementModelUtil::runMeasurement(m_Data->measurement);
}

bool CSIMeasurement::hasMeasuredResultAt(int lrIndex, int fmnIndex) const
{
    return m_Data->measurement->hasMeasuredResultsAt(lrIndex, 0, 0);
}

bool CSIMeasurement::hasMeasuredResults(bool validate)
{
    return  m_Data->measurement->hasMeasuredResults(0, validate);
}

CSMeasResults CSIMeasurement::getMeasResults(int lrIndex, int fmnIndex) const
{
    m_Data->measResults = m_Data->measurement->measuredResults();
    return &m_Data->measResults;
}

int CSIMeasurement::count() const
{
    if(m_Data->measurement->type() == MeasureType_Study)
    {
        StudyMeasurement *studyMeas = qobject_cast<StudyMeasurement*>(m_Data->measurement);
        Q_ASSERT(studyMeas != NULL);
        if(studyMeas != NULL)
        {
            return studyMeas->count();
        }
    }
    else if(m_Data->measurement->type() == MeasureType_Package)
    {
        PackageMeasurement *pkgMeas = qobject_cast<PackageMeasurement*>(m_Data->measurement);
        Q_ASSERT(pkgMeas != NULL);
        if(pkgMeas != NULL)
        {
            return pkgMeas->count();
        }
    }
    else if(m_Data->measurement->type() == MeasureType_Packages)
    {
        PackagesMeasurement *pkgsMeas = qobject_cast<PackagesMeasurement*>(m_Data->measurement);
        Q_ASSERT(pkgsMeas != NULL);
        if(pkgsMeas != NULL)
        {
            return pkgsMeas->count();
        }
    }
    Q_ASSERT(false);
    return -1;
}

CSIMeasurement *CSIMeasurement::at(int index) const
{
    IMeasurement *meas = NULL;
    if(m_Data->measurement->type() == MeasureType_Study)
    {
        StudyMeasurement *studyMeas = qobject_cast<StudyMeasurement*>(m_Data->measurement);
        Q_ASSERT(studyMeas != NULL);
        if(studyMeas != NULL)
        {
            meas = studyMeas->at(index);
        }
    }
    else if(m_Data->measurement->type() == MeasureType_Package)
    {
        PackageMeasurement *pkgMeas = qobject_cast<PackageMeasurement*>(m_Data->measurement);
        Q_ASSERT(pkgMeas != NULL);
        if(pkgMeas != NULL)
        {
            meas = pkgMeas->at(index);
        }
    }
    else if(m_Data->measurement->type() == MeasureType_Packages)
    {
        PackagesMeasurement *pkgsMeas = qobject_cast<PackagesMeasurement*>(m_Data->measurement);
        Q_ASSERT(pkgsMeas != NULL);
        if(pkgsMeas != NULL)
        {
            meas = pkgsMeas->at(index);
        }
    }
    else
    {
        Q_ASSERT(false);
    }
    if(meas != NULL)
    {
        if(m_Data->wrapMeasurement == NULL)
        {
            m_Data->wrapMeasurement = new CSIMeasurement();
        }
        m_Data->wrapMeasurement->setMeasurement(meas);
        return m_Data->wrapMeasurement;
    }
    return NULL;
}

CSIMeasurement *CSIMeasurement::defaultMeasure() const
{
    if(m_Data->measurement->type() == MeasureType_Study)
    {
        StudyMeasurement *studyMeas = qobject_cast<StudyMeasurement*>(m_Data->measurement);
        Q_ASSERT(studyMeas != NULL);
        if(studyMeas != NULL)
        {
            IMeasurement *meas = studyMeas->defaultChildMeasurement();
            if(meas != NULL)
            {
                if(m_Data->wrapMeasurement == NULL)
                {
                    m_Data->wrapMeasurement = new CSIMeasurement();
                }
                m_Data->wrapMeasurement->setMeasurement(meas);
                return m_Data->wrapMeasurement;
            }
        }
    }
    return NULL;
}

int CSIMeasurement::rulerIndex() const
{
    Q_ASSERT(m_Data->measurement->type() == MeasureType_Meas);
    if(m_Data->measurement->type() == MeasureType_Meas)
    {
        MeasMeasurement *meas = qobject_cast<MeasMeasurement*>(m_Data->measurement);
        if(meas != NULL)
        {
            return meas->rulerIndex();
        }
    }
    return -1;
}

int CSIMeasurement::region() const
{
    return m_Data->measurement->region();
}
