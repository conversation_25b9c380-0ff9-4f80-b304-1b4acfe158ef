#ifndef CSPACKAGESMEASUREMENT_H
#define CSPACKAGESMEASUREMENT_H

#include "measurementapi_global.h"
#include "measurementinfohelper.h"

class CSIMeasurement;
class CSPackageMeasurement;
class PackagesMeasurement;
class CSPackagesMeasurementPrivate;
class MEASUREMENTAPISHARED_EXPORT CSPackagesMeasurement
{
public:
    CSPackagesMeasurement();
    CSPackagesMeasurement(const CSPackagesMeasurement& packages);
    ~CSPackagesMeasurement();

    const char* id() const;
    const char* composedId() const;

    int currentIndex() const;
    void setCurrentIndex(int value);

    int packageCount() const;

    CSPackageMeasurement* currentPackage() const;
    CSPackageMeasurement* getPackage(int index) const;
    CSPackageMeasurement* quickPackage() const;
    CSPackageMeasurement* findPackage(const char* id) const;

    bool isCurrentQuickMeasure() const;
    void clear();
    void clearResult();

    void run();

    bool isActive() const;

    void getPackagesResults(CSPackagesMeasResults value);

    void setPackagesResults(CSPackagesMeasResults value);

    /**
     * @brief load package from file packageFilePath, once package loaded,
     * it will create only one PackagesMeasurement pointer object.
     */
    int load(const char* packageFilePath, bool reload=false);

    PackagesMeasurement *packagesMeasurement() const;

    CSIMeasurement *findMeasurement(const char* composedId);

    CSIMeasurement *nextChild();

private:
    CSPackagesMeasurementPrivate *m_Data;
};


#endif // CSPACKAGESMEASUREMENT_H
