#include "csscreenmeasresult.h"
#include "screenmeasresult.h"
#include "screenmeasresultitem.h"
#include "screenmeasresults.h"
#include "measureglyphs.h"
#include <string>
#include <QDebug>
#include <QPointer>


//CSScreenMeasResults createScreenMeasResults()
//{
//    ScreenMeasResults * results = new ScreenMeasResults();
//    return results;
//}

//void releaseScreenMeasResults(CSScreenMeasResults results)
//{
//    ScreenMeasResults *res = reinterpret_cast<ScreenMeasResults *>(results);
//    if(res != nullptr)
//    {
//        delete res;
//        res = nullptr;
//    }
//}

int getScreenMeasResultsCount(CSScreenMeasResults results)
{
    ScreenMeasResults *res = reinterpret_cast<ScreenMeasResults *>(results);
    if(res != nullptr)
    {
        return res->count();
    }
    Q_ASSERT_X(false, "getScreenMeasResultsCount",
               "Input parameter results is not CSScreenMeasResults or it is NULL");
    return 0;
}

CSScreenMeasResult getScreenMeasResult(CSScreenMeasResults results, int index)
{
    ScreenMeasResults *res = reinterpret_cast<ScreenMeasResults *>(results);
    if(res != nullptr)
    {
        return res->at(index);
    }
    Q_ASSERT_X(false, "getScreenMeasResult",
               "Input parameter results is not CSScreenMeasResults or it is NULL");
    return nullptr;
}

bool isScreenMeasResultsEmpty(CSScreenMeasResults results)
{
    ScreenMeasResults *res = reinterpret_cast<ScreenMeasResults *>(results);
    if(res != nullptr)
    {
        return res->isEmpty();
    }
    Q_ASSERT_X(false, "isScreenMeasResultsEmpty",
               "Input parameter results is not CSScreenMeasResults or it is NULL");
    return true;
}

CSScreenMeasResult findScreenMeasResult(CSScreenMeasResults results, const char *id)
{
    ScreenMeasResults *res = reinterpret_cast<ScreenMeasResults *>(results);
    if(res != nullptr)
    {
        return res->find(id);
    }
    Q_ASSERT_X(false, "findScreenMeasResult",
               "Input parameter results is not CSScreenMeasResults or it is NULL");
    return nullptr;
}

int getScreenMeasResultIndex(CSScreenMeasResult result)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        return res->index();
    }
    Q_ASSERT_X(false, "getScreenMeasResultIndex",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
    return 0;
}

void getScreenMeasResultCaption(CSScreenMeasResult result, char *caption)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        memcpy(caption, res->caption().toStdString().c_str(), res->caption().length() + 1);
        return;
    }
    Q_ASSERT_X(false, "getScreenMeasResultCaption",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
}

void getScreenMeasResultComposedId(CSScreenMeasResult result, char *id)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        memcpy(id, res->id().toStdString().c_str(), res->id().length() + 1);
        return;
    }
    Q_ASSERT_X(false, "getScreenMeasResultComposedId",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
}

bool isScreenMeasResultEmpty(CSScreenMeasResult result)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        return res->isEmpty();
    }
    Q_ASSERT_X(false, "isScreenMeasResultEmpty",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
    return true;
}

int getScreenMeasResultChildrenCount(CSScreenMeasResult result)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        return res->items().count();
    }
    Q_ASSERT_X(false, "getScreenMeasResultChildrenCount",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
    return 0;
}

void getScreenMeasResultChildCaption(CSScreenMeasResult result, int index, char *caption)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        QString text = res->itemAt(index).caption();
        memcpy(caption, text.toStdString().c_str(), text.length() + 1);
        return;
    }
    Q_ASSERT_X(false, "getScreenMeasResultChildCaption",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
}

bool isScreenMeasResultChildValueValid(CSScreenMeasResult result, int index)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        return res->itemAt(index).isValueValid();
    }
    Q_ASSERT_X(false, "isScreenMeasResultChildValueValid",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
    return false;
}

void getScreenMeasResultChildValueText(CSScreenMeasResult result, int index, char *valueText)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        QString text = res->itemAt(index).text().first();
        memcpy(valueText, text.toStdString().c_str(), text.length() + 1);
        return;
    }
    Q_ASSERT_X(false, "getScreenMeasResultChildValueText",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
}

void getScreenMeasResultChildUnitText(CSScreenMeasResult result, int index, char *unitText)
{
    ScreenMeasResult *res = reinterpret_cast<ScreenMeasResult*>(result);
    if(res != nullptr)
    {
        QString text = res->itemAt(index).unitText().first();
        memcpy(unitText, text.toStdString().c_str(), text.length() + 1);
        return;
    }
    Q_ASSERT_X(false, "getScreenMeasResultChildUnitText",
               "Input parameter results is not CSScreenMeasResult or it is NULL");
}
