#include "mouseactiondispather.h"
#include "modelconfig.h"
#include "touchmeasuredispather.h"
#include <QCursor>
#include <QDebug>

MouseActionDispather::MouseActionDispather(QObject *parent)
    : QObject(parent)
    , m_MouseInteraction(false)
{
}

MouseActionDispather::~MouseActionDispather()
{
}

void MouseActionDispather::mouseDoubleClicked(int x, int y)
{
    m_LastPos = QPoint(x, y);
    qDebug() << "MouseActionDispather::mouseDoubleClicked for touch screen";
    TouchMeasureDispather::instance().onLeftButtonDoubleClicked(m_LastPos);
}

void MouseActionDispather::leftMouseClicked(int x, int y)
{
    m_LastPos = QPoint(x, y);
    TouchMeasureDispather::instance().onLeftButtonPressed(m_LastPos);
}

void MouseActionDispather::rightMouseClicked(int x, int y)
{
    m_LastPos = QPoint(x, y);
    TouchMeasureDispather::instance().onRightButtonPressed(m_LastPos);
}

void MouseActionDispather::mouseMove(int x, int y)
{
    QPoint pt = QPoint(x, y);
    qDebug() << "Mouse Position: " << pt;
    TouchMeasureDispather::instance().onMovedAction(pt - m_LastPos, pt);
    m_LastPos = pt;
}

void MouseActionDispather::setCenterPos(int x, int y)
{
    m_CenterPos = QPoint(x, y);
    m_LastPos = m_CenterPos;
}

void MouseActionDispather::resetCenterAndLastPos()
{
    m_LastPos = m_CenterPos;
}

bool MouseActionDispather::testStep(const QVariant &offset) const
{
    QPoint pt = offset.toPoint();
    return (qAbs(pt.x()) >= m_Step.x()) || (qAbs(pt.y()) >= m_Step.y());
}
