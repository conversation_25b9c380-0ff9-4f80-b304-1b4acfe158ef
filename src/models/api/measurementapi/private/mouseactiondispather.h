#ifndef MOUSEACTIONDISPATHER_H
#define MOUSEACTIONDISPATHER_H

#include "measurementapi_global.h"
#include <QObject>
#include <QPoint>

/**
 * @brief 鼠标测量和触摸屏测量都通过该类操作
 */
class MEASUREMENTAPISHARED_EXPORT MouseActionDispather : public QObject
{
    Q_OBJECT
public:
    explicit MouseActionDispather(QObject *parent = NULL);
    ~MouseActionDispather();

    void mouseDoubleClicked(int x, int y);

    void leftMouseClicked(int x, int y);

    void rightMouseClicked(int x, int y);

    void mouseMove(int x, int y);

    void setCenterPos(int x, int y);

signals:
    void leftButtonPressed();

private:
    void resetCenterAndLastPos();

    bool testStep(const QVariant& offset) const;

private:
    QPoint m_LastPos;
    QPoint m_Step;
    QPoint m_CenterPos;
    bool m_MouseInteraction; 
};

#endif // MOUSEACTIONDISPATHER_H
