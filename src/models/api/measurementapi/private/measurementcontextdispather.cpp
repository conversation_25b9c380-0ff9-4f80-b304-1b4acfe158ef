#include "measurementcontextdispather.h"
#include "csimeasurement.h"
#include "cspackagesmeasurement.h"
#include "defaultmeasureglyphscontrol.h"
#include "glyphscontrol.h"
#include "glyphscontrolmanager.h"
#include "iruler.h"
#include "measformulafunction.h"
#include "measmeasurement.h"
#include "measurecallbackfuncs.h"
#include "measurecontext.h"
#include "measurementdef.h"
#include "measurementinfohelper.h"
#include "measurementmodelutil.h"
#include "measurestaterecorder.h"
#include "modelconfig.h"
#include "mouseactiondispather.h"
#include "overlay.h"
#include "packagemeasurement.h"
#include "packagesmeasurement.h"
#include "private/screenmeasresultsdispather.h"
#include "rulerfactory.h"
#include "screenmeasresult.h"
#include "screenmeasresults.h"
#include "screenmeasurecontroller.h"
#include "studymeasurement.h"
#include "touchmeasuredispather.h"
#include "util.h"
#include <QDebug>

MeasurementContextDispather::MeasurementContextDispather(QObject *parent)
    : QObject(parent)
    , m_UsContext(NULL)
    , m_MeasureUsContext(NULL)
    , m_PackagesMeasurement(NULL)
    , m_ScreenResultDispather(NULL)
    , m_MeasureParameters(NULL)
    , m_MouseDispather(NULL)
    , m_MeasureStateChangedObject(NULL)
    , m_MeasureStateChangedCallback(NULL)
    , m_CurrentMeasurementChangedObject(NULL)
    , m_CurrentMeasurementChangedCallback(NULL)
    , m_MeasuredChangedObject(NULL)
    , m_MeasuredChangedCallback(NULL)
    , m_DisplayMeasureTipsObject(nullptr)
    , m_DisplayMeasureTips(nullptr)
    , m_Initialized(false)
    , m_Overlay(nullptr)
{
    m_MouseInteraction = ModelConfig::instance().value(ModelConfig::MouseInteraction, true).toBool();
}

MeasurementContextDispather::~MeasurementContextDispather()
{
    if (m_ScreenResultDispather != NULL)
    {
        delete m_ScreenResultDispather;
    }
    if (m_PackagesMeasurement != NULL)
    {
        delete m_PackagesMeasurement;
    }
}

CSPackagesMeasurement *MeasurementContextDispather::packagesMeasurement()
{
    return m_PackagesMeasurement;
}

MeasurementParameters *MeasurementContextDispather::measurementParameters()
{
    return m_MeasureParameters;
}

ScreenMeasResultsDispather *MeasurementContextDispather::screenMeasResultsDispather()
{
    return m_ScreenResultDispather;
}

MeasureStateType MeasurementContextDispather::loadPackage(const char *filePath)
{
    m_PackageResPath = filePath;
    if (m_PackageResPath.empty())
    {
        return PackagePathError;
    }

    int state = -1;
    if (m_PackagesMeasurement == nullptr)
    {
        m_PackagesMeasurement = new CSPackagesMeasurement();
        state = m_PackagesMeasurement->load(m_PackageResPath.c_str(), false);
    }
    else
    {
        Q_ASSERT(m_PackagesMeasurement->packagesMeasurement() != nullptr);
        if (m_PackagesMeasurement->packagesMeasurement()->isEmpty())
        {
            state = m_PackagesMeasurement->load(m_PackageResPath.c_str(), true);
        }
    }
    return state == 0 ? PackageLoadOK : PackageAlreadyLoaded;
}

MeasureStateType MeasurementContextDispather::initialize(UsContext usContext)
{
    Q_ASSERT(m_Initialized == false);
    Q_ASSERT(m_PackagesMeasurement != NULL);
    Q_ASSERT(usContext != NULL);

    qDebug() << "MeasurementContextDispather init started";

    if (m_Initialized)
    {
        return AlreadyInit;
    }

    m_UsContext = usContext;
    qDebug() << "MeasurementContextDispather init ruler OK";

    m_ScreenResultDispather = new ScreenMeasResultsDispather();

    m_Overlay = static_cast<Overlay *>(UsGetOverlay(usContext));
    Q_ASSERT(m_Overlay != NULL);

    ScreenMeasureController::instance().setOverlay(m_Overlay);

    m_ScreenResultDispather->setPackagesMeasurement(m_PackagesMeasurement->packagesMeasurement());
    m_ScreenResultDispather->startDispather();

    qDebug() << "MeasurementContextDispather init screen result OK";

    m_PackagesMeasurement->packagesMeasurement()->initialize();

    connect(m_PackagesMeasurement->packagesMeasurement(), SIGNAL(measuredChanged(IMeasurement *, bool)), this,
            SLOT(onMeasuredChanged(IMeasurement *, bool)));

    connect(m_PackagesMeasurement->packagesMeasurement(), SIGNAL(currentChildChanged(IMeasurement *)), this,
            SLOT(onCurrentMeasurementChanged(IMeasurement *)));

    UsMeasureContext measCtx = UsGetMeasureContext(m_UsContext);
    Q_ASSERT(measCtx != nullptr);
    if (measCtx != nullptr)
    {
        MeasureContext *ctx = reinterpret_cast<MeasureContext *>(measCtx);
        m_MeasureUsContext = ctx;
    }

    RulerFactory::instance().setContext(m_MeasureUsContext);

    MeasFormulaFunction::setUseStudyResult(false);

    qDebug() << "MeasurementContextDispather setContext OK";

    m_GlyphStoreManager.setAllGlyphsControl();

    m_Initialized = true;
    return InitOK;
}

void MeasurementContextDispather::resetContext()
{
    qDebug() << "MeasurementContextDispather::resetContext started";

    Q_ASSERT(m_Initialized == true);
    Q_ASSERT(m_UsContext != nullptr);
    if (!m_Initialized)
    {
        return;
    }
    m_Initialized = false;

    m_ScreenResultDispather->stopDispather();

    disconnect(m_PackagesMeasurement->packagesMeasurement(), SIGNAL(measuredChanged(IMeasurement *, bool)), this,
               SLOT(onMeasuredChanged(IMeasurement *, bool)));

    disconnect(m_PackagesMeasurement->packagesMeasurement(), SIGNAL(currentChildChanged(IMeasurement *)), this,
               SLOT(onCurrentMeasurementChanged(IMeasurement *)));

    clearMeasurementInfo();

    m_PackagesMeasurement->clear();

    qDebug() << "MeasurementContextDispather::resetContext ended";
}

void MeasurementContextDispather::setMouseActionDispather(MouseActionDispather *dispather)
{
    m_MouseDispather = dispather;
}

void MeasurementContextDispather::setMeasureStateChangedCallBack(void *object, CBMeasureStateChanged callback)
{
    m_MeasureStateChangedObject = object;
    m_MeasureStateChangedCallback = callback;
}

void MeasurementContextDispather::setCurrentMeasurementChangedCallBack(void *object,
                                                                       CBCurrentMeasurementChanged callback)
{
    m_CurrentMeasurementChangedObject = object;
    m_CurrentMeasurementChangedCallback = callback;
}

void MeasurementContextDispather::setMeasuredChangedCallBack(void *object, CBMeasuredChanged callback)
{
    m_MeasuredChangedObject = object;
    m_MeasuredChangedCallback = callback;
}

void MeasurementContextDispather::setDisplayMeasureTipsCallBack(void *object, CBDisplayMeasureTips callback)
{
    m_DisplayMeasureTipsObject = object;
    m_DisplayMeasureTips = callback;
}

void MeasurementContextDispather::startMeasure()
{
    UsGetMeasureContext(m_UsContext);

    UsUpdateMeasureContext(m_UsContext, true);

    qDebug() << "MeasurementContextDispather::startMeasure pixel size MM: " << m_MeasureUsContext->pixelSizeMM();
    connectSignals(true);
    m_Overlay->resetCursorToLimitCenter();
}

void MeasurementContextDispather::stopMeasure()
{
    connectSignals(false);
}

void MeasurementContextDispather::connectSignals(bool isConnect)
{
    qDebug() << "MeasurementContextDispather:connectSignals -- onRulerChanged"
             << m_PackagesMeasurement->packagesMeasurement()->caption();
    Q_ASSERT(m_MouseDispather != NULL);
    Util::connectSignal(&MeasureStateRecorder::instance(), SIGNAL(displayMeasureTips(const QString &, bool)), this,
                        SLOT(onDisplayMeasureTips(const QString &, bool)), isConnect);
}

void MeasurementContextDispather::setActivedGlyphs(const char *id)
{
    ScreenMeasResult *result = m_ScreenResultDispather->getScreenMeasResult(id);
    Q_ASSERT(result != NULL);

    IMeasurement *meas = MeasurementModelUtil::findMeasurement(m_PackagesMeasurement->packagesMeasurement(), result);

    Q_ASSERT(meas != nullptr);

    MeasMeasurement *measure = dynamic_cast<MeasMeasurement *>(meas);
    Q_ASSERT(measure != NULL);

    if (measure != NULL)
    {
#ifndef USE_STRICTMODE
        measure->setStep(Measurement::Step_Updated);
        result->setStep(Measurement::Step_Updated);
#else
        Q_ASSERT(false);
#endif
        IRuler *ruler = measure->currentRuler();
        if (ruler != NULL)
        {
            TouchMeasureDispather::instance().modify(measure);
        }
    }
}

void MeasurementContextDispather::removeMeasureResult(const char *id)
{
    // [1] remove measureItem result
    ScreenMeasResult *result = m_ScreenResultDispather->getScreenMeasResult(id);

    IMeasurement *measure = MeasurementModelUtil::findMeasurement(m_PackagesMeasurement->packagesMeasurement(), result);

#ifndef USE_STRICTMODE
    measure->clearResult();
#else
    Q_ASSERT_X(false, "MeasurementContextDispather::removeMeasureResult",
               "we need to instruct left right, far mid near result ");
#endif
    // [2] remove screen measure result and update screen result Widget
    m_ScreenResultDispather->removeScreenMeasResult(result);

    // [3] udpate Measurement Menu
    clearMeasured(measure);
}

void MeasurementContextDispather::clearMeasurementInfo()
{
    m_PackagesMeasurement->clearResult();
    clearGlyphsAndNextMeasurement();
}

void MeasurementContextDispather::clearGlyphs()
{
    m_ScreenResultDispather->clearResults();

    TouchMeasureDispather::instance().clear();

    m_Overlay->removeAllItems();
    GlyphsControlManager::instance().resetMeasureGlyphs();
}

void MeasurementContextDispather::clearGlyphsAndNextMeasurement()
{
    clearGlyphs();
    MeasureStateRecorder::instance().setNextChild(nullptr);
}

bool MeasurementContextDispather::saveImage(const char *fileName)
{
    saveGlyphs(fileName);

    UsRetCode ret = UsSaveImage(m_UsContext, fileName);
    if (ret != UsOK)
    {
        qDebug() << "UsPlatformContext::saveImage: error id: " << ret;
        return false;
    }
    return true;
}

bool MeasurementContextDispather::saveGlyphs(const char *fileName)
{
    ScreenMeasResults *results = m_ScreenResultDispather->screenMeasResults();
    Q_ASSERT(results != nullptr);
    if (results != nullptr)
    {
        m_GlyphStoreManager.setScreenMeasResults(results);
    }

    UsMeasureContext measCtx = UsGetMeasureContext(m_UsContext);
    Q_ASSERT(measCtx != nullptr);
    if (measCtx != nullptr)
    {
        MeasureContext *ctx = reinterpret_cast<MeasureContext *>(measCtx);
        m_MeasureUsContext = ctx;
    }

    m_GlyphStoreManager.setMeasureContex(m_MeasureUsContext);

    m_GlyphStoreManager.setOverlay(m_Overlay);

    m_GlyphStoreManager.saveGlyphInfo(fileName);

    return true;
}

bool MeasurementContextDispather::loadImage(const char *fileName, bool loadGlyph)
{
    UsRetCode ret = UsLoadImage(m_UsContext, fileName);
    if (ret != UsOK)
    {
        qDebug() << "UsPlatformContext::loadImage failed, error id: " << ret;
        return false;
    }

    if (loadGlyph)
    {
        loadGlyphs(fileName);
    }
    return true;
}

bool MeasurementContextDispather::loadGlyphs(const char *fileName)
{
    ScreenMeasResults *realResults = m_ScreenResultDispather->screenMeasResults();
    Q_ASSERT(realResults != nullptr);
    if (realResults != nullptr)
    {
        m_GlyphStoreManager.setScreenMeasResults(realResults);
    }

    m_GlyphStoreManager.setPackagesMeasurement(m_PackagesMeasurement->packagesMeasurement());

    UsMeasureContext measCtx = UsGetMeasureContext(m_UsContext);
    Q_ASSERT(measCtx != nullptr);
    if (measCtx != nullptr)
    {
        MeasureContext *ctx = reinterpret_cast<MeasureContext *>(measCtx);
        m_MeasureUsContext = ctx;
    }

    m_GlyphStoreManager.setOverlay(m_Overlay);

    m_GlyphStoreManager.setMeasureContex(m_MeasureUsContext);

    m_GlyphStoreManager.loadGlyphInfo(fileName);

    return true;
}

void MeasurementContextDispather::setBSA(float value)
{
    MeasFormulaFunction::setBSA(value);
}

void MeasurementContextDispather::setHeartRate(float value)
{
    MeasFormulaFunction::setHeartRate(value);
}

void MeasurementContextDispather::onCurrentMeasurementChanged(IMeasurement *value)
{
    qDebug() << "Callback: onCurrentMeasurementChanged";
    if (m_CurrentMeasurementChangedCallback != NULL)
    {
        bool reset = true;
        if (value != NULL)
        {
            m_AttributeProxy = value->composedId().toStdString();
            reset = false;
        }
        m_CurrentMeasurementChangedCallback(m_CurrentMeasurementChangedObject,
                                            value != NULL ? m_AttributeProxy.c_str() : NULL, reset);
    }
}

void MeasurementContextDispather::onMeasuredChanged(IMeasurement *measurement, bool value)
{
    qDebug() << "Callback: onMeasuredChanged";
    if (m_MeasuredChangedCallback != NULL)
    {
        m_AttributeProxy = measurement->composedId().toStdString();
        m_MeasuredChangedCallback(m_MeasuredChangedObject, m_AttributeProxy.c_str(), value);
    }
}

void MeasurementContextDispather::onMeasureResultChanged(ScreenMeasResult *result, int state)
{
    qDebug() << "Callback: onMeasureResultChanged";
    if (m_MeasureStateChangedCallback != NULL)
    {
        m_MeasureStateChangedCallback(m_MeasureStateChangedObject, state);
    }
}

void MeasurementContextDispather::clearMeasured(IMeasurement *measure)
{
    onMeasuredChanged(measure, false);
    StudyMeasurement *parentMeas = dynamic_cast<StudyMeasurement *>(measure->parentMeasurement());
    if (parentMeas != NULL && !parentMeas->isModePackage())
    {
        bool hasMeasChildren = false;
        foreach (IMeasurement *meas, parentMeas->visibleEnabledChildren())
        {
            if (meas->measured())
            {
                hasMeasChildren = true;
                break;
            }
        }
        if (!hasMeasChildren)
        {
            clearMeasured(parentMeas);
        }
    }
}

void MeasurementContextDispather::onDisplayMeasureTips(const QString &text, bool value)
{
    Q_ASSERT(m_DisplayMeasureTips != nullptr && m_DisplayMeasureTipsObject != nullptr);
    if (m_DisplayMeasureTips != nullptr)
    {
        m_AttributeProxy = text.toStdString();
        m_DisplayMeasureTips(m_DisplayMeasureTipsObject, m_AttributeProxy.c_str(), value);
    }
}
