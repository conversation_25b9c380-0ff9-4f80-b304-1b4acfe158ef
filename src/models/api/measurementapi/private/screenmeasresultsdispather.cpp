#include "screenmeasresultsdispather.h"
#include "csscreenmeasresult.h"
#include "iruler.h"
#include "measmeasurement.h"
#include "screenmeasresult.h"
#include "screenmeasresults.h"
#include "screenmeasurecontroller.h"
#include <QDebug>

ScreenMeasResultsDispather::ScreenMeasResultsDispather(QObject *parent)
    : QObject(parent)
    , m_PackagesMeasurement(nullptr)
    , m_ScreenMeasResults(new ScreenMeasResults())
    , m_ScreenMeasResultsChangedCallBack(NULL)
    , m_ClearScreenMeasResultsCallBack(NULL)
    , m_CBScreenMeasResultsChangedObject(NULL)
    , m_CBClearScreenMeasObject(NULL)
{
}

ScreenMeasResultsDispather::~ScreenMeasResultsDispather()
{
    if (m_ScreenMeasResults != nullptr)
    {
        delete m_ScreenMeasResults;
        m_ScreenMeasResults = nullptr;
    }
}

void ScreenMeasResultsDispather::startDispather()
{
    Q_ASSERT(m_ScreenMeasResults != nullptr && m_PackagesMeasurement != nullptr);

    ScreenMeasureController *resultManager = &ScreenMeasureController::instance();
    resultManager->setScreenMeasResults(m_ScreenMeasResults);

    connect(resultManager, SIGNAL(cleared()), this, SLOT(cleared()));
    connect(resultManager, SIGNAL(added(ScreenMeasResult *)), this, SLOT(added(ScreenMeasResult *)));
    connect(resultManager, SIGNAL(removed(ScreenMeasResult *)), this, SLOT(removed(ScreenMeasResult *)));
    connect(resultManager, SIGNAL(changed(ScreenMeasResult *, bool)), this, SLOT(changed(ScreenMeasResult *, bool)));
}

void ScreenMeasResultsDispather::stopDispather()
{
    ScreenMeasureController *resultManager = &ScreenMeasureController::instance();

    disconnect(resultManager, SIGNAL(cleared()), this, SLOT(cleared()));
    disconnect(resultManager, SIGNAL(added(ScreenMeasResult *)), this, SLOT(added(ScreenMeasResult *)));
    disconnect(resultManager, SIGNAL(removed(ScreenMeasResult *)), this, SLOT(removed(ScreenMeasResult *)));
    disconnect(resultManager, SIGNAL(changed(ScreenMeasResult *, bool)), this, SLOT(changed(ScreenMeasResult *, bool)));
}

ScreenMeasResults *ScreenMeasResultsDispather::screenMeasResults()
{
    return m_ScreenMeasResults;
}

void ScreenMeasResultsDispather::setPackagesMeasurement(PackagesMeasurement *packages)
{
    m_PackagesMeasurement = packages;
}

void ScreenMeasResultsDispather::setScreenMeasResultsChangedCallBack(void *object, CBScreenMeasResultsChanged callback)
{
    m_CBScreenMeasResultsChangedObject = object;
    m_ScreenMeasResultsChangedCallBack = callback;
}

void ScreenMeasResultsDispather::setClearScreenMeasResultsCallBack(void *object, CBClearScreenMeasResults callback)
{
    m_CBClearScreenMeasObject = object;
    m_ClearScreenMeasResultsCallBack = callback;
}

void ScreenMeasResultsDispather::clearResults()
{
    if (m_ScreenMeasResults != nullptr)
    {
        ScreenMeasureController::instance().clearResult();
    }
}

void ScreenMeasResultsDispather::removeScreenMeasResult(ScreenMeasResult *result)
{
    if (result != nullptr)
    {
        ScreenMeasureController::instance().removeResult(result);
    }
}

// IMeasurement *ScreenMeasResultsDispather::getMeasurement(const char *id)
//{
//    ScreenMeasResult *result = m_ScreenMeasResults->screenMeasResults()->find(id);
//    if(result != NULL)
//    {
//        return result->measurement();
//    }
//    return NULL;
//}

ScreenMeasResult *ScreenMeasResultsDispather::getScreenMeasResult(const char *id)
{
    return m_ScreenMeasResults->find(id);
}

void ScreenMeasResultsDispather::cleared()
{
    if (m_ClearScreenMeasResultsCallBack != NULL)
    {
        m_ClearScreenMeasResultsCallBack(m_CBClearScreenMeasObject);
    }
}

void ScreenMeasResultsDispather::added(ScreenMeasResult *toBeAdded)
{
    if (m_ScreenMeasResultsChangedCallBack != NULL)
    {
        m_ScreenMeasResultsChangedCallBack(m_CBScreenMeasResultsChangedObject, toBeAdded, 0);
    }
}

void ScreenMeasResultsDispather::removed(ScreenMeasResult *toBeRemoved)
{
    if (m_ScreenMeasResultsChangedCallBack != NULL)
    {
        m_ScreenMeasResultsChangedCallBack(m_CBScreenMeasResultsChangedObject, toBeRemoved, 1);
    }
}

void ScreenMeasResultsDispather::changed(ScreenMeasResult *toBeChanged, bool lazy)
{
    Q_UNUSED(lazy)
    if (m_ScreenMeasResultsChangedCallBack != NULL)
    {
        m_ScreenMeasResultsChangedCallBack(m_CBScreenMeasResultsChangedObject, toBeChanged, 2);
    }
}
