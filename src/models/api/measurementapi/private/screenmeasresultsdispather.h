#ifndef SCREENMEASRESULTSDISPATHER_H
#define SCREENMEASRESULTSDISPATHER_H

#include "measurementapi_global.h"
#include "measurecallbackfuncs.h"
#include "csscreenmeasresult.h"
#include <QObject>

class ScreenMeasResult;
class ScreenMeasResults;
class ProxyGroup;
class IMeasurement;
class IRuler;
class PackagesMeasurement;

class MEASUREMENTAPISHARED_EXPORT ScreenMeasResultsDispather : public QObject
{
    Q_OBJECT
public:
    explicit ScreenMeasResultsDispather(QObject *parent = 0);
    ~ScreenMeasResultsDispather();

    void startDispather();

    void stopDispather();

    ScreenMeasResults *screenMeasResults();

    void setPackagesMeasurement(PackagesMeasurement *packages);

    void setScreenMeasResultsChangedCallBack(void *object, CBScreenMeasResultsChanged callback);
    void setClearScreenMeasResultsCallBack(void *object, CBClearScreenMeasResults callback);

    void clearResults();
    void removeScreenMeasResult(ScreenMeasResult *result);

//    IMeasurement *getMeasurement(const char* id);
    ScreenMeasResult *getScreenMeasResult(const char* id);

private slots:
    void cleared();
    void added(ScreenMeasResult* toBeAdded);
    void removed(ScreenMeasResult* toBeRemoved);
    void changed(ScreenMeasResult* toBeChanged, bool lazy = false);

private:
    PackagesMeasurement *m_PackagesMeasurement;
    ScreenMeasResults *m_ScreenMeasResults;
    CBScreenMeasResultsChanged m_ScreenMeasResultsChangedCallBack;
    void *m_CBScreenMeasResultsChangedObject;
    CBClearScreenMeasResults m_ClearScreenMeasResultsCallBack;
    void *m_CBClearScreenMeasObject;
};

#endif // SCREENMEASRESULTSDISPATHER_H
