#ifndef MEASUREMENTCONTEXTDISPATHER_H
#define MEASUREMENTCONTEXTDISPATHER_H

#include "measurementapi_global.h"
#include "usapi.h"
#include "measurecallbackfuncs.h"
#include <QObject>
#include <string>
#include "glyphstoremanager.h"

class IMeasurement;
class CSPackagesMeasurement;
class ScreenMeasResultsDispather;
class MeasurementParameters;
class m_MouseDispather;
class MouseActionDispather;
class ScreenMeasResult;
class IRuler;
class Overlay;
class QGraphicsItem;
class StudyMeasurement;
class DefaultMeasureGlyphsControl;
class GlyphsControl;
class MeasureContext;

class MEASUREMENTAPISHARED_EXPORT MeasurementContextDispather : public QObject
{
    Q_OBJECT
public:
    explicit MeasurementContextDispather(QObject *parent = 0);
    ~MeasurementContextDispather();

    CSPackagesMeasurement *packagesMeasurement();

    MeasurementParameters *measurementParameters();

    ScreenMeasResultsDispather *screenMeasResultsDispather();

    MeasureStateType loadPackage(const char* filePath);

    MeasureStateType initialize(UsContext usContext);

    void resetContext();

    void setMouseActionDispather(MouseActionDispather *dispather);

    void setMeasureStateChangedCallBack(void *object, CBMeasureStateChanged callback);

    void setCurrentMeasurementChangedCallBack(void *object, CBCurrentMeasurementChanged callback);

    void setMeasuredChangedCallBack(void *object, CBMeasuredChanged callback);

    void setDisplayMeasureTipsCallBack(void *object, CBDisplayMeasureTips callback);

    void startMeasure();

    void stopMeasure();

    void connectSignals(bool isConnect);

    void setActivedGlyphs(const char* id);

    void removeMeasureResult(const char* id);

    void clearMeasurementInfo();

    void clearGlyphs();

    void clearGlyphsAndNextMeasurement();

    bool saveImage(const char* fileName);

    bool saveGlyphs(const char* fileName);

    bool loadImage(const char* fileName, bool loadGlyph=false);

    bool loadGlyphs(const char* fileName);

    void setBSA(float value);

    void setHeartRate(float value);

private slots:
    void onCurrentMeasurementChanged(IMeasurement *value);
    void onMeasuredChanged(IMeasurement* measurement, bool value);
    void onMeasureResultChanged(ScreenMeasResult *result, int state);
    void clearMeasured(IMeasurement *measure);
    void onDisplayMeasureTips(const QString &text, bool value);

private:
    UsContext m_UsContext;
    Overlay *m_Overlay;
    MeasureContext *m_MeasureUsContext;

    CSPackagesMeasurement *m_PackagesMeasurement;
    ScreenMeasResultsDispather *m_ScreenResultDispather;
    MeasurementParameters *m_MeasureParameters;
    MouseActionDispather *m_MouseDispather;

    void *m_MeasureStateChangedObject;
    CBMeasureStateChanged m_MeasureStateChangedCallback;

    void *m_CurrentMeasurementChangedObject;
    CBCurrentMeasurementChanged m_CurrentMeasurementChangedCallback;

    void *m_MeasuredChangedObject;
    CBMeasuredChanged m_MeasuredChangedCallback;

    void *m_DisplayMeasureTipsObject;
    CBDisplayMeasureTips m_DisplayMeasureTips;

    std::string m_PackageResPath;
    bool m_Initialized;
    std::string m_AttributeProxy;

    GlyphStoreManager m_GlyphStoreManager;

    bool m_MouseInteraction;
};

#endif // MEASUREMENTCONTEXTDISPATHER_H
