#ifndef CSMEASUREMOUSETOOL_H
#define CSMEASUREMOUSETOOL_H

#include "measurementapi_global.h"

class MeasureMouseTool;

/**
 * @brief The CSMeasureMouseTool class used to control
 */
class MEASUREMENTAPISHARED_EXPORT CSMeasureMouseTool
{
public:
    CSMeasureMouseTool();
    CSMeasureMouseTool(const CSMeasureMouseTool& tool);
    ~CSMeasureMouseTool();

//    void setMeasureMouseTool(MeasureMouseTool *tool);
//    MeasureMouseTool *measureMouseTool();

private:
//    MeasureMouseTool *m_MeasureMouseTool;
};

#endif // CSMEASUREMOUSETOOL_H
