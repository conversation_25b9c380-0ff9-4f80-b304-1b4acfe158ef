#ifndef CSBODYMARKWRAPPER_H
#define CSBODYMARKWRAPPER_H

#include "measurementapi_global.h"


class CSBodyMarkWrapperPrivate;
class MEASUREMENTAPISHARED_EXPORT CSBodyMarkWrapper
{
public:
    CSBodyMarkWrapper();
    ~CSBodyMarkWrapper();
    CSBodyMarkWrapper(const CSBodyMarkWrapper &other);
    /**
     * @brief Get type count of system bodymark.
     */
    int systemBMCount() const;
    /**
     * @brief Get system bodymark id.
     */
    const char* systemBMID(int index) const;
    /**
     * @brief Get system bodymark name.
     */
    const char* systemBMName(int index) const;
    /**
     * @brief Get bodymark count of target system bodymark type.
     */
    int systemBMTargetCount(const char* sysId) const;

    const char* systemBMFilePath(int index);

    /**
     * @brief Get type count of user bodymark.
     */
    int userBMCount() const;
    /**
     * @brief Get user bodymark id
     */
    const char* userBMID(int index) const;

    const char* userBMName(int index) const;

    int userBMType(int index) const;
    /**
     * @brief Get bodymark count of target user bodymark type.
     */
    int userBMTargetCount(const char* userId) const;
    /**
     * @brief Get target bodymark file path
     */
    const char* userBMFilePath(int index);

private:
    CSBodyMarkWrapperPrivate *d;
};

#endif // CSBODYMARKWRAPPER_H
