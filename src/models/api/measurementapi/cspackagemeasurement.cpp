#include "cspackagemeasurement.h"
#include "packagemeasurement.h"
#include "csimeasurement.h"
#include <string>

class CSPackageMeasurementPrivate
{
public:
    CSPackageMeasurementPrivate()
        : package(NULL)
        , wrapStudyMeasurement(new CSIMeasurement()){}
    ~CSPackageMeasurementPrivate() { delete wrapStudyMeasurement; }
    PackageMeasurement* package;
    CSIMeasurement* wrapStudyMeasurement;
    std::string proxyAttribute; // 防止直接返回QString造成内存释放奔溃
};

CSPackageMeasurement::CSPackageMeasurement()
    : m_Data(new CSPackageMeasurementPrivate)
{
}

CSPackageMeasurement::CSPackageMeasurement(const CSPackageMeasurement &pkgmeas)
{
    setPackageMeasurement(pkgmeas.packageMeasurement());
}

CSPackageMeasurement::~CSPackageMeasurement()
{
    delete m_Data;
}

void CSPackageMeasurement::setPackageMeasurement(PackageMeasurement *package)
{
    Q_ASSERT(package != NULL);
    m_Data->package = package;
}

PackageMeasurement *CSPackageMeasurement::packageMeasurement() const
{
    return m_Data->package;
}

const char *CSPackageMeasurement::caption() const
{
    m_Data->proxyAttribute = m_Data->package->caption().toStdString();
    return m_Data->proxyAttribute.c_str();
}

const char *CSPackageMeasurement::id() const
{
    Q_ASSERT(m_Data->package != NULL);
    m_Data->proxyAttribute = m_Data->package->id().toStdString();
    return m_Data->proxyAttribute.c_str();
}

const char *CSPackageMeasurement::composedId() const
{
    m_Data->proxyAttribute = m_Data->package->composedId().toStdString();
    return m_Data->proxyAttribute.c_str();
}

int CSPackageMeasurement::currentImageType() const
{
    return m_Data->package->currentImageType();
}

void CSPackageMeasurement::setCurrentImageType(int value)
{
    m_Data->package->setCurrentImageType(value);
}

const char *CSPackageMeasurement::species() const
{
    m_Data->proxyAttribute = m_Data->package->species().toStdString();
    return m_Data->proxyAttribute.c_str();
}

void CSPackageMeasurement::setSpecies(const char *value)
{
    m_Data->package->setSpecies(value);
}

const char *CSPackageMeasurement::reportCaption() const
{
    m_Data->proxyAttribute = m_Data->package->reportCaption().toStdString();
    return m_Data->proxyAttribute.c_str();
}

void CSPackageMeasurement::setReportCaption(const char *value)
{
    m_Data->package->setReportCaption(value);
}

PackageRegion CSPackageMeasurement::packageRegion() const
{
    return (PackageRegion)m_Data->package->region();
}

int CSPackageMeasurement::multiFetusCount() const
{
    return m_Data->package->multiFetusCount();
}

int CSPackageMeasurement::fetusIndex() const
{
    return m_Data->package->fetusIndex();
}

void CSPackageMeasurement::setFetusIndex(int index)
{
    m_Data->package->setFetusIndex(index);
}
bool CSPackageMeasurement::isActive() const
{
    return m_Data->package->isActive();
}

CSIMeasurement *CSPackageMeasurement::currentStudy() const
{
    m_Data->wrapStudyMeasurement->setMeasurement(m_Data->package->currentStudy());
    return m_Data->wrapStudyMeasurement;
}

CSIMeasurement *CSPackageMeasurement::getStudy(int imageType) const
{
    m_Data->wrapStudyMeasurement->setMeasurement(m_Data->package->getStudy(imageType));
    Q_ASSERT(m_Data->package->getStudy(imageType) != NULL);
    return m_Data->wrapStudyMeasurement;
}

void CSPackageMeasurement::run()
{
    m_Data->package->start();
}
