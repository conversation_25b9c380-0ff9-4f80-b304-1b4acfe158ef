#ifndef CSBODYMARKCONTEXT_H
#define CSBODYMARKCONTEXT_H

#include "measurementapi_global.h"
#include "usapidef.h"

class CSBodyMarkContextPrivate;
class MEASUREMENTAPISHARED_EXPORT CSBodyMarkContext
{
public:
    CSBodyMarkContext();
    ~CSBodyMarkContext();
    CSBodyMarkContext(const CSBodyMarkContext &other) = delete;

    void startBodyMark();
    void stopBodyMark();
    void setMouseHandle(MouseHandle_t handle);
    void setUsContext(UsContext context);
    void setCurrentBodyMark(const char* bmPath);

private:
    CSBodyMarkContextPrivate *d;
};

#endif // CSBODYMARKCONTEXT_H
