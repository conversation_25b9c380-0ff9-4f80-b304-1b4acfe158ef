#include "csmeasresult.h"
#include "measresult.h"
#include "cspackagesmeasurement.h"
#include "packagesmeasurement.h"
#include "measresultitem.h"
#include "measurementdef.h"
#include <QList>

#ifdef __cplusplus
extern "C"
{
#endif

CSPackagesMeasResults createPackagesMeasResults()
{
    PackagesMeasResults *results = new PackagesMeasResults();
    return results;
}

void releasePackagesMeasResults(CSPackagesMeasResults results)
{
    PackagesMeasResults *res = reinterpret_cast<PackagesMeasResults*>(results);
    if(res != NULL)
    {
        delete res;
        res = NULL;
    }
}

bool hasPackagesMeasResults(CSPackagesMeasResults results)
{
    PackagesMeasResults *res = reinterpret_cast<PackagesMeasResults*>(results);
    if(res != NULL)
    {
        return res->hasResult();
    }
    return false;
}

CSMeasResults getOnePackageMeasResults(CSPackagesMeasResults results, const char *packageId)
{
    PackagesMeasResults *res = reinterpret_cast<PackagesMeasResults*>(results);
    if(res != NULL)
    {
        return res->packageMeasResults(packageId);
    }
    return NULL;
}

bool save(CSPackagesMeasResults results, const char *fileName)
{
    PackagesMeasResults *res = reinterpret_cast<PackagesMeasResults*>(results);
    if(res != NULL)
    {
        return res->save(fileName);
    }
    return false;
}

void load(CSPackagesMeasResults results, const char *fileName)
{
    PackagesMeasResults *res = reinterpret_cast<PackagesMeasResults*>(results);
    if(res != NULL)
    {
        res->load(QString(fileName));
    }
}

int getMeasResultCount(CSMeasResults result)
{
    MeasResults *res = reinterpret_cast<MeasResults*>(result);
    if(res != NULL)
    {
        return res->count();
    }
    return 0;
}

CSMeasResult getMeasResultAt(CSMeasResults result, int index)
{
    MeasResults *res = reinterpret_cast<MeasResults*>(result);
    if(res != NULL)
    {
        if(index >= 0 && index < res->count())
        {
            return const_cast<MeasResult*>(&(res->at(index)));
        }
    }
    return NULL;
}

bool isMeasResultValid(CSMeasResult result)
{
    MeasResult *res = reinterpret_cast<MeasResult*>(result);
    if(res != NULL)
    {
        return res->hasValidItem();
    }
    return false;
}

int getLeftRightCount(CSMeasResult result)
{
    MeasResult *res = reinterpret_cast<MeasResult*>(result);
    if(res != NULL)
    {
        return res->lrCount();
    }
    return -1;
}

int getFarMidNearCount(CSMeasResult result)
{
    MeasResult *res = reinterpret_cast<MeasResult*>(result);
    if(res != NULL)
    {
        return res->fmnCount();
    }
    return -1;
}

CSMeasResultItem getMeasResultItem(CSMeasResult result, int leftRight, int farMidNear, int fetusIndex)
{
    MeasResult *res = reinterpret_cast<MeasResult*>(result);
    if(res != NULL)
    {
        return &res->resultItem(leftRight, farMidNear, fetusIndex);
    }
    return NULL;
}

bool getMeasResultItemIsValid(CSMeasResultItem result)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        return res->isValid();
    }
    return false;
}

bool getMeasResultItemReportDisplay(CSMeasResultItem result)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        return res->rptDisplay() == Measurement::Bool_True;
    }
    return false;
}

//int getMeasResultItemLeftRight(CSMeasResultItem result)
//{
//    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
//    if(res != NULL)
//    {
//        return res->leftRight();
//    }
//    return -1;
//}

//int getMeasResultItemFarMidNear(CSMeasResultItem result)
//{
//    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
//    if(res != NULL)
//    {
//        return res->farMidNear();
//    }
//    return -1;
//}

void getMeasResultItemCaption(CSMeasResultItem result, char *text, int size)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        memcpy(text, res->caption().toStdString().c_str(), res->caption().length() + 1);
    }
}

void getMeasResultItemValueText(CSMeasResultItem result, char *text, int size)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        if(!res->valueText().isEmpty())
        {
            memcpy(text, res->valueText().first().toStdString().c_str(), res->valueText().first().length() + 1);
        }
    }
}

void getMeasResultItemUnitText(CSMeasResultItem result, char *text, int size)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        if(!res->unitText().isEmpty())
        {
            memcpy(text, res->unitText().first().toStdString().c_str(), res->unitText().first().length() + 1);
        }
    }
}

double getMeasResultItemValue(CSMeasResultItem result)
{
    MeasResultItem *res = reinterpret_cast<MeasResultItem*>(result);
    if(res != NULL)
    {
        bool ok = false;
        double value = res->value().toDouble(&ok);
        if(ok)
        {
            return value;
        }
    }
    return -9999;
}

#ifdef __cplusplus
}
#endif


