#ifndef MEASUREMENTINFOHELPER_H
#define MEASUREMENTINFOHELPER_H

#include "measurementapi_global.h"

class CSMeasurementPackageInfoPrivate;
class MEASUREMENTAPISHARED_EXPORT CSMeasurementPackageInfo
{
public:
    CSMeasurementPackageInfo();
    CSMeasurementPackageInfo(const CSMeasurementPackageInfo &info);
    CSMeasurementPackageInfo(const char* _id, const char* _caption, int _type);
    ~CSMeasurementPackageInfo();

    const char * id() const;
    const char * caption() const;
    int type() const;

private:
    CSMeasurementPackageInfoPrivate *m_Data;
};

class CSReportInfoPrivate;
class MEASUREMENTAPISHARED_EXPORT CSReportInfo
{
public:
    CSReportInfo();
    CSReportInfo(const CSReportInfo& info);
    CSReportInfo(const char* _id, int _region, const char* _title,
               int _fetusCount, int _fetusIndex);
    ~CSReportInfo();

private:
    CSReportInfoPrivate *m_Data;
};

class CSPackagesMeasurement;
class MeasurementInfoHelperPrivate;
class MEASUREMENTAPISHARED_EXPORT MeasurementInfoHelper
{
public:
    MeasurementInfoHelper();
    MeasurementInfoHelper(const MeasurementInfoHelper& info);
    ~MeasurementInfoHelper();

    void setPackagesMeasurement(CSPackagesMeasurement *packages);

    int packageInfoCount() const;
    CSMeasurementPackageInfo getPackageInfo(int index) const;
    CSMeasurementPackageInfo getPackageInfo(const char* id) const;

    int reportInfoCount() const;
    CSReportInfo getReportInfo(int index) const;
    CSReportInfo getReportInfo(const char* id) const;

private:
    MeasurementInfoHelperPrivate *m_Data;
};

#endif // MEASUREMENTINFOHELPER_H
