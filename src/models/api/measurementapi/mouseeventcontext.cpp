#include "mouseeventcontext.h"
#include "private/mouseactiondispather.h"
#include "private/measurementcontextdispather.h"
#include "overlay.h"
#include "bodymarkglyphscontrol.h"
#include <QSize>

#ifdef __cplusplus
extern "C"
{
#endif


CSMouseAction::CSMouseAction()
{
    actionType = 0;
    posX = 0;
    posY = 0;
    isPressed = 0;
}

CSMouseAction::CSMouseAction(int type, int x, int y, int pressed)
{
    actionType = type;
    posX = x;
    posY = y;
    isPressed = pressed;
}

CSMouseAction::CSMouseAction(const CSMouseAction& action)
{
    actionType = action.actionType;
    posX = action.posX;
    posY = action.posY;
    isPressed = action.isPressed;
}

enum MouseActionType{
    LeftMouseClick = 0,
    MouseDblClick = 1,
    MouseMove = 2,
    RightMouseClick = 3
};

MouseHandle_t createMouseHandle()
{
    MouseActionDispather *dispather = new MouseActionDispather();
    return (MouseHandle_t)dispather;
}

void releaseMouseHandle(MouseHandle_t handle)
{
    MouseActionDispather *dispather = reinterpret_cast<MouseActionDispather*>(handle);
    if(dispather != NULL)
    {
        delete  dispather;
        dispather = NULL;
        handle = NULL;
    }
}

void mouseActionHandle(MouseHandle_t handle, CSMouseAction action)
{
    if(action.actionType < 0 || action.actionType > 3)
    {
        return;
    }
    MouseActionDispather *dispather = reinterpret_cast<MouseActionDispather*>(handle);
    if(dispather != NULL)
    {
        switch ((MouseActionType)action.actionType)
        {
        case LeftMouseClick:
            dispather->leftMouseClicked(action.posX, action.posY);
            break;
        case MouseMove:
            dispather->mouseMove(action.posX, action.posY);
            break;
        case MouseDblClick:
            dispather->mouseDoubleClicked(action.posX, action.posY);
            break;
        case RightMouseClick:
            dispather->rightMouseClicked(action.posX, action.posY);
            break;
        default:
            break;
        }
    }
}

void setMousePostion(UsOverlay overlay, int x, int y)
{
    Overlay *lay = reinterpret_cast<Overlay*>(overlay);
    if(lay != NULL)
    {
        lay->setCurrentPos(QPoint(x, y));
    }
}

void resetMouseToLimitCenter(UsOverlay overlay)
{
    Overlay *lay = reinterpret_cast<Overlay*>(overlay);
    if(lay != NULL)
    {
        lay->resetCursorToLimitCenter();
    }
}

void setOverlaySize(UsOverlay overlay, int width, int height)
{
    Overlay *lay = reinterpret_cast<Overlay*>(overlay);
    if(lay != NULL)
    {
        lay->setSize(QSize(width, height));
        lay->setLimitRect(QRectF(0, 0, width, height));
    }
}

#ifdef __cplusplus
}
#endif

