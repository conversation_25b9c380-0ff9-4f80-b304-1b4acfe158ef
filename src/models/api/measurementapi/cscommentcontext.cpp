#include "cscommentcontext.h"
#include "private/mouseactiondispather.h"
#include "commentglyphscontrol.h"
#include "usapi.h"
#include "glyphscontrolmanager.h"

class CSCommentContextPrivate
{
public:
    CSCommentContextPrivate()
        : dispather(nullptr), usCxt(nullptr), glyphsControl(nullptr){}
    MouseActionDispather *dispather;
    UsContext usCxt;
    CommentGlyphsControl *glyphsControl;
};

CSCommentContext::CSCommentContext()
    : d(new CSCommentContextPrivate())
{
}

CSCommentContext::~CSCommentContext()
{
    delete d;
}

void CSCommentContext::startComment()
{
    if(d->glyphsControl == nullptr)
    {
        Q_ASSERT(d->usCxt != nullptr);
        d->glyphsControl = GlyphsControlManager::instance(
                    ).getChildGlyphsControl<CommentGlyphsControl>(GlyphsCtl::CommentGlyphsType);
    }
    Q_ASSERT(d->glyphsControl != nullptr);
    d->glyphsControl->onBeginAction();
}

void CSCommentContext::stopComment()
{
    d->glyphsControl->onEndAction();
}

void CSCommentContext::setMouseHandle(MouseHandle_t handle)
{
    MouseActionDispather *dispather = reinterpret_cast<MouseActionDispather*>(handle);
    if(dispather != nullptr)
    {
        d->dispather = dispather;
    }
}

void CSCommentContext::setUsContext(UsContext context)
{
    d->usCxt = context;
}

void CSCommentContext::setCommentText(const char *text)
{

}
