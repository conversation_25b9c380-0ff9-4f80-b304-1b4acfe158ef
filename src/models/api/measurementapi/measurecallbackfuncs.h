#ifndef MEASURECALLBACKFUNCS_H
#define MEASURECALLBACKFUNCS_H

#include "measurementapi_global.h"

#ifdef __cplusplus
extern "C"
{
#endif
/**
 *  current Child Changed
 */
typedef void (*CT_CALL CBCurrentMeasurementChanged)(void *object, const char* id, bool reset);

/**
 *  current measurement item is measured or not.
 */
typedef void (*CT_CALL CBMeasuredChanged)(void *object, const char* id, bool measured);

/**
 * represent the state of measurement procedure:
 *  Started = 0,
 *  Updated = 1,
 *  Completed = 2,
 *  Canceled = 3,
 *  Canceled_Unfinshed = 4
 */
typedef void (*CT_CALL CBMeasureStateChanged)(void *object, int state);

/**
 * screen measurement results changed:
 *  add results , type = 0
 *  remove results, type = 1
 *  change results, type = 2
 */
typedef void (*CT_CALL CBScreenMeasResultsChanged)(void *object, CSScreenMeasResult result, int type);

/**
 *  clear screen measurement results
 */
typedef void (*CT_CALL CBClearScreenMeasResults)(void *object);

/**
 *  display measurement tips
 */
typedef void (*CT_CALL CBDisplayMeasureTips)(void *object, const char *id, bool display);

#ifdef __cplusplus
}
#endif

#endif // MEASURECALLBACKFUNCS_H
