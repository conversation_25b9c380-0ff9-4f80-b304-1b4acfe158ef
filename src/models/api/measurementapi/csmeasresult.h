#ifndef CSMEASRESULTS_H
#define CSMEASRESULTS_H

#include "measurementapi_global.h"


#ifdef __cplusplus
extern "C"
{
#endif

MEASUREMENTAPISHARED_EXPORT CSPackagesMeasResults createPackagesMeasResults();

MEASUREMENTAPISHARED_EXPORT void releasePackagesMeasResults(CSPackagesMeasResults results);

MEASUREMENTAPISHARED_EXPORT bool hasPackagesMeasResults(CSPackagesMeasResults results);

MEASUREMENTAPISHARED_EXPORT CSMeasResults getOnePackageMeasResults(CSPackagesMeasResults results, const char* packageId);

MEASUREMENTAPISHARED_EXPORT bool save(CSPackagesMeasResults results, const char *fileName);

MEASUREMENTAPISHARED_EXPORT void load(CSPackagesMeasResults results, const char *fileName);

MEASUREMENTAPISHARED_EXPORT int getMeasResultCount(CSMeasResults result);

MEASUREMENTAPISHARED_EXPORT CSMeasResult getMeasResultAt(CSMeasResults result, int index);

MEASUREMENTAPISHARED_EXPORT bool isMeasResultValid(CSMeasResult result);

MEASUREMENTAPISHARED_EXPORT int getLeftRightCount(CSMeasResult result);

MEASUREMENTAPISHARED_EXPORT int getFarMidNearCount(CSMeasResult result);

MEASUREMENTAPISHARED_EXPORT CSMeasResultItem getMeasResultItem(CSMeasResult result,
                                                               int leftRight, int farMidNear, int fetusIndex);

MEASUREMENTAPISHARED_EXPORT bool getMeasResultItemIsValid(CSMeasResultItem result);

MEASUREMENTAPISHARED_EXPORT bool getMeasResultItemReportDisplay(CSMeasResultItem result);

//MEASUREMENTAPISHARED_EXPORT int getMeasResultItemLeftRight(CSMeasResultItem result);

//MEASUREMENTAPISHARED_EXPORT int getMeasResultItemFarMidNear(CSMeasResultItem result);

MEASUREMENTAPISHARED_EXPORT void getMeasResultItemCaption(CSMeasResultItem result, char* text, int size);

MEASUREMENTAPISHARED_EXPORT void getMeasResultItemValueText(CSMeasResultItem result, char* text, int size);

MEASUREMENTAPISHARED_EXPORT void getMeasResultItemUnitText(CSMeasResultItem result, char* text, int size);

MEASUREMENTAPISHARED_EXPORT double getMeasResultItemValue(CSMeasResultItem result);

#ifdef __cplusplus
}
#endif

#endif // CSMEASRESULTS_H
