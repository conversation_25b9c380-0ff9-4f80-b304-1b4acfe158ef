#include "csbodymarkwrapper.h"
#include "bodymarkpresetdatahandler.h"
#include "resource.h"
#include <QHash>
#include <QStringList>
#include <algorithm>
#include <string>
#include <vector>

struct BodyMarkBaseInfo
{
    BodyMarkBaseInfo()
    {
    }
    BodyMarkBaseInfo(const QString &_iconPath, bool _isUserType)
        : iconPath(_iconPath)
        , isUserType(_isUserType)
    {
    }
    /// iconPath全路径
    QString iconPath; //包括全路径
    /// 是否是用户名
    bool isUserType;
};

class CSBodyMarkWrapperPrivate
{
  public:
    CSBodyMarkWrapperPrivate()
    {
        handler = BodyMarkPresetDataHandler::instance();
        setupBodyMarkInfo();
        setupBodyMarkStore();
    }
    void setupBodyMarkStore()
    {
        QList<BodyMark> bmks;
        handler->getAllBodyMark(bmks);
        int total = bmks.size();
        for (int i = 0; i < total; ++i)
        {
            const BodyMark &k = bmks.at(i);
            bmkStore.insert(k.bodyMarkId(), BodyMarkBaseInfo(k.iconFileName(), k.type() == BodyMark::user));
        }
    }
    void setupBodyMarkInfo()
    {
        libInfoList.clear();
        handler->getAllBodyMarkLibInfo(libInfoList);

        typeInfoList.clear();
        handler->getAllBodyMarkTypeInfo(typeInfoList);
    }
    QList<BodyMarkLibInfo> libInfoList;
    QList<BodyMarkTypeInfo> typeInfoList;
    BodyMarkPresetDataHandler *handler;
    QHash<QString, BodyMarkBaseInfo> bmkStore;
    std::vector<std::string> systemBMFilePathList;
    std::vector<std::string> userBMFilePathList;
    std::string proxyAttr;
};

CSBodyMarkWrapper::CSBodyMarkWrapper()
    : d(new CSBodyMarkWrapperPrivate())
{
}

CSBodyMarkWrapper::~CSBodyMarkWrapper()
{
    delete d;
}

int CSBodyMarkWrapper::systemBMCount() const
{
    return d->libInfoList.count();
}

const char *CSBodyMarkWrapper::systemBMID(int index) const
{
    Q_ASSERT(index >= 0 && index < d->libInfoList.count());
    d->proxyAttr = d->libInfoList[index].libId.toStdString();
    return d->proxyAttr.c_str();
}

const char *CSBodyMarkWrapper::systemBMName(int index) const
{
    Q_ASSERT(index >= 0 && index < d->libInfoList.count());
    d->proxyAttr = d->libInfoList[index].libName.toStdString();
    return d->proxyAttr.c_str();
}

static QString getIconFullName(const QString &path)
{
    static QString dir = Resource::bodymarkDir + Resource::pathSeparator;
    return dir + path;
}

int CSBodyMarkWrapper::systemBMTargetCount(const char *sysId) const
{
    //    auto iter = std::find_if(m_Data->libInfoList.begin(), m_Data->libInfoList.end(),
    //                          [=](const BodyMarkLibInfo &info){return info.libName == sysId;});

    d->systemBMFilePathList.clear();

    QStringList bmkIds;
    d->handler->getLibAllBodyMarkIDList(sysId, bmkIds);
    QHash<QString, BodyMarkBaseInfo>::ConstIterator iter;
    for (int i = 0; i < bmkIds.count(); ++i)
    {
        iter = d->bmkStore.find(bmkIds.at(i));
        if (iter != d->bmkStore.end())
        {
            d->systemBMFilePathList.push_back(getIconFullName(iter.value().iconPath).toStdString());
        }
    }
    return d->systemBMFilePathList.size();
}

const char *CSBodyMarkWrapper::systemBMFilePath(int index)
{
    Q_ASSERT(!d->systemBMFilePathList.empty());
    Q_ASSERT(index >= 0 && index < (int)d->systemBMFilePathList.size());
    return d->systemBMFilePathList[index].c_str();
}

int CSBodyMarkWrapper::userBMCount() const
{
    return d->typeInfoList.count();
}

const char *CSBodyMarkWrapper::userBMID(int index) const
{
    Q_ASSERT(index >= 0 && index < d->typeInfoList.count());
    d->proxyAttr = d->typeInfoList[index].typeId.toStdString();
    return d->proxyAttr.c_str();
}

const char *CSBodyMarkWrapper::userBMName(int index) const
{
    Q_ASSERT(index >= 0 && index < d->typeInfoList.count());
    d->proxyAttr = d->typeInfoList[index].typeName.toStdString();
    return d->proxyAttr.c_str();
}

int CSBodyMarkWrapper::userBMType(int index) const
{
    Q_ASSERT(index >= 0 && index < d->typeInfoList.count());
    return d->typeInfoList[index].type;
}

int CSBodyMarkWrapper::userBMTargetCount(const char *userId) const
{
    d->userBMFilePathList.clear();

    QStringList bmkIds;
    d->handler->getTypeAllBodyMarkIDList(userId, bmkIds);
    QHash<QString, BodyMarkBaseInfo>::ConstIterator iter;
    for (int i = 0; i < bmkIds.count(); ++i)
    {
        iter = d->bmkStore.find(bmkIds.at(i));
        if (iter != d->bmkStore.end())
        {
            const BodyMarkBaseInfo &info = iter.value();
            d->userBMFilePathList.push_back(getIconFullName(info.iconPath).toStdString());
        }
    }
    return d->userBMFilePathList.size();
}

const char *CSBodyMarkWrapper::userBMFilePath(int index)
{
    Q_ASSERT(!d->userBMFilePathList.empty());
    Q_ASSERT(index >= 0 && index < (int)d->userBMFilePathList.size());
    return d->userBMFilePathList[index].c_str();
}

CSBodyMarkWrapper::CSBodyMarkWrapper(const CSBodyMarkWrapper &other) ： d(nullptr)
{
    Q_ASSERT(false);
}
