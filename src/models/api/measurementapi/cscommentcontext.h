#ifndef CSCOMMENTCONTEXT_H
#define CSCOMMENTCONTEXT_H

#include "measurementapi_global.h"
#include "usapidef.h"

class CSCommentContextPrivate;
class MEASUREMENTAPISHARED_EXPORT CSCommentContext
{
public:
    CSCommentContext();
    CSCommentContext(const CSCommentContext &context) = delete;
    ~CSCommentContext();

    void startComment();
    void stopComment();
    void setMouseHandle(MouseHandle_t handle);
    void setUsContext(UsContext context);
    void setCommentText(const char* text);

private:
    CSCommentContextPrivate *d;
};

#endif // CSCOMMENTCONTEXT_H
