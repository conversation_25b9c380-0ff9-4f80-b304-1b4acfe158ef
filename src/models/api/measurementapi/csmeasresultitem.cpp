#include "csmeasresultitem.h"
#include "measresultitem.h"
#include <QVariant>
#include <string>

class CSMeasResultItemPrivate
{
public:
    CSMeasResultItemPrivate() : dataValid(false), reportDisplay(false), leftRight(-1), farMidNear(-1){}
    bool dataValid;
    bool reportDisplay;
    int leftRight;
    int farMidNear;
    std::string caption;
    std::string valueText;
    QVariant value;
    std::string unitText;
};

CSMeasResultItem::CSMeasResultItem(const MeasResultItem &item)
    : m_Data(new CSMeasResultItemPrivate())
{
    m_Data->dataValid = item.isValid();
    m_Data->reportDisplay = item.rptDisplay();
    m_Data->leftRight = item.leftRight();
    m_Data->farMidNear = item.farMidNear();
    m_Data->caption = item.caption().toStdString();
    m_Data->valueText = item.valueText().first().toStdString();     //目前不考虑英制单位
    m_Data->value = item.value();
    m_Data->unitText = item.unitText().first().toStdString();
}

CSMeasResultItem::CSMeasResultItem(const CSMeasResultItem &item)
{
    m_Data->dataValid = item.m_Data->dataValid;
    m_Data->reportDisplay = item.m_Data->reportDisplay;
    m_Data->leftRight = item.m_Data->leftRight;
    m_Data->farMidNear = item.m_Data->farMidNear;
    m_Data->caption = item.m_Data->caption;
    m_Data->valueText = item.m_Data->valueText;
    m_Data->value = item.value();
    m_Data->unitText = item.unitText();
}

CSMeasResultItem::CSMeasResultItem()
    : m_Data(new CSMeasResultItemPrivate())
{
}

CSMeasResultItem::~CSMeasResultItem()
{
    delete m_Data;
}

bool CSMeasResultItem::isValid() const
{
    return m_Data->dataValid;
}

bool CSMeasResultItem::reportDisplay() const
{
    return m_Data->reportDisplay;
}

int CSMeasResultItem::leftRight() const
{
    return m_Data->leftRight;
}

int CSMeasResultItem::farMidNear() const
{
    return m_Data->farMidNear;
}

const char *CSMeasResultItem::caption() const
{
    return m_Data->caption.c_str();
}

const char *CSMeasResultItem::valueText() const
{
    return m_Data->valueText.c_str();
}

double CSMeasResultItem::value() const
{
    return m_Data->value.toDouble();
}

const char *CSMeasResultItem::unitText() const
{
    return m_Data->unitText.c_str();
}

