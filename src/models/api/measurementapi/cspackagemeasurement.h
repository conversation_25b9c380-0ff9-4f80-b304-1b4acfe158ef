#ifndef CSPACKAGEMEASUREMENT_H
#define CSPACKAGEMEASUREMENT_H

#include "measurementapi_global.h"

class CSIMeasurement;
class PackageMeasurement;
class CSPackageMeasurementPrivate;
class MEASUREMENTAPISHARED_EXPORT CSPackageMeasurement
{
public:
    CSPackageMeasurement();
    CSPackageMeasurement(const CSPackageMeasurement &pkgmeas);
    ~CSPackageMeasurement();

    void setPackageMeasurement(PackageMeasurement* package);
    PackageMeasurement *packageMeasurement() const;

    const char* caption() const;

    const char *id() const;
    const char* composedId() const;

    int currentImageType() const;
    void setCurrentImageType(int value);

    const char * species() const;
    void setSpecies(const char* value);

    const char* reportCaption() const;
    void setReportCaption(const char* value);

    PackageRegion packageRegion() const;

    int multiFetusCount() const;

    int fetusIndex() const;
    void setFetusIndex(int index);

    bool isActive() const;

    CSIMeasurement* currentStudy() const;

    CSIMeasurement* getStudy(int imageType) const;

    void run();

private:
    CSPackageMeasurementPrivate *m_Data;
};

#endif // CSPACKAGEMEASUREMENT_H
