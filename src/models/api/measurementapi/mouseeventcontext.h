#ifndef MOUSEEVENTCONTEXT_H
#define MOUSEEVENTCONTEXT_H

#include "measurementapi_global.h"
#include "usapidef.h"


#ifdef __cplusplus
extern "C"
{
#endif

MEASUREMENTAPISHARED_EXPORT MouseHandle_t createMouseHandle();

MEASUREMENTAPISHARED_EXPORT void releaseMouseHandle(Mouse<PERSON>andle_t handle);

MEASUREMENTAPISHARED_EXPORT void mouseActionHandle(MouseHandle_t handle, CSMouseAction action);

////////////////////////////////////////////////////////////////////////////////////////////

MEASUREMENTAPISHARED_EXPORT void setMousePostion(UsOverlay overlay, int x, int y);

MEASUREMENTAPISHARED_EXPORT void resetMouseToLimitCenter(UsOverlay overlay);

MEASUREMENTAPISHARED_EXPORT void setOverlaySize(UsOverlay overlay, int width, int height);

#ifdef __cplusplus
}
#endif
#endif // MOUSEEVENTCONTEXT_H
