#include "measurementinfohelper.h"
#include "measurementdef.h"
#include "packagesmeasurement.h"
#include "packagemeasurement.h"
#include "cspackagesmeasurement.h"
#include <string>

class CSMeasurementPackageInfoPrivate
{
public:
    CSMeasurementPackageInfoPrivate(): type(Measurement::USER_TYPE){}
    std::string id;
    std::string caption;
    int type;
};

CSMeasurementPackageInfo::CSMeasurementPackageInfo()
    : m_Data(new CSMeasurementPackageInfoPrivate)
{
}

CSMeasurementPackageInfo::CSMeasurementPackageInfo(const CSMeasurementPackageInfo &info)
    : m_Data(new CSMeasurementPackageInfoPrivate)
{
    *m_Data = *(info.m_Data);
}

CSMeasurementPackageInfo::CSMeasurementPackageInfo(const char *_id, const char *_caption, int _type)
    : m_Data(new CSMeasurementPackageInfoPrivate)
{
    m_Data->id = _id;
    m_Data->caption = _caption;
    m_Data->type = _type;
}

CSMeasurementPackageInfo::~CSMeasurementPackageInfo()
{
    delete m_Data;
}

const char *CSMeasurementPackageInfo::id() const
{
    return m_Data->id.c_str();
}

const char *CSMeasurementPackageInfo::caption() const
{
    return m_Data->caption.c_str();
}

int CSMeasurementPackageInfo::type() const
{
    return m_Data->type;
}


class CSReportInfoPrivate
{
public:
    CSReportInfoPrivate() : region(-1), fetusCount(-1), fetusIndex(-1){}
    std::string id;
    int region;
    std::string title;
    int fetusCount;
    int fetusIndex;
};

CSReportInfo::CSReportInfo()
    : m_Data(new CSReportInfoPrivate)
{
}

CSReportInfo::CSReportInfo(const CSReportInfo &info)
    : m_Data(new CSReportInfoPrivate)
{
    *m_Data = *info.m_Data;
}

CSReportInfo::CSReportInfo(const char *_id, int _region, const char *_title, int _fetusCount, int _fetusIndex)
    : m_Data(new CSReportInfoPrivate)
{
    m_Data->id = _id;
    m_Data->region = _region;
    m_Data->title = _title;
    m_Data->fetusCount = _fetusCount;
    m_Data->fetusIndex = _fetusIndex;
}

CSReportInfo::~CSReportInfo()
{
    delete m_Data;
}

class MeasurementInfoHelperPrivate
{
public:
    MeasurementInfoHelperPrivate()
        : packages(NULL){}
    PackagesMeasurement *packages;
};

MeasurementInfoHelper::MeasurementInfoHelper()
    : m_Data(new MeasurementInfoHelperPrivate)
{
}

MeasurementInfoHelper::MeasurementInfoHelper(const MeasurementInfoHelper &info)
    : m_Data(new MeasurementInfoHelperPrivate)
{
    m_Data->packages = info.m_Data->packages;
}

MeasurementInfoHelper::~MeasurementInfoHelper()
{
    delete m_Data;
}

void MeasurementInfoHelper::setPackagesMeasurement(CSPackagesMeasurement *packages)
{
    Q_ASSERT(packages != NULL);
    m_Data->packages = packages->packagesMeasurement();
}

int MeasurementInfoHelper::packageInfoCount() const
{
    return m_Data->packages->count() - 1;    // the last one is quick measurement
}

CSMeasurementPackageInfo MeasurementInfoHelper::getPackageInfo(int index) const
{
    if(index >= 0)
    {
        IMeasurement *child = m_Data->packages->at(index);
        return CSMeasurementPackageInfo(child->id().toStdString().c_str(),
                                        child->caption().toStdString().c_str(), child->systemType());
    }
    return CSMeasurementPackageInfo();
}

CSMeasurementPackageInfo MeasurementInfoHelper::getPackageInfo(const char *id) const
{
    const MeasurementPackageInfo &info = m_Data->packages->getPackageInfo(id);
    return CSMeasurementPackageInfo(info.id.toStdString().c_str(), info.caption.toStdString().c_str(), info.type);
}

int MeasurementInfoHelper::reportInfoCount() const
{
    return m_Data->packages->count() - 1;   // the last one is quick measurement
}

CSReportInfo MeasurementInfoHelper::getReportInfo(int index) const
{
    if(index >= 0)
    {
        PackageMeasurement* package = m_Data->packages->getPackage(index);
        return CSReportInfo(package->id().toStdString().c_str(),
                          package->region(), package->reportCaption().toStdString().c_str(),
                          package->multiFetusCount(), package->fetusIndex());
    }
    return CSReportInfo();
}

CSReportInfo MeasurementInfoHelper::getReportInfo(const char *id) const
{
    const ReportInfo& info = m_Data->packages->getReportInfo(id);
    return CSReportInfo(info.id.toStdString().c_str(),
                      info.region, info.title.toStdString().c_str(),
                      info.fetusCount, info.fetusIndex);
}

