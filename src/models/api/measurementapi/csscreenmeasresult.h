#ifndef CSSCREENMEASRESULT_H
#define CSSCREENMEASRESULT_H

#include "measurementapi_global.h"


#ifdef __cplusplus
extern "C"
{
#endif

//MEASUREMENTAPISHARED_EXPORT CSScreenMeasResults createScreenMeasResults();

//MEASUREMENTAPISHARED_EXPORT void releaseScreenMeasResults(CSScreenMeasResults results);

MEASUREMENTAPISHARED_EXPORT int getScreenMeasResultsCount(CSScreenMeasResults results);

MEASUREMENTAPISHARED_EXPORT CSScreenMeasResult getScreenMeasResult(CSScreenMeasResults results, int index);

MEASUREMENTAPISHARED_EXPORT bool isScreenMeasResultsEmpty(CSScreenMeasResults results);

MEASUREMENTAPISHARED_EXPORT CSScreenMeasResult findScreenMeasResult(CSScreenMeasResults results, const char* id);

///////////////////////////////////////////////////////////////////////////////////////////

MEASUREMENTAPISHARED_EXPORT int getScreenMeasResultIndex(CSScreenMeasResult result);

MEASUREMENTAPISHARED_EXPORT void getScreenMeasResultCaption(CSScreenMeasResult result, char* caption);

MEASUREMENTAPISHARED_EXPORT void getScreenMeasResultComposedId(CSScreenMeasResult result, char* id);

MEASUREMENTAPISHARED_EXPORT bool isScreenMeasResultEmpty(CSScreenMeasResult result);

MEASUREMENTAPISHARED_EXPORT int getScreenMeasResultChildrenCount(CSScreenMeasResult result);

MEASUREMENTAPISHARED_EXPORT void getScreenMeasResultChildCaption(CSScreenMeasResult result, int index, char* caption);

MEASUREMENTAPISHARED_EXPORT bool isScreenMeasResultChildValueValid(CSScreenMeasResult result, int index);

MEASUREMENTAPISHARED_EXPORT void getScreenMeasResultChildValueText(CSScreenMeasResult result, int index, char *valueText);

MEASUREMENTAPISHARED_EXPORT void getScreenMeasResultChildUnitText(CSScreenMeasResult result, int index, char *unitText);

#ifdef __cplusplus
}
#endif


#endif // CSSCREENMEASRESULT_H
