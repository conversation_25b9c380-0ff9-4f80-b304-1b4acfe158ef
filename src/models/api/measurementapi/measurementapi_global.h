#ifndef MEASUREMENTAPI_GLOBAL_H
#define MEASUREMENTAPI_GLOBAL_H

#if !defined(SAG_COM) && (defined(WIN64) || defined(_WIN64) || defined(__WIN64__))
#  define C_OS_WIN32
#  define C_OS_WIN64
#elif !defined(SAG_COM) && (defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__))
#  if defined(WINCE) || defined(_WIN32_WCE)
#    define C_OS_WINCE
#  else
#    define C_OS_WIN32
#  endif
#elif defined(__linux__) || defined(__linux)
#  define C_OS_LINUX
#elif defined(__APPLE__)
#  define C_OS_APPLE
#else
#  error "API has not been ported to this OS"
#endif

#if defined(C_OS_WIN32) || defined(C_OS_WIN64) || defined(C_OS_WINCE)
#  define C_OS_WIN
#endif

#ifndef C_DECL_EXPORT
#  if defined(C_OS_WIN)
#    define C_DECL_EXPORT __declspec(dllexport)
#  endif
#  ifndef C_DECL_EXPORT
#    define C_DECL_EXPORT __attribute__((visibility("default")))
#  endif
#endif
#ifndef C_DECL_IMPORT
#  if defined(C_OS_WIN)
#    define C_DECL_IMPORT __declspec(dllimport)
#  else
#    define C_DECL_IMPORT __attribute__((visibility("default")))
#  endif
#endif

#if defined(MEASUREMENTAPI_LIBRARY)
    #define MEASUREMENTAPISHARED_EXPORT C_DECL_EXPORT
#else
    #define MEASUREMENTAPISHARED_EXPORT C_DECL_IMPORT
#endif

#define CT_CALL

#ifdef __cplusplus
extern "C"
{
#endif

typedef  void* MeasurementId;
typedef void* MouseHandle_t;

typedef void* CSPackagesMeasResults;
typedef void* CSMeasResults;
typedef void* CSMeasResult;
typedef void* CSMeasResultItem;

typedef void* CSScreenMeasResults;
typedef void* CSScreenMeasResult;

enum MeasureStateType{
    PackagePathError,
    PackageLoadOK,
    PackageAlreadyLoaded,
    InitFailed,
    AlreadyInit,
    InitOK
};

//
//  actionType mouse action type 0 ~ 3
//   LeftMouseClick = 0,
//   MouseDblClick = 1,
//   MouseMove = 2,
//   RightMouseClick = 3
class MEASUREMENTAPISHARED_EXPORT CSMouseAction
{
public:
    CSMouseAction();
    CSMouseAction(int type, int x, int y, int pressed = 0);
    CSMouseAction(const CSMouseAction& action);
    int actionType;
    int posX;
    int posY;
    int isPressed;   // 为0说明处于释放状态，为1说明处于按压状态, 目前不用设置。
};

enum MeasurementState
{
    Started,
    Updated,
    Completed,
    Canceled,
    Canceled_Unfinshed
};

enum APIImageType
{
    APIImageType_B,
    APIImageType_M,
    APIImageType_D,
    APIImageType_Count
};

enum PackageRegion
{
    General_Region,
    Abdomen_Region,
    Obstetric_Region,
    Gynecology_Region,
    Cardiology_Region,
    Vascular_Region,
    Urology_Region,
    SmallPart_Region,
    ORTH_Region,
    Quick_Region,
    Count_Region
};

enum APIMeasureType
{
    MeasureType_Meas = 0,
    MeasureType_Calc = 1,
    MeasureType_Study = 2,
    MeasureType_Package = 3,
    MeasureType_Packages = 4,
    MeasureType_Count = 5
};

#ifdef __cplusplus
}
#endif

#endif // MEASUREMENT_GLOBAL_H
