#ifndef CSMEASRESULTITEM_H
#define CSMEASRESULTITEM_H

#include "measurementapi_global.h"


class MeasResultItem;
class CSMeasResultItemPrivate;
class MEASUREMENTAPISHARED_EXPORT CSMeasResultItem
{
public:
    CSMeasResultItem(const MeasResultItem& item);
    CSMeasResultItem(const CSMeasResultItem &item);
    CSMeasResultItem();
    ~CSMeasResultItem();

    bool isValid() const;
    bool reportDisplay() const;
    int leftRight() const;
    int farMidNear() const;
    const char* caption() const;
    const char* valueText() const;
    double value() const;
    const char* unitText() const;

private:
    CSMeasResultItemPrivate *m_Data;
};

#endif // CSMEASRESULTITEM_H
