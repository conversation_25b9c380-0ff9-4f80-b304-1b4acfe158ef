#include "cspackagesmeasurement.h"
#include "packagesmeasurement.h"
#include "cspackagemeasurement.h"
#include "measurementserialization.h"
#include "csimeasurement.h"
#include "measurementmodelutil.h"
#include "basemeasuredispather.h"
#include "measresult.h"
#include "csmeasresult.h"
#include "measurestaterecorder.h"
#include <string>
#include <QDebug>

class CSPackagesMeasurementPrivate
{
public:
    CSPackagesMeasurementPrivate()
        : packagesMeasurement(NULL)
        , wrapPackageMeasurement(new CSPackageMeasurement)
        , wrapImeasurement(new CSIMeasurement){}

    ~CSPackagesMeasurementPrivate()
    {
        delete wrapImeasurement;
        delete wrapPackageMeasurement;
        if(packagesMeasurement != NULL)
        {
            delete  packagesMeasurement;
            packagesMeasurement = NULL;
        }
    }

    PackagesMeasurement *packagesMeasurement;
    CSPackageMeasurement *wrapPackageMeasurement;
    CSIMeasurement *wrapImeasurement;
    std::string proxyAttribute; // 防止直接返回QString造成内存释放奔溃
};

CSPackagesMeasurement::CSPackagesMeasurement()
    : m_Data(new CSPackagesMeasurementPrivate)
{
}

CSPackagesMeasurement::CSPackagesMeasurement(const CSPackagesMeasurement &packages)
{
    qDebug() << "CSPackagesMeasurement::CSPackagesMeasurement copy constructor";
    m_Data->packagesMeasurement = packages.m_Data->packagesMeasurement;
}

CSPackagesMeasurement::~CSPackagesMeasurement()
{
    delete m_Data;
}

const char *CSPackagesMeasurement::id() const
{
    Q_ASSERT(m_Data->packagesMeasurement != NULL);
    m_Data->proxyAttribute = m_Data->packagesMeasurement->id().toStdString();
    return m_Data->proxyAttribute.c_str();
}

const char *CSPackagesMeasurement::composedId() const
{
    m_Data->proxyAttribute = m_Data->packagesMeasurement->composedId().toStdString();
    return m_Data->proxyAttribute.c_str();
}

int CSPackagesMeasurement::currentIndex() const
{
    return m_Data->packagesMeasurement->currentIndex();
}

void CSPackagesMeasurement::setCurrentIndex(int value)
{
    m_Data->packagesMeasurement->setCurrentIndex(value);
}

int CSPackagesMeasurement::packageCount() const
{
    return m_Data->packagesMeasurement->count();
}

CSPackageMeasurement *CSPackagesMeasurement::currentPackage() const
{
    m_Data->wrapPackageMeasurement->setPackageMeasurement(m_Data->packagesMeasurement->currentPackage());
    return  m_Data->wrapPackageMeasurement;
}

CSPackageMeasurement *CSPackagesMeasurement::getPackage(int index) const
{
    m_Data->wrapPackageMeasurement->setPackageMeasurement(m_Data->packagesMeasurement->getPackage(index));
    return m_Data->wrapPackageMeasurement;
}

CSPackageMeasurement *CSPackagesMeasurement::quickPackage() const
{
    m_Data->wrapPackageMeasurement->setPackageMeasurement(m_Data->packagesMeasurement->quickPackage());
    return m_Data->wrapPackageMeasurement;
}

CSPackageMeasurement *CSPackagesMeasurement::findPackage(const char *id) const
{
    m_Data->wrapPackageMeasurement->setPackageMeasurement(m_Data->packagesMeasurement->findPackage(id));
    return m_Data->wrapPackageMeasurement;
}

bool CSPackagesMeasurement::isCurrentQuickMeasure() const
{
    return m_Data->packagesMeasurement->isCurrentQuickMeasure();
}

void CSPackagesMeasurement::clear()
{
    m_Data->packagesMeasurement->clear();
}

void CSPackagesMeasurement::clearResult()
{
    m_Data->packagesMeasurement->clearResult();
}

void CSPackagesMeasurement::getPackagesResults(CSPackagesMeasResults value)
{
    PackagesMeasResults *results = reinterpret_cast<PackagesMeasResults *>(value);
    if(results != NULL)
    {
        m_Data->packagesMeasurement->packagesMeasuredResults(results,false);
    }
}

void CSPackagesMeasurement::run()
{
    if(!m_Data->packagesMeasurement->isActive())
    {
        m_Data->packagesMeasurement->start();
    }
}

bool CSPackagesMeasurement::isActive() const
{
    return m_Data->packagesMeasurement->isActive();
}

void CSPackagesMeasurement::setPackagesResults(CSPackagesMeasResults value)
{
    PackagesMeasResults *results = reinterpret_cast<PackagesMeasResults *>(value);
    if(results != NULL)
    {
        m_Data->packagesMeasurement->setPackagesResults(*results);
    }
}

int CSPackagesMeasurement::load(const char *packageFilePath, bool reload)
{
    if(!reload)
    {
        Q_ASSERT(m_Data->packagesMeasurement == nullptr);
        if(m_Data->packagesMeasurement != nullptr)
        {
            return -1;  // package already loaded
        }
        MeasurementSerialization loader;
        m_Data->packagesMeasurement = loader.load(packageFilePath); // create packagesmeasurement

        Q_ASSERT(m_Data->packagesMeasurement != nullptr);

        return 0;
    }
    else
    {
        Q_ASSERT(m_Data->packagesMeasurement != nullptr);

        MeasurementSerialization loader;
        loader.load(m_Data->packagesMeasurement, packageFilePath);

        return 0;
    }
}

PackagesMeasurement *CSPackagesMeasurement::packagesMeasurement() const
{
    return m_Data->packagesMeasurement;
}

CSIMeasurement *CSPackagesMeasurement::findMeasurement(const char *composedId)
{
    IMeasurement *measure = MeasurementModelUtil::findMeasurement(m_Data->packagesMeasurement, composedId);
    Q_ASSERT(measure != NULL);
    m_Data->wrapImeasurement->setMeasurement(measure);
    return m_Data->wrapImeasurement;
}

CSIMeasurement *CSPackagesMeasurement::nextChild()
{
    IMeasurement *measure = MeasureStateRecorder::instance().nextChild();
    if(measure != NULL)
    {
        m_Data->wrapImeasurement->setMeasurement(measure);
        return m_Data->wrapImeasurement;
    }
    return NULL;
}
