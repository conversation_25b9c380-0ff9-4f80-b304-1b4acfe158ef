add_definitions(-DMEASUREMENTAPI_LIBRARY)

include_depends(measurementmodel usf.i.mark.meas.bs uscontextapi serialization)

add_library_qt(measurementapi
    measurementapi_global.h
    measurecallbackfuncs.h
    csimeasurement.cpp
    csmeasuremousetool.cpp
    cspackagemeasurement.cpp
    cspackagesmeasurement.cpp
    measurementinfohelper.cpp
    csmeasurementcontext.cpp
    mouseeventcontext.cpp
    private/mouseactiondispather.cpp
    private/screenmeasresultsdispather.cpp
    private/measurementcontextdispather.cpp
    csmeasresult.cpp
    csscreenmeasresult.cpp
    csbodymarkwrapper.cpp
    csbodymarkcontext.cpp
    cscommentcontext.cpp
    cscommentwrapper.cpp
)

target_link_libraries(measurementapi measurementmodel usf.i.mark.meas.bs uscontextapi serialization)

install(DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    DESTINATION bin
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    DESTINATION lib
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION include
    FILES_MATCHING PATTERN "*.h")

