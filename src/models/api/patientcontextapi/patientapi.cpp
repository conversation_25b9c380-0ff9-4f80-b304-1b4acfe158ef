#include "patientapi.h"
#include "patientwrapper.h"
#include "studywrapper.h"
#include "dataaccesslayerhelper.h"
#include "modeldirectorygetter.h"
#include "patient.h"
#include "patientmanagement.h"
#include <qdebug.h>
#include <QtCore>
#include <QFile>
#include "querycriteria.h"
#include "resource.h"
#include "syncer.h"
#include "util.h"
#include "appsetting.h"
#include "adminconfigmodel.h"
#include "userinfo.h"
#include "userpresetdatahandler.h"
#include <QList>

struct PatientApiData
{
    ~PatientApiData()
    {
        clearPatients();
    }
    void clearPatients()
    {
        qDeleteAll(patientBuffer);
        patientBuffer.clear();
    }
    QList<PatientWrapper*> patientBuffer;
    std::string stringCache;
    QList<UserInfo> allUserInfo;
};

PatientApi::PatientApi()
    : d(new PatientApiData())
    , m_AdminConfigModel(new AdminConfigModel)
{
}

PatientApi::~PatientApi()
{
    delete d;

    delete m_AdminConfigModel;
    m_AdminConfigModel = NULL;
}

PatientApi& PatientApi::getInstance()
{
    static PatientApi api;
    return api;
}

void PatientApi::initialize()
{
    // 原先ios数据库存放在Document中名字为CommonData.s3db，后来换到其他数据库中，并且人兽数据库分离了。
    const QString OldDataBase =
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/CommonData.s3db";

    QString filePath = Resource::getDefaultDbFileName();
    if (QFile::exists(OldDataBase) && AppSetting::isHuman())
    {
        Q_ASSERT(!QFile::exists(filePath));

        if (!QFile::copy(OldDataBase, filePath))
        {
            QFile file(filePath);
            file.setPermissions(file.permissions() | QFile::WriteOwner);

            if (!QFile::remove(OldDataBase))
            {
                qDebug() << "remove file failed: " << OldDataBase;
            }

            UTIL_DATA_SYNCER
        }
        else
        {
            qDebug() << "Copy failed: raw file: " << OldDataBase << "new file: " << filePath;
        }
    }

    DataAccessLayerHelper::initialize();
    DataAccessLayerHelper::setDbFileName();
}

PatientWrapper* PatientApi::createPatient()
{
    return new PatientWrapper(PatientManagement::createPatient());
}

PatientWrapper* PatientApi::createPatient(const char* patientId)
{
    return new PatientWrapper(PatientManagement::createPatient(patientId));
}

void PatientApi::releasePatientWrapper(PatientWrapper* wrapper)
{
    if (wrapper != nullptr)
    {
        delete wrapper;
        wrapper = nullptr;
    }
}

void PatientApi::savePatient(PatientWrapper& p)
{
    p.save();
}

void PatientApi::saveStudy(StudyWrapper& s)
{
    s.save();
}

void PatientApi::updatePatient(PatientWrapper& p)
{
    p.update();
}

void PatientApi::updateStudy(StudyWrapper& p)
{
    p.update();
}

void PatientApi::removePatient(PatientWrapper& p, bool changeDB)
{
    Q_ASSERT(&p != NULL);
    if (changeDB)
    {
        p.remove();
    }
    d->patientBuffer.removeOne(&p);
    Q_ASSERT(&p != NULL);
    delete &p;
}

void PatientApi::removeAllPatient(bool changeDB)
{
    if (changeDB)
    {
        foreach (PatientWrapper* p, d->patientBuffer)
        {
            p->remove();
        }
    }
    d->clearPatients();
}

int PatientApi::queryPatients() const
{
    d->clearPatients();
    const QList<Patient*>& lists = DataAccessLayerHelper::getPatients(true);
    foreach (Patient* p, lists)
    {
        PatientWrapper* pm = new PatientWrapper(p);
        d->patientBuffer.append(pm);
    }
    return lists.size();
}

PatientWrapper* PatientApi::operator[](int index) const
{
    return at(index);
}

PatientWrapper* PatientApi::at(int index) const
{
    if (index < d->patientBuffer.size())
    {
        return d->patientBuffer.at(index);
    }
    return NULL;
}

int PatientApi::speciesCount() const
{
    return Resource::species().count();
}

const char* PatientApi::species(int index) const
{
    d->stringCache = Resource::species()[index].toStdString();
    return d->stringCache.c_str();
}

int PatientApi::displaySexCount(bool isHuman) const
{
    return Resource::trSexes(isHuman).count();
}

const char* PatientApi::displaySex(int index, bool isHuman)
{
    d->stringCache = Resource::trSexes(isHuman).at(index).toStdString();
    return d->stringCache.c_str();
}

const char* PatientApi::databasePath() const
{
    d->stringCache = PatientPath::instance().databasePath().toStdString();
    return d->stringCache.c_str();
}

// void PatientApi::queryAllUserInfo()
//{
//    d->allUserInfo = AdminConfigModel::instance()->getAllUserInfo();
//}
int PatientApi::getUserCount()
{
    d->allUserInfo.clear();
    d->allUserInfo = m_AdminConfigModel->getAllUserInfo();
    return d->allUserInfo.count();
}

const char* PatientApi::getUserNameAt(int index)
{
    const UserInfo& info = d->allUserInfo.at(index);

    d->stringCache = info.userName().toStdString();

    return d->stringCache.c_str();
}

const char* PatientApi::getUserIdAt(int index)
{
    const UserInfo& info = d->allUserInfo.at(index);

    d->stringCache = info.userId().toStdString();

    return d->stringCache.c_str();
}

void PatientApi::resetPassword(const char* name)
{
    m_AdminConfigModel->resetPassword(name);
}

int PatientApi::changePassword(const char* pwd, const char* newPwd)
{
    m_AdminConfigModel->changePwd(pwd, newPwd);
}

int PatientApi::addUser(const char* username, const char* pwd, const char* quthorityName)
{
    return m_AdminConfigModel->addUser(username, pwd, quthorityName);
}

int PatientApi::removeUser(const char* username)
{
    return m_AdminConfigModel->removeUser(username);
}

int PatientApi::login(const char* username, const char* pwd)
{
    return m_AdminConfigModel->login(username, pwd);
}

int PatientApi::logout()
{
    return m_AdminConfigModel->logOut();
}

const char* PatientApi::getUserID(const char* username)
{
    UserInfo info;

    bool ret = m_AdminConfigModel->getUserInfo(username, info);

    if (ret)
    {
        d->stringCache = info.userId().toStdString();
        return d->stringCache.c_str();
    }

    return "";
}
