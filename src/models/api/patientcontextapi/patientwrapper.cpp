#include "patientwrapper.h"
#include "dataaccesslayerhelper.h"
#include "modeldirectorygetter.h"
#include "patient.h"
#include "resource.h"
#include <QDebug>
#include <QStandardPaths>
#include <QtCore>
#include <QtGlobal>

#define MAX_SIZE 1024

static const QString OldHardDiskDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/harddisk";

struct PatientWrapperPrivate
{
    ~PatientWrapperPrivate()
    {
        clearStudys();
        if (patient != nullptr)
        {
            delete patient;
            patient = nullptr;
        }
    }
    void clearStudys()
    {
        qDeleteAll(studyWrappers);
        studyWrappers.clear();
    }

    bool patientDataInOldHardDisk()
    {
        qDebug() << " harddisk @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@: " << OldHardDiskDir;
        if (QDir(OldHardDiskDir).exists())
        {
            const QString &path = PatientPath::instance().path(*patient, OldHardDiskDir);
            if (QDir(path).exists())
            {
                return true;
            }
        }
        qDebug() << " harddisk nooooooooot exist ....";
        return false;
    }

    Patient *patient{nullptr};
    std::string stringCache;
    std::string stringImagePath;
    std::string stringCinePath;
    std::string stringMeasPath;
    std::vector<StudyWrapper *> studyWrappers;
};

PatientWrapper::PatientWrapper(Patient *p)
    : d(new PatientWrapperPrivate())
{
    d->patient = p;
}

PatientWrapper::~PatientWrapper()
{
    delete d;
}

void PatientWrapper::save()
{
    //    DataAccessLayerHelper::save(*d->patient);
    DataAccessLayerHelper::saveOrUpdate(*d->patient);
}

void PatientWrapper::update()
{
    DataAccessLayerHelper::update(*d->patient);
}

void PatientWrapper::remove()
{
    DataAccessLayerHelper::del(*d->patient);
}

void PatientWrapper::setFirstName(const char *fn)
{
    d->patient->setfirstName(fn);
}

void PatientWrapper::setMiddleName(const char *mn)
{
    d->patient->setmiddleName(mn);
}

const char *PatientWrapper::middleName() const
{
    d->stringCache = d->patient->middleName().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setLastName(const char *ln)
{
    d->patient->setlastName(ln);
}

const char *PatientWrapper::lastName() const
{
    d->stringCache = d->patient->lastName().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setPatientsAge(int age)
{
    d->patient->setPatientsAge(age);
}

int PatientWrapper::patientsAge() const
{
    return d->patient->PatientsAge();
}

void PatientWrapper::setPatientsBirthDate(const char *date)
{
    d->patient->setPatientsBirthDateRaw(date);
}
const char *PatientWrapper::patientsBirthDate() const
{
    d->stringCache = d->patient->PatientsBirthDateRaw().toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::patientId() const
{
    d->stringCache = d->patient->PatientId().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setPatientId(const char *id)
{
    d->patient->setPatientId(id);
}

const char *PatientWrapper::firstName() const
{
    d->stringCache = d->patient->firstName().toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::patientName() const
{
    d->stringCache = d->patient->PatientsName().toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::patientSex() const
{
    d->stringCache = d->patient->PatientsSex().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setPatientSex(const char *sex)
{
    d->patient->setPatientsSex(sex);
}

int PatientWrapper::displayPatientSexIndex() const
{
    return Resource::sexIndex(d->patient->PatientsSex());
}

void PatientWrapper::setDisplayPatientSexIndex(int index)
{
    d->patient->setPatientsSex(Resource::getSex(index));
}

const char *PatientWrapper::displayPatientSex() const
{
    const QString &dbSex = d->patient->PatientsSex();
    d->stringCache = Resource::getTrSex(dbSex).toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::patientMRN() const
{
    d->stringCache = d->patient->MRN().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setPatientMRN(const char *mrn)
{
    d->patient->setMRN(mrn);
}

const char *PatientWrapper::species() const
{
    d->stringCache = d->patient->Species().toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setSpecies(const char *value)
{
    d->patient->setSpecies(value);
}

const char *PatientWrapper::fileName() const
{
    d->stringCache = PatientPath::instance().fileName(*d->patient).toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::patientDataPath() const
{
    if (d->patientDataInOldHardDisk())
    {
        d->stringCache = PatientPath::instance().path(*d->patient, OldHardDiskDir).toStdString();
    }
    else
    {
        d->stringCache = PatientPath::instance().path(*d->patient).toStdString();
    }

    qDebug() << "PatientWrapper::patientDataPath: " << d->stringCache.c_str();

    return d->stringCache.c_str();
}

const char *PatientWrapper::patientDataFilePath() const
{
    if (d->patientDataInOldHardDisk())
    {
        d->stringCache = PatientPath::instance().filePath(*d->patient, OldHardDiskDir).toStdString();
    }
    else
    {
        d->stringCache = PatientPath::instance().filePath(*d->patient).toStdString();
    }

    qDebug() << "PatientWrapper::patientDataFilePath: " << d->stringCache.c_str();

    return d->stringCache.c_str();
}

const char *PatientWrapper::storeImageFullName() const
{
    if (d->patientDataInOldHardDisk())
    {
        d->stringImagePath =
            QString(PatientPath::instance().filePath(*d->patient, OldHardDiskDir) + Resource::imgExt).toStdString();
    }
    else
    {
        d->stringImagePath = QString(PatientPath::instance().filePath(*d->patient) + Resource::imgExt).toStdString();
    }

    qDebug() << "PatientWrapper::storeImageFullName: " << d->stringImagePath.c_str();
    return d->stringImagePath.c_str();
}

const char *PatientWrapper::storeCineFullName() const
{
    if (d->patientDataInOldHardDisk())
    {
        d->stringCinePath =
            QString(PatientPath::instance().filePath(*d->patient, OldHardDiskDir) + Resource::cineExt).toStdString();
    }
    else
    {
        d->stringCinePath = QString(PatientPath::instance().filePath(*d->patient) + Resource::cineExt).toStdString();
    }

    qDebug() << "PatientWrapper::storeCineFullName: " << d->stringCinePath.c_str();
    return d->stringCinePath.c_str();
}

const char *PatientWrapper::patientCreateDatetime() const
{
    d->stringCache = d->patient->PatientCreateTime().toString(Qt::ISODate).toStdString();
    return d->stringCache.c_str();
}

void PatientWrapper::setPatientCreateDatetime(const char *time)
{
    d->patient->setPatientCreateTime(QDateTime::fromString(time, Qt::ISODate));
}

const char *PatientWrapper::patientCreateDate() const
{
    d->stringCache = d->patient->PatientCreateTime().date().toString(Qt::ISODate).toStdString();
    return d->stringCache.c_str();
}

const char *PatientWrapper::measureResultPath() const
{
    if (d->patientDataInOldHardDisk())
    {
        d->stringMeasPath = PatientPath::instance().measFilePath(*(d->patient), OldHardDiskDir).toStdString();
    }
    else
    {
        d->stringMeasPath = PatientPath::instance().measFilePath(*(d->patient)).toStdString();
    }

    qDebug() << "PatientWrapper::measureResultPath: " << d->stringMeasPath.c_str();

    return d->stringMeasPath.c_str();
}

StudyWrapper *PatientWrapper::at(int index) const
{
    if (index < 0)
    {
        Q_ASSERT(false);
        return NULL;
    }

    if ((int)d->studyWrappers.size() != d->patient->Studies().count())
    {
        d->clearStudys();
        foreach (Study *s, d->patient->Studies())
        {
            d->studyWrappers.push_back(new StudyWrapper(s));
        }
    }

    if (index < (int)d->studyWrappers.size())
    {
        return d->studyWrappers.at(index);
    }
    return NULL;
}

Patient *PatientWrapper::innerPatient() const
{
    return d->patient;
}

PatientWrapper::PatientWrapper(const PatientWrapper &other)
    : d(nullptr)
{
    Q_ASSERT(false);
}
