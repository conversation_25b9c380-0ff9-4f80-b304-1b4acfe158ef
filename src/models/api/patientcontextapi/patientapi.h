#ifndef PATIENTAPI_H
#define PATIENTAPI_H

#include "patientapi_global.h"
#include "patientwrapper.h"

class PatientApiData;
class IAdminConfigModel;
class PATIENT_API PatientApi
{
public:
    static PatientApi& getInstance();
    PatientApi(const PatientApi& api) = delete;
    PatientApi& operator=(const PatientApi& api) = delete;
    ~PatientApi();

    void initialize();

    PatientWrapper* createPatient();

    PatientWrapper* createPatient(const char* patientId);

    void releasePatientWrapper(PatientWrapper* wrapper);

    /**
     * @brief save Patient Object info into database.
     */
    void savePatient(PatientWrapper& p);
    /**
     * @brief save Study Object info into database.
     */
    void saveStudy(StudyWrapper& p);
    /**
     * @brief update Patient Object info from database.
     */
    void updatePatient(PatientWrapper& p);
    /**
     * @brief update Study Object info from database.
     */
    void updateStudy(StudyWrapper& p);
    /**
     * @brief remove Patient Object and deleted, if changeDB is true, database will also remove this patient info.
     */
    void removePatient(PatientWrapper& p, bool changeDB = true);
    /**
     * @brief Remove all Patient Object, if changeDB is true, database will also remove all patient info.
     */
    void removeAllPatient(bool changeDB = true);
    /**
     * this function will read all the patients records to the internal buffer
     */
    int queryPatients() const;
    /**
     * get one PatientWrapper from the internal buffer
     */
    PatientWrapper* operator[](int index) const;
    PatientWrapper* at(int index) const;

    int speciesCount() const;
    const char* species(int index) const;

    int displaySexCount(bool isHuman) const;
    const char* displaySex(int index, bool isHuman);

    /**
     * @brief all patient info store path
     */
    const char* databasePath() const;

    int getUserCount();
    const char* getUserNameAt(int index);
    const char* getUserIdAt(int index);
    void resetPassword(const char* name);
    int changePassword(const char* pwd, const char* newPwd);
    int addUser(const char* username, const char* pwd, const char* quthorityName);
    int removeUser(const char* username);
    int login(const char* username, const char* pwd);
    int logout();
    const char* getUserID(const char* username);

private:
    PatientApi();
    //    PatientApi(const PatientApi& api) = delete;
    //    PatientApi &operator=(const PatientApi &api) = delete;

    PatientApiData* d;
    IAdminConfigModel* m_AdminConfigModel;
};

#endif // PATIENTAPI_H
