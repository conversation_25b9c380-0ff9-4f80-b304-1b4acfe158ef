add_definitions(-DPATIENTAPI_LIBRARY)

project(patientapi)

include_depends(uscontextapi measurementapi)

add_library_qt(${PROJECT_NAME}
    patientapi_global.h
    patientapi.h
    patientapi.cpp
    patientwrapper.h
    patientwrapper.cpp
    studywrapper.h
    studywrapper.cpp
)

target_link_libraries(${PROJECT_NAME} uscontextapi measurementapi)

install(DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    DESTINATION bin
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    DESTINATION lib
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION include
    FILES_MATCHING PATTERN "*.h")
