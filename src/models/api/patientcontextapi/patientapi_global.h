#ifndef PATIENTAPI_GLOBAL_H
#define PATIENTAPI_GLOBAL_H

#if !defined(SAG_COM) && (defined(WIN64) || defined(_WIN64) || defined(__WIN64__))
#  define C_OS_WIN32
#  define C_OS_WIN64
#elif !defined(SAG_COM) && (defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__))
#  if defined(WINCE) || defined(_WIN32_WCE)
#    define C_OS_WINCE
#  else
#    define C_OS_WIN32
#  endif
#elif defined(__linux__) || defined(__linux)
#  define C_OS_LINUX
#elif defined(__APPLE__)
#  define C_OS_APPLE
#else
#  error "API has not been ported to this OS"
#endif

#if defined(C_OS_WIN32) || defined(C_OS_WIN64) || defined(C_OS_WINCE)
#  define C_OS_WIN
#endif

#ifndef C_DECL_EXPORT
#  if defined(C_OS_WIN)
#    define C_DECL_EXPORT __declspec(dllexport)
#  endif
#  ifndef C_DECL_EXPORT
#    define C_DECL_EXPORT __attribute__((visibility("default")))
#  endif
#endif
#ifndef C_DECL_IMPORT
#  if defined(C_OS_WIN)
#    define C_DECL_IMPORT __declspec(dllimport)
#  else
#    define C_DECL_IMPORT __attribute__((visibility("default")))
#  endif
#endif

#if defined(PATIENTAPI_LIBRARY)
    #define PATIENT_API C_DECL_EXPORT
#else
    #define PATIENT_API C_DECL_IMPORT
#endif

#endif // PATIENTAPI_GLOBAL_H
