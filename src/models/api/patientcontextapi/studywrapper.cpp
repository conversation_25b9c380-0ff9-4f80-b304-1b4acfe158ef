#include "studywrapper.h"
#include "dataaccesslayerhelper.h"
#include "study.h"

struct StudyWrapperPrivate
{
    ~StudyWrapperPrivate()
    {
        delete study;
        study = nullptr;
    }
    std::string proxyAttr;
    Study *study{nullptr};
};

StudyWrapper::StudyWrapper(Study *s)
    : d(new StudyWrapperPrivate())
{
    d->study = s;
}

StudyWrapper::~StudyWrapper()
{
    delete d;
}

void StudyWrapper::save()
{
    DataAccessLayerHelper::saveOrUpdate(*d->study);
}

void StudyWrapper::update()
{
    DataAccessLayerHelper::update(*d->study);
}

void StudyWrapper::remove()
{
    DataAccessLayerHelper::del(*d->study);
}

const char *StudyWrapper::studyInstanceUid() const
{
    d->proxyAttr = d->study->StudyInstanceUid().toStdString();
    return d->proxyAttr.c_str();
}

const char *StudyWrapper::accessionnumber() const
{
    d->proxyAttr = d->study->AccessionNumber().toStdString();
    return d->proxyAttr.c_str();
}

const char *StudyWrapper::studyTimeRaw() const
{
    d->proxyAttr = d->study->StudyTimeRaw().toStdString();
    return d->proxyAttr.c_str();
}

const char *StudyWrapper::studyDate() const
{
    d->proxyAttr = d->study->StudyDateRaw().toStdString();
    return d->proxyAttr.c_str();
}

const char *StudyWrapper::studyDateTime() const
{
    d->proxyAttr = d->study->StoreTime().toString(Qt::ISODate).toStdString();
    return d->proxyAttr.c_str();
}

int StudyWrapper::hr() const
{
    return d->study->HR();
}

void StudyWrapper::setHr(int value)
{
    d->study->setHR(value);
}

float StudyWrapper::bsa() const
{
    return d->study->BSA();
}

void StudyWrapper::setBsa(float value)
{
    d->study->setBSA(value);
}

const char *StudyWrapper::sonographer() const
{
    d->proxyAttr = d->study->Sonographer().toStdString();
    return d->proxyAttr.c_str();
}

void StudyWrapper::setSonographer(const char *name)
{
    d->study->setSonographer(name);
}

void StudyWrapper::setAccessionNumber(const char *accessionnumber)
{
    d->study->setAccessionNumber(accessionnumber);
}

void StudyWrapper::setStudyInstanceUid(const char *studyInstanceUid)
{
    d->study->setStudyInstanceUid(studyInstanceUid);
}

const char *StudyWrapper::userid() const
{
    d->proxyAttr = d->study->UserId().toStdString();
    return d->proxyAttr.c_str();
}

void StudyWrapper::setUserid(const char *id)
{
    d->study->setUserId(id);
}

float StudyWrapper::height() const
{
    return d->study->Height();
}

float StudyWrapper::weight() const
{
    return d->study->Weight();
}

void StudyWrapper::setWeight(const float weight)
{
    d->study->setWeight(weight);
}

void StudyWrapper::setHeight(const float height)
{
    d->study->setHeight(height);
}

StudyWrapper::StudyWrapper(const StudyWrapper &other)
    : d(nullptr)
{
    Q_ASSERT(false);
}
