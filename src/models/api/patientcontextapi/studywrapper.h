#ifndef STUDYMODEL_H
#define STUDYMODEL_H

#include "patientapi_global.h"

class Study;
struct StudyWrapperPrivate;

class PATIENT_API StudyWrapper
{
public:
    StudyWrapper(Study* study);
    StudyWrapper(const StudyWrapper& other);
    ~StudyWrapper();

    void save();
    void update();
    void remove();

    void setHeight(const float height);
    void setWeight(const float weight);

    float weight() const;
    float height() const;
    const char* studyInstanceUid() const;
    void setStudyInstanceUid(const char* studyInstanceUid);

    const char* userid() const;
    void setUserid(const char* id);

    const char* studyTimeRaw() const;
    const char* studyDate() const;
    const char* studyDateTime() const;

    int hr() const;
    void setHr(int value);

    float bsa() const;
    void setBsa(float value);

    const char* sonographer() const;
    void setSonographer(const char* name);

    const char* accessionnumber() const;
    void setAccessionNumber(const char* accessionnumber);

private:
    
    StudyWrapperPrivate* d;
};

#endif // STUDYMODEL_H
