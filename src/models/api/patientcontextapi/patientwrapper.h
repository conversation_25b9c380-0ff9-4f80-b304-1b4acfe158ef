#ifndef PATIENTWrapper_H
#define PATIENTWrapper_H

#include "patientapi_global.h"
#include "studywrapper.h"

struct PatientWrapperPrivate;
class Patient;

class PATIENT_API PatientWrapper
{
public:
    explicit PatientWrapper(Patient* p);
    PatientWrapper(const PatientWrapper& other);
    ~PatientWrapper();
    /**
     * @brief save patient info to database
     */
    void save();
    /**
     * @brief update patient info from database
     */
    void update();
    /**
     * @brief remove patient in database
     */
    void remove();

    void setFirstName(const char* fn);
    void setMiddleName(const char* mn);
    void setLastName(const char* ln);
    void setPatientsAge(int age);
    void setPatientsBirthDate(const char* date);

    const char* patientId() const;
    void setPatientId(const char* id);

    const char* firstName() const;
    const char* middleName() const;
    const char* lastName() const;
    int patientsAge() const;
    const char* patientsBirthDate() const;
    const char* patientName() const;

    const char* patientSex() const;
    void setPatientSex(const char* sex);

    int displayPatientSexIndex() const;
    void setDisplayPatientSexIndex(int index);

    const char* displayPatientSex() const;

    const char* patientMRN() const;
    void setPatientMRN(const char* mrn);

    const char* species() const;
    void setSpecies(const char* value);

    /**
     * @brief get patient file name
     */
    const char* fileName() const;
    /**
     * @brief get patient relative data store path, such as store image, store cine, report path, etc
     */
    const char* patientDataPath() const;
    /**
     * @brief get patient relative data store file path, but has no subfix!!!!
     */
    const char* patientDataFilePath() const;
    /**
     * @brief return store Image Full Name
     */
    const char* storeImageFullName() const;
    /**
     * @brief store Cine Full Name
     */
    const char* storeCineFullName() const;
    /**
     * @brief date time when patient created
     */
    const char* patientCreateDatetime() const;
    void setPatientCreateDatetime(const char* time);
    /**
     * @brief date when patient created
     */
    const char* patientCreateDate() const;

    const char* measureResultPath() const;

    StudyWrapper* at(int index) const;

    Patient *innerPatient() const;

private:
    PatientWrapperPrivate* d;
};

#endif // PATIENTWrapper_H
