#ifndef DEVICECONTEXTAPI_H
#define DEVICECONTEXTAPI_H

#include "deviceapidef.h"
#include <QObject>
#include <QString>
#include <QVariant>

class ISystemInfo;
class DeviceContextApi : public QObject
{
    Q_OBJECT
public:
    enum CallbackType
    {
        BatteryConnectState_CT,
        BatteryQuantity_CT,
        BatteryRemainTime_CT,
        BatteryHealthyState_CT,
        DeviceException_CT,
        Count_CT
    };

    explicit DeviceContextApi();

    virtual ~DeviceContextApi();

    DeviceRetCode setCallbackInfo(CallbackType type, void* callback, void* userData);
    /**
     * 电池相关接口
     */
    virtual DeviceRetCode initBatteryModule();

    virtual DeviceRetCode sendBatteryShutDownCmd();

    virtual DeviceRetCode getECVersion(QString& version);

    virtual DeviceRetCode getBatteryHealthState(bool& state, int& value);

    virtual DeviceRetCode getRelativeStateOfCharge(bool& state, int& value);

    virtual DeviceRetCode getAdapterState(bool& state);

    virtual DeviceRetCode standbyBattery();

    virtual DeviceRetCode wakeBattery();

    virtual DeviceRetCode openCheckTestMode();

    virtual DeviceRetCode closeCheckTestMode();

    virtual DeviceRetCode getBatteryRunTimeToEmpty(int* value);

    virtual DeviceRetCode getBatteryAverageTimeToFull(int* value);

    virtual DeviceRetCode getBatteryCycleCount(int* value);

    virtual DeviceRetCode getBatteryCellToVoltage(int index, int* value);

    virtual DeviceRetCode getBatteryTemperature(int* value);

    virtual DeviceRetCode getBatteryVoltage(int* value);

    virtual DeviceRetCode getBatteryCurrent(int* value);

    virtual DeviceRetCode getBatteryRemainingCapacity(int* value);

    virtual DeviceRetCode getBatteryFullChargeCapacity(int* value);

private slots:
    //电池适配器连接状态改变
    void onConnectedStateChanged(bool adapterState, bool batteryState_1, bool batteryState_2);
    //电池相关参数更新
    void onBatteryValueUpdate(QString cmd, int value, int batteryIndex);
    //电池剩余时间更新
    void onBatteryRemainTimeUpdate(int value);
    //按下HOTKEY
    void onHotKeyPressed(const QString& hotKey);
    //设备异常
    void onDeviceException(int code);

protected:
    typedef QPair<void*, void*> CallbackInfo;
    CallbackInfo m_CallbackInfo[CallbackType::Count_CT];
    ISystemInfo* m_SystemInfo;
};

#endif // DEVICECONTEXTAPI_H
