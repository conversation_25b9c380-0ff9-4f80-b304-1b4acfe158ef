#include "devicecontextapi.h"
#include "qapp.h"
#include "logger.h"
#include <QStringList>
#include "systeminfo.h"
#include "batteryinfo.h"
#ifdef USE_BATTERY_COUNT_2
#include "batterydevice_2battery.h"
#else
#include "batterydevice_1battery.h"
#endif
LOG4QT_DECLARE_STATIC_LOGGER(log, DeviceContextApi)
#include "batterytool.h"
#include "modelconfig.h"
#include <Windows.h>

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    initQApp();
    return TRUE;
}
DeviceContextApi::DeviceContextApi()
    : m_SystemInfo(new SystemInfo)
{
    // initQApp();
    memset(m_CallbackInfo, 0, sizeof(m_CallbackInfo));
}

DeviceContextApi::~DeviceContextApi()
{
}

DeviceRetCode DeviceContextApi::setCallbackInfo(DeviceContextApi::CallbackType type, void* callback, void* userData)
{
    if (type < DeviceContextApi::Count_CT)
    {
        CallbackInfo ci;
        ci.first = callback;
        ci.second = userData;
        m_CallbackInfo[type] = ci;
        return DeviceRetCode::DeviceOK;
    }
    return DeviceRetCode::DeviceFailed;
}

DeviceRetCode DeviceContextApi::initBatteryModule()
{
    connect(&m_SystemInfo, SIGNAL(batteryConnectedState(bool, bool, bool)), this,
            SLOT(onConnectedStateChanged(bool, bool, bool)));
    connect(&m_SystemInfo, SIGNAL(batteryValueUpdate(QString, int, int)), this,
            SLOT(onBatteryValueUpdate(QString, int, int)));
    connect(&m_SystemInfo, SIGNAL(batteryRemainTimeInfoUpdated(int)), this, SLOT(onBatteryRemainTimeUpdate(int)));
    connect(&m_SystemInfo, SIGNAL(hotKeyPress(const QString&)), this, SLOT(onHotKeyPressed(const QString&)));
    connect(&m_SystemInfo, SIGNAL(deviceException(int)), this, SLOT(onDeviceException(int)));
    //    log()->info(QString(" *** BatteryDevice is open : %1").arg(BatteryDevice::isOpen()));
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::sendBatteryShutDownCmd()
{
    if (m_SystemInfo->sendBatteryShutdownCmd())
    {
        return DeviceOK;
    }
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getECVersion(QString& version)
{
    //    if (!m_SystemInfo->isBatteryPrepared())
    //    {
    //        return DeviceFailed;
    //    }
    version = m_SystemInfo->getECVersion();
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::getBatteryHealthState(bool& state, int& value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    state = m_SystemInfo->getBatteryState();
    value = m_SystemInfo->getBatteryHealthState();
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::getRelativeStateOfCharge(bool& state, int& value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    state = m_SystemInfo->getBatteryState();
    value = BatteryTool::adjustShowBatteryCharge(m_SystemInfo->getRelativeStateOfCharge());
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::getAdapterState(bool& state)
{
    //    if (!m_SystemInfo->isBatteryPrepared())
    //    {
    //        return DeviceFailed;
    //    }
    state = m_SystemInfo->getAdapterState();
    return DeviceOK;
}

void DeviceContextApi::onConnectedStateChanged(bool adapterState, bool batteryState_1, bool batteryState_2)
{
    CallbackInfo ci = m_CallbackInfo[BatteryConnectState_CT];
    if (ci.first != NULL)
    {
#ifdef USE_BATTERY_COUNT_2
#else
        Q_UNUSED(batteryState_2)
        ((DeviceBatteryConnectStateCallback)ci.first)(this, adapterState, batteryState_1, ci.second);
#endif
    }
    else
    {
        qDebug() << "onConnectedStateChanged callback is NULL";
    }
}

void DeviceContextApi::onBatteryValueUpdate(QString cmd, int value, int batteryIndex)
{
    if (cmd.compare("0d") == 0) //电量
    {
        CallbackInfo ci = m_CallbackInfo[BatteryQuantity_CT];
        if (ci.first != NULL)
        {
            ((DeviceBatteryQuantityCallback)ci.first)(this, BatteryTool::adjustShowBatteryCharge(value), ci.second);
        }
    }
    else if (cmd.compare("4f") == 0) //健康状态
    {
#if 0
        CallbackInfo ci = m_CallbackInfo[UsBatteryHealthyState_CT];
        if (ci.first != NULL)
        {
            ((DeviceBatteryHealthyStateCallback)ci.first)(this, value, ci.second);
        }
#endif
    }
}

void DeviceContextApi::onBatteryRemainTimeUpdate(int value)
{
    //适配器在位，就不回调
    if (!m_SystemInfo->adapterConnected())
    {
        if (value > 1000 || value <= 0)
        {
            // standby time is error
            Log4Qt::Logger::rootLogger()->info("%1 standby time is error %2", PRETTY_FUNCTION, value);
        }
        else
        {
            CallbackInfo ci = m_CallbackInfo[BatteryRemainTime_CT];
            if (ci.first != NULL)
            {
                ((DeviceBatteryRemainTimeCallback)ci.first)(this, value, ci.second);
            }
        }
    }
}

void DeviceContextApi::onHotKeyPressed(const QString& hotKey)
{
#ifdef USE_P9API
    if (!hotKey.compare("F1"))
    {
        // 1234 is generic message ID , 0x83 is F20 scan mode;
        bool result = ::PostMessageA(HWND_BROADCAST, WM_HOTKEY, 1234, 0x83);
        log()->info("%1 catch hotkey F20 and post message ret = %1", result);
    }
#endif
}

void DeviceContextApi::onDeviceException(int code)
{
    log()->info("%1 %2", PRETTY_FUNCTION, code);
    CallbackInfo ci = m_CallbackInfo[DeviceException_CT];
    if (ci.first != NULL)
    {
        ((DeviceExceptionCodeCallback)ci.first)(this, code, ci.second);
    }
}

DeviceRetCode DeviceContextApi::standbyBattery()
{
    m_SystemInfo->standbyBattery();
    Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::wakeBattery()
{
    m_SystemInfo->wakeBattery();
    Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
    return DeviceOK;
}

DeviceRetCode DeviceContextApi::openCheckTestMode()
{
    if (m_SystemInfo->openCheckTestMode())
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::closeCheckTestMode()
{
    if (m_SystemInfo->closeCheckTestMode())
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryRunTimeToEmpty(int* value)
{
    if (m_SystemInfo->getAdapterState())
    {
        return DeviceNotSupportNow;
    }

    *value = m_SystemInfo->getBatteryRunTimeToEmpty();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }

    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryAverageTimeToFull(int* value)
{
    if (!m_SystemInfo->getAdapterState() || !m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }

    if (m_SystemInfo->getRelativeStateOfCharge() >=
        ModelConfig::instance().value(ModelConfig::BatteryChargeThreshold, 95).toInt())
    {
        return DeviceNotSupportNow;
    }

    *value = m_SystemInfo->getAverageTimeToFull();

    int capacity = m_SystemInfo->getRelativeStateOfCharge();

    /**
     *  这个平均充电时间读数换成下面公式来测试一下：
     *  充电时间：t= t1 + t2      ；    所需寄存器： 满电容量：0x10；  电流：0x0a；  实际电量百分比：0x0d
     *  实际电量百分比＜85%，      t1=60min*（85%-电量百分比）*满电容量mAh/电流mA
     *                      t2=60min*（95%-85%）*满电容量mAh * 2 /（电流+800mA）
     *  85%≤实际电量百分比＜95%， t1=0， t2=60min*（95%-电量百分比）*满电容量mAh * 2 /（电流+800mA）
     *  95%≤实际电量百分比，      t1=0， t2=0；
     */
    if (capacity >= 95)
    {
        *value = 0;
        return DeviceOK;
    }

    int fullChargeCapacity = m_SystemInfo->getFullChargeCapacity();

    if (fullChargeCapacity <= 0)
    {
        *value = 0;
        return DeviceOK;
    }
    int current = m_SystemInfo->getCurrent();
    if (current <= 0)
    {
        return DeviceNotSupportNow;
    }

    if (capacity >= 85)
    {
        *value = 60 * (95 - capacity) / 100 * fullChargeCapacity * 2 / (current + 800);
        return DeviceOK;
    }
    else
    {
        int t1 = 60 * (85 - capacity) / 100 * fullChargeCapacity / current;
        int t2 = 60 * (95 - 85) / 100 * fullChargeCapacity * 2 / (current + 800);
        *value = t1 + t2;
        return DeviceOK;
    }

    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryCycleCount(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getCycleCount();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryCellToVoltage(int index, int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    if (index > 4 || index <= 0)
    {
        return DeviceInvalidArgs;
    }

    *value = m_SystemInfo->getCellToVoltage(index);
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryTemperature(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getTemperature();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryVoltage(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getVoltage();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryCurrent(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getCurrent();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceNotSupportNow;
}

DeviceRetCode DeviceContextApi::getBatteryRemainingCapacity(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getRemainingCapacity();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}

DeviceRetCode DeviceContextApi::getBatteryFullChargeCapacity(int* value)
{
    if (!m_SystemInfo->getBatteryState())
    {
        return DeviceNotSupportNow;
    }
    *value = m_SystemInfo->getFullChargeCapacity();
    if (*value != -1)
    {
        Log4Qt::Logger::rootLogger()->info("%1 ok", PRETTY_FUNCTION);
        return DeviceOK;
    }
    Log4Qt::Logger::rootLogger()->info("%1 failed", PRETTY_FUNCTION);
    return DeviceFailed;
}
