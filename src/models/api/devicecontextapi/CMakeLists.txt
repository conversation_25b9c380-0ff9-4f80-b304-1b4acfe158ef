add_definitions(-DDEVICECONTEXTAPI_LIBRARY)

add_library_qt(devicecontextapi
    qapp.cpp
    deviceapi.cpp
    devicecontextapi.cpp
    hwctrlDLL.cpp
    batterytool.cpp
  )

target_link_libraries(devicecontextapi ${ANDROID_EXTRA_LIBS})

if(${USE_P9_HWCTRL})
add_custom_command(TARGET devicecontextapi
        POST_BUILD
        COMMAND cmake -DsourceDirector=${THIRDPARTYLIB_PATH}/shutdownintercept/Hwctrl.dll -DtargetDirector=${CMAKE_LIBRARY_OUTPUT_DIRECTORY} -P ${PROJECT_SOURCE_DIR}/cmake/customcopy.cmake
        )
endif()

install(DIRECTORY ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}
    DESTINATION bin
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
    DESTINATION lib
    FILES_MATCHING PATTERN "*")

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    DESTINATION include
    FILES_MATCHING PATTERN "*.h")
