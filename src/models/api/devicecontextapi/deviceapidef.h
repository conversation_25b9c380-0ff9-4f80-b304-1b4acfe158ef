#ifndef DEVICEAPIDEF_H
#define DEVICEAPIDEF_H

#if !defined(SAG_COM) && (defined(WIN64) || defined(_WIN64) || defined(__WIN64__))
#define C_OS_WIN32
#define C_OS_WIN64
#elif !defined(SAG_COM) && (defined(WIN32) || defined(_WIN32) || defined(__WIN32__) || defined(__NT__))
#if defined(WINCE) || defined(_WIN32_WCE)
#define C_OS_WINCE
#else
#define C_OS_WIN32
#endif
#elif defined(__linux__) || defined(__linux)
#define C_OS_LINUX
#elif defined(__APPLE__)
#define C_OS_APPLE
#include <string.h>
#else
#error "API has not been ported to this OS"
#endif

#if defined(C_OS_WIN32) || defined(C_OS_WIN64) || defined(C_OS_WINCE)
#define C_OS_WIN
#endif

#ifndef C_DECL_EXPORT
#if defined(C_OS_WIN)
#define C_DECL_EXPORT __declspec(dllexport)
#endif
#ifndef C_DECL_EXPORT
#define C_DECL_EXPORT __attribute__((visibility("default")))
#endif
#endif
#ifndef C_DECL_IMPORT
#if defined(C_OS_WIN)
#define C_DECL_IMPORT __declspec(dllimport)
#else
#define C_DECL_IMPORT __attribute__((visibility("default")))
#endif
#endif

#if defined(DEVICECONTEXTAPI_LIBRARY)
#define DEVICE_API C_DECL_EXPORT
#else
#define DEVICE_API C_DECL_IMPORT
#endif

//#ifdef C_OS_WIN
//#define CT_CALL __stdcall
//#else
#define CT_CALL
//#endif

#ifdef __cplusplus
extern "C"
{
#endif

    typedef void* DeviceContext;

    //错误码
    enum DeviceRetCode
    {
        DeviceNotSupportNow = -4,
        DeviceInvalidArgs = -3,
        DeviceInvalidContext = -2,
        DeviceFailed = -1,
        DeviceOK = 0,
    };

    //适配器和电池连接状态信息通知
    /**
     * @param context                   句柄
     * @param adapterState              适配器连接状态
     * @param batteryState              电池连接状态
     * @param erData                  用户数据
     */
    typedef void (*CT_CALL DeviceBatteryConnectStateCallback)(DeviceContext context, const bool adapterState,
                                                              const bool batteryState, void* userData);
    //电池电量信息通知
    /**
     * @param context                   句柄
     * @param quantity                  电池电量
     * @param erData                  用户数据
     */
    typedef void (*CT_CALL DeviceBatteryQuantityCallback)(DeviceContext context, const int quantity, void* userData);

    //电池剩余时间信息通知
    /**
     * @param context                   句柄
     * @param remainTime                电池剩余时间
     * @param erData                  用户数据
     */
    typedef void (*CT_CALL DeviceBatteryRemainTimeCallback)(DeviceContext context, const int remainTime,
                                                            void* userData);

    //电池健康状态信息通知
    /**
     * @param context                   句柄
     * @param healthyState              电池健康状态
     * @param erData                  用户数据
     */
    typedef void (*CT_CALL DeviceBatteryHealthyStateCallback)(DeviceContext context, const int healthyState,
                                                              void* userData);

    //设备异常信息通知
    /**
     * @param context                 句柄
     * @param code                    异常码
     * @param erData                  用户数据
     */
    typedef void (*CT_CALL DeviceExceptionCodeCallback)(DeviceContext context, const int code, void* userData);

#ifdef __cplusplus
}
#endif

#endif // DEVICEAPIDEF_H
