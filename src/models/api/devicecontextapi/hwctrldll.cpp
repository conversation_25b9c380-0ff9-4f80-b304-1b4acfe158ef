#include "hwctrldll.h"

HwctrlDLL::HwctrlDLL()
    : _dllName(HWCTRLDLL_NAME)
    , _isLoad(false)
    , dllInit(nullptr)
{
}

HwctrlDLL::~HwctrlDLL()
{
}

HwctrlDLL &HwctrlDLL::instance()
{
    static HwctrlDLL _hwctrlDLL;
    return _hwctrlDLL;
}

bool HwctrlDLL::load()
{
    _dll.setFileName(_dllName);
    if (!_dll.load())
    {
        return false;
    }
    _isLoad = true;
    return true;
}

bool HwctrlDLL::unLoad()
{
    return _dll.unload();
}
