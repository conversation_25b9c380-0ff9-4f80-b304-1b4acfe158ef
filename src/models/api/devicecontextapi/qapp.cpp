#include <QApplication>
#include <QDateTime>
#include <QTime>
#include <QDebug>
#include <QDir>
#include <QJsonDocument>
#include <QJsonParseError>
#include <mutex>
#include <QTextStream>
#include "resource.h"

static std::once_flag _flag;
static QApplication * m_qApp;

void initQApp()
{
    std::call_once(_flag, []
    {
        int argc = 0;
        if(!QApplication::instance())
            m_qApp = new QApplication(argc, nullptr);
    });
}


