#ifndef DEVICEAPI_H
#define DEVICEAPI_H

#include "deviceapidef.h"
#ifdef __cplusplus
extern "C"
{
#endif
/**
 * @brief DeviceCreate 创建句柄
 * @return 若为NULL,则创建失败
 */
DEVICE_API DeviceContext CT_CALL DeviceCreate();

/**
 * @brief DeviceRelease 销毁句柄
 * @param context
 * @return
 */
DEVICE_API void CT_CALL DeviceRelease(DeviceContext context);

/**
 * @brief DeviceInitBatteryModule 电池模块初始化
 * @param context
 * @return RetCode 0表示成功，-1表示失败
 */
DEVICE_API DeviceRetCode CT_CALL DeviceInitBatteryModule(DeviceContext context);

/**
* @brief DeviceSetBatteryConnectStateCallback 电池适配器连接状态回调函数
* @param context       句柄
* @param callback      电池适配器连接状态回调函数
* @param erData      用户数据
* @return
*/
DEVICE_API DeviceRetCode CT_CALL DeviceSetBatteryConnectStateCallback(DeviceContext context,
                                           DeviceBatteryConnectStateCallback callback,
                                           void* userData);
/**
* @brief DeviceSetBatteryQuantityCallback 电池电量回调函数
* @param context       句柄
* @param callback      电池电量回调函数
* @param erData      用户数据
* @return
*/
DEVICE_API DeviceRetCode CT_CALL DeviceSetBatteryQuantityCallback(DeviceContext context,
                                           DeviceBatteryQuantityCallback callback,
                                           void* userData);

/**
* @brief DeviceSetDeviceExcepitonCodeCallback 设置设备异常信息回调
* @param context       句柄
* @param callback      回调函数
* @param erData      用户数据
* @return
*/
DEVICE_API DeviceRetCode CT_CALL DeviceSetDeviceExceptionCodeCallback(DeviceContext context,
                                           DeviceExceptionCodeCallback callback,
                                           void* userData);

/**
* @brief DeviceSetBatteryRemainTimeCallback 电池剩余时间回调函数
* @param context       句柄
* @param callback      电池剩余时间回调函数
* @param erData      用户数据
* @return
*/
DEVICE_API DeviceRetCode CT_CALL DeviceSetBatteryRemainTimeCallback(DeviceContext context,
                                           DeviceBatteryRemainTimeCallback callback,
                                           void* userData);

/**
* @brief DeviceSetBatteryHealthyStateCallback 电池健康状态回调函数
* @param context       句柄
* @param callback      电池健康状态回调函数
* @param erData      用户数据
* @return
*/
DEVICE_API DeviceRetCode CT_CALL DeviceSetBatteryHealthyStateCallback(DeviceContext context,
                                           DeviceBatteryHealthyStateCallback callback,
                                           void* userData);
/**
 * @brief DeviceSendBatteryShutDownCmd 发送电池shutdown命令
 * @return 正常:0 错误:错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceSendBatteryShutDownCmd(DeviceContext context);

/**
 * @brief DeviceGetECVersion 获取EC版本
 * @param context
 * @param ecVersion
 * @param count
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetECVersion(DeviceContext context, char* ecVersion, const int count);

/**
 * @brief DeviceGetBatteryHealthState 获取电池健康状态
 * @param context
 * @param state 电池连接状态
 * @param value 电池健康度，单位百分比
 * @return RetCode 0表示成功，-1表示失败
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryHealthState(DeviceContext context, bool* state, int* value);

/**
 * @brief DeviceGetBatteryCharge 获取电池电量
 * @param context
 * @param state 电池连接状态
 * @param value 电池电量，单位百分比
 * @return RetCode 0表示成功，-1表示失败
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryCharge(DeviceContext context, bool* state, int* value);

/**
 * @brief DeviceGetAdapterState 获取电源适配器状态
 * @param context
 * @param state
 * @return RetCode 0表示成功，-1表示失败
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetAdapterState(DeviceContext context, bool* state);

/**
 * @brief DeviceLoadShutDownRemind 关机提醒设置
 * @param context
 * @param open 是否打开
 * @return RetCode 0表示成功，-1表示失败
 */
DEVICE_API DeviceRetCode CT_CALL DeviceLoadShutDownRemind(DeviceContext context);
/**
 * @brief DeviceStandbyBattery   设备待机
 * @param context           句柄
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceStandbyBattery(DeviceContext context);
/**
 * @brief DeviceWakeBattery      设备唤醒
 * @param context           句柄
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceWakeBattery(DeviceContext context);
/**
 * @brief DeviceOpenCheckMode     打开设备模拟检测模式
 * @param context           句柄
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceOpenCheckTestMode(DeviceContext context);
/**
 * @brief DeviceCloseCheckMode    关闭设备模拟检测模式
 * @param context           句柄
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceCloseCheckTestMode(DeviceContext context);
/**
 * @brief DeviceGetBatteryRunTimeToEmpty    获取电池剩余时间
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryRunTimeToEmpty(DeviceContext context, int *value);

/**
 * @brief DeviceAverageTimeToFull   获取电池充电时间
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryAverageTimeToFull(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryCycleCount      获取电池充放电循环次数
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryCycleCount(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryCellToVoltage     获取电芯电压
 * @param context           句柄
 * @param index             几号电芯
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryCellToVoltage(DeviceContext context, int index, int *value);
/**
 * @brief DeviceGetBatteryTemperature      获取电池温度
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryTemperature(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryVoltage      获取电池电压
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryVoltage(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryCurrent      获取电池电流
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryCurrent(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryRemainingCapacity      获取电池剩余容量
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryRemainingCapacity(DeviceContext context, int *value);
/**
 * @brief DeviceGetBatteryFullChargeCapacity      获取电池满电容量
 * @param context           句柄
 * @param value             值
 * @return 成功:0  失败: 错误码
 */
DEVICE_API DeviceRetCode CT_CALL DeviceGetBatteryFullChargeCapacity(DeviceContext context, int *value);
#ifdef __cplusplus
}
#endif

#endif // DEVICEAPI_H
