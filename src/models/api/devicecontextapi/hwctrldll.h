#ifndef HWCTRLDLL_H
#define HWCTRLDLL_H
#include <QString>
#include <QLibrary>

#define HWCTRLDLL_NAME "hwctrl.dll"

typedef bool (*HwctrlInit)();

class HwctrlDLL
{
private:
    explicit HwctrlDLL();
    virtual ~HwctrlDLL();

public:
    static HwctrlDLL& instance();

    bool load();

    bool unLoad();

    HwctrlInit dllInit;

private:
    QString _dllName;
    QLibrary _dll;
    bool _isLoad;
};

#endif // HWCTRLDLL_H
