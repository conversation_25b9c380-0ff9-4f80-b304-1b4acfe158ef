#include "deviceapi.h"
#include "devicecontextapi.h"
#include "hwctrldll.h"
#define TRACE
#ifdef TRACE
    #define API_TRACE(...) API_TRACE_LOG(__func__,__VA_ARGS__);
#else
     #define API_TRACE(...) 
#endif
#define T_S(x) to_string(x)
#ifdef __cplusplus
extern "C"
{
#endif

 DeviceContext DeviceCreate()
 {
     DeviceContextApi *api = new DeviceContextApi();
     return api;
 }

void DeviceRelease(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api != NULL)
    {
        delete api;
        context = NULL;
    }
}

DeviceRetCode DeviceInitBatteryModule(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->initBatteryModule();
}

DeviceRetCode DeviceSendBatteryShutDownCmd(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->sendBatteryShutDownCmd();
}

DeviceRetCode DeviceGetECVersion(DeviceContext context, char* ecVersion, const int count)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    QString version;
    if(api->getECVersion(version) == DeviceOK)
    {
        memset(ecVersion, 0, count);
        int retValue = (int)strlen(version.toStdString().c_str())+1;
        if(count >= retValue)
        {
            memcpy(ecVersion, version.toStdString().c_str(), retValue);
            return DeviceRetCode::DeviceOK;
        }
    }
    return DeviceFailed;
}

DeviceRetCode DeviceSetBatteryConnectStateCallback(DeviceContext context, DeviceBatteryConnectStateCallback callback, void* userData)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->setCallbackInfo(DeviceContextApi::BatteryConnectState_CT, (void*)callback, userData);
}

DeviceRetCode DeviceSetBatteryQuantityCallback(DeviceContext context, DeviceBatteryQuantityCallback callback, void* userData)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->setCallbackInfo(DeviceContextApi::BatteryQuantity_CT, (void*)callback, userData);
}

DeviceRetCode DeviceSetBatteryRemainTimeCallback(DeviceContext context, DeviceBatteryRemainTimeCallback callback, void* userData)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->setCallbackInfo(DeviceContextApi::BatteryRemainTime_CT, (void*)callback, userData);
}

DeviceRetCode DeviceSetBatteryHealthyStateCallback(DeviceContext context, DeviceBatteryHealthyStateCallback callback, void* userData)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->setCallbackInfo(DeviceContextApi::BatteryHealthyState_CT, (void*)callback, userData);
}

DeviceRetCode DeviceSetDeviceExceptionCodeCallback(DeviceContext context, DeviceExceptionCodeCallback callback, void *userData)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->setCallbackInfo(DeviceContextApi::DeviceException_CT, (void*)callback, userData);
}

DeviceRetCode DeviceGetBatteryHealthState(DeviceContext context, bool* state, int* value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->getBatteryHealthState(*state, *value);
}

DeviceRetCode DeviceGetBatteryCharge(DeviceContext context, bool* state, int* value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->getRelativeStateOfCharge(*state, *value);
}

DeviceRetCode DeviceGetAdapterState(DeviceContext context, bool* state)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceRetCode::DeviceInvalidContext;
    }
    return api->getAdapterState(*state);
}

DeviceRetCode DeviceLoadShutDownRemind(DeviceContext context)
{
    return HwctrlDLL::instance().load() ? DeviceRetCode::DeviceOK : DeviceRetCode::DeviceFailed;
}

DeviceRetCode DeviceStandbyBattery(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->standbyBattery();
}

DeviceRetCode DeviceWakeBattery(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->wakeBattery();
}

DeviceRetCode DeviceOpenCheckTestMode(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->openCheckTestMode();
}

DeviceRetCode DeviceCloseCheckTestMode(DeviceContext context)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->closeCheckTestMode();
}

DeviceRetCode DeviceGetBatteryRunTimeToEmpty(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryRunTimeToEmpty(value);
}

DeviceRetCode DeviceGetBatteryAverageTimeToFull(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryAverageTimeToFull(value);
}

DeviceRetCode DeviceGetBatteryCycleCount(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryCycleCount(value);
}

DeviceRetCode DeviceGetBatteryCellToVoltage(DeviceContext context, int index, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryCellToVoltage(index, value);
}



DeviceRetCode DeviceGetBatteryTemperature(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryTemperature(value);
}

DeviceRetCode DeviceGetBatteryVoltage(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryVoltage(value);
}

DeviceRetCode DeviceGetBatteryCurrent(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryCurrent(value);
}

DeviceRetCode DeviceGetBatteryRemainingCapacity(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryRemainingCapacity(value);
}

DeviceRetCode DeviceGetBatteryFullChargeCapacity(DeviceContext context, int *value)
{
    DeviceContextApi *api = reinterpret_cast<DeviceContextApi*>(context);
    if(api == NULL)
    {
        return DeviceInvalidContext;
    }
    return api->getBatteryFullChargeCapacity(value);
}

#ifdef __cplusplus
}
#endif
