# 4D数据链路设计文档

## 1. 系统概述

本文档描述了4D超声成像系统的数据链路设计，从FPGA数据采集到最终4D重建的完整流程。

## 2. 数据流程图

```mermaid
graph TD
    A[FPGA数据采集] -->|原始线数据| B[数据有效性校验]
    B -->|有效帧| C[线数据缓存]
    C -->|帧数据| D[Zeus处理]
    D -->|iImage回调| E[Pangu图像优化]
    E -->|优化后数据| F[体数据缓存]
    F -->|Volume数据| G[中心帧识别]
    G -->|完整Volume| H[4D重建成像]
```

## 3. 详细设计

### 3.1 数据采集层

```mermaid
sequenceDiagram
    participant FPGA
    participant 数据校验
    participant 缓存
    
    FPGA->>数据校验: 发送原始线数据
    数据校验->>数据校验: 去除16字节头数据
    数据校验->>数据校验: 提取前8 Byte 4D信息
    数据校验->>数据校验: 执行有效性校验
    alt 有效帧
        数据校验->>缓存: 存储有效帧
    else 无效帧
        数据校验->>数据校验: 丢弃
    end
```

#### 3.1.1 数据格式
- 原始数据：FPGA采集的线数据
- 头部信息：16字节
- 4D信息：前8bit
- 线数据：剩余部分

#### 3.1.2 有效性校验
- 数据完整性检查
- 帧同步检查
- 数据范围验证

### 3.2 数据处理层

```mermaid
graph LR
    A[缓存读取] --> B[Zeus处理]
    B --> C[iImage回调]
    C --> D[Pangu优化]
    D --> E[数据计算与检查]
    E --> F[体数据缓存]
```

#### 3.2.1 Zeus处理
- 从缓存读取帧数据
- 通过iImage节点回调处理
- 数据格式转换

#### 3.2.2 Pangu优化
- 图像质量优化
- 数据格式标准化
- 性能优化

#### 3.2.3 数据计算与检查
- 方向信息计算
- 帧中心标记识别
- 数据有效性验证
- 帧完整性检查
- 数据范围验证

### 3.3 体数据管理

```mermaid
graph TD
    A[体数据缓存] --> B[Volume组织]
    B --> C[Volume完整性检查]
    C -->|不完整| D[继续收集]
    C -->|完整| E[4D重建]
```

#### 3.3.1 体数据缓存
- 基于预处理后的帧数据组织
- 动态缓存管理
- 内存优化

#### 3.3.2 Volume组织
- 基于预处理的方向信息组织帧
- 中心帧位置确认
- Volume索引管理