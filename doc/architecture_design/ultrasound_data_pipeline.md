# 超声数据链路流程

本文档描述了超声数据从采集到显示的完整处理链路。

## 数据流程概述

超声数据经过以下几个主要阶段处理：
1. 数据采集 - 通过外设接口获取原始数据
2. 前处理 - 将原始数据转换为线数据
3. 缓存管理 - 存储线数据用于回放和处理
4. 后处理 - 将线数据转换为图像数据
5. 图像渲染 - 将处理后的图像显示在屏幕上

## 详细数据流程图

```mermaid
flowchart TD
    subgraph 数据采集
        A[FPGA设备] -->|PCIe/USB/WiFi| B[BFIODevice模块]
    end
    
    subgraph 前处理
        B -->|原始数据| C{需要前处理?}
        C -->|是 - IQ/BF数据| D[Zeus前处理]
        D -->|线数据| E[LineImageDataReceiver]
        C -->|否 - B/4D等线数据| E
    end
    
    subgraph 缓存管理
        E -->|线数据| F[SonoBuffer缓存]
        F -->|线数据| G[SonoBuffers管理器]
    end
    
    subgraph 后处理
        G -->|缓存数据| H[ZeusContext后处理]
    end
    
    subgraph 图像渲染
        H -->|RGBA数据| I[ImagePainterRender渲染器]
        I -->|显示| J[屏幕显示]
    end

    style 数据采集 fill:#f9f,stroke:#333,stroke-width:2px
    style 前处理 fill:#bbf,stroke:#333,stroke-width:2px
    style 缓存管理 fill:#bfb,stroke:#333,stroke-width:2px
    style 后处理 fill:#fbb,stroke:#333,stroke-width:2px
    style 图像渲染 fill:#ffb,stroke:#333,stroke-width:2px
```

## 数据链路关键组件

### 1. 外设数据采集模块 (BFIODevice)

**模块路径**：`src/usf/interface/peripheral/bfiodevice/`

**功能描述**：
- 负责与硬件设备通信，通过PCIe、USB或WiFi接口获取原始数据
- 处理数据传输协议和硬件交互
- 将原始数据传递给后续处理模块

**主要特点**：
- 支持多种硬件接口
- 处理数据传输的底层细节
- 负责数据包的接收和解析

### 2. 线数据接收模块 (LineImageDataReceiver)

**模块路径**：`src/usf/interface/imaging/usapi/lineimagedatareceiver.cpp`

**功能描述**：
- 接收原始数据并根据需要进行前处理
- 对于IQ或BF数据，通过Zeus前处理转换为线数据
- 对于已经是线数据格式的数据(如B模式、4D)，直接传递

**主要特点**：
- 支持不同类型数据的处理逻辑
- 处理不同机型可能有不同的重载实现
- 未来计划重构为独立的前处理模块

### 3. 缓存管理模块 (SonoBuffer/SonoBuffers)

**模块路径**：
- SonoBuffer: `src/usf/interface/imaging/buffer/sonobuffer.cpp`
- SonoBuffers: `src/usf/interface/imaging/buffer/sonobuffers.cpp`

**功能描述**：
- SonoBuffer：负责单个缓冲区的管理
- SonoBuffers：管理多个SonoBuffer实例，支持多布局显示
- 存储线数据用于回放和冻结功能

**主要特点**：
- 通常有四个布局，对应四个SonoBuffer实例
- 支持数据冻结和回放功能
- 管理不同类型的图像数据(B模式、彩色多普勒等)

### 4. 后处理模块 (ZeusContext)

**模块路径**：`src/usf/interface/imaging/adapter/zeuscontext.cpp`

**功能描述**：
- 从缓存中获取线数据
- 使用Zeus后处理引擎将线数据转换为图像数据
- 输出32位RGBA格式的图像数据

**主要特点**：
- 处理不同成像模式的后处理逻辑
- 可能包含多种图像增强算法
- 处理彩色映射和其他后处理效果

### 5. 图像渲染模块 (ImagePainterRender)

**模块路径**：`src/usf/interface/imaging/render/ui/imagepainterrender.cpp`

**功能描述**：
- 接收RGBA格式的图像数据
- 根据布局安排将图像绘制到屏幕上的正确位置
- 处理缩放、平移等显示操作

**主要特点**：
- 支持多种布局模式的渲染
- 处理图像的变换和合成
- 支持实时渲染和交互功能

## 优化方向

当前系统的数据链路已经能够满足基本需求，但仍有以下优化方向：

1. 前处理模块独立化 - 将LineImageDataReceiver中的前处理逻辑抽象为独立模块
2. 多线程优化 - 提高数据处理的并行能力
3. 内存管理优化 - 减少数据复制和内存占用
4. 接口标准化 - 统一不同模块间的数据交互接口

## 结论

超声数据链路是一个复杂的数据处理流程，涉及多个专业模块的协同工作。理解这一流程对于系统开发、维护和优化都具有重要意义。 