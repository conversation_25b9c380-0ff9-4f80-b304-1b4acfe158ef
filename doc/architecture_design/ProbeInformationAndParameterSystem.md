# 探头信息管理与参数下发系统设计

## 1. 探头信息包数据结构

### 1.1 信息包定义

```cpp
// 探头信息包结构
struct ProbeInfoPacket {
    // 基本标识信息
    uint32_t packetHeader;        // 包头标识，固定值 0xAA55BB66
    uint16_t packetVersion;       // 信息包版本号
    uint16_t packetLength;        // 信息包总长度
    
    // 探头基本信息
    uint8_t slotPosition;         // 探头插槽位置 (1-8)
    uint32_t probeId;             // 探头唯一ID
    char probeModel[32];          // 探头型号
    char serialNumber[32];        // 探头序列号
    
    // 硬件信息
    uint16_t fpgaVersionMajor;    // FPGA主版本号
    uint16_t fpgaVersionMinor;    // FPGA次版本号
    uint32_t firmwareVersion;     // 固件版本号
    uint8_t hardwareRevision;     // 硬件版本
    
    // 探头能力信息
    uint8_t supportedFrequencies[8]; // 支持的频率列表
    uint8_t supportedModes;       // 支持的成像模式，按位表示
    uint16_t maxFrameRate;        // 最大帧率
    uint16_t elements;            // 探头元件数量
    
    // 校验与状态
    uint8_t probeStatus;          // 探头状态码
    uint8_t temperatureStatus;    // 温度状态
    uint32_t usageCounter;        // 使用计数器
    uint32_t checksum;            // 校验和
};
```

### 1.2 临床应用与预设映射结构

```cpp
// 临床应用结构
struct ClinicalApplication {
    uint32_t appId;               // 应用唯一ID
    char appName[64];             // 应用名称
    char appDescription[256];     // 应用描述
    uint8_t appCategory;          // 应用类别
    
    // 支持的预设列表
    uint32_t presetIds[16];       // 该应用支持的预设ID列表
    uint8_t presetCount;          // 预设数量
};

// 预设详细信息结构
struct PresetInfo {
    uint32_t presetId;            // 预设唯一ID
    char presetName[64];          // 预设名称
    char presetDescription[256];  // 预设描述
    bool isDefault;               // 是否为默认预设
    bool isCustomizable;          // 是否允许用户自定义
};
```

## 2. 信息包解析模块设计

### 2.1 信息包解析类

```cpp
// 探头信息解析器
class ProbeInfoParser {
private:
    std::map<uint32_t, ProbeInfoPacket> activeProbes;        // 当前活跃的探头信息
    std::map<uint32_t, std::vector<ClinicalApplication>> probeApplications;  // 探头支持的临床应用
    std::map<uint32_t, std::map<uint32_t, PresetInfo>> appPresets;  // 临床应用的预设

public:
    // 解析探头信息包
    bool parseProbeInfoPacket(const uint8_t* data, size_t length);
    
    // 获取活跃的探头列表
    std::vector<ProbeInfoPacket> getActiveProbes() const;
    
    // 获取探头支持的临床应用
    std::vector<ClinicalApplication> getProbeApplications(uint32_t probeId) const;
    
    // 获取应用的预设列表
    std::vector<PresetInfo> getApplicationPresets(uint32_t appId) const;
    
    // 获取特定预设的详细信息
    PresetInfo getPresetInfo(uint32_t appId, uint32_t presetId) const;
    
    // 从数据库加载探头相关的应用和预设
    bool loadProbeApplicationsAndPresets(uint32_t probeId);
    
    // 更新探头状态
    void updateProbeStatus(uint32_t probeId, uint8_t newStatus);
    
    // 注册探头连接/断开回调
    using ProbeCallback = std::function<void(uint32_t probeId, bool isConnected)>;
    void registerProbeCallback(ProbeCallback callback);
};
```

### 2.2 信息包处理流程

```cpp
// 在设备层接收到探头信息包后的处理
void DeviceLayer::handleProbeInfoPacket(const uint8_t* data, size_t length) {
    // 校验信息包有效性
    if (!validatePacket(data, length)) {
        logError("Invalid probe info packet received");
        return;
    }
    
    // 解析信息包
    if (probeInfoParser.parseProbeInfoPacket(data, length)) {
        // 获取探头ID
        uint32_t probeId = extractProbeId(data);
        
        // 加载该探头支持的临床应用和预设
        probeInfoParser.loadProbeApplicationsAndPresets(probeId);
        
        // 通知探头管理模块更新界面
        notifyProbeManagerUpdate();
    }
}
```

## 3. 探头选择与预设界面设计

### 3.1 探头选择界面

```
+-----------------------------------------------------------------------+
|                          探头选择                                      |
+-----------------------------------------------------------------------+
| 已检测到的探头:                                           [刷新]      |
|                                                                       |
| +-------------------------------------------------------------------+ |
| | 插槽 | 探头型号      | 序列号     | FPGA版本 | 状态    | 选择     | |
| |------|--------------|------------|----------|---------|----------| |
| | 1    | C5-2 Convex  | SN20230001 | v2.3.1   | 正常    | [选择]   | |
| | 3    | L12-5 Linear | SN20230002 | v2.3.2   | 正常    | [选择]   | |
| | 4    | P4-2 Phased  | SN20230003 | v2.3.0   | 警告    | [选择]   | |
| +-------------------------------------------------------------------+ |
|                                                                       |
| 探头详情:                                                             |
| 型号: C5-2 Convex                                                     |
| 序列号: SN20230001                                                    |
| FPGA版本: v2.3.1                                                      |
| 固件版本: v4.1.2                                                      |
| 支持频率: 2.0-5.0 MHz                                                 |
| 支持模式: B模式, 彩色多普勒, 脉冲多普勒                               |
| 使用次数: 1205                                                        |
|                                                                       |
| [选择此探头]                             [探头诊断] [固件升级]        |
+-----------------------------------------------------------------------+
```

### 3.2 临床应用选择界面

```
+-----------------------------------------------------------------------+
|                    临床应用选择 - C5-2 Convex                         |
+-----------------------------------------------------------------------+
| 请选择临床应用:                                                       |
|                                                                       |
| +-------------------------------------------------------------------+ |
| | 类别       | 临床应用         | 描述                     | 选择   | |
| |------------|------------------|--------------------------|--------| |
| | 腹部       | 腹部常规检查     | 肝、胆、脾、胰等检查     | [选择] | |
| | 腹部       | 肝脏增强成像     | 肝脏病变对比增强检查     | [选择] | |
| | 产科       | 早期妊娠         | 妊娠12周内检查           | [选择] | |
| | 产科       | 中晚期妊娠       | 妊娠12周后检查           | [选择] | |
| | 泌尿       | 肾脏检查         | 肾脏及周围组织检查       | [选择] | |
| +-------------------------------------------------------------------+ |
|                                                                       |
| 应用详情:                                                             |
| 名称: 腹部常规检查                                                    |
| 描述: 用于肝、胆、脾、胰等腹部器官的常规超声检查                      |
| 推荐预设: 腹部标准、浅层增强、深层增强                                |
|                                                                       |
| [选择此应用]                            [返回探头选择]               |
+-----------------------------------------------------------------------+
```

### 3.3 预设选择界面

```
+-----------------------------------------------------------------------+
|              预设选择 - C5-2 Convex - 腹部常规检查                    |
+-----------------------------------------------------------------------+
| 请选择预设:                                                           |
|                                                                       |
| +-------------------------------------------------------------------+ |
| | 预设名称         | 描述                           | 类型     | 选择 | |
| |------------------|--------------------------------|----------|------| |
| | 腹部标准         | 标准腹部检查参数               | 系统     | [●] | |
| | 浅层增强         | 优化浅表结构显示               | 系统     | [ ] | |
| | 深层增强         | 优化深部结构显示               | 系统     | [ ] | |
| | 对比度增强       | 增强组织间对比度               | 系统     | [ ] | |
| | 我的腹部设置     | 用户自定义设置                 | 自定义   | [ ] | |
| +-------------------------------------------------------------------+ |
|                                                                       |
| 预设参数预览:                                                         |
| 增益: 65%                                                             |
| 深度: 15cm                                                            |
| 频率: 3.5MHz                                                          |
| 动态范围: 65dB                                                        |
| 聚焦位置: 8cm                                                         |
| 谐波成像: 开启                                                        |
| 持久性: 中                                                            |
|                                                                       |
| [应用此预设]       [预览效果]       [复制并自定义]       [返回]       |
+-----------------------------------------------------------------------+
```

## 4. 探头-应用-预设关联管理

### 4.1 关联数据存储结构

```cpp
// 数据库表结构示例
// 1. 探头表
CREATE TABLE Probes (
    probe_id INTEGER PRIMARY KEY,
    model VARCHAR(32),
    serial_number VARCHAR(32),
    fpga_version VARCHAR(16),
    firmware_version VARCHAR(16),
    hardware_revision INTEGER,
    max_frame_rate INTEGER,
    elements INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

// 2. 临床应用表
CREATE TABLE ClinicalApplications (
    app_id INTEGER PRIMARY KEY,
    app_name VARCHAR(64),
    app_description TEXT,
    app_category INTEGER,
    icon_path VARCHAR(256),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

// 3. 预设表
CREATE TABLE Presets (
    preset_id INTEGER PRIMARY KEY,
    preset_name VARCHAR(64),
    preset_description TEXT,
    is_default BOOLEAN,
    is_customizable BOOLEAN,
    created_by VARCHAR(64),
    parameter_json TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

// 4. 探头支持的应用表（关联表）
CREATE TABLE ProbeApplications (
    id INTEGER PRIMARY KEY,
    probe_id INTEGER,
    app_id INTEGER,
    is_recommended BOOLEAN,
    FOREIGN KEY (probe_id) REFERENCES Probes(probe_id),
    FOREIGN KEY (app_id) REFERENCES ClinicalApplications(app_id)
);

// 5. 应用支持的预设表（关联表）
CREATE TABLE ApplicationPresets (
    id INTEGER PRIMARY KEY,
    app_id INTEGER,
    preset_id INTEGER,
    is_default BOOLEAN,
    display_order INTEGER,
    FOREIGN KEY (app_id) REFERENCES ClinicalApplications(app_id),
    FOREIGN KEY (preset_id) REFERENCES Presets(preset_id)
);
```

### 4.2 探头应用映射管理器

```cpp
// 探头应用映射管理器
class ProbeApplicationMappingManager {
private:
    Database& db;
    std::map<uint32_t, std::vector<uint32_t>> probeAppMap;  // 探头ID -> 应用ID列表
    std::map<uint32_t, std::vector<uint32_t>> appPresetMap; // 应用ID -> 预设ID列表
    
public:
    ProbeApplicationMappingManager(Database& database);
    
    // 加载探头支持的应用
    std::vector<ClinicalApplication> loadProbeApplications(uint32_t probeId);
    
    // 加载应用支持的预设
    std::vector<PresetInfo> loadApplicationPresets(uint32_t appId);
    
    // 判断探头是否支持特定应用
    bool isApplicationSupportedByProbe(uint32_t probeId, uint32_t appId);
    
    // 为探头添加应用支持
    bool addApplicationToProbe(uint32_t probeId, uint32_t appId, bool isRecommended = false);
    
    // 为应用添加预设
    bool addPresetToApplication(uint32_t appId, uint32_t presetId, bool isDefault = false, int displayOrder = 0);
    
    // 获取探头的默认应用
    uint32_t getDefaultApplication(uint32_t probeId);
    
    // 获取应用的默认预设
    uint32_t getDefaultPreset(uint32_t appId);
};
```

## 5. 参数计算与下发流程

### 5.1 参数计算流程

```cpp
// 参数计算与下发流程
class ParameterProcessor {
private:
    ParameterManager& paramManager;
    ProbeApplicationMappingManager& mappingManager;
    
    // 当前选中的状态
    uint32_t selectedProbeId;
    uint32_t selectedAppId;
    uint32_t selectedPresetId;
    
    // 参数依赖图
    std::map<std::string, std::set<std::string>> paramDependencyGraph;
    
    // 构建参数依赖图
    void buildDependencyGraph();
    
    // 拓扑排序获取计算顺序
    std::vector<std::string> getCalculationOrder();
    
public:
    ParameterProcessor(ParameterManager& paramManager, ProbeApplicationMappingManager& mappingManager);
    
    // 选择探头
    void selectProbe(uint32_t probeId);
    
    // 选择临床应用
    void selectApplication(uint32_t appId);
    
    // 选择预设
    void selectPreset(uint32_t presetId);
    
    // 计算所有参数
    void calculateAllParameters();
    
    // 将参数下发到各处理链路
    void applyParametersToProcessingChain();
    
    // 获取当前选择状态
    struct SelectionState {
        uint32_t probeId;
        uint32_t appId;
        uint32_t presetId;
        std::string probeName;
        std::string appName;
        std::string presetName;
    };
    
    SelectionState getCurrentSelection() const;
};
```

### 5.2 参数下发执行流程

```cpp
// 参数下发流程实现示例
void ParameterProcessor::applyParametersToProcessingChain() {
    // 1. 获取所有需要下发的参数
    auto allParams = paramManager.getAllParameters();
    
    // 2. 按照处理链路分组参数
    std::map<LayerType, std::vector<Parameter*>> layerParams;
    for (auto param : allParams) {
        LayerType layer = getParameterLayer(param->getId());
        layerParams[layer].push_back(param);
    }
    
    // 3. 按照优先级排序各层参数
    for (auto& [layer, params] : layerParams) {
        sortParametersByPriority(params);
    }
    
    // 4. 向各层下发参数
    // 4.1 设备层参数
    if (layerParams.count(LayerType::DEVICE) > 0) {
        deviceLayerManager.applyParameters(layerParams[LayerType::DEVICE]);
    }
    
    // 4.2 前处理层参数
    if (layerParams.count(LayerType::PREPROCESSING) > 0) {
        preprocessingLayerManager.applyParameters(layerParams[LayerType::PREPROCESSING]);
    }
    
    // 4.3 缓存层参数
    if (layerParams.count(LayerType::CACHE) > 0) {
        cacheLayerManager.applyParameters(layerParams[LayerType::CACHE]);
    }
    
    // 4.4 后处理层参数
    if (layerParams.count(LayerType::POSTPROCESSING) > 0) {
        postprocessingLayerManager.applyParameters(layerParams[LayerType::POSTPROCESSING]);
    }
    
    // 4.5 显示层参数
    if (layerParams.count(LayerType::DISPLAY) > 0) {
        displayLayerManager.applyParameters(layerParams[LayerType::DISPLAY]);
    }
    
    // 5. 下发完成后通知UI更新
    notifyUIParametersApplied();
}
```

## 6. 交互流程设计

### 6.1 探头选择与参数下发流程图

```
┌─────────────────┐
│ 系统启动        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 设备层接收      │
│ 探头信息包      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 解析探头信息    │
│ 更新探头列表    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│ 显示探头选择    │     │ 加载探头支持的  │
│ 界面           │◄────┤ 临床应用信息    │
└────────┬────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐
│ 用户选择探头    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐     ┌─────────────────┐
│ 显示临床应用    │     │ 加载应用支持的  │
│ 选择界面        │◄────┤ 预设信息        │
└────────┬────────┘     └─────────────────┘
         │
         ▼
┌─────────────────┐
│ 用户选择临床应用│
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 显示预设选择    │
│ 界面           │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 用户选择预设    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 执行参数计算    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 参数下发到      │
│ 各处理链路      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 开始成像过程    │
└─────────────────┘
```

### 6.2 参数下发详细流程

```
┌─────────────────┐
│ 用户选择预设    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 加载预设参数    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 构建参数依赖图  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 拓扑排序确定    │
│ 参数计算顺序    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 按顺序计算      │
│ 派生参数        │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 应用参数约束    │
│ 和有效性检查    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 将参数分组      │
│ 按层级分类      │
└────────┬────────┘
         │
         ▼
┌─────────────────────────────────────────┐
│               并行下发                  │
└─────────────────────────────────────────┘
         │
         ├─────────────┬─────────────┬─────────────┬─────────────┐
         │             │             │             │             │
         ▼             ▼             ▼             ▼             ▼
┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│ 设备层参数  │ │ 前处理层    │ │ 缓存层      │ │ 后处理层    │ │ 显示层      │
│ 下发        │ │ 参数下发    │ │ 参数下发    │ │ 参数下发    │ │ 参数下发    │
└──────┬──────┘ └──────┬──────┘ └──────┬──────┘ └──────┬──────┘ └──────┬──────┘
       │               │               │               │               │
       └───────────────┴───────────────┴───────────────┴───────────────┘
                                       │
                                       ▼
                             ┌─────────────────┐
                             │ 下发完成回调    │
                             └────────┬────────┘
                                      │
                                      ▼
                             ┌─────────────────┐
                             │ 通知UI更新      │
                             └────────┬────────┘
                                      │
                                      ▼
                             ┌─────────────────┐
                             │ 开始成像        │
                             └─────────────────┘
```

## 7. 系统扩展与未来工作

### 7.1 探头信息与应用扩展

1. **探头能力自动发现**
   - 设计探头能力自动发现机制，根据探头硬件特性自动匹配支持的应用
   - 实现探头插入即用功能，减少手动配置

2. **远程探头库更新**
   - 设计在线探头库更新机制，支持下载新探头信息和应用映射
   - 提供云端预设库，便于用户下载最新预设

### 7.2 预设推荐系统

1. **基于AI的预设推荐**
   - 根据检查对象特征自动推荐最佳预设
   - 学习用户偏好，提供个性化预设推荐

2. **动态参数优化**
   - 检查过程中实时调整参数以获得最佳图像
   - 根据图像质量指标自动微调参数

### 7.3 多探头协同工作

1. **探头组合检查**
   - 支持多探头同时连接并协同工作
   - 为不同探头组合设计专用应用和预设

2. **探头快速切换**
   - 优化探头间切换速度，保持参数设置
   - 提供探头对比模式，方便比较不同探头的成像效果

## 8. 结论

本设计详细描述了从设备层接收探头信息包到用户选择探头、应用、预设，以及参数下发的完整流程。设计遵循了分层架构原则，保持各模块职责清晰，接口明确。通过标准化的数据结构和处理流程，确保了系统的稳定性和可扩展性。 