# 超声彩色IQ数据格式分析

本文档分析了超声彩色IQ数据的输入和输出格式，通过分析代码理解数据流转过程，并用可视化方式呈现。

## 1. 输入数据格式 (copytobufferbyframeindex中的data)

根据`PreProcessBufManager4color.cpp`文件中的`copytobufferbyframeindex`函数分析，输入数据有以下特点：

### 输入数据格式详解

1. **数据头部结构(LineDataHead)**：
   - 大小：16字节
   - 包含同步头、包类型、线类型、线号、帧标记等信息
   - 重要字段包括：
     - LineNo：线号（0-1023或0-2047）
     - FrameFlag：帧标记（帧开始、中间线、帧结束）
     - getIQAccumulativeTimes()：IQ积累次数（0-15）
     - getIQFlag()：IQ标志（I=0，Q=1）
     - getFrameNo()：帧号

2. **数据体结构**：
   数据体从LINEDATAHEADLEN（16字节）之后开始，根据设备类型和场景有两种处理方式：

   a. **掌超WiFi模式** (m_Colorpointsize == m_Pointsize/2)：
   ```
   +----------------+----------------+----------------+----------------+
   |      P1Q1      |      P2Q2      |      ....      |      PnQn      |
   +----------------+----------------+----------------+----------------+
   ```
   - 每次发送ROI框内的血流数据
   - 每次发送两根线的PQ数据：P1 Q1 P2 Q2
   - 采用隔点采样方式（只取ROI区域内）
   - 特点是顶部和底部有特别边界处理

   b. **普通模式** (m_Colorpointsize == m_Pointsize)：
   ```
   +----------------+----------------+----------------+----------------+
   |       I/Q      |       I/Q      |      ....      |       I/Q      |
   +----------------+----------------+----------------+----------------+
   ```
   - 完整的I或Q数据（由lineColorIQFlag决定）
   - 一次处理一根线的I或Q数据

3. **数据存储方式**：
   - 将收到的数据按帧号存储在内部缓冲区中
   - 按照线号、IQ标志、积累次数进行索引
   - 当积累足够的数据（由m_Assemblecolorbatchsize定义）后，重置并输出一帧数据

## 2. 输出数据格式 (pushData4Corlor中的iqptr)

根据`pretreatmentpipleline.cpp`文件中的`pushData4Corlor`函数分析：

### 输出数据格式详解

1. **数据结构体(DataPreProcessInfo)**：
   - 包含数据类型(DataType)和数据处理数组(DataPostProcess)
   - 每个DataForPreProcess包含：
     - Data：数据指针
     - DataPointNum：每条线的点数
     - DataLineNum：线数
     - PointBytesCount：每个点的字节数（通常为2）

2. **数据组织方式**：
   ```
   +-----+-----+-----+-----+-----+-----+-----+-----+
   | I1  | Q1  | I2  | Q2  | ... | In  | Qn  | ... |
   +-----+-----+-----+-----+-----+-----+-----+-----+
   ```
   
   - 按照IQ交替的方式排列：I1, Q1, I2, Q2, ..., In, Qn
   - 针对多次积累，排列为：I1Q1, I2Q2, ... InQn
   - 对于m_PacketSize次积累，总共有m_PacketSize*2组IQ数据

3. **转换过程**：
   ```cpp
   for (int i = 0; i < data.m_PacketSize; i++) {
       // 复制I数据
       memcpy(&iqptr[i * 2 * alineIorQpiecesize], 
              &pData[i * alineIorQpiecesize], 
              alineIorQpiecesize);
       
       // 复制Q数据
       memcpy(&iqptr[(i * 2 + 1) * alineIorQpiecesize], 
              &pData[(i + data.m_PacketSize) * alineIorQpiecesize],
              alineIorQpiecesize);
   }
   ```
   
   这段代码将输入数据的I和Q部分（原本存储在两个连续区域）重组为IQ交替的格式。

## 3. 数据格式可视化

### 彩色超声IQ数据流转图

#### 1. 输入数据格式

```
+----------------+--------------------------------------------+
| LineDataHead   | 数据体 (Data)                              |
| (16字节)       |                                            |
+----------------+--------------------------------------------+
```

LineDataHead包含:
- SyncHead: 同步头 (0xaa55)
- LineType: 线类型 (Line_C_Vel=1)
- LineNo: 线号 [0-1023]
- FrameFlag: 帧标记
- IQAccumulativeTimes: 积累次数 [0-15]
- IQFlag: IQ标志 (I=0, Q=1)

数据体格式分两种:

a. 掌超WiFi模式:
```
+-------+-------+-------+-------+
|  P1Q1  |  P2Q2  |  ...  |  PnQn  |
+-------+-------+-------+-------+
```

b. 普通模式:
```
+-------+-------+-------+-------+
|  I/Q  |  I/Q  |  ...  |  I/Q  |
+-------+-------+-------+-------+
```

#### 2. 数据缓存方式

每帧数据的多次积累按如下方式存储:

```
+---------------------------------------------------+
| 帧#N                                              |
+-----------+-----------+-----------+---------------+
| 积累#0    | 积累#1    | ...       | 积累#(n-1)    |
+-----------+-----------+-----------+---------------+
| I线  | Q线 | I线  | Q线| ...      | I线   | Q线   |
+-----+-----+-----+-----+-----------+------+-------+
|线#1 |线#1 |线#1 |线#1 | ...       |线#1  |线#1   |
|线#2 |线#2 |线#2 |线#2 | ...       |线#2  |线#2   |
|... |... |... |... | ...       |...  |...   |
|线#m |线#m |线#m |线#m | ...       |线#m  |线#m   |
+-----+-----+-----+-----+-----------+------+-------+
```

#### 3. 输出数据格式

数据按IQ交替排列方式输出:

```
+-----------------------------------------------------------+
| 积累#0             | 积累#1             | ...             |
+-------------------+-------------------+-------------------+
| I1 | Q1 | I2 | Q2 | I1 | Q1 | I2 | Q2 | ...             |
+----+----+----+----+----+----+----+----+-------------------+
```

DataPreProcessInfo结构:
- DataType: CData (彩色数据类型)
- DataPostProcess[]: 数据处理数组
  - Data: 指向重组后的IQ数据
  - DataPointNum: 每条线的点数
  - DataLineNum: 线数
  - PointBytesCount: 每个点的字节数(=2)

## 4. 数据处理流程

1. **数据接收阶段**
   - `copytobufferbyframeindex`接收原始IQ数据
   - 根据线号、IQ标志和积累次数将数据存入缓冲区
   - 当数据量达到阈值时，将完整帧数据发送给下一级处理

2. **数据重组阶段**
   - `pushData4Corlor`将收到的IQ数据重组为IQ交替格式
   - 按照积累顺序排列：I1Q1, I2Q2, ..., InQn

3. **数据输出阶段**
   - 通过`pushDataForPreProcess`将重组后的数据发送到ZEUS前处理引擎
   - 携带帧信息等附加数据

这个流程确保了彩色超声IQ数据能够以正确的格式传递给处理引擎，实现有效的超声图像处理。 