# 参数依赖图构建与拓扑排序详细设计

## 1. 参数依赖图构建实现

参数依赖图本质上是表示参数之间依赖关系的有向图。下面详细解释如何实现这一关键部分：

### 1.1 数据结构定义

```cpp
// 参数依赖图的数据结构定义
class ParameterDependencyGraph {
private:
    // 存储参数依赖关系的图结构
    std::map<std::string, std::set<std::string>> dependencyGraph;  // 参数ID -> 依赖它的参数ID集合
    std::map<std::string, std::set<std::string>> reverseDependencyGraph;  // 参数ID -> 它依赖的参数ID集合
    
    // 记录每个参数的入度（用于拓扑排序）
    std::map<std::string, int> inDegree;
    
    // 所有参数的集合
    std::set<std::string> allParameters;
    
public:
    // 添加参数
    void addParameter(const std::string& paramId);
    
    // 添加依赖关系: fromParam 依赖 toParam (fromParam的值依赖于toParam的值)
    void addDependency(const std::string& fromParam, const std::string& toParam);
    
    // 获取依赖特定参数的所有参数
    std::set<std::string> getDependentParameters(const std::string& paramId) const;
    
    // 获取特定参数依赖的所有参数
    std::set<std::string> getParameterDependencies(const std::string& paramId) const;
    
    // 检查是否存在循环依赖
    bool hasCyclicDependency() const;
    
    // 获取拓扑排序结果
    std::vector<std::string> getTopologicalOrder() const;
    
    // 清空图
    void clear();
};
```

### 1.2 构建参数依赖图实现

```cpp
// 在ParameterProcessor类中实现的构建依赖图方法
void ParameterProcessor::buildDependencyGraph() {
    // 清空现有的依赖图
    paramDependencyGraph.clear();
    
    // 1. 获取所有参数并添加到图中
    auto allParams = paramManager.getAllParameters();
    for (const auto& param : allParams) {
        paramDependencyGraph.addParameter(param->getId());
    }
    
    // 2. 从计算参数中收集依赖信息
    for (const auto& param : allParams) {
        // 如果是计算参数
        if (auto computedParam = dynamic_cast<ComputedParameter*>(param)) {
            // 获取该参数依赖的所有参数ID
            const auto& dependencies = computedParam->getDependentParamIds();
            
            // 添加依赖关系到图中
            for (const auto& depId : dependencies) {
                paramDependencyGraph.addDependency(param->getId(), depId);
            }
        }
    }
    
    // 3. 从参数约束中收集依赖信息
    auto constraints = paramManager.getAllConstraints();
    for (const auto& constraint : constraints) {
        // 获取约束涉及的所有参数
        const auto& involvedParams = constraint->getInvolvedParameters();
        // 添加约束依赖关系
        for (size_t i = 0; i < involvedParams.size(); ++i) {
            for (size_t j = 0; j < involvedParams.size(); ++j) {
                if (i != j) {
                    paramDependencyGraph.addDependency(involvedParams[i], involvedParams[j]);
                }
            }
        }
    }
    
    // 4. 检查是否存在循环依赖
    if (paramDependencyGraph.hasCyclicDependency()) {
        logError("检测到参数间存在循环依赖！");
        // 记录具体的循环依赖路径供调试使用
        detectAndLogCyclicDependencies();
    }
}
```

### 1.3 循环依赖检测算法

```cpp
// 检测循环依赖的深度优先搜索实现
bool ParameterDependencyGraph::hasCyclicDependency() const {
    std::set<std::string> visited;
    std::set<std::string> recursionStack;
    
    for (const auto& param : allParameters) {
        if (hasCyclicDependencyDFS(param, visited, recursionStack)) {
            return true;
        }
    }
    
    return false;
}

bool ParameterDependencyGraph::hasCyclicDependencyDFS(const std::string& paramId, 
                                                     std::set<std::string>& visited,
                                                     std::set<std::string>& recursionStack) const {
    // 如果参数不在已访问集合中
    if (visited.find(paramId) == visited.end()) {
        // 标记为已访问
        visited.insert(paramId);
        recursionStack.insert(paramId);
        
        // 遍历该参数依赖的所有参数
        for (const auto& depParam : getParameterDependencies(paramId)) {
            // 如果依赖参数在递归栈中，说明存在循环
            if (recursionStack.find(depParam) != recursionStack.end()) {
                return true;
            }
            
            // 如果依赖参数未访问且存在循环，返回true
            if (visited.find(depParam) == visited.end() && 
                hasCyclicDependencyDFS(depParam, visited, recursionStack)) {
                return true;
            }
        }
    }
    
    // 回溯，将参数从递归栈中移除
    recursionStack.erase(paramId);
    return false;
}
```

### 1.4 依赖图可视化示例

下面是一个简单的参数依赖关系可视化示例：

```
探头频率(frequency) ────► 谐波频率(harmonicFreq)
     │                         │
     ▼                         ▼
成像深度(depth) ────────► 聚焦位置(focusPos)
     │                         │
     ▼                         ▼
增益值(gain) ────────────► 降噪参数(noiseReduction)
                               │
                               ▼
                         图像锐化(sharpness)
```

在这个示例中：
- 谐波频率依赖于探头频率
- 聚焦位置依赖于成像深度和谐波频率
- 降噪参数依赖于增益值和聚焦位置
- 图像锐化依赖于降噪参数

## 2. 拓扑排序详细描述

拓扑排序是一种对有向无环图(DAG)的节点进行线性排序的算法，使得对于图中任何一条有向边(u,v)，节点u在排序中都出现在节点v之前。

### 2.1 拓扑排序的实现

```cpp
// 拓扑排序实现
std::vector<std::string> ParameterProcessor::getCalculationOrder() {
    // 拓扑排序结果
    std::vector<std::string> result;
    
    // 1. 计算每个参数的入度（依赖它的参数数量）
    std::map<std::string, int> inDegree;
    for (const auto& param : paramManager.getAllParameters()) {
        inDegree[param->getId()] = 0;
    }
    
    // 计算入度
    for (const auto& param : paramManager.getAllParameters()) {
        for (const auto& dep : paramDependencyGraph.getParameterDependencies(param->getId())) {
            inDegree[dep]++;
        }
    }
    
    // 2. 将入度为0的参数加入队列
    std::queue<std::string> zeroInDegree;
    for (const auto& [paramId, degree] : inDegree) {
        if (degree == 0) {
            zeroInDegree.push(paramId);
        }
    }
    
    // 3. BFS遍历
    while (!zeroInDegree.empty()) {
        // 获取当前入度为0的参数
        std::string current = zeroInDegree.front();
        zeroInDegree.pop();
        
        // 添加到结果列表
        result.push_back(current);
        
        // 更新依赖于current的参数的入度
        for (const auto& dependent : paramDependencyGraph.getDependentParameters(current)) {
            inDegree[dependent]--;
            // 如果入度变为0，加入队列
            if (inDegree[dependent] == 0) {
                zeroInDegree.push(dependent);
            }
        }
    }
    
    // 4. 检查是否所有参数都已排序
    if (result.size() != paramManager.getAllParameters().size()) {
        logError("拓扑排序未完成，可能存在循环依赖！");
        // 记录未能参与排序的参数
        std::set<std::string> unsortedParams;
        for (const auto& [paramId, degree] : inDegree) {
            if (degree > 0) {
                unsortedParams.insert(paramId);
            }
        }
        logError("未参与排序的参数: " + formatSetToString(unsortedParams));
    }
    
    return result;
}
```

### 2.2 拓扑排序的时间复杂度和空间复杂度

- **时间复杂度**：O(V + E)，其中V是参数数量，E是依赖关系数量
- **空间复杂度**：O(V)，需要存储入度信息和队列

### 2.3 拓扑排序算法流程图

```
┌─────────────────┐
│ 初始化入度表    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 计算各参数入度  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 将入度为0的     │
│ 参数加入队列    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 队列为空？      │────────┐是
└────────┬────────┘        │
         │否               │
         ▼                 ▼
┌─────────────────┐  ┌─────────────────┐
│ 弹出队首参数    │  │ 排序完成        │
│ 并添加到结果    │  └─────────────────┘
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 更新依赖当前    │
│ 参数的入度      │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ 将新的入度为0   │
│ 的参数加入队列  │
└────────┬────────┘
         │
         └─────────────────┘
```

### 2.4 拓扑排序应用案例

以上面依赖图为例，执行拓扑排序的过程如下：

1. 初始化入度：
   - frequency: 0
   - depth: 1 (依赖于frequency)
   - gain: 0
   - harmonicFreq: 1 (依赖于frequency)
   - focusPos: 2 (依赖于depth和harmonicFreq)
   - noiseReduction: 2 (依赖于gain和focusPos)
   - sharpness: 1 (依赖于noiseReduction)

2. 将入度为0的参数加入队列: [frequency, gain]

3. 拓扑排序过程：
   - 取出frequency，减少依赖项入度：depth(0), harmonicFreq(0)
   - 队列: [gain, depth, harmonicFreq]
   - 取出gain，减少依赖项入度：noiseReduction(1)
   - 队列: [depth, harmonicFreq]
   - 取出depth，减少依赖项入度：focusPos(1)
   - 队列: [harmonicFreq]
   - 取出harmonicFreq，减少依赖项入度：focusPos(0)
   - 队列: [focusPos]
   - 取出focusPos，减少依赖项入度：noiseReduction(0)
   - 队列: [noiseReduction]
   - 取出noiseReduction，减少依赖项入度：sharpness(0)
   - 队列: [sharpness]
   - 取出sharpness
   - 队列为空，排序完成

4. 最终的计算顺序为: 
   ```
   [frequency, gain, depth, harmonicFreq, focusPos, noiseReduction, sharpness]
   ```

这个顺序确保了当计算某个参数时，它所依赖的所有参数都已经计算完成。

### 2.5 参数计算实现

```cpp
// 按照拓扑排序结果计算参数
void ParameterProcessor::calculateAllParameters() {
    // 获取参数计算顺序
    std::vector<std::string> calculationOrder = getCalculationOrder();
    
    // 按顺序计算每个参数
    for (const auto& paramId : calculationOrder) {
        // 获取参数对象
        Parameter* param = paramManager.getParameter(paramId);
        
        // 如果是计算参数，需要根据依赖参数计算
        if (auto computedParam = dynamic_cast<ComputedParameter*>(param)) {
            // 收集依赖参数
            std::map<std::string, Parameter*> dependentParams;
            for (const auto& depId : computedParam->getDependentParamIds()) {
                dependentParams[depId] = paramManager.getParameter(depId);
            }
            
            // 执行计算
            computedParam->compute(dependentParams);
            
            // 记录日志
            logInfo("计算参数 '" + paramId + "' 的值: " + 
                   formatParameterValue(computedParam));
        }
    }
    
    // 应用参数约束
    applyParameterConstraints();
}
```

## 3. 实际应用案例

### 3.1 超声成像参数依赖示例

在超声系统中，以下是一组典型的参数依赖关系：

```cpp
// 1. 添加基本参数
paramManager.registerParameter(new NumericParameter<double>("probeFrequency", "探头频率", 3.5, 1.0, 15.0));
paramManager.registerParameter(new NumericParameter<double>("imagingDepth", "成像深度", 12.0, 2.0, 30.0));
paramManager.registerParameter(new NumericParameter<int>("gain", "增益", 50, 0, 100));
paramManager.registerParameter(new NumericParameter<double>("dynamicRange", "动态范围", 65.0, 40.0, 100.0));

// 2. 添加计算参数
auto harmonicFreq = new ComputedParameter<double>("harmonicFreq", "谐波频率");
harmonicFreq->setComputeFunction([](const std::map<std::string, Parameter*>& params) {
    // 谐波频率通常是探头频率的2倍
    double probeFreq = static_cast<NumericParameter<double>*>(params.at("probeFrequency"))->getValue();
    return probeFreq * 2.0;
});
harmonicFreq->addDependentParam("probeFrequency");
paramManager.registerParameter(harmonicFreq);

// 3. 添加更复杂的计算参数
auto focusPosition = new ComputedParameter<double>("focusPosition", "聚焦位置");
focusPosition->setComputeFunction([](const std::map<std::string, Parameter*>& params) {
    // 聚焦位置通常基于成像深度，默认为深度的60%处
    double depth = static_cast<NumericParameter<double>*>(params.at("imagingDepth"))->getValue();
    return depth * 0.6;
});
focusPosition->addDependentParam("imagingDepth");
paramManager.registerParameter(focusPosition);

// 4. 添加多依赖参数
/*
代码解释：
1. 这里创建了一个名为"noiseReduction"的计算参数，用于控制降噪强度
2. setComputeFunction中的params参数是系统自动传入的，它包含了所有依赖参数的引用
3. params的来源：
   - 当参数系统调用计算函数时，会自动收集所有通过addDependentParam()添加的依赖参数
   - 在本例中，通过addDependentParam("gain")和addDependentParam("focusPosition")声明了依赖
   - 系统在执行计算前，会构建一个包含所有依赖参数的map，键为参数ID，值为参数指针
   - 这个map就是传递给计算函数的params参数
4. 计算函数内部通过params.at("参数ID")访问依赖的参数，然后进行类型转换获取具体值
5. 整个过程由参数管理系统自动处理，确保在依赖参数变化时重新计算
*/
auto noiseReduction = new ComputedParameter<int>("noiseReduction", "降噪强度");
noiseReduction->setComputeFunction([](const std::map<std::string, Parameter*>& params) {
    // 降噪参数依赖于增益和聚焦位置
    int gain = static_cast<NumericParameter<int>*>(params.at("gain"))->getValue();
    double focusPos = static_cast<NumericParameter<double>*>(params.at("focusPosition"))->getValue();
    
    // 降噪强度与增益成正比，与聚焦位置成反比
    return std::min(100, std::max(0, static_cast<int>(gain * 0.8 - focusPos * 0.5)));
});
noiseReduction->addDependentParam("gain");
noiseReduction->addDependentParam("focusPosition");
paramManager.registerParameter(noiseReduction);
```

### 3.2 参数连锁变化过程

假设用户将探头频率从3.5MHz调整到5.0MHz，系统将按照以下步骤更新参数值：

1. 用户修改 `probeFrequency` = 5.0MHz
2. 系统检测到 `probeFrequency` 变化，找出依赖它的参数
3. 系统构建参数依赖图：
   ```
   probeFrequency → harmonicFreq
   imagingDepth → focusPosition
   gain, focusPosition → noiseReduction
   ```
4. 执行拓扑排序，得到计算顺序：
   ```
   [probeFrequency, imagingDepth, gain, harmonicFreq, focusPosition, noiseReduction]
   ```
5. 按顺序计算参数：
   - `harmonicFreq` = 5.0 * 2 = 10.0MHz
   - `focusPosition` = 12.0 * 0.6 = 7.2cm
   - `noiseReduction` = min(100, max(0, 50 * 0.8 - 7.2 * 0.5)) = 36

6. 系统将新的参数值应用到各处理链路

这种参数计算机制确保了当用户修改某个参数时，所有依赖它的派生参数会自动正确更新，保持系统的一致性和合理性。

### 3.3 处理循环依赖的策略

在实际系统中，可能会不小心引入循环依赖。以下是处理循环依赖的几种策略：

1. **循环依赖检测与报警**：
   - 在构建参数依赖图时检测循环依赖
   - 向开发人员发出警告或错误日志
   - 提供循环依赖的具体路径以便调试

2. **参数优先级打破循环**：
   - 为每个参数分配优先级
   - 在存在循环依赖时，优先计算高优先级参数
   - 这种方法可能导致某些参数计算结果不精确

3. **迭代计算法**：
   - 对于循环依赖的参数组，使用迭代法计算
   - 设置初始值，然后多次循环计算直到收敛
   - 设置最大迭代次数防止无限循环

4. **依赖重构**：
   - 重新设计参数间的依赖关系，消除循环
   - 引入中间参数打破循环依赖
   - 这是最彻底的解决方案，但可能需要重构代码

### 3.4 依赖图变更的动态处理

参数依赖图不是静态的，会随着用户操作和系统状态而变化。以下是处理动态依赖图的策略：

1. **条件依赖**：
   ```cpp
   // 条件依赖示例：只有在特定模式下才添加某些依赖
   if (imagingMode == ImagingMode::HARMONIC) {
       // 在谐波成像模式下，添加谐波频率依赖
       paramDependencyGraph.addDependency("filterStrength", "harmonicFreq");
   } else {
       // 在B模式下，移除这个依赖
       paramDependencyGraph.removeDependency("filterStrength", "harmonicFreq");
   }
   ```

2. **依赖图重建触发机制**：
   - 在关键参数变化时触发依赖图重建
   - 设置观察者模式监听特定参数变化
   - 优化重建过程，只更新受影响部分

3. **依赖图缓存**：
   - 缓存常用场景的依赖图和拓扑排序结果
   - 对于频繁使用的参数组合，避免重复计算
   - 在系统资源允许的情况下，预计算可能的依赖图 
```

## 4. 参数约束系统工作原理

参数约束是确保系统中多个参数之间满足特定关系的机制。参数约束与参数依赖不同，依赖描述的是计算关系，而约束描述的是合法性关系。

### 4.1 参数约束的概念与类型

参数约束主要用于维护系统参数的一致性，防止出现不合理或冲突的参数组合。在超声系统中，常见的约束类型包括：

1. **范围约束**：限制参数值必须在指定范围内
   ```cpp
   // 例：成像深度必须在2.0-30.0cm之间
   ```

2. **关系约束**：描述多个参数之间的数学关系
   ```cpp
   // 例：谐波频率不能高于探头最大频率的2倍
   ```

3. **离散值约束**：参数值必须从预定义的离散值集合中选择
   ```cpp
   // 例：能量档位只能是[I, II, III, IV]中的一个
   ```

4. **条件约束**：在特定条件下才生效的约束
   ```cpp
   // 例：在谐波模式下，探头频率必须大于3MHz
   ```

5. **互斥约束**：描述参数间的互斥关系
   ```cpp
   // 例：当开启谐波成像时，不能同时开启频谱多普勒
   ```

### 4.2 约束系统的数据结构

```cpp
// 基础约束接口
class ParameterConstraint {
protected:
    // 该约束涉及的所有参数ID
    std::vector<std::string> involvedParameters;
    
    // 约束描述
    std::string description;
    
    // 约束优先级（用于解决约束冲突）
    int priority;
    
public:
    ParameterConstraint(const std::string& desc, int priority = 0)
        : description(desc), priority(priority) {}
    
    virtual ~ParameterConstraint() = default;
    
    // 添加涉及的参数
    void addInvolvedParameter(const std::string& paramId) {
        involvedParameters.push_back(paramId);
    }
    
    // 获取所有涉及的参数
    const std::vector<std::string>& getInvolvedParameters() const {
        return involvedParameters;
    }
    
    // 验证参数值是否满足约束
    virtual bool validate(const std::map<std::string, Parameter*>& params) const = 0;
    
    // 修正参数值以满足约束（如果可能）
    virtual bool adjust(std::map<std::string, Parameter*>& params) const = 0;
    
    // 获取约束优先级
    int getPriority() const { return priority; }
    
    // 获取约束描述
    const std::string& getDescription() const { return description; }
};

// 范围约束实现
template<typename T>
class RangeConstraint : public ParameterConstraint {
private:
    std::string paramId;
    T minValue;
    T maxValue;
    
public:
    RangeConstraint(const std::string& paramId, T min, T max, 
                   const std::string& desc = "")
        : ParameterConstraint(desc.empty() ? 
                            "参数 " + paramId + " 必须在 [" + std::to_string(min) + 
                            ", " + std::to_string(max) + "] 范围内" : desc),
          paramId(paramId), minValue(min), maxValue(max) {
        addInvolvedParameter(paramId);
    }
    
    bool validate(const std::map<std::string, Parameter*>& params) const override {
        if (params.find(paramId) == params.end()) return false;
        
        auto param = dynamic_cast<NumericParameter<T>*>(params.at(paramId));
        if (!param) return false;
        
        T value = param->getValue();
        return value >= minValue && value <= maxValue;
    }
    
    bool adjust(std::map<std::string, Parameter*>& params) const override {
        if (params.find(paramId) == params.end()) return false;
        
        auto param = dynamic_cast<NumericParameter<T>*>(params.at(paramId));
        if (!param) return false;
        
        T value = param->getValue();
        if (value < minValue) {
            param->setValue(minValue);
            return true;
        } else if (value > maxValue) {
            param->setValue(maxValue);
            return true;
        }
        
        return false; // 无需调整
    }
};

// 关系约束实现
class RelationConstraint : public ParameterConstraint {
private:
    std::string paramId1;
    std::string paramId2;
    RelationType relationType;
    double factor;
    
public:
    enum RelationType {
        LESS_THAN,          // param1 < param2 * factor
        LESS_EQUAL,         // param1 <= param2 * factor
        GREATER_THAN,       // param1 > param2 * factor
        GREATER_EQUAL,      // param1 >= param2 * factor
        EQUAL_TO,           // param1 == param2 * factor
        NOT_EQUAL_TO        // param1 != param2 * factor
    };
    
    RelationConstraint(const std::string& id1, const std::string& id2,
                      RelationType type, double factor = 1.0,
                      const std::string& desc = "")
        : ParameterConstraint(desc), paramId1(id1), paramId2(id2),
          relationType(type), factor(factor) {
        addInvolvedParameter(id1);
        addInvolvedParameter(id2);
        
        if (description.empty()) {
            std::string relationStr;
            switch (type) {
                case LESS_THAN: relationStr = " < "; break;
                case LESS_EQUAL: relationStr = " <= "; break;
                case GREATER_THAN: relationStr = " > "; break;
                case GREATER_EQUAL: relationStr = " >= "; break;
                case EQUAL_TO: relationStr = " == "; break;
                case NOT_EQUAL_TO: relationStr = " != "; break;
            }
            
            description = "参数关系: " + id1 + relationStr + id2;
            if (factor != 1.0) {
                description += " * " + std::to_string(factor);
            }
        }
    }
    
    bool validate(const std::map<std::string, Parameter*>& params) const override {
        // 检查参数是否存在
        if (params.find(paramId1) == params.end() || 
            params.find(paramId2) == params.end()) {
            return false;
        }
        
        // 获取参数值（假设所有参数都可以转换为double进行比较）
        double value1 = getParameterValueAsDouble(params.at(paramId1));
        double value2 = getParameterValueAsDouble(params.at(paramId2)) * factor;
        
        // 根据关系类型验证
        switch (relationType) {
            case LESS_THAN:      return value1 < value2;
            case LESS_EQUAL:     return value1 <= value2;
            case GREATER_THAN:   return value1 > value2;
            case GREATER_EQUAL:  return value1 >= value2;
            case EQUAL_TO:       return std::abs(value1 - value2) < 1e-6; // 浮点数比较
            case NOT_EQUAL_TO:   return std::abs(value1 - value2) >= 1e-6;
            default:             return false;
        }
    }
    
    bool adjust(std::map<std::string, Parameter*>& params) const override {
        // 检查参数是否存在
        if (params.find(paramId1) == params.end() || 
            params.find(paramId2) == params.end()) {
            return false;
        }
        
        // 获取参数值
        double value1 = getParameterValueAsDouble(params.at(paramId1));
        double value2 = getParameterValueAsDouble(params.at(paramId2)) * factor;
        
        // 只调整第一个参数（通常是从属参数）
        bool needAdjust = false;
        double newValue1 = value1;
        
        switch (relationType) {
            case LESS_THAN:
                if (value1 >= value2) {
                    newValue1 = value2 - 0.01; // 减小一点以满足约束
                    needAdjust = true;
                }
                break;
            case LESS_EQUAL:
                if (value1 > value2) {
                    newValue1 = value2;
                    needAdjust = true;
                }
                break;
            case GREATER_THAN:
                if (value1 <= value2) {
                    newValue1 = value2 + 0.01; // 增加一点以满足约束
                    needAdjust = true;
                }
                break;
            case GREATER_EQUAL:
                if (value1 < value2) {
                    newValue1 = value2;
                    needAdjust = true;
                }
                break;
            case EQUAL_TO:
                if (std::abs(value1 - value2) >= 1e-6) {
                    newValue1 = value2;
                    needAdjust = true;
                }
                break;
            case NOT_EQUAL_TO:
                if (std::abs(value1 - value2) < 1e-6) {
                    newValue1 = value2 + 0.1; // 增加一点以满足不等约束
                    needAdjust = true;
                }
                break;
        }
        
        if (needAdjust) {
            setParameterValueFromDouble(params.at(paramId1), newValue1);
            return true;
        }
        
        return false; // 无需调整
    }
    
private:
    // 将任意参数转换为double值
    double getParameterValueAsDouble(Parameter* param) const {
        if (auto doubleParam = dynamic_cast<NumericParameter<double>*>(param)) {
            return doubleParam->getValue();
        } else if (auto intParam = dynamic_cast<NumericParameter<int>*>(param)) {
            return static_cast<double>(intParam->getValue());
        } else if (auto floatParam = dynamic_cast<NumericParameter<float>*>(param)) {
            return static_cast<double>(floatParam->getValue());
        }
        
        // 对于不支持的类型，返回0.0
        return 0.0;
    }
    
    // 从double值设置参数
    void setParameterValueFromDouble(Parameter* param, double value) const {
        if (auto doubleParam = dynamic_cast<NumericParameter<double>*>(param)) {
            doubleParam->setValue(value);
        } else if (auto intParam = dynamic_cast<NumericParameter<int>*>(param)) {
            intParam->setValue(static_cast<int>(std::round(value)));
        } else if (auto floatParam = dynamic_cast<NumericParameter<float>*>(param)) {
            floatParam->setValue(static_cast<float>(value));
        }
        
        // 对于不支持的类型，不做处理
    }
};
```

### 4.3 约束验证与应用流程

参数约束验证通常发生在以下几个时刻：

1. **参数修改后**：当用户或系统修改参数值时，立即验证相关约束
2. **预设加载时**：加载预设参数组合时，检查所有约束
3. **成像模式切换时**：模式切换可能导致约束条件发生变化
4. **系统初始化时**：确保初始参数集合满足所有约束

参数约束的应用流程如下：

```cpp
// 在ParameterProcessor类中实现约束应用
void ParameterProcessor::applyParameterConstraints() {
    // 1. 按优先级对约束排序（从高到低）
    std::vector<ParameterConstraint*> sortedConstraints = paramManager.getAllConstraintsSortedByPriority();
    
    // 2. 收集所有参数
    std::map<std::string, Parameter*> allParams;
    for (const auto& param : paramManager.getAllParameters()) {
        allParams[param->getId()] = param;
    }
    
    // 3. 记录初始状态以便检测循环调整
    std::map<std::string, std::string> initialState;
    for (const auto& [id, param] : allParams) {
        initialState[id] = param->getValueAsString();
    }
    
    // 4. 应用约束（最多尝试MAX_ITERATIONS次）
    const int MAX_ITERATIONS = 10;
    for (int iteration = 0; iteration < MAX_ITERATIONS; iteration++) {
        bool anyParamAdjusted = false;
        
        // 应用每个约束
        for (auto constraint : sortedConstraints) {
            // 检查约束是否满足
            if (!constraint->validate(allParams)) {
                // 不满足则调整参数
                bool adjusted = constraint->adjust(allParams);
                
                if (adjusted) {
                    anyParamAdjusted = true;
                    
                    // 记录调整信息
                    logInfo("应用约束: " + constraint->getDescription());
                    
                    // 检查约束是否导致参数值变为非法值
                    for (const auto& paramId : constraint->getInvolvedParameters()) {
                        Parameter* param = allParams[paramId];
                        if (!param->isValidValue()) {
                            logWarning("约束导致参数 '" + paramId + "' 值变为非法值：" + 
                                      param->getValueAsString());
                        }
                    }
                }
            }
        }
        
        // 如果没有任何参数被调整，说明所有约束已满足
        if (!anyParamAdjusted) {
            break;
        }
        
        // 检测是否出现循环调整（参数值回到初始状态）
        bool cycleDetected = true;
        for (const auto& [id, param] : allParams) {
            if (initialState[id] != param->getValueAsString()) {
                cycleDetected = false;
                break;
            }
        }
        
        if (cycleDetected) {
            logWarning("检测到约束调整循环，可能存在冲突的约束");
            break;
        }
    }
    
    // 5. 触发参数变更事件，通知系统其他部分
    for (const auto& [id, param] : allParams) {
        if (initialState[id] != param->getValueAsString()) {
            paramManager.notifyParameterChanged(id);
        }
    }
}
```

### 4.4 约束与依赖图的交互

参数约束与参数依赖图紧密交互，在系统中形成完整的参数管理体系：

1. **约束影响依赖计算**：
   - 当约束导致参数值调整时，会触发依赖参数的重新计算
   - 约束应用后需要重新执行拓扑排序的计算过程

2. **依赖图构建时纳入约束关系**：
   - 约束中涉及的参数间也存在依赖关系
   - 在依赖图构建时，会将约束关系也添加到图中

```cpp
// 从约束中收集依赖信息
for (const auto& constraint : paramManager.getAllConstraints()) {
    // 获取约束涉及的所有参数
    const auto& involvedParams = constraint->getInvolvedParameters();
    // 添加约束依赖关系（约束中的参数互相依赖）
    for (size_t i = 0; i < involvedParams.size(); ++i) {
        for (size_t j = 0; j < involvedParams.size(); ++j) {
            if (i != j) {
                paramDependencyGraph.addDependency(involvedParams[i], involvedParams[j]);
            }
        }
    }
}
```

### 4.5 约束冲突解决策略

在复杂系统中，参数约束间可能存在冲突。常见的解决策略包括：

1. **优先级机制**：
   - 为每个约束分配优先级
   - 高优先级约束先应用，低优先级约束后应用
   - 如果低优先级约束导致高优先级约束违反，再次应用高优先级约束

2. **约束分类**：
   - 将约束分为硬约束（必须满足）和软约束（尽量满足）
   - 硬约束优先级高于软约束
   - 软约束可以被违反，但系统会记录并警告用户

3. **启发式调整**：
   - 检测到约束冲突时，使用启发式算法寻找最优解
   - 可以使用目标函数评估不同参数组合的"满意度"
   - 选择总体满意度最高的参数组合

4. **用户确认机制**：
   - 当检测到不可解决的约束冲突时，提示用户选择
   - 提供参数调整建议，但最终决策权交给用户
   - 记录用户决策以便后续学习和改进

### 4.6 实际应用案例

下面是超声系统中参数约束的几个实际应用案例：

#### 案例1：谐波成像模式下的约束

```cpp
// 添加谐波成像模式下的相关约束
void addHarmonicModeConstraints() {
    // 1. 谐波频率约束（必须是基础频率的1.5-2.5倍）
    auto harmonicFreqConstraint = new RelationConstraint(
        "harmonicFreq", "probeFrequency", 
        RelationConstraint::LESS_EQUAL, 2.5,
        "谐波频率不能超过基础频率的2.5倍");
    harmonicFreqConstraint->setPriority(10);
    paramManager.addConstraint(harmonicFreqConstraint);
    
    auto harmonicFreqMinConstraint = new RelationConstraint(
        "harmonicFreq", "probeFrequency", 
        RelationConstraint::GREATER_EQUAL, 1.5,
        "谐波频率至少为基础频率的1.5倍");
    harmonicFreqMinConstraint->setPriority(10);
    paramManager.addConstraint(harmonicFreqMinConstraint);
    
    // 2. 谐波模式下的频率最小值约束
    auto probeFreqMinConstraint = new RangeConstraint<double>(
        "probeFrequency", 2.0, 15.0,
        "谐波模式下探头频率必须在2.0-15.0MHz范围内");
    probeFreqMinConstraint->setPriority(20);
    paramManager.addConstraint(probeFreqMinConstraint);
    
    // 3. 谐波模式下的能量约束
    auto powerConstraint = new RangeConstraint<int>(
        "transmitPower", 50, 100,
        "谐波模式下发射能量必须在50-100%范围内");
    powerConstraint->setPriority(5);
    paramManager.addConstraint(powerConstraint);
}
```

#### 案例2：成像深度与焦点位置约束

```cpp
// 添加成像深度相关的约束
void addDepthConstraints() {
    // 1. 焦点位置必须在成像深度内
    auto focusDepthConstraint = new RelationConstraint(
        "focusPosition", "imagingDepth", 
        RelationConstraint::LESS_THAN, 1.0,
        "焦点位置不能超过成像深度");
    focusDepthConstraint->setPriority(15);
    paramManager.addConstraint(focusDepthConstraint);
    
    // 2. 焦点位置不能太浅
    auto minFocusConstraint = new RangeConstraint<double>(
        "focusPosition", 1.0, 30.0,
        "焦点位置不能小于1.0cm");
    minFocusConstraint->setPriority(5);
    paramManager.addConstraint(minFocusConstraint);
    
    // 3. 成像深度与探头频率的关系约束（频率越高，最大深度越小）
    auto depthFreqConstraint = new CustomConstraint(
        "最大成像深度与频率关系约束",
        [](const std::map<std::string, Parameter*>& params) -> bool {
            // 获取参数值
            double frequency = static_cast<NumericParameter<double>*>(
                params.at("probeFrequency"))->getValue();
            double depth = static_cast<NumericParameter<double>*>(
                params.at("imagingDepth"))->getValue();
            
            // 频率越高，最大深度越小（简化的关系模型）
            double maxDepth = 40.0 / frequency;
            return depth <= maxDepth;
        },
        [](std::map<std::string, Parameter*>& params) -> bool {
            // 获取参数值
            double frequency = static_cast<NumericParameter<double>*>(
                params.at("probeFrequency"))->getValue();
            double depth = static_cast<NumericParameter<double>*>(
                params.at("imagingDepth"))->getValue();
            
            // 频率越高，最大深度越小
            double maxDepth = 40.0 / frequency;
            if (depth > maxDepth) {
                // 调整深度值
                static_cast<NumericParameter<double>*>(
                    params.at("imagingDepth"))->setValue(maxDepth);
                return true;
            }
            return false;
        });
    
    depthFreqConstraint->addInvolvedParameter("probeFrequency");
    depthFreqConstraint->addInvolvedParameter("imagingDepth");
    depthFreqConstraint->setPriority(25); // 高优先级
    paramManager.addConstraint(depthFreqConstraint);
}
```

#### 案例3：参数约束的动态变化

```cpp
// 模式切换时更新约束
void updateConstraintsForMode(ImagingMode newMode) {
    // 清除模式相关的约束
    paramManager.clearConstraintsByTag("mode_specific");
    
    switch (newMode) {
        case ImagingMode::B_MODE:
            addBModeConstraints();
            break;
            
        case ImagingMode::COLOR_DOPPLER:
            addColorDopplerConstraints();
            break;
            
        case ImagingMode::POWER_DOPPLER:
            addPowerDopplerConstraints();
            break;
            
        case ImagingMode::HARMONIC:
            addHarmonicModeConstraints();
            break;
            
        // 其他模式...
    }
    
    // 重新验证所有约束
    parameterProcessor.applyParameterConstraints();
    
    // 由于约束可能改变了参数值，需要重新计算依赖参数
    parameterProcessor.calculateAllParameters();
}
```

### 4.7 约束系统的优势与注意事项

#### 优势

1. **一致性保证**：确保系统参数始终保持在合理有效的状态
2. **简化用户界面**：避免用户设置不合理的参数组合
3. **自动调整**：当某个参数改变时，自动调整相关参数以维持一致性
4. **安全保障**：防止错误参数组合可能对设备或患者造成的安全风险
5. **模块化设计**：约束系统与参数管理分离，便于维护和扩展

#### 注意事项

1. **性能考虑**：复杂约束系统可能导致性能问题，特别是在约束频繁触发时
2. **约束循环**：避免创建可能导致无限循环调整的约束组合
3. **约束优先级设计**：需要仔细设计约束优先级以避免冲突
4. **用户体验**：约束触发的自动调整应该对用户可见且可理解
5. **测试覆盖**：约束系统需要全面的测试覆盖，包括边界条件和异常情况 