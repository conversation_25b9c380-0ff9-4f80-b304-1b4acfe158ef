# 图像子系统设计文档

## 1. 概述

图像子系统是超声系统的核心组件，负责处理从FPGA获取的原始数据，通过多级处理转换为可视化的超声图像。本文档详细描述了图像子系统的架构设计，特别是C4架构的组件层。

## 2. C4架构 - 组件层 (Component)

图像子系统主要由五个核心模块组成：图像设备模块、前处理模块、缓存模块、后处理模块和显示模块。这些模块共同协作，完成从原始数据采集到最终图像显示的全过程。

```
+----------------------------------图像子系统--------------------------------------+
|                                                                                  |
|  +----------------+      +----------------+      +----------------+              |
|  |                |      |                |      |                |              |
|  |  图像设备模块   |----->|   前处理模块    |----->|   缓存模块     |              |
|  |                |      |                |      |                |              |
|  +----------------+      +----------------+      +----------------+              |
|         ^                                               |                        |
|         |                                               v                        |
|  +----------------+                            +----------------+                |
|  |                |                            |                |                |
|  |  参数控制管理   |<---------------------------|   后处理模块    |                |
|  |                |                            |                |                |
|  +----------------+                            +----------------+                |
|         ^                                               |                        |
|         |                                               v                        |
|         |                                      +----------------+                |
|         |                                      |                |                |
|         +--------------------------------------|   显示模块     |                |
|                                               |                |                |
|                                               +----------------+                |
|                                                                                  |
+----------------------------------------------------------------------------------+
```

### 2.1 图像设备模块

图像设备模块是超声系统与FPGA硬件交互的桥梁，负责数据获取和设备控制。

```
+----------------------------------图像设备模块------------------------------------+
|                                                                                 |
|  +----------------+      +----------------+      +----------------+             |
|  |                |      |                |      |                |             |
|  |  FPGA数据接收器 |----->|  信息包解析器   |----->|  数据包分发器   |             |
|  |                |      |                |      |                |             |
|  +----------------+      +----------------+      +----------------+             |
|         ^                                              |                        |
|         |                                              v                        |
|  +----------------+      +----------------+      +----------------+             |
|  |                |      |                |      |                |             |
|  |  参数下发管理器 |<-----|  控制表生成器   |<-----|  数据块构建器   |             |
|  |                |      |                |      |                |             |
|  +----------------+      +----------------+      +----------------+             |
|                                                                                 |
+---------------------------------------------------------------------------------+
```

**主要职责**：
- 从FPGA获取BF、IQ或线数据等数据包
- 解析信息包，提取探头码、FPGA版本、探头温度、EC硬件信息等
- 设置参数，以控制表、数据块方式下发来控制超声工作和扫查
- 作为上层软件和FPGA的主要通讯协议接口

**核心组件**：
- **FPGA数据接收器**：建立与FPGA的通信连接，接收原始数据包
- **信息包解析器**：解析各类信息包，提取硬件信息
- **数据包分发器**：根据数据类型将数据包分发给适当的处理模块
- **控制表生成器**：根据参数配置生成控制表
- **数据块构建器**：构建连续内存数据块
- **参数下发管理器**：负责将控制表和数据块下发至FPGA

### 2.2 前处理模块

前处理模块接收来自图像设备模块的原始数据，并进行初步处理和转换。

```
+----------------------------------前处理模块-------------------------------------+
|                                                                                |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  数据包拆包器   |----->|  数据校验器     |----->|  数据转换控制器 |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|                                                         |                      |
|                                                         v                      |
|                                               +----------------+               |
|                                               |                |               |
|                                               |  Zeus转换接口   |               |
|                                               |                |               |
|                                               +----------------+               |
|                                                                                |
+--------------------------------------------------------------------------------+
```

**主要职责**：
- 接收FPGA上传的数据包并进行拆包
- 对数据包进行校验，确保数据完整性
- 调用Zeus模块将IQ或BF数据转换为线数据

**核心组件**：
- **数据包拆包器**：将原始数据包拆分为可处理的数据单元
- **数据校验器**：检查数据完整性和有效性
- **数据转换控制器**：根据数据类型选择合适的转换方式
- **Zeus转换接口**：与Zeus模块集成，用于数据格式转换

### 2.3 缓存模块

缓存模块负责存储前处理后的线数据，支持冻结和回放功能。

```
+----------------------------------缓存模块--------------------------------------+
|                                                                                |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  线数据接收器   |----->|  缓冲区管理器   |----->|  数据检索器     |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|                                  |                      |                      |
|                                  v                      v                      |
|                          +----------------+     +----------------+             |
|                          |                |     |                |             |
|                          |  帧管理器       |---->|  回放控制器     |             |
|                          |                |     |                |             |
|                          +----------------+     +----------------+             |
|                                                                                |
+--------------------------------------------------------------------------------+
```

**主要职责**：
- 将前处理出来的线数据缓存起来
- 管理和组织缓存的数据
- 支持冻结和回放功能

**核心组件**：
- **线数据接收器**：接收来自前处理模块的线数据
- **缓冲区管理器**：管理内存缓冲区，优化存储效率
- **数据检索器**：提供数据快速检索能力
- **帧管理器**：管理帧序列，支持帧选择
- **回放控制器**：控制回放速度、方向和范围

### 2.4 后处理模块

后处理模块处理缓存的线数据，通过调用各种处理算法和库，生成最终的图像数据。

```
+----------------------------------后处理模块-------------------------------------+
|                                                                                |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  线数据处理器   |----->|  Zeus处理接口   |<---->|  MRD优化接口   |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|         |                       |                        |                     |
|         |                       v                        |                     |
|         |               +----------------+               |                     |
|         |               |                |               |                     |
|         |               |  回调处理管理器  |               |                     |
|         |               |                |               |                     |
|         |               +----------------+               |                     |
|         |                       |                        |                     |
|         v                       v                        v                     |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  3D重建接口     |<-----|  图像合成器     |<-----|  DSC处理接口    |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|                                                                                |
+--------------------------------------------------------------------------------+
```

**主要职责**：
- 接收线数据并设置给Zeus处理引擎
- 通过Zeus进行DSC和图像处理
- 调用MRD库进行图像优化
- 调用3D库进行重建
- 生成最终的RGBA整帧图像

**核心组件**：
- **线数据处理器**：接收和预处理线数据
- **Zeus处理接口**：与Zeus引擎交互，进行基础图像处理
- **MRD优化接口**：调用MRD库进行图像优化
- **回调处理管理器**：管理各种处理回调
- **DSC处理接口**：数字扫描转换接口
- **图像合成器**：合成各种处理后的图像数据
- **3D重建接口**：调用3D库进行立体重建

### 2.5 显示模块

显示模块接收处理后的图像，根据布局和显示模式进行最终渲染和显示。

```
+----------------------------------显示模块--------------------------------------+
|                                                                                |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  图像接收器     |----->|  布局管理器     |----->|  渲染引擎       |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|                                  |                      |                      |
|                                  v                      v                      |
|                          +----------------+     +----------------+             |
|                          |                |     |                |             |
|                          |  显示模式控制器  |---->|  UI叠加层管理器 |             |
|                          |                |     |                |             |
|                          +----------------+     +----------------+             |
|                                                                                |
+--------------------------------------------------------------------------------+
```

**主要职责**：
- 接收来自后处理模块的整帧图像
- 根据布局和模式进行渲染显示
- 提供图像交互功能

**核心组件**：
- **图像接收器**：接收处理完成的图像
- **布局管理器**：管理多图像的布局显示
- **渲染引擎**：高效渲染图像
- **显示模式控制器**：控制不同的显示模式
- **UI叠加层管理器**：管理UI层与图像层的叠加

### 2.6 参数控制管理

贯穿各模块的参数管理系统，负责协调各模块参数设置。

```
+----------------------------------参数控制管理-----------------------------------+
|                                                                                |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  |  参数存储管理器 |<---->|  参数计算引擎   |<---->|  参数依赖分析器 |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|         ^                       ^                       ^                      |
|         |                       |                       |                      |
|         v                       v                       v                      |
|  +----------------+      +----------------+      +----------------+            |
|  |                |      |                |      |                |            |
|  | 图像设备参数接口 |      | 图像处理参数接口 |      | 显示参数接口    |            |
|  |                |      |                |      |                |            |
|  +----------------+      +----------------+      +----------------+            |
|                                                                                |
+--------------------------------------------------------------------------------+
```

**主要职责**：
- 管理各模块参数
- 处理参数间的依赖和计算关系
- 提供参数接口给各模块

**核心组件**：
- **参数存储管理器**：集中存储和管理所有参数
- **参数计算引擎**：根据参数间关系计算衍生参数
- **参数依赖分析器**：分析参数间的依赖关系
- **图像设备参数接口**：与图像设备模块交互的参数接口
- **图像处理参数接口**：与前处理、后处理模块交互的参数接口
- **显示参数接口**：与显示模块交互的参数接口

## 3. 模块交互流程

1. **数据采集流程**：
   - 图像设备模块从FPGA获取原始数据
   - 图像设备模块解析信息包，获取硬件信息
   - 数据包传递给前处理模块

2. **数据处理流程**：
   - 前处理模块拆包和校验数据
   - 前处理模块通过Zeus将IQ/BF数据转换为线数据
   - 线数据传递给缓存模块存储
   - 缓存模块将线数据传递给后处理模块
   - 后处理模块通过Zeus进行DSC和图像处理
   - 后处理模块调用MRD库进行图像优化
   - 后处理模块调用3D库进行重建（如需要）
   - 生成RGBA整帧图像传递给显示模块

3. **图像显示流程**：
   - 显示模块接收整帧图像
   - 显示模块根据布局和模式渲染图像
   - 显示模块更新UI叠加层
   - 最终图像呈现给用户

4. **参数控制流程**：
   - 参数控制管理接收用户参数设置
   - 参数依赖分析器分析参数依赖关系
   - 参数计算引擎计算衍生参数
   - 更新后的参数分发给各相关模块

## 4. 关键技术点

1. **高效数据传输**：优化模块间数据传输，避免不必要的数据拷贝
2. **内存管理**：缓存模块高效管理内存使用，平衡性能和资源消耗
3. **并行处理**：充分利用多核CPU和GPU资源进行并行处理
4. **实时性能**：保证从数据获取到图像显示的全流程延迟控制在可接受范围
5. **可扩展性**：模块化设计支持新功能和新算法的无缝集成
