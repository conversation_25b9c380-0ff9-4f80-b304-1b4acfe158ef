

前处理相关问题汇总——清单

1. 什么是【前处理】？它包含哪些基本过程？
2. 前处理中的专业术语（概念）包括哪些？每个概念分别处于什么范畴（上下文）？他们的基本含义？
3. 什么是线数据？线数据来源？线数据会被哪些地方使用？如何使用？接收线数据的链路？
4. 线数据传输协议的作用，什么阶段会使用到该协议，协议制定者和协议的管理维护，不同机型的协议是否一致？
6. 现有超声软件架构中，【前处理】包括哪些重要类？软件设计？类之间交互关系？



尝试回答：

1. 【前处理】是相对【后处理】来说，整个【图像处理】包括【前处理】和【后处理】。总的来说，【前处理】负责将探头扫查的物理信息（超声波）转为有效的数字化信息（图像数据），该图像数据可以是线数据或整帧数据。【后处理】负责再对这些图像数据进行各种处理，最终产出可以渲染到屏幕上的RGB图。具体来说，【前处理】包括FPGA控制，数据采集，数据解析，数据前端处理（zeus）等过程，难度在于需要和FPGA部门深入合作。
2. 【前处理】中的术语主要有：设备I/O通讯（PCIe,USB,Wi-Fi,DMA）、探头信息（探头码,基元,类型）、FPGA升级、控制表、数据块、预设（检查模式）、声功率、声功率压制、线数（起始线、终止线）、线数据传输协议、深度、CQYZ、焦点、频率、扫查模式、I/Q数据、超声参数等。

> > * 控制表和数据块都用于控制FPGA的底层处理模块，控制表大小一般1k或2k，可以看作是一块特殊的数据块。数据块分为直接下发的固定式数据块和待定下发的参数式数据块。所谓参数式数据块就是跟超声参数绑定，当超声参数变化时除了修改控制表，还要根据当前值从相关联的数据块中抽取数据下发给FPGA，抽取规则见：[控制表和数据块 - 软件开发 - Confluence](http://192.168.20.40:8090/pages/viewpage.action?pageId=2097926)。
> >
> > * I/O通讯作为硬件与软件传递数据的桥梁，涉及驱动的正确使用，目前包括PCIe,USB,Wi-Fi,DMA几类，软件实现中一般在非主线程上进行。
> > * 探头信息来自FPGA上传的信息包，软件通过解析信息包先识别到探头。解析方式按照FPGA定义好的协议。
> > * 预设参数，就是提前存储的一套配置，主要包括三大类：探头、频率、检查模式。目前探头和频率参数存储在文件中，检查模式参数存储在数据库。非预设参数就是软件计算出的参数。
> > * 声功率是探头发射超声波的能量度量，其功率在一定范围内才能保证超声图像清晰，但过大的功率可能使MI/TI过高，危害人体组织，因此需要声功率压制。
> > * 线数与点数的讨论范畴是线数据的上下文，线数据来自于FPGA上来的包数据，包数据分为原始数据，I/Q数据等，经过前处理变为线数据。在前处理过程中，要确定线数，线间隔，点数等。他们有一些基本的关系，线数与扫描宽度有关，又由起始线与终止线直接推算；点数由系统组严格定义，是一种系统性方案配置；线间隔与线密度相关。
> > * 深度与CQYZ存在直接联系，CQYZ越大，深度越深。具体见[CQYZ与图像深度 - 软件开发 - Confluence](http://192.168.20.40:8090/pages/viewpage.action?pageId=38964308)。
> > * 探头除了Wafer，Pitch，MinCQYZ，MaxCQYZ等基本概念外，还有对应的频率参数。频率参数定义该探头的某个预设下的射频方式，它与频率的高低，THI基波谐波，compound（包括频率复合与空间复合）都相关。从物理原理来说，频率越高，超声波穿透性越弱，检测深度就越浅，但分辨率高。
> > * 扫查模式基本上分为：B,C,PW,M这几种，其他一些大都是基础模式的变体。具体见[扫查模式 - 软件开发 - Confluence](http://192.168.20.40:8090/pages/viewpage.action?pageId=2097562)。扫查模式首先是硬件层面需要支持，即探头是否支持，然后再是软件来实现上层。例如TDI模式一般只有相控阵探头才支持。
> > * I/Q数据用于彩色模式和多普勒模式，即会用到多普勒效应模式。I/Q信号源于DSP信号处理，具体是探头接收回波信号，从中提取多普勒频移分量，再通过混频与滤波，将高频转为低频，再将低频信号进行正交解调就得到I/Q信号，再经过FFT变换即可得到具体的多普勒频谱，然后就能求解速度。I/Q信号是FPGA处理好发给软件，软件将I/Q数据再传给专门的前处理模块，该模块主要就是做FFT变换。
> > * 超声参数是超声系统控制中的关键，它大体分为控制表和非控制表两类，前者直接作用于FPGA，后者在软件中进行逻辑运算与控制。代码中有一套完备的超声参数的获取、下发和更新逻辑，它是超声软件最关键的骨架。并不是所有超声参数用户可参与调节，事实上只有少数超声参数直接对应用户UI上可调项，整理在[可调超声参数含义 - 软件开发 - Confluence](http://192.168.20.40:8090/pages/viewpage.action?pageId=38964608)



3. 线数据上下文：开启probe scanning后，软件根据线数据传输协议，对FPGA上来的数据（称为包数据）进行拆包解析，对于简单扫查模式，解析出来直接就是线数据；对于I/Q这样的，需要FFT处理变为线数据（可由专门模块处理）。线数据有线数，点数，buffer大小，帧号等基本属性，一般规格例如“720×512× 8”，代表720点数，512根线，每个点8bit。这种规格的定义是整个系统方案的体现。探头设备打开后，IO读线程开启后一直在收数据，直到收满一帧（例如32个包）后再发信号通知上层软件来取数据。一帧的数据可保存为一个文件，它就是线数据，是软件进而处理的数据单元，即按帧为最小单位进行处理。一帧数据可以由线长和线宽被描述，线长可以由点数（PointNumPerLine） 代表，线宽可以由线数代表。线长影响深度，线宽影响扫描宽度，同时他们都影响FPS。之后，线数据会被【后处理】使用，例如传入zeus图像算法处理，再结合colormap，最终变为可渲染的RGB图像。

4. FPGA上数据传输中分为数据包和信息包，线数据传输协议是用来解析数据包和信息包的。信息包中信息包括探头码、实际发射线数等信息。探头识别环节需要这些信息。线数据传输协议由FPGA部分统一制定，原则上所有平台所有机型的FPGA的线数据传输协议都是一致的。当然，存在不同时需要特别注意处理。

5. 【前处理】包含模块主要有bfiodevice、beamformer模块，usapi中的imagedatareceiver。具体的实现可参考：[上数据缓存流程 - 软件开发 - Confluence](http://192.168.20.40:8090/pages/viewpage.action?pageId=6226962)。

> > * bfiodevice模块：设备的创建,读写线程的开启都是由BeamFormer模块控制的。探头数据是由BeamFormer通过信号槽转发到ChisonUltrasoundContext中，再由该Context转给具体的接收对象。
> > * imagedatareceiver模块：负责解析，处理，转存线数据。
> > * sonobuffer模块：缓存线数据。



>  通过信号与槽体现的数据链路如下：

> 1. [ImageManager] startRealtimeSystem() --call-- [UltrasoundDevice] open()
> 2. [UltrasoundDevice] open() --call-- [IoThreadForRead] start() --call-- [IoThreadForRead] run()
> 3. [IoThreadForRead] run() --call-- emit update(uchar*, qint32)
> 4. [IoThreadForRead] SIGNAL(update(uchar*, qint32)) -> [UltrasoundDevice] SLOT(dataReaded(uchar* data, qint32 len))
> 5. [UltrasoundDevice] SLOT(dataReaded(uchar* data, qint32 len)) --call-- [BeamFormerBase] update(unsigned char* buf, int len)
> 6. [BeamFormerBase] update(unsigned char* buf, int len) --call-- emit updateImageData(ByteBuffer(buf, len));
> 7. [IBeamFormer] SIGNAL(updateImageData(ByteBuffer)) -> [ChisonUltrasoundContext] SIGNAL(updateImageData(ByteBuffer))
> 8. [ChisonUltrasoundContext] SIGNAL(updateImageData(ByteBuffer)) -> [ImageDataReceiverSwitcher] SIGNAL(updateImageData(ByteBuffer))
> 9. [ImageDataReceiverSwitcher] SIGNAL(updateImageData(ByteBuffer)) -> [IImageDataReceiver] SLOT(receive(ByteBuffer))
