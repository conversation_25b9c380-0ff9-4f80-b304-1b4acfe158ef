# 超声软件模块依赖关系图

## 1. 总体依赖结构

```
+-------------+     +---------------+     +---------------+
|    app      | --> |  usf.program  | --> |  usf.interface|
+-------------+     +---------------+     +---------------+
                                                  ^
                                                  |
            +--------------------------------------+
            |               |            |         |
+-----------v----+ +--------v-----+ +----v-------+ +--------v-----+
| usf.peripheral | |  usf.preset  | | usf.imaging| |   usf.mark   |
+----------------+ +--------------+ +------------+ +--------------+
                                         |                |
                                    +----v-------+  +-----v------+
                                    | usf.common |  | usf.exam   |
                                    +------------+  +------------+
```

## 2. 模块依赖关系详细分析

### 2.1 核心依赖链

1. **应用程序初始化链**
   - `app` → `usf.program` → `usf.interface`

2. **图像处理链**
   - `usf.interface.imaging` → `usf.imaging` → `usf.imaging.beamformer`/`usf.imaging.common`
   - `usf.imaging` → `usf.peripheral` (探头/设备控制)

3. **用户界面链**
   - `usf.interface.framewnd` → `usf.exam`/`usf.mark`/`usf.peripheral`

4. **数据存储链**
   - `usf.imaging` → `usf.preset` → (文件系统存储)

### 2.2 跨模块耦合点

| 源模块 | 目标模块 | 耦合方式 | 耦合强度 |
|--------|----------|----------|----------|
| ImageManager | PeripheralManager | 成员引用 | 强耦合 |
| ImageManager | ExamManager | 成员引用 | 强耦合 |
| ImageManager | MarkManager | 成员引用 | 强耦合 |
| ImageManager | StateManager | 成员引用 | 强耦合 |
| usf.imaging | usf.peripheral | 接口依赖 | 中等 |
| usf.exam | usf.imaging | 接口依赖 | 中等 |
| usf.mark | usf.imaging | 接口依赖 | 中等 |

## 3. 循环依赖分析

以下是识别出的潜在循环依赖：

1. **图像管理-检查管理循环**
   ```
   ImageManager.setExamManager(IExamManager*)
   ExamManager.setImageManager(IImageManager*)
   ```

2. **图像管理-标记管理循环**
   ```
   ImageManager.setMarkManager(IMarkManager*)
   MarkManager.setImageManager(IImageManager*)
   ```

## 4. 关键类的依赖结构

### 4.1 `ImageManager`类的依赖结构

```
                +----------------+
                | IStateManager  |
                +----------------+
                        ^
                        |
+----------------+      |      +----------------+
| IExamManager   | <--> |      | IMarkManager   |
+----------------+      |      +----------------+
        ^               |               ^
        |               |               |
        |        +------v-------+       |
        +------> | ImageManager | <-----+
                 +--------------+
                        |
                        v
                +----------------+
                | IPeripheralMgr |
                +----------------+
```

### 4.2 外设管理的依赖结构

```
+----------------+      +----------------+
| ProbeManager   | ---> | IProbeDataSet  |
+----------------+      +----------------+
        |
        v
+----------------+
| PeripheralMgr  | ---> 硬件设备访问层
+----------------+
```

## 5. 横切关注点分析

系统中存在的横切关注点包括：

1. **状态管理**：贯穿多个模块的状态迁移逻辑
2. **日志记录**：分散在各模块中的日志记录功能 
3. **错误处理**：缺乏统一的错误处理机制
4. **配置管理**：多个模块都需访问配置信息

## 6. 不合理依赖识别

### 6.1 图像管理器过度依赖

`ImageManager`类依赖了过多的其他管理器：
- 外设管理器
- 检查管理器
- 标记管理器
- 状态管理器
- 磁盘设备
- 色彩映射管理器
- 探头数据集
等

这种"上帝类"模式导致职责过重，维护困难。

### 6.2 UI与业务逻辑耦合

在多个模块中，UI代码与业务逻辑没有清晰分离，导致代码重用困难，测试复杂。

### 6.3 直接依赖，缺少抽象层

部分模块间直接依赖具体实现类，而非接口，增加了模块间耦合度。

## 7. 重构方向

### 7.1 拆分ImageManager

将`ImageManager`按功能域拆分为多个管理器：
- **ProbeInteractionManager**: 处理与探头的交互
- **ImageProcessingManager**: 处理图像处理流程
- **ImageDisplayManager**: 处理图像显示逻辑

### 7.2 引入中介者模式

为解决多个管理器之间的复杂交互，引入中介者模式：
```
+----------------+      +----------------+
| ImageManager   | ---> |                |
+----------------+      |                |
                        |                |
+----------------+      |   ModuleHub    |
| ExamManager    | ---> |   (中介者)     |
+----------------+      |                |
                        |                |
+----------------+      |                |
| MarkManager    | ---> |                |
+----------------+      +----------------+
```

### 7.3 应用依赖倒置原则

重新设计接口层次，确保高层模块不依赖低层模块：
```
+----------------+      +----------------+
| 高层模块       | ---> | 抽象接口       |
+----------------+      +----------------+
                               ^
                               |
                        +----------------+
                        | 低层模块       |
                        +----------------+
``` 