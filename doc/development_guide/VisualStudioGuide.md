# Visual Studio 项目开发指南

本文档提供了使用 Visual Studio 2019 打开、编译和运行 xultrasound 项目的详细步骤。

## 目录

- [环境准备](#环境准备)
- [打开项目](#打开项目)
- [编译项目](#编译项目)
- [运行时依赖配置](#运行时依赖配置)
- [运行与调试](#运行与调试)
- [常见问题](#常见问题)

## 环境准备

在开始之前，请确保您的系统已安装以下组件：

- Visual Studio 2019（推荐使用 Enterprise、Professional 或 Community 版本）
- CMake 3.16 或更高版本
- Qt 5.15.2（安装在 D:/Qt/5.15.2/msvc2019_64 或自定义路径）
- Boost 1.75.0（安装在 D:/boost_1_75_0 或自定义路径）

## 打开项目

### 使用 Visual Studio 打开 CMake 项目

1. 启动 Visual Studio 2019
2. 点击菜单栏 "文件" → "打开" → "文件夹"
3. 选择 CMakeLists.txt 所在的根目录（即项目根文件夹）
4. Visual Studio 会自动识别 CMake 项目并开始配置
   - 首次打开时：Visual Studio 会自动生成构建缓存（在项目根目录下的 out/build 文件夹）

## 编译项目

### 使用 Visual Studio 编译

1. **选择构建配置**：
   - 在工具栏的下拉菜单中，选择构建配置（Debug 或 Release）
   - 选择平台（通常为 x64）

2. **生成解决方案**：
   - 点击 "生成" → "生成解决方案"（或使用快捷键 F7）
   - 或者右键点击解决方案资源管理器中的项目，选择"生成"

## 运行时依赖配置

为了确保程序能够正确运行，需要配置运行时依赖库的路径。Visual Studio会自动生成并管理CMakePresets.json文件来处理这些配置。

### 环境配置

1. **Visual Studio自动配置**
   - Visual Studio会在首次打开项目时自动生成CMakePresets.json
   - 该文件包含了项目的构建配置和环境变量设置
   - 该文件已被添加到.gitignore中，每个开发者会有自己的本地配置

2. **关键环境变量**
   以下环境变量会被自动配置到PATH中：
   - Qt库路径：
     ```
     C:/Qt/5.15.2/msvc2019_64/bin
     D:/Qt/5.15.2/msvc2019_64/bin
     ```
   - 项目生成的库路径：
     ```
     ${sourceDir}/out/build/${presetName}/bin/lib
     ${sourceDir}/out/build/${presetName}/bin
     ```
   - 例如：
     ```
     "environment": {
           "PATH": "C:/Qt/5.15.2/msvc2019_64/bin;D:/Qt/5.15.2/msvc2019_64/bin;${sourceDir}/out/build/${presetName}/bin/lib;${sourceDir}/out/build/${presetName}/bin;${sourceDir}/thirdparty/windows_x86_64bit/bin"
         },
     ```

3. **自定义配置**
   如果需要自定义配置，可以：
   - 在Visual Studio中修改CMake设置
   - 使用"CMake工具"窗口管理配置
   - 在需要时手动编辑CMakePresets.json（不推荐）

4. **修改构建参数**
   可以通过以下方式修改构建参数（如机型配置）：
   - 在Visual Studio中：
     1. 打开"CMake工具"窗口（视图 → CMake工具）
     2. 展开"缓存变量"
     3. 找到需要修改的变量（如MODEL）
     4. 双击进行编辑
   
   - 通过CMakePresets.json：
     ```json
     "cacheVariables": {
       "CMAKE_BUILD_TYPE": "Debug",
       "CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}",
       "MODEL": "SonoMax"  // 修改这里的值来更改机型
     }
     ```
   
   - 常用参数说明：
     - MODEL：设置机型（如"SonoMax"）
     - CMAKE_BUILD_TYPE：构建类型（Debug/Release）
     - CMAKE_INSTALL_PREFIX：安装路径

### 配置生效

1. Visual Studio会自动检测配置变更并重新生成CMake缓存
2. 如果需要手动重新生成：
   - 选择"项目" → "重新生成CMake缓存"
   - 或删除build目录后重新生成

### 验证配置

1. 使用Visual Studio的依赖项查看器检查DLL依赖
2. 运行程序，确认没有DLL加载错误
3. 如果出现DLL加载错误：
   - 检查Qt安装路径是否正确
   - 验证环境变量是否正确配置

## 运行与调试

### 设置启动项

1. 在解决方案资源管理器中，右键点击要运行的可执行文件项目
2. 选择"设为启动项目"

### 启动调试

- 按 F5 开始调试
- 或点击 "调试" → "开始调试"
- 或点击工具栏上的"开始"按钮（绿色三角形）

### 不调试运行

- 按 Ctrl+F5 开始运行（不调试）
- 或点击 "调试" → "开始执行（不调试）"

## 常见问题

### 1. CMake 配置失败

**问题**：Visual Studio 无法配置 CMake 项目

**解决方案**：
- 确保已安装 CMake 3.16 或更高版本
- 检查 CMakeLists.txt 文件是否有语法错误
- 确保已正确设置 Qt 和 Boost 的路径

### 2. 找不到 Qt 或 Boost 库

**问题**：编译时报错找不到 Qt 或 Boost 库

**解决方案**：
- 确保 Qt 和 Boost 已正确安装
- 在 CMakeSettings.json 中正确设置 Qt 和 Boost 的路径
- 手动设置 CMAKE_PREFIX_PATH 和 BOOST_ROOT 环境变量

### 3. 链接错误

**问题**：编译成功但链接失败

**解决方案**：
- 检查库版本是否匹配（特别是 Qt 和 Boost）
- 确保使用了正确的编译器和平台设置
- 检查项目依赖项是否已正确构建

---

如有任何问题或建议，请联系项目维护团队。 